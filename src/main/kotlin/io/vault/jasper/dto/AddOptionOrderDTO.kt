package io.vault.jasper.dto

import io.swagger.annotations.ApiModel
import io.swagger.annotations.ApiModelProperty
import io.vault.jasper.enums.ChainType

@ApiModel("添加期权合约DTO")
data class AddOptionOrderDTO(
    @ApiModelProperty("链", required = true)
    val chain: ChainType,
    @ApiModelProperty("期权合约交易Hash", required = true)
    val txHash: String,
    @ApiModelProperty("期权合约交易数量", required = false)
    val inputBidAmount: String?,
    @ApiModelProperty("排除OrderID", required = false)
    val exceptOrderId: String?,
)
