package io.vault.jasper.model

import io.vault.jasper.enums.OptionDirection
import io.vault.jasper.enums.Symbol
import org.springframework.data.annotation.CreatedDate
import org.springframework.data.annotation.Id
import org.springframework.data.annotation.LastModifiedDate
import org.springframework.data.mongodb.core.index.Indexed
import org.springframework.data.mongodb.core.mapping.Document
import org.springframework.data.mongodb.core.mapping.Field
import java.math.BigDecimal
import java.math.BigInteger
import java.time.LocalDateTime

@Document("airdrop_trade_rebate_record")
data class AirdropTradeRebateRecord(
    @Id
    val id: String? = null,
    @Field("option_order_id")
    @Indexed(unique = true, background = true)
    val optionOrderId: String,
    @Field("buyer_address")
    @Indexed(background = true)
    val buyerAddress: String,
    var direction: OptionDirection,
    @Field("bid_amount")
    var bidAmount: BigDecimal = BigDecimal("0.2"),
    @Field("bid_asset")
    var bidAsset: Symbol = Symbol.ETH,
    @Field("strike_price")
    var strikePrice: BigDecimal = BigDecimal.ZERO,
    @Field("settlement_price")
    var settlementPrice: BigDecimal = BigDecimal.ZERO,
    @Field("premium_fee")
    var premiumFee: BigDecimal = BigDecimal.ZERO,
    @Field("premium_asset")
    var premiumAsset: Symbol = Symbol.USDT,
    @Field("premium_fee_in_usdt")
    var premiumFeeInUsdt: BigDecimal = BigDecimal.ZERO,
    var profit: BigDecimal = BigDecimal.ZERO,
    @Field("profit_asset")
    var profitAsset: Symbol = Symbol.USDT,
    @Field("profit_in_usdt")
    var profitInUsdt: BigDecimal = BigDecimal.ZERO,
    @Field("net_profit")
    var netProfit: BigDecimal = BigDecimal.ZERO,
    @Field("rebate_amount")
    var rebateAmount: BigDecimal = BigDecimal.ZERO,
    @Field("discord_level")
    var discordLevel: Int = 0,
    var status: AirdropTradeRebateRecordStatus = AirdropTradeRebateRecordStatus.CREATED,
    @Field("settle_tx_id")
    var settleTxId: String? = null,
    @Field("arb_settle_tx_id")
    var arbSettleTxId: String? = null,
    @CreatedDate
    val created: LocalDateTime = LocalDateTime.now(),
    @LastModifiedDate
    val updated: LocalDateTime = LocalDateTime.now()
) {}

enum class AirdropTradeRebateRecordStatus {
     CREATED, PENDING, SETTLED, ARB_SETTLED
}