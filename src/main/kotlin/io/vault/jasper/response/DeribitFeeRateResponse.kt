package io.vault.jasper.response

import io.swagger.annotations.ApiModel
import io.swagger.annotations.ApiModelProperty
import io.vault.jasper.enums.ChainType
import io.vault.jasper.enums.Symbol
import io.vault.jasper.model.OrderType

@ApiModel
data class DeribitFeeRateResponse(
    @ApiModelProperty("链类型", required = true)
    val chainType: ChainType,
    @ApiModelProperty("订单类型", required = true)
    val orderType: OrderType,
    @ApiModelProperty("标的资产", required = true)
    val underlyingAsset: Symbol,
    @ApiModelProperty("行权资产", required = true)
    val strikeAsset: Symbol,
    @ApiModelProperty("权利金费率", required = false)
    val rate: DeribitPriceResponse?,
    @ApiModelProperty("资产抵押数量", required = true)
    val availableUnderlyingAssetAmount: List<String>,
    @ApiModelProperty("到期时间", required = false)
    val expiryHour: Long?,
    @ApiModelProperty("到期时间列表", required = true)
    val expiryInHour: List<String>,
    @ApiModelProperty("权利金补充信息")
    val premiumFeeExtra: PremiumFeeExtra?
)

data class DeribitPriceResponse(
    @ApiModelProperty("价格", required = true)
    val price: String,
    @ApiModelProperty("询价时间", required = true)
    val timestamp: String,
    @ApiModelProperty("价格签名", required = true)
    val sign: String
)

data class PremiumFeeExtra(
    @ApiModelProperty("权利金费率", required = true)
    val rate: String?,
    @ApiModelProperty("原价", required = true)
    val originFee: String?
)