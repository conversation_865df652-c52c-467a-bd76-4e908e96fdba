package io.vault.jasper.model

import org.springframework.data.annotation.Id
import org.springframework.data.mongodb.core.index.Indexed
import org.springframework.data.mongodb.core.mapping.Document
import org.springframework.data.mongodb.core.mapping.Field
import java.math.BigDecimal
import java.time.LocalDateTime

@Document("btr_campaign_record")
data class BtrCampaignRecord(
    @Id
    val id: String? = null,
    
    @Field("address")
    @Indexed(background = true)
    val address: String,
    
    @Field("option_order_id")
    @Indexed(background = true)
    val optionOrderId: String,
    
    @Field("btr_amount")
    val btrAmount: BigDecimal,
    
    @Field("created_time")
    val createdTime: LocalDateTime = LocalDateTime.now()
) 