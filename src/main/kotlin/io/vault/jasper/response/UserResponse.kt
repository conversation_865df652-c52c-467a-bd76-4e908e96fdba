package io.vault.jasper.response

import io.swagger.annotations.ApiModel
import io.swagger.annotations.ApiModelProperty
import io.vault.jasper.enums.ChainType
import io.vault.jasper.enums.OptionDirection
import io.vault.jasper.enums.Symbol
import io.vault.jasper.model.OptionStatus

@ApiModel
data class UserResponse(

    @ApiModelProperty("钱包地址", required = true)
    val address: String,

    @ApiModelProperty("注册时间", required = true)
    val createDate: Long,

    @ApiModelProperty("邀请人钱包地址", required = false)
    val parentAddress: String?
)