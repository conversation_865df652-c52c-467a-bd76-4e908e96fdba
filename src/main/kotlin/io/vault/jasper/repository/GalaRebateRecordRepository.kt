package io.vault.jasper.repository

import io.vault.jasper.enums.ChainType
import io.vault.jasper.model.*
import org.springframework.data.domain.Page
import org.springframework.data.domain.Pageable
import org.springframework.data.mongodb.repository.MongoRepository
import org.springframework.stereotype.Repository

@Repository
interface GalaRebateRecordRepository : MongoRepository<GalaRebateRecord, String> {

    fun findByBuyerAddressIgnoreCase(
        buyerAddress: String
    ): List<GalaRebateRecord>

    fun findByStatus(
        status: GalaRebateRecordStatus
    ): List<GalaRebateRecord>

    fun findByStatusAndSettleTxIdIsNotNull(
        status: GalaRebateRecordStatus
    ): List<GalaRebateRecord>

    fun findByBuyerAddressIgnoreCaseAndStatusAndSettleTxIdIsNotNull(
        buyerAddress: String,
        status: GalaRebateRecordStatus
    ): List<GalaRebateRecord>

    fun findByBuyerAddressIgnoreCaseAndStatus(
        buyerAddress: String,
        status: GalaRebateRecordStatus
    ): List<GalaRebateRecord>

    fun findByBuyerAddressIgnoreCaseAndStatusIn(
        buyerAddress: String,
        statuses: List<GalaRebateRecordStatus>
    ): List<GalaRebateRecord>

}