package io.vault.jasper.repository

import io.vault.jasper.model.*
import org.springframework.data.mongodb.repository.MongoRepository
import org.springframework.stereotype.Repository

@Repository
interface AirdropNFTRebateRecordRepository : MongoRepository<AirdropNFTRebateRecord, String> {

    fun findByAddressIgnoreCase(
        buyerAddress: String
    ): AirdropNFTRebateRecord?

    fun findByStatus(
        status: AirdropNFTRebateRecordStatus
    ): List<AirdropNFTRebateRecord>
}