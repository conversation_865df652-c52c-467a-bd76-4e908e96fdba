package io.vault.jasper.task

import io.vault.jasper.enums.ChainType
import io.vault.jasper.model.UserTradeRebateStatus
import io.vault.jasper.repository.UserTradeRebateRecordRepository
import io.vault.jasper.service.UserTradeRebateService
import io.vault.jasper.utils.ProfileUtil
import org.slf4j.LoggerFactory
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.beans.factory.annotation.Value
import org.springframework.context.annotation.Profile
import org.springframework.scheduling.annotation.Scheduled
import org.springframework.stereotype.Service

@Service
@Profile("prod", "test")
class UserTradeRebateProcessor @Autowired constructor(
    private val userTradeRebateRecordRepository: UserTradeRebateRecordRepository,
    private val userTradeRebateService: UserTradeRebateService
) {

    private val logger = LoggerFactory.getLogger(this::class.java)

    @Value("\${schedule_task_on}")
    lateinit var scheduleOn: String

    // 每5分钟执行一次
    @Scheduled(fixedDelay = 5 * 60 * 1000, initialDelay = 60 * 1000)
    fun run() {
        if (scheduleOn == "false") {
            return
        }

        try {
            exec()
        } catch (e: Exception) {
            logger.error("Error processing user trade rebates: ${e.message}", e)
        }
    }

    private fun exec() {
        logger.info("Starting user trade rebate processor")

        if(ProfileUtil.activeProfile == "prod"){
            changeStatusToPending()
        }

        // 获取所有未结算的交易返利记录
        val pendingRebates = userTradeRebateRecordRepository.findByStatus(UserTradeRebateStatus.PENDING)
        logger.info("Found ${pendingRebates.size} pending trade rebates")

        // 按链分组
        val rebatesByChain = pendingRebates.groupBy { it.chain }

        // 逐链批量结算
        for ((chain, rebates) in rebatesByChain) {
            try {
                logger.info("Processing ${rebates.size} rebates for chain: $chain")
                userTradeRebateService.batchSettleTradeRebates(chain)
            } catch (e: Exception) {
                logger.error("Error batch settling trade rebates for chain $chain: ${e.message}", e)

                // 如果批量处理失败，尝试逐个处理
                logger.info("Falling back to individual processing for chain: $chain")
                for (rebate in rebates) {
                    try {
                        userTradeRebateService.settleTradeRebate(rebate)
                    } catch (e: Exception) {
                        logger.error("Error settling individual trade rebate ${rebate.id}: ${e.message}", e)
                    }
                }
            }
        }

        logger.info("Completed user trade rebate processor")
    }

    private fun changeStatusToPending() {
        val createdRebates = userTradeRebateRecordRepository.findByStatus(UserTradeRebateStatus.CREATED)
        if (createdRebates.isNotEmpty()) {
            logger.info("Found ${createdRebates.size} created trade rebates, updating to pending status")
            createdRebates.forEach { rebate ->
                rebate.status = UserTradeRebateStatus.PENDING
                userTradeRebateRecordRepository.save(rebate)
            }
        }
    }
}
