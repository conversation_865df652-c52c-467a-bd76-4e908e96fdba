package io.vault.jasper.service

import com.fasterxml.jackson.databind.JsonNode
import com.fasterxml.jackson.databind.ObjectMapper
import io.vault.jasper.model.DiscordInfo
import io.vault.jasper.model.User
import io.vault.jasper.repository.UserRepository
import org.slf4j.LoggerFactory
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.beans.factory.annotation.Value
import org.springframework.http.HttpEntity
import org.springframework.http.HttpHeaders
import org.springframework.http.HttpMethod
import org.springframework.http.MediaType
import org.springframework.stereotype.Service
import org.springframework.util.LinkedMultiValueMap
import org.springframework.web.client.RestTemplate
import org.springframework.web.util.UriComponentsBuilder
import java.net.URLEncoder
import java.nio.charset.StandardCharsets
import java.time.LocalDateTime

@Service
class DiscordService @Autowired constructor(
    private val userRepository: UserRepository
){

    private val logger = LoggerFactory.getLogger(this::class.java)

    private val endpoint = "https://discord.com/api/v10"

    @Value("\${discord.client.id}")
    private lateinit var clientId: String

    @Value("\${discord.client.secret}")
    private lateinit var clientSecret: String

    @Value("\${discord.client.callback_uri}")
    private lateinit var callbackUrl: String

    @Value("\${discord.client.token_url}")
    private lateinit var tokenUrl: String

    @Value("\${discord.client.authorization_url}")
    private lateinit var clientAuthorizationUrl: String

    @Value("\${discord.bot_token}")
    private lateinit var botToken: String

    @Value("\${discord.guild_id}")
    lateinit var guildId: String

    // private val restTemplate: RestTemplate = RestTemplate()
    private val restTemplate: RestTemplate

    private val objectMapper = ObjectMapper()

    // for test
    // val requestFactory = SimpleClientHttpRequestFactory()
    // val proxy = Proxy(Proxy.Type.HTTP, java.net.InetSocketAddress("127.0.0.1", 7897))
    // requestFactory.setProxy(proxy)
    // val apiEndpoint = "https://discord.com/api/v10"
    // val url = "${apiEndpoint}/oauth2/token"
    // val clientId = "1278987034668564491"
    // val clientSecret = "Wl7-EhjCQytmnEbR6feYO1KPZTUHa73Q"
    // val data = LinkedMultiValueMap<String, String>()
    // data.add("grant_type", "client_credentials")
    // data.add("scope", "identify connections")
    // val headers = HttpHeaders()
    // headers.setBasicAuth(clientId, clientSecret)
    // headers.contentType = MediaType.APPLICATION_FORM_URLENCODED
    // val entity = HttpEntity(data, headers)
    // val restTemplate = RestTemplate(requestFactory)
    // val responseEntity = restTemplate.postForEntity(url, entity, String::class.java)
    // if (responseEntity.statusCodeValue == 200) {
    //     val objectMapper = ObjectMapper()
    //     val responseJson = objectMapper.readTree(responseEntity.body)
    //     val tokenType = responseJson["token_type"].asText()
    //     val accessToken = responseJson["access_token"].asText()
    //     val expiresIn = responseJson["expires_in"].asInt() // seconds
    //     logger.info("response: ${responseEntity.body}")
    //
    //     val userId = "stars2011"
    //     val userUrl = "${apiEndpoint}/users/${userId}"
    // }

    init {
        // val requestFactory = SimpleClientHttpRequestFactory()
        // val proxy = Proxy(Proxy.Type.HTTP, InetSocketAddress("127.0.0.1", 7897))
        // requestFactory.setProxy(proxy)
        // restTemplate = RestTemplate(requestFactory)
        restTemplate = RestTemplate()
    }

    private fun getHeader(tokenType: String? = null, accessToken: String? = null, baseAuth: Pair<String, String>? = null): HttpHeaders {
        val headers = HttpHeaders()
        if (tokenType != null && accessToken != null) {
            headers.add("Authorization", "$tokenType $accessToken")
        } else {
            headers.add("Authorization", "Bot $botToken")
        }
        if (baseAuth != null) {
            headers.setBasicAuth(baseAuth.first, baseAuth.second)
        }

        return headers
    }

    // fun getUserById(userId: String): JsonNode? {
    //     val url = "${endpoint}/users/$userId"
    //     val entity = HttpEntity(null, getHeader())
    //     val response = try {
    //         restTemplate.exchange(url, HttpMethod.GET, entity, String::class.java)
    //     } catch (e: Exception) {
    //         logger.error("$url error: ${e.message}")
    //         return null
    //     }
    //     if (response.statusCodeValue != 200) {
    //         logger.error("$url error, status code: ${response.statusCodeValue}, response: ${response.body}")
    //         return null
    //     }
    //
    //     return objectMapper.readTree(response.body)
    // }

    /**
     * Response:
     * [
        {
            "avatar": null,
            "banner": null,
            "communication_disabled_until": null,
            "flags": 0,
            "joined_at": "2023-08-03T08:15:34.534000+00:00",
            "nick": null,
            "pending": false,
            "premium_since": null,
            "roles": [],
            "unusual_dm_activity_until": null,
            "user": {
            "id": "751350023190471118",
            "username": "abc",
            "avatar": "71584ebf6133c8c2cd7a409bec368ccc",
            "discriminator": "0",
            "public_flags": 0,
            "flags": 0,
            "banner": null,
            "accent_color": null,
            "global_name": "abc",
            "avatar_decoration_data": null
            },
            "mute": false,
            "deaf": false
        }
       ]
     */
    fun getUserInGuild(guildId: String, username: String): JsonNode? {
        val url = "${endpoint}/guilds/${guildId}/members/search?query=$username"
        val entity = HttpEntity(null, getHeader())
        val response = try {
            restTemplate.exchange(url, HttpMethod.GET, entity, String::class.java)
        } catch (e: Exception) {
            logger.error("$url error: ${e.message}")
            return null
        }
        if (response.statusCodeValue != 200) {
            logger.error("$url error, status code: ${response.statusCodeValue}, response: ${response.body}")
            return null
        }

        return objectMapper.readTree(response.body)
    }

    // fun queryUserInGuild(username: String): JsonNode? = getUserInGuild(guildId, username)

    /**
     * 获取Oauth2授权链接
     */
    fun getOauth2AuthorizationUrl(state: String): String {
        val uri = UriComponentsBuilder.fromHttpUrl(clientAuthorizationUrl)
            .queryParam("client_id", clientId)
            .queryParam("redirect_uri", URLEncoder.encode(callbackUrl, StandardCharsets.UTF_8.toString()))
            .queryParam("response_type", "code")
            .queryParam("scope", "identify email guilds")
            .queryParam("state", state)
            .build()
            .toUriString()
        logger.info("Discord redirect auth url: $uri")

        return uri
    }

    fun getAccessToken(code: String): JsonNode {
        // val data = LinkedMultiValueMap<String, String>()
        // data.add("grant_type", "authorization_code")
        // data.add("code", code)
        // data.add("redirect_uri", callbackUrl)
        // val headers = HttpHeaders()
        // headers.setBasicAuth(clientId, clientSecret)
        // headers.contentType = MediaType.APPLICATION_FORM_URLENCODED
        // val responseEntity = restTemplate.postForEntity(tokenUrl, HttpEntity(data, headers), String::class.java)
        // val response = responseEntity.body

        val responseData = postRequest(tokenUrl, mapOf(
            "grant_type" to "authorization_code",
            "code" to code,
            "redirect_uri" to callbackUrl
        ), basicAuth = Pair(clientId, clientSecret))

        // logger.info("Discord get access token response: $response")
        // val responseData = objectMapper.readTree(response)
        // val accessToken = responseData["access_token"]?.asText()
        // val tokenType = responseData["token_type"]?.asText()
        // val refreshToken = responseData["refresh_token"]?.asText()
        // val scope = responseData["scope"]?.asText()
        // val expiresIn = responseData["expires_in"]?.asInt()
        // logger.info("Discord get access token: $accessToken, $tokenType, $refreshToken, $scope, $expiresIn")

        return responseData
    }

    // fun inGuild(user: User): Boolean {
    //     val discordInfo = user.discordInfo
    //     if (discordInfo?.refreshToken == null) return false
    //     val expiredTime = discordInfo.grantTime.plusSeconds(discordInfo.expiresIn)
    //
    //     return if (expiredTime.isBefore(LocalDateTime.now())) {
    //         val newToken = refreshToken(discordInfo.refreshToken)
    //         val accessToken = newToken["access_token"]?.asText()
    //         val tokenType = newToken["token_type"]?.asText()
    //         val inGuild = inGuild(tokenType!!, accessToken!!)
    //         user.discordInfo = DiscordInfo(
    //             tokenType = tokenType,
    //             accessToken = accessToken,
    //             refreshToken = newToken["refresh_token"]?.asText()!!,
    //             grantTime = LocalDateTime.now(),
    //             expiresIn = newToken["expires_in"]?.asLong()!!,
    //             inGuild = inGuild
    //         )
    //         userRepository.save(user)
    //         inGuild
    //     } else {
    //         discordInfo.inGuild
    //     }
    // }

    data class AccessTokenResponse(
        val accessToken: String,
        val tokenType: String,
        val expiresIn: Long,
        val refreshToken: String,
        val scope: String
    )

    fun refreshToken(refreshToken: String): AccessTokenResponse {
        val url = "${endpoint}/oauth2/token"
        val response = postRequest(url, mapOf(
            "grant_type" to "refresh_token",
            "refresh_token" to refreshToken
        ), basicAuth = Pair(clientId, clientSecret))

        return AccessTokenResponse(
            accessToken = response["access_token"].asText(),
            tokenType = response["token_type"].asText(),
            expiresIn = response["expires_in"].asLong(),
            refreshToken = response["refresh_token"].asText(),
            scope = response["scope"].asText()
        )
    }

    fun inGuild(tokenType: String, accessToken: String): Boolean {
        val url = "${endpoint}/users/@me/guilds"
        val response = getRequest(url, null, Pair(tokenType, accessToken))
        val inGuild = response.let { node ->
            node.map { n ->
                val id = n["id"]?.asText()
                id == guildId
            }.firstOrNull { it }
        }

        return inGuild ?: false
    }

    private fun getRequest(
        url: String,
        queryData: Map<String, Any?>? = null,
        auth: Pair<String, String>? = null
    ): JsonNode {
        val uri = UriComponentsBuilder.fromHttpUrl(url)
        queryData?.forEach { (k, v) ->
            uri.queryParam(k, v)
        }
        val entity = HttpEntity(null, getHeader(auth?.first, auth?.second))
        val urlStr = uri.toUriString()
        val response = restTemplate.exchange(urlStr, HttpMethod.GET, entity, String::class.java)
        logger.info("Request [GET]:\n$urlStr\nHeaders: ${entity.headers}\nResponse: \n" +
                "Status code: ${response.statusCodeValue}\n${response.body}")

        return objectMapper.readTree(response.body)
    }

    private fun postRequest(
        url: String,
        body: Map<String, Any?>? = null,
        contentType: MediaType = MediaType.APPLICATION_FORM_URLENCODED,
        auth: Pair<String, String>? = null,
        basicAuth: Pair<String, String>? = null
    ): JsonNode {
        val data = body?.let {
            if (contentType == MediaType.APPLICATION_JSON) {
                objectMapper.writeValueAsString(it)
            } else {
                val linkedMap = LinkedMultiValueMap<String, Any?>()
                it.forEach { (k, v) ->
                    linkedMap.add(k, v)
                }
                linkedMap
            }
        }
        val entity = HttpEntity(data, getHeader(auth?.first, auth?.second, basicAuth))
        val response = restTemplate.postForEntity(url, entity, String::class.java)
        logger.info("Request [POST]:\n$url\nHeaders: ${entity.headers}\nBody: $data\nResponse: \n" +
                "Status code: ${response.statusCodeValue}\n${response.body}")

        return objectMapper.readTree(response.body)
    }

    fun getMe(tokenType: String, accessToken: String): JsonNode? {
        val url = "${endpoint}/users/@me"
        return try {
            getRequest(url, null, Pair(tokenType, accessToken))
        } catch (e: Exception) {
            logger.error(e.message, e)
            null
        }
    }

    fun getAvatarUrl(userId: String, avatarHash: String): String {
        return "https://cdn.discordapp.com/avatars/$userId/$avatarHash.png"
    }

    fun isExpired(grantTime: LocalDateTime, expiresIn: Long): Boolean {
        return grantTime.plusSeconds(expiresIn).isBefore(LocalDateTime.now())
    }
}