package io.vault.jasper.response

import io.swagger.annotations.ApiModel
import io.swagger.annotations.ApiModelProperty
import io.vault.jasper.enums.ChainType
import io.vault.jasper.enums.OptionDirection
import io.vault.jasper.enums.Symbol

@ApiModel
data class MinimalistOptionsBuyListResponse(
    @ApiModelProperty("成交期权ID", required = true)
    val id: String,
    @ApiModelProperty("链上订单ID", required = true)
    val onChainOrderId: String?,
    @ApiModelProperty("创建时间", required = true)
    val createDate: Long,
    @ApiModelProperty("链类型", required = true)
    val chain: ChainType,
    @ApiModelProperty("订单类型", required = true)
    val businessType: OptionDirection,
    @ApiModelProperty("标的资产", required = true)
    val underlyingAsset: Symbol,
    @ApiModelProperty("标的资产数量", required = true)
    val underlyingAssetAmount: String,
    @ApiModelProperty("行权资产", required = true)
    val strikeAsset: Symbol,
    @ApiModelProperty("行权价格", required = true)
    val strikePrice: String,
    @ApiModelProperty("行权数量", required = true)
    val strikeAmount: String,
    @ApiModelProperty("到期日", required = true)
    val expiryDate: Long,
    @ApiModelProperty("权利金", required = true)
    val premiumFee: String,
    @ApiModelProperty("权利金资产", required = true)
    val premiumAsset: Symbol,
    @ApiModelProperty("实际支付权利金")
    val premiumFeePay: String?,
)
