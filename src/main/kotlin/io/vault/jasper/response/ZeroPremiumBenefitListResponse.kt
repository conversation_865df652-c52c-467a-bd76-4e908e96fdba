package io.vault.jasper.response

import io.swagger.annotations.ApiModel
import io.swagger.annotations.ApiModelProperty
import io.vault.jasper.enums.OptionDirection
import io.vault.jasper.enums.Symbol

@ApiModel
data class ZeroPremiumBenefitListResponse(

    @ApiModelProperty("权益ID", required = true)
    val id: String,

    @ApiModelProperty("活动ID(新增)", required = true)
    val activityId: String,

    @ApiModelProperty("名称（弃用，不作唯一依据）", required = false)
    @Deprecated("名称, 不作唯一依据")
    val name: String?,

    @ApiModelProperty("抵押币", required = true)
    val underlyingAsset: Symbol,

    @ApiModelProperty("数量", required = true)
    val amount: String,

    @ApiModelProperty("类型/合约方向", required = false)
    val optionDirection: OptionDirection?,

    @ApiModelProperty("订单过期小时数", required = true)
    val expiryInHour: String,

    @ApiModelProperty("次数", required = true)
    val times: Int
)