package io.vault.jasper.service


/**
 * 用户积分系统
 */

import io.vault.jasper.enums.Symbol
import io.vault.jasper.model.*
import io.vault.jasper.repository.*
import io.vault.jasper.utils.DateTimeUtil
import org.slf4j.LoggerFactory
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.data.mongodb.core.MongoTemplate
import org.springframework.data.mongodb.core.aggregation.Aggregation
import org.springframework.data.mongodb.core.aggregation.ConvertOperators
import org.springframework.data.mongodb.core.query.Criteria
import org.springframework.data.repository.findByIdOrNull
import org.springframework.stereotype.Service
import java.math.BigDecimal
import java.time.LocalDate
import java.time.LocalDateTime
import java.time.format.DateTimeFormatter

@Service
class UserPointService @Autowired constructor(
    private val userPointRecordRepository: UserPointRecordRepository,
    private val userPointDailySummaryRepository: UserPointDailySummaryRepository,
    private val optionOrderService: OptionOrderService,
    private val mongoTemplate: MongoTemplate,
    private val currencyService: CurrencyService,
    private val userRepository: UserRepository,
    private val orderRepository: OrderRepository,
    private val referralInfoService: ReferralInfoService,
    private val userCampaignInfoRepository: UserCampaignInfoRepository
){

    private val logger = LoggerFactory.getLogger(this::class.java)

//    fun updateUserPoint(
//        optionOrder: OptionOrder,
//        record: UserPointRecord
//    ): UserPointRecord {
//
//        val multi = BigDecimal("100")
//
//        val premiumAsset = Symbol.valueOf(optionOrder.premiumAsset!!.asset)
//
//        val premiumDecimal = currencyService.getCurrencyDecimal(optionOrder.chain, premiumAsset)
//        val usdtDecimal = currencyService.getCurrencyDecimal(optionOrder.chain, Symbol.USDT)
//
//        record.premium = optionOrder.premiumFeePay!!.movePointLeft(premiumDecimal)
//        record.premiumInUsdt = optionOrder.premiumFeePayInUsdt!!.movePointLeft(usdtDecimal)
//
//        val premiumPoint = record.premiumInUsdt.multiply(multi)
//        record.premiumPoint = premiumPoint.setScale(0, BigDecimal.ROUND_HALF_UP)
//
//        //val netProfit = optionOrderService.calculateNetProfit(optionOrder)
//        //record.netProfit = netProfit.movePointLeft(premiumDecimal)
//
//        // 取消 netprofit 的jpoint  2024-07-03
////        if(netProfit.compareTo(BigDecimal.ZERO) == -1){
////            val netProfitPoint = netProfit.multiply(multi).multiply(BigDecimal("-1"))
////            record.netProfitPoint = netProfitPoint.movePointLeft(usdtDecimal).setScale(0, BigDecimal.ROUND_HALF_UP)
////        }
//
//        record.netProfitPoint = BigDecimal.ZERO
//        record.totalPoint = record.premiumPoint.add(record.netProfitPoint)
//        record.status = OptionStatus.SETTLED
//        val newRecord = userPointRecordRepository.save(record)
//
//        // Update user point daily summary
//        updateUserPointDailySummary(newRecord)
//        return newRecord
//    }

    /**
     * 杠杆积分
     */
    fun updateUserPoint(
        optionOrder: OptionOrder,
        record: UserPointRecord
    ): UserPointRecord {

        val order = orderRepository.findByIdOrNull(optionOrder.orderId) ?: return record
        val volume = order.volume ?: BigDecimal.ZERO
        record.volume = volume

        val premiumAsset = Symbol.valueOf(optionOrder.premiumAsset!!.asset)

        val premiumDecimal = currencyService.getCurrencyDecimal(optionOrder.chain, premiumAsset)
        val usdtDecimal = currencyService.getCurrencyDecimal(optionOrder.chain, Symbol.USDT)

        record.premium = optionOrder.premiumFeePay!!.movePointLeft(premiumDecimal)
        record.premiumInUsdt = optionOrder.premiumFeePayInUsdt!!.movePointLeft(usdtDecimal)

        val premiumPoint = record.volume
        record.premiumPoint = premiumPoint.setScale(0, BigDecimal.ROUND_HALF_UP)

        record.netProfitPoint = BigDecimal.ZERO
        record.totalPoint = record.premiumPoint.add(record.netProfitPoint)
        record.status = OptionStatus.SETTLED
        val newRecord = userPointRecordRepository.save(record)

        // Update user point daily summary
        updateUserPointDailySummary(newRecord)

        //Update User Point Info
        try {
            val user = userRepository.findByAddressIgnoreCase(record.address)
            if (user != null) {
                updateUserPointInfo(user)
            }
        } catch (e: Exception) {
            logger.error("Update User Point Info Error: ${e.message}")
        }

        return newRecord
    }

    fun updateUserPointDailySummary(
        record: UserPointRecord
    ): UserPointDailySummary {
        val dailySummaryTime = DateTimeUtil.getDailySummaryTime(record.created)
        val dateString = dailySummaryTime.first
        val start = dailySummaryTime.second
        val end = dailySummaryTime.third

        var dailySummary = userPointDailySummaryRepository.findByAddressAndDateString(
            record.address,
            dateString
        )

        if(dailySummary == null){

            val user = userRepository.findByAddressIgnoreCase(record.address)

            dailySummary = UserPointDailySummary(
                address = record.address,
                dateString = dateString,
                userId = user?.id,
                registerTime = user?.created
            )

            //更新 Loyalty Point
            val streak = getUserLoyaltyStreak(
                record.address,
                dateString
            )

            val loyaltyPoint = calculateLoyaltyPoint(streak)
            dailySummary.streak = streak
            dailySummary.loyaltyPoint = loyaltyPoint
        }

        val summary = sumPremiumPointByAddressAndCreatedBetween(
            record.address,
            start,
            end
        )

        dailySummary.premiumPoint = summary.first
        dailySummary.premiumFees = summary.second
        dailySummary.fixPremium = true

        return userPointDailySummaryRepository.save(dailySummary)
    }

    fun createUserPointRecord(
        optionOrder: OptionOrder
    ): UserPointRecord? {

        if(optionOrder.status == OptionStatus.PENDING
            || optionOrder.status == OptionStatus.CANCELLED
            || optionOrder.status == OptionStatus.FAILED
            || optionOrder.status == OptionStatus.TO_BE_CONFIRMED
            || optionOrder.status == OptionStatus.SETTLE_FAILED){
            return null
        }

        val record = UserPointRecord(
            address = optionOrder.buyer!!,
            optionOrderId = optionOrder.id!!,
            optionOrderAction = OptionOrderAction.BUY,
            optionOrderDirection = optionOrder.direction!!,
            optionOrderType = optionOrder.orderType,
            underlyingSymbol = optionOrder.bidAsset!!,
            expiryInHour = optionOrder.expiryInHour ?: "8",
            expiryDate = optionOrder.expiryDate,
            created = optionOrder.created,
            status = OptionStatus.EXECUTED
        )

        val savedRecord = userPointRecordRepository.save(record)
        return updateUserPoint(optionOrder, savedRecord)
    }

    fun sumTotalPointByAddress(
        address: String,
        fieldName: String
    ): BigDecimal {
        val agg = Aggregation.newAggregation(
            Aggregation.match(Criteria.where(UserPointDailySummary::address.name).`is`(address)),
            Aggregation.group()
                .sum(
                    ConvertOperators.valueOf(
                        fieldName
                    ).convertToDecimal()).`as`("total")
            )

        val groupResults = mongoTemplate.aggregate(agg, UserPointDailySummary::class.java, Map::class.java)
        val result = groupResults.mappedResults.firstOrNull()
        val total = (result?.get("total")?:"0").toString().toBigDecimal()

        return total
    }

    fun sumPremiumPointByAddressAndCreatedBetween(
        address: String,
        start: LocalDateTime,
        end: LocalDateTime
    ): Pair<BigDecimal, BigDecimal> {

        val criteria = Criteria.where(UserPointRecord::address.name).`is`(address)
            .and(UserPointRecord::created.name).gte(start).lt(end)

        val agg = Aggregation.newAggregation(
            Aggregation.match(criteria),
            Aggregation.group()
                .sum(
                    ConvertOperators.valueOf(
                        UserPointRecord::totalPoint.name
                    ).convertToDecimal()).`as`("total")
                .sum(
                    ConvertOperators.valueOf(
                        UserPointRecord::premiumInUsdt.name
                    ).convertToDecimal()).`as`("premium")
            )

        val groupResults = mongoTemplate.aggregate(agg, UserPointRecord::class.java, Map::class.java)
        val result = groupResults.mappedResults.firstOrNull()
        val total = (result?.get("total")?:"0").toString().toBigDecimal()
        val premium = (result?.get("premium")?:"0").toString().toBigDecimal()

        return Pair(total, premium)
    }

    fun getUserLoyaltyStreak(
        address: String,
        dateString: String
    ): Int {

        val yesterdayString = DateTimeUtil.getCalculatedDayString(
            -1,
            dateString
        )

        //logger.info("dateStringStr: $dateString")
        //logger.info("yesterdayStr: $yesterdayString")

        val yesterdayRecord = userPointDailySummaryRepository.findByAddressAndDateString(
            address,
            yesterdayString
        )

        if (yesterdayRecord == null) {
            return 1
        }

        return yesterdayRecord.streak + 1
    }

    fun calculateLoyaltyPoint(
        streak: Int
    ): BigDecimal {

        if(streak < 1){
            return BigDecimal.ZERO
        }

        if(streak <= 7){
            return BigDecimal("50")
        }

        if(streak <= 14){
            return BigDecimal("100")
        }

        if(streak <= 21){
            return BigDecimal("150")
        }

        if(streak <= 28){
            return BigDecimal("200")
        }

        return BigDecimal("250")
    }

    fun getPointsInDays(
        address: String,
        days: Int = 1
    ): BigDecimal{

        val targetDay = LocalDate.now().minusDays(days.toLong())
        val dateFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd")
        val targetDayStr = dateFormatter.format(targetDay)

        //logger.info("targetDayStr: $targetDayStr")
        val userPointDailySummarys = userPointDailySummaryRepository.findByAddressAndDateStringGreaterThanEqual(
            address,
            targetDayStr
        )

        var totalPoint = BigDecimal.ZERO
        for(summary in userPointDailySummarys){
            totalPoint += summary.premiumPoint
        }

        return totalPoint
    }

    fun updateUserPointInfo(
        user: User
    ){
        val totalJPoint = sumTotalPointByAddress(
            user.address,
            UserPointDailySummary::premiumPoint.name
        )

        val totalLPoint = sumTotalPointByAddress(
            user.address,
            UserPointDailySummary::loyaltyPoint.name
        )

        val campaignInfo = userCampaignInfoRepository.findByAddressIgnoreCase(user.address)
        val totalCampaignPoint = campaignInfo?.totalPoint ?: BigDecimal.ZERO

        //val totalPoint = referralInfoService.getUserReferralTotalPoint(user, UserReferralTransactionType.FIRST_TRADE_REWARD)
        user.referralSPoint = campaignInfo?.tradeTaskPoint ?: BigDecimal.ZERO

        user.jPoint = totalJPoint
        user.lPoint = totalLPoint
        user.sPoint = totalCampaignPoint

        userRepository.save(user)
    }
}