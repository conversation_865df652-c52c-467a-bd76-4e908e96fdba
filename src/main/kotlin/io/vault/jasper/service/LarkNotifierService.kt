package io.vault.jasper.service

import org.json.JSONObject
import org.slf4j.LoggerFactory
import org.springframework.stereotype.Service
import java.util.*

@Service
class LarkNotifierService {

    private val logger = LoggerFactory.getLogger(this::class.java)

    private val secret = "NfRIby8qxSvOkAmiizziMb"

    private val url = "https://open.larksuite.com/open-apis/bot/v2/hook/f7dde930-77dd-4ba7-9410-299b28f27278"

    fun sendLark(title: String, text: String? = null): JSONObject {
        val notifier = LarkNotifier(secret, url)
        val result = notifier.sendLark(title, text)
        logger.info("Lark message sent: ${result.toString(4)}")

        return result
    }
}