package io.vault.jasper.model

import io.vault.jasper.enums.ChainType
import org.springframework.data.annotation.Id
import org.springframework.data.annotation.LastModifiedDate
import org.springframework.data.mongodb.core.index.Indexed
import org.springframework.data.mongodb.core.mapping.Document
import org.springframework.data.mongodb.core.mapping.Field
import java.math.BigInteger
import java.time.LocalDateTime

@Document
data class BlockchainGasPrice(
    @Id
    val id: String? = null,
    @Indexed(unique = true)
    val chain: ChainType,
    @Field("gas_price")
    var gasPrice: BigInteger = BigInteger.ZERO, // Unit: GWEI
    @Field("gas_limit")
    var gasLimit: Int = 0,
    @Field("contract_gas_limit")
    var contractGasLimit: Int = 0
)
