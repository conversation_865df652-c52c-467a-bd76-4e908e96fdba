package io.vault.jasper.listener

import io.vault.jasper.enums.ChainType
import io.vault.jasper.event.OptionExecutedEvent
import io.vault.jasper.model.OptionOrder
import io.vault.jasper.repository.OptionOrderRepository
import io.vault.jasper.service.*
import org.slf4j.LoggerFactory
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.context.event.EventListener
import org.springframework.data.repository.findByIdOrNull
import org.springframework.stereotype.Component

@Component
class BitlayerCampaignEventListener @Autowired constructor(
    private val optionOrderRepository: OptionOrderRepository,
    private val bitlayerCampaignService: BitlayerCampaignService,
    private val binanceCampaignService: BinanceCampaignService,
    private val jpCampaignService: JPCampaignService,
    private val echoooCampaignService: EchoooCampaignService
) {

    private val logger = LoggerFactory.getLogger(this::class.java)

    @EventListener
    fun handleOptionExecutedEvent(event: OptionExecutedEvent) {
        logger.info("BitlayerCampaignEventListener Option execute completed for order: ${event.orderId}")

        val optionOrder = optionOrderRepository.findByIdOrNull(event.orderId) ?: return
        if(optionOrder.chain == ChainType.BITLAYER){
            handleBitlayerTradeRebateRecord(optionOrder)

        }

        if(optionOrder.chain == ChainType.ARBITRUM){
            try{
                jpCampaignService.handleJPArbitrumOptionOrder(optionOrder)
            }catch (e: Exception){
                logger.error("BitlayerCampaignEventListener arbitrum first trade failed, optionOrderId: ${optionOrder.id}", e)
            }

            handleEchoooOptionOrder(optionOrder)
        }
    }

    private fun handleEchoooOptionOrder(optionOrder: OptionOrder) {
        logger.info("BitlayerCampaignEventListener echooo airdrop trade rebate for option order: ${optionOrder.id}")
        try {
            echoooCampaignService.createTradeRebateRecord(optionOrder)
        } catch (e: Exception) {
            logger.error("BitlayerCampaignEventListener echooo first trade failed, optionOrderId: ${optionOrder.id}", e)
        }
    }

    private fun handleBitlayerTradeRebateRecord(
        optionOrder: OptionOrder,
    ) {

//        val buyer = optionOrder.buyer ?: return
//        if(!bitlayerCampaignService.isFirstOrderInCampaign(optionOrder)){
//            logger.info("BitlayerCampaignEventListener Option order is not first order in campaign for buyer: $buyer")
//            return
//        }
//
//        logger.info("BitlayerCampaignEventListener Option order check bitlayer coupone for buyer: $buyer")
//        val getCouponTime = bitlayerCampaignService.checkBitlayerCoupon(buyer)
//
//        if(getCouponTime == 0L){
//            logger.info("BitlayerCampaignEventListener Option order buyer: $buyer not get bitlayer coupon")
//            return
//        }
//
//        logger.info("BitlayerCampaignEventListener Option order buyer: $buyer get bitlayer coupon at: $getCouponTime")
//
//        bitlayerCampaignService.createBitlayerTradeRebateRecord(optionOrder, getCouponTime)

        logger.info("BitlayerCampaignEventListener Option order check binance campaign for buyer: ${optionOrder.buyer}")
        binanceCampaignService.createTradeRebateRecord(optionOrder)
    }
}