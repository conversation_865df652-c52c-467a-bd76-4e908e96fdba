package io.vault.jasper.service

import io.vault.jasper.enums.ChainType
import io.vault.jasper.enums.Symbol
import io.vault.jasper.model.*
import io.vault.jasper.repository.*
import org.slf4j.LoggerFactory
import org.springframework.stereotype.Service
import java.time.LocalDateTime
import javax.annotation.PostConstruct
import org.springframework.data.mongodb.core.MongoTemplate
import org.springframework.data.mongodb.core.query.Criteria
import org.springframework.data.mongodb.core.query.Query
import org.springframework.data.mongodb.core.query.Update
import org.springframework.data.repository.findByIdOrNull
import java.math.BigDecimal

@Service
class TgeCampaignService(
    private val tgeCampaignConfigRepository: TgeCampaignConfigRepository,
    private val tgeCampaignRecordRepository: TgeCampaignRecordRepository,
    private val mongoTemplate: MongoTemplate,
    private val currencyService: CurrencyService,
    private val optionOrderInfoRepository: OptionOrderInfoRepository,
    private val tgeCampaignParticipateRecordRepository: TgeCampaignParticipateRecordRepository,
    private val optionOrderService: OptionOrderService
) {
    private val logger = LoggerFactory.getLogger(this::class.java)

    @PostConstruct
    fun init() {
        // 检查是否已存在配置
        if (tgeCampaignConfigRepository.count() == 0L) {
            // 创建新配置
            val config = TgeCampaignConfig(
                startTime = LocalDateTime.of(2025, 3, 1, 0, 0, 0),
                endTime = LocalDateTime.of(2025, 3, 30, 0, 0, 0),
                enabled = false
            )

            tgeCampaignConfigRepository.save(config)
            logger.info("TGE Campaign: Created initial campaign config")
        }
    }

    @Synchronized
    fun handleOptionOrder(optionOrder: OptionOrder) {

        // 检查是否是免费订单
        if (optionOrder.premiumFree == true) {
            logger.info("TGE Campaign: Option order ${optionOrder.id} is a premium free order")
            return
        }

//        if (optionOrder.premiumFeePay?.compareTo(optionOrder.premiumFeeShouldPay) != 0) {
//            logger.info("TGE Campaign: Option order ${optionOrder.id} is a trading credit order.")
//            return
//        }

        // 检查实际支付的权利金 是否大于等于 3 USDT
        if (optionOrder.premiumFeePayInUsdt == null) {
            logger.info("TGE Campaign: Option order ${optionOrder.id} premium fee pay in USDT is null")
            return
        }

        val usdtDecimal = currencyService.getCurrencyDecimal(
            optionOrder.chain,
            Symbol.USDT
        )
        val readablePremiumInUsdt = optionOrder.premiumFeePayInUsdt!!.movePointLeft(usdtDecimal)
        if (readablePremiumInUsdt <= BigDecimal("2.98")) {
            logger.info("TGE Campaign: Option order ${optionOrder.id} premium fee payed is less than 3 USDT")
            return
        }

        // 检查是否已经处理过
        if (tgeCampaignRecordRepository.existsByOptionOrderId(optionOrder.id!!)) {
            logger.info("TGE Campaign: Option order ${optionOrder.id} already processed")
            return
        }

        // 获取活动配置
        val config = tgeCampaignConfigRepository.findFirstByEnabled(true) ?: run {
            logger.info("TGE Campaign: No active campaign found")
            return
        }

        // 检查是否在活动期间
        if (!config.isInCampaignPeriod(optionOrder.created)) {
            logger.info("TGE Campaign: Option order ${optionOrder.id} not in campaign period")
            return
        }

        //看看是否Bitstone订单
        var optionProduct = optionOrder.product
        if(optionProduct == null){
            val optionProductInfo = optionOrderInfoRepository.findByIdOrNull(optionOrder.id)
            if(optionProductInfo != null){
                optionProduct = optionOrderService.getOptionOrderProduct(
                    optionProductInfo.channel,
                    optionOrder
                )
            }
        }

        logger.info("TGE Campaign: Option order ${optionOrder.txHash} product is $optionProduct")

        if(optionProduct == null){
            logger.info("TGE Campaign: Option order ${optionOrder.id} is a null product order")
            return
        }

        if(optionProduct == OptionOrderProduct.BTC_FI){
            logger.info("TGE Campaign: Option order ${optionOrder.id} is a bitstone order")
            return
        }

        // 检查是否是普通订单 (stoneActivityNftId 为 null)
        if (optionOrder.stoneActivityNftId != null) {
            logger.info("TGE Campaign: Option order ${optionOrder.id} is not a normal order")
            return
        }

        // 创建奖励记录
        val record = TgeCampaignRecord(
            address = optionOrder.buyer!!,
            optionOrderId = optionOrder.id,
            chain = optionOrder.chain,
        )
        tgeCampaignRecordRepository.save(record)

        var campaignParticipateRecord: TgeCampaignParticipateRecord? = null
        val campaignRecords = tgeCampaignParticipateRecordRepository.findByAddressIgnoreCase(
            optionOrder.buyer!!,
        )

        if(campaignRecords.isEmpty()) {
            // 创建参与记录
            val participateRecord = TgeCampaignParticipateRecord(
                address = optionOrder.buyer!!,
                optionOrderId = optionOrder.id,
                chain = optionOrder.chain,
                status = TgeCampaignParticipateRecordStatus.PENDING
            )
            campaignParticipateRecord = tgeCampaignParticipateRecordRepository.save(participateRecord)
        } else {
            campaignParticipateRecord = campaignRecords[0]
        }

        val btrCount = tgeCampaignRecordRepository.countByAddressIgnoreCaseAndChain(
            optionOrder.buyer!!,
            ChainType.BITLAYER
        )

        if(btrCount == 1L) {
            campaignParticipateRecord!!.bitlayerTradeOptionOrderId = optionOrder.id
            tgeCampaignParticipateRecordRepository.save(campaignParticipateRecord)
        }
    }
}