package io.vault.jasper.model

import org.springframework.data.annotation.CreatedDate
import org.springframework.data.annotation.Id
import org.springframework.data.annotation.LastModifiedDate
import org.springframework.data.mongodb.core.index.CompoundIndex
import org.springframework.data.mongodb.core.mapping.Document
import org.springframework.data.mongodb.core.mapping.Field
import java.time.LocalDateTime

@Document("user_zero_premium_benefit")
@CompoundIndex(
    name = "user_benefit",
    def = "{'userId': 1, 'zeroPremiumBenefitId': 1}",
)
data class UserZeroPremiumBenefit(
    @Id
    val id: String? = null,

    @Field("user_id")
    val userId: String,

    @Field("benefit_id")
    val benefitId: String,

    var used: Boolean = false,

    @Field("used_time")
    var usedTime: LocalDateTime? = null,

    @CreatedDate
    val created: LocalDateTime = LocalDateTime.now(),

    @LastModifiedDate
    val updated: LocalDateTime = LocalDateTime.now()
)
