package io.vault.jasper.model

import io.swagger.annotations.ApiModelProperty
import org.springframework.data.annotation.Id
import org.springframework.data.annotation.LastModifiedDate
import org.springframework.data.mongodb.core.index.Indexed
import org.springframework.data.mongodb.core.mapping.Document
import org.springframework.data.mongodb.core.mapping.Field
import java.time.LocalDateTime

@Document("option_order_info")
data class OptionOrderInfo(
    @Id
    val id: String? = null,
    @Indexed(unique = true, background = true)
    val txHash: String,
    @Indexed(background = true)
    var channel: UserChannel,
    var created: LocalDateTime = LocalDateTime.now(),
    @LastModifiedDate
    val updated: LocalDateTime = LocalDateTime.now(),
    var synced: Boolean? = null,
    var buyer: String? = null,
    var buyerVault: String? = null,
    var systemBuyer: String? = null,
    var ipAddress: String? = null,
    @Indexed(background = true)
    var product: OptionOrderProduct? = null,
    @Indexed(background = true)
    var wallet: UserWallet? = null,
    @Indexed(background = true)
    var campaign: String? = null,
    @Indexed(background = true)
    var from: String? = null,
    var utmInfo: UTMInfo? = null
)

data class UTMInfo(
    @Field("utm_source")
    var utmSource: String? = null,
    @Field("utm_medium")
    var utmMedium: String? = null,
    @Field("utm_campaign")
    var utmCampaign: String? = null,
    @Field("utm_term")
    var utmTerm: String? = null,
    @Field("utm_content")
    var utmContent: String? = null
)