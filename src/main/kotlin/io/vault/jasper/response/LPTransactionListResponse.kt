package io.vault.jasper.response

import io.swagger.annotations.ApiModel
import io.swagger.annotations.ApiModelProperty
import io.vault.jasper.enums.ChainType
import io.vault.jasper.enums.OptionDirection
import io.vault.jasper.enums.Symbol
import io.vault.jasper.model.OptionStatus
import io.vault.jasper.model.OrderType

@ApiModel
data class LPTransactionListResponse(
    @ApiModelProperty("时间", required = true)
    val time: Long,
    @ApiModelProperty("代币数量", required = true)
    val tokenAmount: String,
    @ApiModelProperty("代币价值", required = true)
    val tokenValue: String,
    @ApiModelProperty("EOA 钱包地址", required = true)
    val wallet: String,
)
