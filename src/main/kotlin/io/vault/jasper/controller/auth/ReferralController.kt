package io.vault.jasper.controller.auth

import EVMAddress
import io.swagger.annotations.ApiOperation
import io.vault.jasper.ApiResponse
import io.vault.jasper.annotation.ApiCache
import io.vault.jasper.dto.AuthDTO
import io.vault.jasper.dto.BindInvitorDTO
import io.vault.jasper.dto.ReferralPremiumDTO
import io.vault.jasper.model.AirDropType
import io.vault.jasper.model.AirdropTransaction
import io.vault.jasper.model.User
import io.vault.jasper.model.UserPremiumSummary
import io.vault.jasper.repository.AirDropTransactionRepository
import io.vault.jasper.repository.UserNetworkRepository
import io.vault.jasper.repository.UserReferralTransactionRepository
import io.vault.jasper.repository.UserRepository
import io.vault.jasper.response.*
import io.vault.jasper.service.*
import io.vault.jasper.utils.AuthUtil
import io.vault.jasper.utils.DateTimeUtil
import org.slf4j.LoggerFactory
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.data.domain.PageImpl
import org.springframework.data.domain.PageRequest
import org.springframework.data.domain.Sort
import org.springframework.data.mongodb.core.MongoTemplate
import org.springframework.data.mongodb.core.query.Criteria
import org.springframework.data.mongodb.core.query.Query
import org.springframework.web.bind.annotation.*
import java.math.BigDecimal
import javax.servlet.http.HttpServletRequest
import javax.validation.Valid


@RestController
@RequestMapping("/referral")
class ReferralController @Autowired constructor(
    private val userService: UserService,
    private val authUtil: AuthUtil,
    private val mongoTemplate: MongoTemplate,
    private val userPremiumSummaryService: UserPremiumSummaryService,
    private val systemService: SystemService,
    private val leaderboardService: LeaderboardService,
    private val userReferralTransactionRepository: UserReferralTransactionRepository,
    private val userNetworkRepository: UserNetworkRepository,
    private val airDropTransactionRepository: AirDropTransactionRepository,
    private val airDropService: AirDropService,
    private val userRepository: UserRepository
) {

    private var logger = LoggerFactory.getLogger(this::class.java)

    @ApiOperation("Bind Invitor")
    @CrossOrigin
    @PostMapping("/bind")
    fun bind(
        request: HttpServletRequest,
        @Valid @RequestBody dto: BindInvitorDTO
    ): ApiResponse<Any?> {
        authUtil.filterIPBlacklist(request)
        authUtil.auth(request, dto.address)
        userService.bindInvitor(dto.address, dto.inviteCode)
        return ApiResponse.success(null)
    }

    @ApiOperation("Referral Info")
    @CrossOrigin
    @PostMapping("/info")
    fun info(
        request: HttpServletRequest,
        @Valid @RequestBody dto: AuthDTO
    ): ApiResponse<ReferralInfoResponse> {
        authUtil.filterIPBlacklist(request)
        val user = authUtil.auth(request, dto.address)

        val systemParameter = systemService.getParameter()
        val referees = userService.getRefereesAddress(user)

        val totalPremium = userPremiumSummaryService.getUserPremiumSummaryTotal(
            referees,
            UserPremiumSummary::totalPremium.name
        )

        //var finalPremium = totalPremium.multiply(systemParameter.premiumPriceRatePercentage)
        //val dividor = systemParameter.premiumPriceRatePercentage.add(BigDecimal(100))
        //finalPremium = finalPremium.divide(dividor, 18, BigDecimal.ROUND_HALF_UP)

        // 2025-01-17 权利金费率算法再次调整
        val markupRatio = systemParameter.premiumPriceRatePercentage.movePointLeft(2)
        val finalPremium = totalPremium.multiply(markupRatio)

        val totalRebate = finalPremium.multiply(BigDecimal("0.12"))

//        val totalVolume = userPremiumSummaryService.getUserPremiumSummaryTotal(
//            referees,
//            UserPremiumSummary::totalVolume.name
//        )

        val totalVolume = leaderboardService.getUserRefereeTotalVolume(
            dto.address
        )

        val parentUser = userService.findUserParent(user)
        val parentAddress = parentUser?.address
        val parentReferral = parentUser?.inviteCode

        return ApiResponse.success(
            ReferralInfoResponse(
                referees.size,
                //totalPremium.stripTrailingZeros().toPlainString(),
                finalPremium.stripTrailingZeros().toPlainString(),
                user.inviteCode,
                parentAddress,
                parentReferral,
                totalVolume.stripTrailingZeros().toPlainString(),
                totalRebate.stripTrailingZeros().toPlainString(),
                useKolRebate = user.useKolRebate
            )
        )
    }

    @ApiOperation("邀请用户花费权利金列表")
    @CrossOrigin
    @PostMapping("/premium_list")
    fun premiumList(
        request: HttpServletRequest,
        @Valid @RequestBody dto: ReferralPremiumDTO
    ): ApiResponse<PageResponse<ReferralPremiumResponse>> {
        authUtil.filterIPBlacklist(request)
        val user = authUtil.auth(request, dto.address!!)
        val referees = userService.getRefereesAddress(user)

        val p = dto.page
        val ps = dto.pageSize
        val pageable = PageRequest.of(p - 1, ps, Sort.by(Sort.Order.desc(UserPremiumSummary::registerTime.name)))

        val query = Query()
        query.addCriteria(Criteria.where(UserPremiumSummary::evmAddress.name).`in`(referees))

        if (dto.startTime != null && dto.endTime != null) {
            val start = DateTimeUtil.convertMilliTimestampToLocalDateTime(dto.startTime)
            val end = DateTimeUtil.convertMilliTimestampToLocalDateTime(dto.endTime)
            query.addCriteria(Criteria.where(UserPremiumSummary::registerTime.name).gte(start).lt(end))
        }

        val total = mongoTemplate.count(query, UserPremiumSummary::class.java)
        query.with(pageable)
        val resultSet = mongoTemplate.find(query, UserPremiumSummary::class.java)
        val optionsPage = PageImpl(resultSet, pageable, total)

        val systemParameter = systemService.getParameter()

        //val markupBase = systemParameter.premiumPriceRatePercentage.add(BigDecimal(100))
        //val markupRatio = systemParameter.premiumPriceRatePercentage.divide(markupBase, 18, BigDecimal.ROUND_HALF_UP)

        // 权利金费率算法再次调整。 2025-01-17
        val markupRatio = systemParameter.premiumPriceRatePercentage.movePointLeft(2)

        val optionsList = optionsPage.content.mapNotNull {

            ReferralPremiumResponse(
                address = it.evmAddress,
                premiumAmount = (it.totalPremium.multiply(markupRatio)).stripTrailingZeros().toPlainString(),
                createDate = DateTimeUtil.convertLocalDateTimeToTimestamp(it.registerTime),
                optionVolume = it.totalVolume.stripTrailingZeros().toPlainString()
            )
        }

        return ApiResponse.success(
            PageResponse(
                total = optionsPage.totalElements,
                page = p,
                pageSize = ps,
                totalPage = optionsPage.totalPages,
                hasNext = optionsPage.hasNext(),
                data = optionsList
            )
        )
    }

    @ApiOperation("新的Referral Info")
    @CrossOrigin
    @ApiCache(key = "referral:v2:info", dynamicKey = ["address"], expire = 10)
    @GetMapping("/v2/info")
    fun infoV2(
        request: HttpServletRequest,
        @RequestParam("address") @EVMAddress address: String
    ): ApiResponse<ReferralInfoV2Response> {
        authUtil.filterIPBlacklist(request)
        val user = authUtil.auth(request, address)

        val parentUser = userService.findUserParent(user)
        val parentAddress = parentUser?.address
        val parentReferral = parentUser?.inviteCode

        var percentage = "0"
        val userNetwork = userNetworkRepository.findByUserId(user.id!!)
        if(userNetwork != null){
            percentage = userNetwork.protocolFeePercentage.stripTrailingZeros().toPlainString()
        }

        if(user.referralCount == 0){
            user.referralCount = userRepository.countByInvitedUserId(user.id).toInt()
            userRepository.save(user)
        }

        if(user.referralCount > 0 && user.referralSPoint == BigDecimal.ZERO){
            airDropService.updateUserCampaignInfo(user)
            val userCampaignInfo = airDropService.getUserCampaignInfo(user)
            user.referralSPoint = userCampaignInfo.tradeTaskPoint
            userRepository.save(user)
        }

        return ApiResponse.success(
            ReferralInfoV2Response(
                user.referralCount,
                user.inviteCode,
                parentAddress,
                parentReferral,
                user.referralSPoint.stripTrailingZeros().toPlainString(),
                user.rebateTradingCredits.stripTrailingZeros().toPlainString(),
                user.address,
                percentage
            )
        )
    }

    @ApiOperation("邀请用户获取spoint列表")
    @CrossOrigin
    @ApiCache(
        key = "referral:v2:spoint_list",
        dynamicKey = ["address", "page", "pageSize"],
        expire = 10
    )
    @GetMapping("/v2/spoint_list")
    fun spointList(
        request: HttpServletRequest,
        @RequestParam("address") @EVMAddress address: String,
        @RequestParam page: Int?,
        @RequestParam pageSize: Int?,
    ): ApiResponse<PageResponse<ReferralSPointResponse>> {
        authUtil.filterIPBlacklist(request)
        val user = authUtil.auth(request, address)

        val p = page ?: 1
        val ps = pageSize ?: 50
        val pageable = PageRequest.of(p - 1, ps, Sort.by(Sort.Order.desc(User::created.name)))

        val query = Query()
        query.addCriteria(Criteria.where(User::invitedUserId.name).`is`(user.id!!))

        val total = mongoTemplate.count(query, User::class.java)
        query.with(pageable)
        val resultSet = mongoTemplate.find(query, User::class.java)
        val optionsPage = PageImpl(resultSet, pageable, total)

        val optionsList = optionsPage.content.mapNotNull {

            val userAddress = it.address
            var sPoint = "0"
            var hasTraded = false

            val tradeCount = airDropTransactionRepository.countByReferralAddressIgnoreCaseAndType(
                userAddress,
                AirDropType.TRADING_TASK
            )

            if(tradeCount > 0L){
                sPoint = "1000"
                hasTraded = true
            }

            ReferralSPointResponse(
                address = userAddress,
                spoint = sPoint,
                traded = hasTraded
            )
        }

        return ApiResponse.success(
            PageResponse(
                total = optionsPage.totalElements,
                page = p,
                pageSize = ps,
                totalPage = optionsPage.totalPages,
                hasNext = optionsPage.hasNext(),
                data = optionsList
            )
        )
    }
}