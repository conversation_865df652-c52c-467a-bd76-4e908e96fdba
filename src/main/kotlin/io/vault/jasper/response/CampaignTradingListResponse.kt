package io.vault.jasper.response

import io.swagger.annotations.ApiModel
import io.swagger.annotations.ApiModelProperty
import io.vault.jasper.enums.ChainType

@ApiModel
data class CampaignTradingListResponse(

    @ApiModelProperty("期权订单ID")
    val optionOrderId: String,

    @ApiModelProperty("买家钱包地址")
    val buyerAddress: String,

    @ApiModelProperty("投资回报率（小数，非百分比）")
    val roi: String,

    @ApiModelProperty("交易链类型")
    val chain: ChainType,

    @ApiModelProperty("期权订单hash")
    val txHash: String,

    @ApiModelProperty("结算hash")
    val settlementHash: String,
)
