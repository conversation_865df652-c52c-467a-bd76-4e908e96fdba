package io.vault.jasper.response

import io.swagger.annotations.ApiModel
import io.swagger.annotations.ApiModelProperty
import io.vault.jasper.enums.ChainType
import io.vault.jasper.model.KolStatus

@ApiModel
data class UserLoyaltyPointInfoResponse(

    @ApiModelProperty("钱包地址", required = true)
    val address: String,

    @ApiModelProperty("总积分", required = true)
    val totalPoint: String,

    @ApiModelProperty("总交易日", required = true)
    val totalTradingDays: Int,

    @ApiModelProperty("最长连续交易天数", required = true)
    val maxStreakDays: Int,

    @ApiModelProperty("当前连续交易天数", required = true)
    val currentStreakDays: Int
)
