package io.vault.jasper.repository

import io.vault.jasper.enums.ChainType
import io.vault.jasper.model.Order
import io.vault.jasper.model.OrderStatus
import org.springframework.data.domain.Sort
import org.springframework.data.mongodb.repository.MongoRepository
import org.springframework.stereotype.Repository
import java.math.BigDecimal

@Repository
interface OrderRepository : MongoRepository<Order, String> {
    fun findByStatus(status: OrderStatus, sort: Sort = Sort.by(Sort.Direction.DESC, Order::id.name)): List<Order>
    fun findByStatusIn(status: List<OrderStatus>): List<Order>
    fun findByStatusAndChain(status: OrderStatus, chain: ChainType): List<Order>
    fun findByVolumeEquals(volume: BigDecimal): List<Order>
}