package io.vault.jasper.service.activity

import io.vault.jasper.model.User
import io.vault.jasper.model.activity.Task
import io.vault.jasper.model.activity.TaskCompletionData
import io.vault.jasper.model.activity.TaskType
import org.springframework.stereotype.Component

/**
 * Interface for task processors
 */
interface TaskProcessor {
    /**
     * Get the task type that this processor handles
     */
    fun getTaskType(): TaskType
    
    /**
     * Check if the task is completed for the given user
     * @param user The user to check
     * @param task The task to check
     * @param context Additional context data for task verification
     * @return A pair of (isCompleted, completionData) where completionData contains details about the completion
     */
    fun isTaskCompleted(user: User, task: Task, context: Map<String, Any> = emptyMap()): Pair<Boolean, Map<String, Any>>
}

/**
 * Factory for task processors
 */
@Component
class TaskProcessorFactory(private val taskProcessors: List<TaskProcessor>) {
    private val processorMap: Map<TaskType, TaskProcessor> = taskProcessors.associateBy { it.getTaskType() }
    
    fun getProcessor(taskType: TaskType): TaskProcessor {
        return processorMap[taskType] ?: throw IllegalArgumentException("No processor found for task type: $taskType")
    }
}
