package io.vault.jasper.model

import io.swagger.annotations.ApiModelProperty
import io.vault.jasper.enums.ChainType
import io.vault.jasper.enums.OptionDirection
import io.vault.jasper.enums.Symbol
import org.springframework.data.annotation.CreatedDate
import org.springframework.data.annotation.Id
import org.springframework.data.annotation.LastModifiedDate
import org.springframework.data.mongodb.core.index.CompoundIndex
import org.springframework.data.mongodb.core.index.IndexDirection
import org.springframework.data.mongodb.core.index.Indexed
import org.springframework.data.mongodb.core.mapping.Document
import org.springframework.data.mongodb.core.mapping.Field
import org.springframework.data.mongodb.core.mapping.FieldType
import java.math.BigDecimal
import java.time.LocalDateTime

/**
 * 期权成交订单
 */
@Document("option_order")
@CompoundIndex(
    name = "chain_underlyingAsset_buyer",
    def = "{'chain' : 1, 'underlyingAsset': 1, 'buyer': 1}",
    background = true
)
@CompoundIndex(
    name = "status_sellerPremiumFee_expiryDate_txHash_vaults_premiumAsset",
    def = "{'status': 1, 'seller_premium_fee': 1, 'expiry_date': 1, 'tx_hash': 1, 'buyer_vault': 1, 'seller_vault': 1, 'premium_asset': 1}",
    background = true
)
@CompoundIndex(
    name = "status_buyerProfit",
    def = "{'status': 1, 'buyer_profit': 1}",
    background = true
)
@CompoundIndex(
    name = "status_roi_expiryDate_created",
    def = "{'status': 1, 'roi': 1, 'expiry_date': 1, 'created': -1}",
    background = true
)
@CompoundIndex(
    name = "kol_level_processor_index",
    def = "{'buyer': 1, 'updated': 1, 'status': 1, 'premium_free': 1}",
    background = true
)
@CompoundIndex(
    name = "chain_blockHeight_index",
    def = "{'chain': 1, 'block_height': 1}",
    background = true
)
data class OptionOrder(
    @Id
    val id: String? = null,
    @Field("order_id")
    @Indexed(background = true)
    val orderId: String,
    @Field("order_type")
    @Indexed(background = true)
    val orderType: OrderType,
    @Indexed(background = true)
    val chain: ChainType,
    @Indexed(background = true)
    val direction: OptionDirection?,
    @Field("on_chain_order_id")
    @Indexed(background = true)
    var onChainOrderId: String? = null,
    @Field("underlying_asset")
    val underlyingAsset: Symbol,
    @Field("underlying_asset_address")
    val underlyingAssetAddress: String?,
    @Indexed(background = true)
    var buyer: String?,
    @Field("buyer_vault")
    @Indexed(background = true)
    var buyerVault: String?,
    @Field("buyer_vault_salt")
    var buyerVaultSalt: String?,
    @Indexed(background = true)
    var seller: String?,
    @Field("seller_vault")
    @Indexed(background = true)
    var sellerVault: String?,
    @Field(targetType = FieldType.DECIMAL128)
    val amount: BigDecimal,
    @Field("strike_asset")
    val strikeAsset: Symbol,
    @Field("strike_asset_address")
    val strikeAssetAddress: String?,
    @Field("strike_amount", targetType = FieldType.DECIMAL128)
    val strikeAmount: BigDecimal, // 行权资产数量
    @Field("actual_strike_amount", targetType = FieldType.DECIMAL128)
    val actualStrikeAmount: BigDecimal?, // 实际行权资产数量
    @Field("liquidate_mode")
    var liquidateMode: String?, // 清算模式
    @Field("tx_hash")
    @Indexed(background = true)
    var txHash: String? = null,
    @Field("expiry_in_hour")
    val expiryInHour: String? = null,

    @Indexed(background = true)
    @Field("expiry_date")
    val expiryDate: LocalDateTime,
    @Indexed(background = true)
    @Field("lock_date")
    val lockDate: LocalDateTime? = null,

    @Indexed(background = true)
    var status: OptionStatus = OptionStatus.PENDING,
    @Field("premium_asset")
    var premiumAsset: PremiumAsset?,
    @Field("premium_asset_price_in_usd_at_created")
    var premiumAssetPriceInUsdAtCreated: BigDecimal? = null,
    @Field("quote_asset")
    var quoteAsset: Symbol? = null,
    @Field("quote_asset_address")
    var quoteAssetAddress: String? = null,
    @Field("quote_asset_price_in_usd_at_created")
    var quoteAssetPriceInUsdAtCreated: BigDecimal? = null,
    @Field("quote_asset_price_in_usd_at_settlement")
    var quoteAssetPriceInUsdAtSettlement: BigDecimal? = null,
    @Field("premium_fee_should_pay", targetType = FieldType.DECIMAL128)
    var premiumFeeShouldPay: BigDecimal? = null, // 应付支付权利金
    @Field("premium_fee_should_pay_in_usdt", targetType = FieldType.DECIMAL128)
    var premiumFeeShouldPayInUsdt: BigDecimal? = null, // 应付支付权利金
    @Field("premium_fee_should_pay_in_usd", targetType = FieldType.DECIMAL128)
    var premiumFeeShouldPayInUsd: BigDecimal? = null, // 应付支付权利金
    @Field("premium_fee_pay", targetType = FieldType.DECIMAL128)
    var premiumFeePay: BigDecimal?, // 实际支付权利金
    @Field("premium_fee_pay_in_usdt", targetType = FieldType.DECIMAL128)
    var premiumFeePayInUsdt: BigDecimal? = null, // 实际支付权利金，以USDT计价
    @Field("premium_fee_pay_in_usd")
    var premiumFeePayInUsd: BigDecimal? = null,
    var premiumFeeLog: String? = null,
    @Indexed(background = true, direction = IndexDirection.DESCENDING)
    @CreatedDate
    var created: LocalDateTime = LocalDateTime.now(),
    @LastModifiedDate
    var updated: LocalDateTime = LocalDateTime.now(),
    @Field("jvault")
    val jvault: Boolean = false,
    @Field("error_msg")
    var errorMsg: String? = null,

    // 结算交易ID
    @Field("settlement_hash")
    @Indexed(background = true)
    var settlementHash: String? = null,

    @Field("settlement_hash_checked")
    var settlementHashChecked: Boolean? = false,

    @Field("settlement_time")
    var settlementTime: LocalDateTime? = null,

    // 结算时的市场价格
    @Field("market_price_at_settlement")
    var marketPriceAtSettlement: String? = null,

    // 买家利润
    @Field("buyer_profit", targetType = FieldType.DECIMAL128)
    var buyerProfit: BigDecimal? = null,
    @Field("buyer_profit_in_usd", targetType = FieldType.DECIMAL128)
    var buyerProfitInUsd: BigDecimal? = null,

    @Field("bid_asset")
    var bidAsset: Symbol?,

    @Field("bid_amount")
    var bidAmount: BigDecimal? = null,

    @Field("bid_asset_price_in_usd_at_created")
    var bidAssetPriceInUsdAtCreated: BigDecimal? = null,

    @Field("bid_asset_price_in_usd_at_settlement")
    var bidAssetPriceInUsdAtSettlement: BigDecimal? = null,

    @Field("premium_fee_info")
    var premiumFeeInfo: PremiumFeeInfo?,

    @Field("premium_free")
    var premiumFree: Boolean? = null,

    @Field("gross_profit", targetType = FieldType.DECIMAL128)
    var grossProfit: BigDecimal? = null,

    @Field(targetType = FieldType.DECIMAL128)
    var roi: BigDecimal? = null,

    @Field("block_height")
    var blockHeight: Long? = null,

    /**
     * 是否已使用月光宝盒
     */
    @Field("used_moonlight_box")
    var usedMoonlightBox: Boolean? = null,

    /**
     * 是否已使用现实宝石
     */
    @Field("used_reality_stone")
    var usedRealityStone: Boolean? = null,

    /**
     * 是否已使用力量宝石
     */
    @Field("used_power_stone")
    var usedPowerStone: Boolean? = null,

    /**
     * 是否已使用空间宝石
     */
    @Field("used_space_stone")
    var usedSpaceStone: Boolean? = null,

    /**
     * 是否已使用时间宝石
     */
    @Field("used_time_stone")
    var usedTimeStone: Boolean? = null,

    /**
     * 宝石活动的nft id
     */
    @Field("stone_activity_nft_id")
    var stoneActivityNftId: String? = null,

    /**
     * 结算模式
     * 2 or null - 利差结算
     * 0 - 清算
     */
    @Field("liquidity_type")
    var liquidityType: Int? = null,

    /**
     * Strike price
     */
    @Field("strike_price")
    var strikePrice: BigDecimal? = null,
    /**
     * Channel
     */
    @Indexed(background = true)
    var channel: UserChannel? = null,
    /**
     * 是否由系统账号创建的订单
     */
    @Field("system_buyer")
    var systemBuyer: String? = null,
    /**
     * 产品分类
     */
    @Indexed(background = true)
    var product: OptionOrderProduct? = null,
    /**
     * 钱包
     */
    @Indexed(background = true)
    var wallet: UserWallet? = null,
    /**
     * 从哪个活动进来
     */
    @Indexed(background = true)
    var campaign: String? = null,
    /**
     * 从哪个渠道进来
     */
    @Indexed(background = true)
    var from: String? = null,
    
    /**
     * 是否为 limit order
     */
    @Field("limit_order")
    var limitOrder: Boolean = false,

    /**
     * 是否为 limit order
     */
    @Field("otm_order")
    var otmOrder: Boolean? = null,

    var distance: BigDecimal? = null,

    @Field("utm_info")
    var utmInfo: UTMInfo? = null,

    @Field("seller_premium_fee")
    var sellerPremiumFee: BigDecimal? = null,

    @Field("platform_premium_fee")
    var platformPremiumFee: BigDecimal? = null,

    @Field("seller_premium_fee_in_usdt")
    var sellerPremiumFeeInUsdt: BigDecimal? = null,

    @Field("platform_premium_fee_in_usdt")
    var platformPremiumFeeInUsdt: BigDecimal? = null,

    @Field("premium_sign_info")
    var premiumSignInfo: PremiumSignInfo? = null,

    @Field("loss_in_usd", targetType = FieldType.DECIMAL128)
    var lossInUsd: BigDecimal? = null,

    @Field("settlement_price_info")
    var settlementPriceInfo: List<SettlementPriceInfo>? = null,

    @Field(targetType = FieldType.DECIMAL128)
    var volume: BigDecimal? = null,
)

enum class OptionOrderAction{
    BUY,
    SELL
}

enum class OptionOrderProduct{
    DEGEN,
    MINI_APP,
    MARKET_PLACE,
    BTC_FI,
    ZERO_DTE
}

enum class OptionStatus {
    PENDING,
    TO_BE_CONFIRMED, // 待确认
    EXECUTED, // 已成交
    SETTLE_TO_BE_CONFIRMED, // 结算待确认
    SETTLED, // 已结算
    CANCELLED,
    FAILED,
    // 结算失败
    SETTLE_FAILED;

    companion object {
        fun executedStatusList(): List<OptionStatus> {
            return listOf(EXECUTED, SETTLED, SETTLE_FAILED)
        }
    }

    fun notExecuted(): Boolean {
        return listOf(PENDING, TO_BE_CONFIRMED).contains(this)
    }
}

data class PremiumFeeInfo(
    @ApiModelProperty("权利金价格", required = true)
    val price: String,
    @ApiModelProperty("获取权利金的时间戳", required = true)
    val timestamp: String,
    @ApiModelProperty("权利金签名", required = true)
    val sign: String
)

data class PremiumSignInfo(
    @ApiModelProperty("生成Premium Sign的时刻的标的资产价格", required = true)
    val price: BigDecimal,
    @ApiModelProperty("生成Premium Sign的时间戳", required = true)
    val timestamp: Long,
    @ApiModelProperty("Premium Sign的有效时间戳", required = true)
    val validTimestamp: Long,
)

data class SettlementPriceInfo(
    @ApiModelProperty("结算价格", required = true)
    val price: BigDecimal,
    @ApiModelProperty("结算价格时间", required = true)
    val timestamp: Long,
)