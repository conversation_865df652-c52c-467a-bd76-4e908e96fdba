package io.vault.jasper.response

import io.swagger.annotations.ApiModel
import io.swagger.annotations.ApiModelProperty
import io.vault.jasper.enums.ChainType
import io.vault.jasper.enums.OptionDirection
import io.vault.jasper.enums.Symbol
import io.vault.jasper.model.OptionStatus

@ApiModel
data class FreePremiumBenefitUserResponse(

    @ApiModelProperty("是否拥有权益", required = true)
    val freePremiumBenefit: Boolean,

    @ApiModelProperty("已使用过的权益", required = true)
    val usedBenefits: List<String>
)