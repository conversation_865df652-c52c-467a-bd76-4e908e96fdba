package io.vault.jasper.listener

import io.vault.jasper.event.CheckBtrCampaignOrderEvent
import io.vault.jasper.repository.OptionOrderRepository
import io.vault.jasper.service.LossIsWinCampaignService
import org.slf4j.LoggerFactory
import org.springframework.context.event.EventListener
import org.springframework.data.repository.findByIdOrNull
import org.springframework.stereotype.Component

@Component
class LossIsWinCampaignEventListener(
    private val lossIsWinCampaignService: LossIsWinCampaignService,
    private val optionOrderRepository: OptionOrderRepository
) {
    private val logger = LoggerFactory.getLogger(this::class.java)

    @EventListener
    fun handleLossIsWinCampaign(event: CheckBtrCampaignOrderEvent) {
        try {
            val optionOrder = optionOrderRepository.findByIdOrNull(event.optionOrderId) ?: return
            lossIsWinCampaignService.handleOptionOrder(optionOrder)
        } catch (e: Exception) {
            logger.error("Failed to handle LossIsWin campaign for order ${event.optionOrderId}", e)
        }
    }
}