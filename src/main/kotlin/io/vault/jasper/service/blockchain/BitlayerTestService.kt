package io.vault.jasper.service.blockchain

import io.vault.jasper.blockchain.BitlayerBlockchainUtil
import io.vault.jasper.blockchain.BitlayerTestBlockchainUtil
import io.vault.jasper.blockchain.BlockchainUtilFactory
import io.vault.jasper.blockchain.evm.arbitrum.BitlayerScanApi
import io.vault.jasper.blockchain.evm.arbitrum.BitlayerTestScanApi
import io.vault.jasper.repository.*
import io.vault.jasper.service.*
import io.vault.jasper.task.AsyncEvmScanner
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.beans.factory.annotation.Value
import org.springframework.context.ApplicationEventPublisher
import org.springframework.stereotype.Service
import java.math.BigDecimal

@Service
class BitlayerTestService @Autowired constructor(
    blockchainUtil: BitlayerTestBlockchainUtil,
    scanApi: BitlayerTestScanApi,
    optionOrderRepository: OptionOrderRepository,
    optionOrderInfoRepository: OptionOrderInfoRepository,
    orderRepository: OrderRepository,
    blockchainRepository: BlockchainRepository,
    asyncEvmScanner: AsyncEvmScanner,
    private val jasperVaultService: JasperVaultService,
    eventPublisher: ApplicationEventPublisher,
    lpVaultRepository: LPVaultRepository,
    contractEventRepository: ContractEventRepository,
    currencyService: CurrencyService,
    optionOrderService: OptionOrderService,
    blockchainUtilFactory: BlockchainUtilFactory,
    subgraphService: SubgraphService,
    historyService: HistoryService,
    miniBridgeSwapOrderRepository: MiniBridgeSwapOrderRepository,
    alertService: AlertService
) : EvmService(
    blockchainUtil,
    blockchainRepository,
    asyncEvmScanner,
    orderRepository,
    optionOrderRepository,
    optionOrderInfoRepository,
    jasperVaultService,
    eventPublisher,
    lpVaultRepository,
    contractEventRepository,
    currencyService,
    optionOrderService,
    blockchainUtilFactory,
    scanApi,
    subgraphService,
    historyService,
    miniBridgeSwapOrderRepository,
    alertService
) {

    @Value("\${blockchain.bitlayer_test.contract.vault_factory}")
    override lateinit var vaultFactoryContract: String

    @Value("\${blockchain.bitlayer_test.contract.option_service}")
    override lateinit var optionServiceContract: String

    @Value("\${blockchain.bitlayer_test.contract.diamond}")
    override lateinit var addOrderLogAddress: String

    @Value("\${blockchain.bitlayer_test.contract.issuance_module}")
    override lateinit var issuanceModuleContract: String

    override fun getAndSetPrice(priceId: String, pythIdList: List<String>): BigDecimal {
        return jasperVaultService.getPythPrice(priceId) ?: throw Exception("Fail to get price by priceId=$priceId")
    }
}