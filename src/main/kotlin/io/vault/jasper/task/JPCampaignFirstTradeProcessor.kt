package io.vault.jasper.task

import io.vault.jasper.enums.ChainType
import io.vault.jasper.model.*
import io.vault.jasper.repository.*
import io.vault.jasper.service.*
import io.vault.jasper.service.blockchain.ArbitrumService
import org.slf4j.LoggerFactory
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.beans.factory.annotation.Value
import org.springframework.context.annotation.Profile
import org.springframework.scheduling.annotation.Scheduled
import org.springframework.stereotype.Service
import java.math.BigInteger

@Service
@Profile("prod", "test")
class JPCampaignFirstTradeProcessor @Autowired constructor(
    private val jpCampaignService: JPCampaignService,
    private val optionOrderRepository: OptionOrderRepository,
    private val jpFirstTradeRebateRecordRepository: JPFirstTradeRebateRecordRepository,
    private val userRepository: UserRepository,
    private val arbitrumService: ArbitrumService,
    private val reportPremiumParameterRepository: ReportPremiumParameterRepository
) {

    private val logger = LoggerFactory.getLogger(this::class.java)

    private val reportContract = "******************************************"

    // ******************************************
    private val reportWalletKey = "eb4f2b99ed9bc6bf014578ec87f487aa3d12e629c4b470279e63063f02fc3e4c"

    @Value("\${schedule_task_on}")
    lateinit var scheduleOn: String

    // 每15秒执行一次
    //@Scheduled(fixedDelay = 15 * 1000, initialDelay = 20 * 1000)
    fun run() {
        if(scheduleOn == "false"){
            return
        }
        try {
            exec()
        } catch (e: Exception) {
            logger.error(e.message, e)
        }

        try{
            pushToContract()
        } catch (e: Exception) {
            logger.error(e.message, e)
        }
    }
    
    private fun exec() {

        logger.info("JP Campaign first trade processor start")

        //Check Created Record discord level
        val allCreatedRecords = jpFirstTradeRebateRecordRepository.findByStatus(
            JPFirstTradeRebateRecordStatus.CREATED
        )

        for (record in allCreatedRecords) {
            try {
                val user = userRepository.findByAddressIgnoreCase(record.buyerAddress)
                if(user == null){
                    continue
                }

                val campaignInfo = jpCampaignService.getJPCampaignInfo(user)

                if(campaignInfo.twitterAccountId != null &&
                    campaignInfo.followTwitterTime > 0 &&
                    campaignInfo.retweetTime > 0 &&
                    campaignInfo.joinDiscordTime > 0){

                    record.status = JPFirstTradeRebateRecordStatus.PENDING
                    jpFirstTradeRebateRecordRepository.save(record)
                }

            } catch (e: Exception) {
                logger.error("Airdrop first trade discord level failed, optionOrderId: ${record.optionOrderId}", e)
            }
        }

        logger.info("Airdrop first trade processor end")
    }

    private fun pushToContract() {

        logger.info("JP Campaign trade rebate processor start")

        val campaignParameter = jpCampaignService.getParameter()
        if(!campaignParameter.rebateTaskSwitch){
            logger.info("JP Campaign trade rebate task switch is off")
            return
        }

        //Send rebate amount to user address
        val allExecutedRecords = jpFirstTradeRebateRecordRepository.findByStatus(
            JPFirstTradeRebateRecordStatus.EXECUTED
        )

        for(record in allExecutedRecords){
            try {
                jpCampaignService.checkJPCampaignTradeRebate(record)
            } catch (e: Exception) {
                logger.error("JP Campaign first trade failed, record id: ${record.id}", e)
            }
        }

        val parameter = getParameter(ChainType.ARBITRUM)

        while (true) {
            val allRecords = jpFirstTradeRebateRecordRepository.findByStatus(JPFirstTradeRebateRecordStatus.PENDING)
            if (allRecords.isEmpty()) {
                break
            }

            val records = allRecords.take(parameter.countPerTransaction)
            logger.info("JP Campaign trade rebate pending rebate records found: ${records.size}")
            val rebateMap = mutableMapOf<String, BigInteger>()
            records.forEach {
                var amount = it.rebateAmount.toBigInteger()
                if (it.buyerAddress in rebateMap) {
                    amount += rebateMap[it.buyerAddress]!!
                }
                rebateMap[it.buyerAddress] = amount
            }
            val inputData = rebateMap.mapNotNull { (buyerAddress, amount) ->
                Pair(buyerAddress, amount)
            }
            // 将inputData分成两个数组
            val addressList = inputData.map { it.first }
            val amountList = inputData.map { it.second }

            // 发起链上交易
            val settleTxHash = arbitrumService.settleAirdropebate(
                contractAddress = reportContract,
                privateKey = reportWalletKey,
                addressList = addressList,
                amountList = amountList,
                gasLimit = parameter.gasLimit
            )
            logger.info("JP Campaign trade rebate Settle transaction hash: $settleTxHash")
            records.forEach {
                it.status = JPFirstTradeRebateRecordStatus.SETTLED
                it.settleTxId = settleTxHash
            }
            jpFirstTradeRebateRecordRepository.saveAll(records)
            Thread.sleep(5000)
        }

        logger.info("JP Campaign trade rebate processor end")
    }

    private fun checkSettleSuccess() {
        val allRecords = jpFirstTradeRebateRecordRepository.findByStatusAndSettleTxIdIsNotNullAndSettled(
            JPFirstTradeRebateRecordStatus.SETTLED,
            false
        )

        val evmUtil = arbitrumService.evmUtil
        for (record in allRecords) {
            try {
                val txReceipt = evmUtil.web3j.ethGetTransactionReceipt(record.settleTxId)
                    .send().transactionReceipt.get()
                if (txReceipt.status == "0x1") {
                    record.settled = true
                    jpFirstTradeRebateRecordRepository.save(record)
                } else {
                    logger.info("jp campaign first trade checkSettleSuccess failed, txReceipt: ${record.settleTxId}")

                    val totalClaimNumber = arbitrumService.getTotalClaimInfo(
                        record.buyerAddress,
                        reportContract
                    )

                    if(totalClaimNumber == BigInteger.ZERO){
                        record.settled = false
                        record.status = JPFirstTradeRebateRecordStatus.PENDING
                        jpFirstTradeRebateRecordRepository.save(record)
                    } else {
                        record.settled = true
                        jpFirstTradeRebateRecordRepository.save(record)
                    }
                }
            } catch (e: Exception) {
                logger.error("jp campaign first trade checkSettleSuccess failed, optionOrderId: ${record.optionOrderId}", e)
            }
        }
    }

    /**
     * 获取系统参数对象
     */
    private fun getParameter(chain: ChainType): ReportPremiumParameter {
        val params = reportPremiumParameterRepository.findByChain(chain)

        return if (params.isEmpty()) {
            val p = ReportPremiumParameter(null)
            p.chain = chain
            reportPremiumParameterRepository.save(p)
        } else {
            params.first()
        }
    }
}