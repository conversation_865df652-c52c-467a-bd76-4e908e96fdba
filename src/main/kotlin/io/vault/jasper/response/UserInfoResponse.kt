package io.vault.jasper.response

import io.swagger.annotations.ApiModel
import io.swagger.annotations.ApiModelProperty
import io.vault.jasper.enums.ChainType
import io.vault.jasper.enums.Symbol
import io.vault.jasper.model.OrderType
import java.math.BigDecimal

@ApiModel("用户信息")
data class UserInfoResponse(
    @ApiModelProperty("邀请码", required = true)
    val inviteCode: String,
    @ApiModelProperty("父级钱包地址", required = false)
    val parentAddress: String? = null,
    @ApiModelProperty("父级邀请码", required = false)
    val parentReferral: String? = null,
    @ApiModelProperty("用户等级", required = true)
    val userLevel: String = "0",
    @ApiModelProperty("用户地址", required = false)
    val address: String? = null,
    @ApiModelProperty("用户是否使用kol返佣制度", required = false)
    val useKolRebate: Boolean = true,
    @ApiModelProperty("用户sPoint", required = false)
    val sPoint: String = "0",
    @ApiModelProperty("用户jPoint", required = false)
    val jPoint: String = "0",
    @ApiModelProperty("用户lPoint", required = false)
    val lPoint: String = "0",
)