package io.vault.jasper.repository

import io.vault.jasper.model.LossIsWinUserSummary
import org.springframework.data.mongodb.repository.MongoRepository
import java.math.BigDecimal

interface LossIsWinUserSummaryRepository : MongoRepository<LossIsWinUserSummary, String> {
    fun findTop10ByOrderByTotalBtrEarnedDesc(): List<LossIsWinUserSummary>
    fun findTop10ByOrderByTotalLossDesc(): List<LossIsWinUserSummary>
    fun countByTotalBtrEarnedGreaterThan(totalBtrEarned: BigDecimal): Long
    fun countByTotalLossGreaterThan(totalLoss: BigDecimal): Long
    fun findFirstByAddress(address: String): LossIsWinUserSummary?
    fun countByTransactionCountGreaterThan(transactionCount: Int): Long
}