package io.vault.jasper.response

import io.swagger.annotations.ApiModel
import io.swagger.annotations.ApiModelProperty
import io.vault.jasper.enums.ChainType
import io.vault.jasper.enums.OptionDirection
import io.vault.jasper.enums.Symbol
import io.vault.jasper.model.MoonlightRebateRecordStatus
import io.vault.jasper.model.OptionStatus
import io.vault.jasper.model.TransactionMethod

@ApiModel
data class StonePiecesResponse(

    @ApiModelProperty("记录ID", required = true)
    val recordId: String,

    @ApiModelProperty("对应的NFT ID", required = true)
    val nftId: String, // 2，3 对应现实宝石，4，5对应力量宝石

    @ApiModelProperty("状态", required = true)
    val status: MoonlightRebateRecordStatus, // CREATED 表示可以领取

    @ApiModelProperty("碎片数量", required = true)
    val optionOrderIds: MutableList<String>,

    @ApiModelProperty("创建时间戳", required = true)
    val created: Long,

)
