package io.vault.jasper.model

import io.vault.jasper.enums.ChainType
import org.springframework.data.annotation.Id
import org.springframework.data.mongodb.core.mapping.Document
import org.springframework.data.mongodb.core.mapping.Field
import org.springframework.data.mongodb.core.mapping.FieldType
import java.math.BigDecimal
import java.time.LocalDateTime

@Document("airdrop_summary")
data class AirdropSummary(
    @Id
    val id: String? = null,
    @Field("social_task_total_point", targetType = FieldType.DECIMAL128)
    var socialTaskTotalPoint: BigDecimal = BigDecimal.ZERO,
    @Field("invite_task_total_point", targetType = FieldType.DECIMAL128)
    var inviteTaskTotalPoint: BigDecimal = BigDecimal.ZERO,
    @Field("trade_task_total_point", targetType = FieldType.DECIMAL128)
    var tradeTaskTotalPoint: BigDecimal = BigDecimal.ZERO,
)