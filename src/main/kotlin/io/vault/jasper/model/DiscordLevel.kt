package io.vault.jasper.model

import org.springframework.data.annotation.CreatedDate
import org.springframework.data.annotation.Id
import org.springframework.data.mongodb.core.index.Indexed
import org.springframework.data.mongodb.core.mapping.Document
import org.springframework.data.mongodb.core.mapping.Field
import java.time.LocalDateTime


/**
 * {"avatar":"2e3f49598047c8a323d370291a6eadf8","discriminator":"0",
 * "guild_id":"1224530137500880947","id":"1006403503524151296",
 * "message_count":1,"monetize_xp_boost":0,"username":"0xcoca","xp":23,
 * "is_monetize_subscriber":false,"detailed_xp":[23,100,23],"level":0}
 */

@Document("discord_level")
data class DiscordLevel(
    @Id
    val id: String? = null,
    var avatar: String,
    var discriminator: String,
    @Field("guild_id")
    var guildId: String,
    @Field("discord_id")
    var discordId: String,
    @Field("message_count")
    var messageCount: Int,
    @Field("monetize_xp_boost")
    var monetizeXPBoost: Int,
    var username: String,
    var xp: Int,
    @Field("is_monetize_subscriber")
    var isMonetizeSubscriber: Boolean,
    @Field("detailed_xp")
    var detailedXP: List<Int>,
    var level: Int,
    @CreatedDate
    val created: LocalDateTime = LocalDateTime.now(),
)