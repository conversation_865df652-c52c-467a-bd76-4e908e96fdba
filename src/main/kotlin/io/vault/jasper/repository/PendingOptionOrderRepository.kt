package io.vault.jasper.repository

import io.vault.jasper.enums.ChainType
import io.vault.jasper.model.PendingOptionOrder
import org.springframework.data.mongodb.repository.MongoRepository
import org.springframework.stereotype.Repository

@Repository
interface PendingOptionOrderRepository : MongoRepository<PendingOptionOrder, String> {
    fun findBySaved(saved: Boolean): List<PendingOptionOrder>
    fun existsByChainAndTxHash(chain: ChainType, txHash: String): Boolean
    fun existsByChainAndTxHashAndOnChainOrderId(chain: ChainType, txHash: String, onChainOrderId: String): Boolean
}