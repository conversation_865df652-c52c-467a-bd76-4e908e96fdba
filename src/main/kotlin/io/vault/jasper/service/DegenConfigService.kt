package io.vault.jasper.service

import io.vault.jasper.enums.ChainType
import io.vault.jasper.enums.Symbol
import io.vault.jasper.exception.BusinessException
import io.vault.jasper.exception.ResultEnum
import io.vault.jasper.model.DegenConfig
import io.vault.jasper.repository.CurrencyRepository
import io.vault.jasper.repository.DegenConfigRepository
import org.slf4j.LoggerFactory
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.stereotype.Service

@Service
class DegenConfigService @Autowired constructor(
    private val degenConfigRepository: DegenConfigRepository
){

    private val logger = LoggerFactory.getLogger(this::class.java)

    private val defaultUnderlyingAssetAmount = listOf("2", "1", "0.1")

    private val defaultExpiryInHour = listOf("2", "8", "24")

    fun initDegenConfig(
        symbol: Symbol
    ){
        val dlcBTCConfigs = degenConfigRepository.findByBidAsset(symbol)
        if (dlcBTCConfigs.isEmpty()) {
            logger.info("Initializing $symbol degen config...")
            degenConfigRepository.save(
                DegenConfig(
                    bidAsset = symbol,
                    underlyingAssetAmount = defaultUnderlyingAssetAmount,
                    expiryInHour = defaultExpiryInHour,
                    lpVaultExpiryInHour = defaultExpiryInHour
                )
            )
            logger.info("$symbol degen config initialized")
        }
    }
}