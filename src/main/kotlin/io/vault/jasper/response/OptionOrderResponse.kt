package io.vault.jasper.response

import io.swagger.annotations.ApiModel
import io.swagger.annotations.ApiModelProperty
import io.vault.jasper.enums.ChainType
import io.vault.jasper.enums.OptionDirection
import io.vault.jasper.enums.Symbol
import io.vault.jasper.model.OptionStatus
import io.vault.jasper.model.OrderType
import io.vault.jasper.model.PremiumAsset

@ApiModel
data class OptionOrderResponse(
    val id: String,
    @ApiModelProperty("链类型", required = true)
    val chain: ChainType,
    @ApiModelProperty("订单类型", required = true)
    val orderType: OrderType,
    @ApiModelProperty("买家地址", required = true)
    val buyerAddress: String,
    @ApiModelProperty("买家的vault地址", required = true)
    val holder: String?,
    @ApiModelProperty("卖家地址", required = true)
    val seller: String?,
    @ApiModelProperty("卖家Vault地址", required = true)
    val sellerVault: String?,
    @ApiModelProperty("业务类型(CALL,PUT)", required = true)
    val businessType: OptionDirection,
    @ApiModelProperty("标的资产(ETH,WBTC)", required = true)
    val underlyingAsset: Symbol,
    @ApiModelProperty("标的资产合约地址(可为空)")
    val underlyingAssetAddress: String?,
    @ApiModelProperty("标的资产总数量", required = true)
    val totalAmount: String,
    @ApiModelProperty("行权价格", required = true)
    val strikePrice: String,
    @ApiModelProperty("行权资产", required = true)
    val strikeAsset: PremiumAsset,
    @ApiModelProperty("行权数量", required = true)
    val strikeAmount: String,
    @ApiModelProperty("实付权利金", required = true)
    val premiumFees: String,
    @ApiModelProperty("应付权利金", required = true)
    val premiumFeesShouldPay: String,
    @ApiModelProperty("权利金资产", required = true)
    val premiumFeesAsset: PremiumAsset,
    @ApiModelProperty("合约到期日", required = true)
    val expiryDate: Long,
    @ApiModelProperty("挂单时间", required = true)
    val createDate: Long,
    @ApiModelProperty("订单状态", required = true)
    val status: OptionStatus,
    @ApiModelProperty("签名前消息", required = true)
    val messageBeforeSign: String?,
    @ApiModelProperty("结算时候的链上Hash", required = true)
    val settlementHash: String?,
    @ApiModelProperty("期权交易撮合时候的链上Hash", required = true)
    val txHash: String?,
    @ApiModelProperty("是否免权利金", required = true)
    val premiumFree: Boolean,
    @ApiModelProperty("到期时间（小时）", required = true)
    val expiryInHour: String?,
    @ApiModelProperty("买家利润", required = false)
    val buyerProfit: String?,
    @ApiModelProperty("结算价格", required = false)
    val settlementPrice: String?,
    @ApiModelProperty("是否已使用月光宝盒", required = false)
    val usedMoonlightBox: Boolean?,
    @ApiModelProperty("标的资产", required = false)
    val bidAsset: String?,
    @ApiModelProperty("期权数量", required = false)
    val bidAmount: String?,
    @ApiModelProperty("是否现实宝石订单", required = true)
    val usedRealityStone: Boolean,
    @ApiModelProperty("是否力量宝石订单", required = true)
    val usedPowerStone: Boolean,
    @ApiModelProperty("宝石活动NFT ID", required = false)
    val stoneActivityNftId: String?,
    @ApiModelProperty("结算价格", required = false)
    val marketPriceAtSettlement: String?,
    @ApiModelProperty("标的资产在下单时刻的价格", required = false)
    val bidAssetPriceInUsdAtCreated: String?,
)
