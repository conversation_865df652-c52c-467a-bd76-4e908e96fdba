package io.vault.jasper.controller.pub

import EVMAddress
import io.swagger.v3.oas.annotations.Operation
import io.vault.jasper.ApiResponse
import io.vault.jasper.annotation.ApiCache
import io.vault.jasper.dto.MiniBridgeSwapOrderDTO
import io.vault.jasper.dto.SubmitMiniBridgeSwapOrderDepositHashDTO
import io.vault.jasper.enums.ChainType
import io.vault.jasper.enums.Symbol
import io.vault.jasper.exception.BusinessException
import io.vault.jasper.exception.ResultEnum
import io.vault.jasper.model.MiniBridgeSwapOrder
import io.vault.jasper.model.MiniBridgeSwapOrderStatus
import io.vault.jasper.repository.*
import io.vault.jasper.response.GetPriceResponse
import io.vault.jasper.response.MiniBridgeParameterResponse
import io.vault.jasper.service.MiniBridgeSwapOrderService
import io.vault.jasper.service.blockchain.BlockchainServiceFactory
import io.vault.jasper.service.blockchain.EvmService
import io.vault.jasper.utils.AuthUtil
import org.slf4j.LoggerFactory
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.data.repository.findByIdOrNull
import org.springframework.web.bind.annotation.*
import java.math.BigDecimal
import java.time.LocalDateTime
import java.util.*
import javax.servlet.http.HttpServletRequest
import javax.validation.Valid


@RestController
@RequestMapping("/mini_bridge_swap_order")
class MiniBridgeSwapOrderController @Autowired constructor(
    private val miniBridgeSwapOrderRepository: MiniBridgeSwapOrderRepository,
    private val authUtil: AuthUtil,
    private val blockchainServiceFactory: BlockchainServiceFactory,
    private val miniBridgeSwapOrderService: MiniBridgeSwapOrderService,
    private val miniBridgePriceOracleRepository: MiniBridgePriceOracleRepository,
) {

    private var logger = LoggerFactory.getLogger(this::class.java)

    @Operation(summary = "获取交易价格")
    @CrossOrigin
    @ApiCache(key = "mini_bridge_swap_order:get_price", dynamicKey = ["bidAsset", "quoteAsset"], expire = 15)
    @GetMapping("/get_price")
    fun getPrice(
        request: HttpServletRequest,
        @RequestParam bidAsset: Symbol,
        @RequestParam quoteAsset: Symbol
    ): ApiResponse<GetPriceResponse>{
        authUtil.getIpAddr(request)

        val priceOracleInfo = miniBridgePriceOracleRepository.findFirstByBidAssetAndQuoteAssetOrderByCreatedDesc(
            bidAsset,
            quoteAsset
        )

        if(priceOracleInfo == null){
            throw BusinessException(ResultEnum.PRICE_ORACLE_NOT_FOUND)
        }

        return ApiResponse.success(
            GetPriceResponse(
                priceOracleInfo.id!!,
                priceOracleInfo.price.stripTrailingZeros().toPlainString(),
                priceOracleInfo.timestamp
            )
        )
    }

    @Operation(summary = "获取AA Vault")
    @CrossOrigin
    @ApiCache(key = "mini_bridge_swap_order:get_vault", dynamicKey = ["address", "chain"], expire = 3600)
    @GetMapping("/get_vault")
    fun getVault(
        request: HttpServletRequest,
        @EVMAddress @RequestParam address: String,
        @RequestParam chain: ChainType
    ): ApiResponse<String>{
        authUtil.getIpAddr(request)

        if(chain != ChainType.BITLAYER &&
            chain != ChainType.ARBITRUM &&
            chain != ChainType.BSC &&
            chain != ChainType.BASE){
            throw BusinessException(ResultEnum.CHAIN_NOT_SUPPORTED)
        }

        val evmService = blockchainServiceFactory.getBlockchainService(chain) as EvmService
        val vault = evmService.getVaultAddress(address)

        return ApiResponse.success(vault)
    }

    @Operation(summary = "创建交易订单, 返回订单ID")
    @CrossOrigin
    @PostMapping("/create")
    fun create(
        request: HttpServletRequest,
        @Valid @RequestBody dto: MiniBridgeSwapOrderDTO
    ): ApiResponse<String> {
        authUtil.filterIPBlacklist(request)

        //Only Support Bitlayer <-> Arbitrum right now
        if(dto.fromChain == ChainType.BITLAYER){
            if(dto.toChain != ChainType.ARBITRUM) {
                throw BusinessException(ResultEnum.INVALID_MINI_BRIDGE_ORDER_CHAIN)
            }

            if(dto.fromAsset != Symbol.BTC && dto.fromAsset != Symbol.USDT){
                throw BusinessException(ResultEnum.INVALID_MINI_BRIDGE_ORDER_BITLAYER_ASSET)
            }

            if(dto.toAsset != Symbol.USDT){
                throw BusinessException(ResultEnum.INVALID_MINI_BRIDGE_ORDER_ARBITRUM_ASSET)
            }

        } else if(dto.fromChain == ChainType.ARBITRUM){
            if(dto.toChain != ChainType.BITLAYER) {
                throw BusinessException(ResultEnum.INVALID_MINI_BRIDGE_ORDER_CHAIN)
            }

            if(dto.toAsset != Symbol.BTC && dto.toAsset != Symbol.USDT){
                throw BusinessException(ResultEnum.INVALID_MINI_BRIDGE_ORDER_BITLAYER_ASSET)
            }

            if(dto.fromAsset != Symbol.USDT){
                throw BusinessException(ResultEnum.INVALID_MINI_BRIDGE_ORDER_ARBITRUM_ASSET)
            }

        } else {
            throw BusinessException(ResultEnum.INVALID_MINI_BRIDGE_ORDER_CHAIN)
        }

        // Verify the to address
        miniBridgeSwapOrderService.validateToAddress(
            dto.toChain,
            dto.address,
            dto.toAddress
        )

        //Verify the price
        val priceId = dto.priceOracleId
        val priceOracleInfo = miniBridgePriceOracleRepository.findByIdOrNull(priceId)
            ?: throw BusinessException(ResultEnum.PRICE_ORACLE_NOT_FOUND)

        val parameter = miniBridgeSwapOrderService.getParameter()
        val now = (Date().time / 1000) - parameter.priceOracleExpiryTime
        if(priceOracleInfo.timestamp < now){
            throw BusinessException(ResultEnum.INVALID_PRICE_ORACLE)
        }

        // Validate Amount
        miniBridgeSwapOrderService.validateAmount(
            dto.fromAsset,
            dto.toAsset,
            BigDecimal(dto.depositAmount),
            BigDecimal(dto.withdrawAmount),
            priceOracleInfo
        )

        val sameOrder = miniBridgeSwapOrderRepository.findFirstByFromAddressAndToAddressAndFromChainAndToChainAndFromAssetAndToAssetAndDepositAmountAndWithdrawAmountAndStatusAndDepositTxHashIsNull(
            dto.address,
            dto.toAddress,
            dto.fromChain,
            dto.toChain,
            dto.fromAsset,
            dto.toAsset,
            BigDecimal(dto.depositAmount),
            BigDecimal(dto.withdrawAmount),
            MiniBridgeSwapOrderStatus.PENDING_DEPOSIT
        )

        if(sameOrder != null){
            return ApiResponse.success(sameOrder.id!!)
        }

        val order = MiniBridgeSwapOrder(
            fromAddress = dto.address,
            toAddress = dto.toAddress,
            fromChain = dto.fromChain,
            toChain = dto.toChain,
            fromAsset = dto.fromAsset,
            toAsset = dto.toAsset,
            depositAmount = BigDecimal(dto.depositAmount),
            withdrawAmount = BigDecimal(dto.withdrawAmount),
            priceId = priceId
        )

        val newOrder = miniBridgeSwapOrderRepository.save(order)

        miniBridgeSwapOrderService.calculateFee(newOrder)

        return ApiResponse.success(newOrder.id!!)
    }

    @Operation(summary = "提交订单存款交易哈希")
    @CrossOrigin
    @PostMapping("/submit_deposit_tx_hash")
    fun submitDepositTxHash(
        request: HttpServletRequest,
        @Valid @RequestBody dto: SubmitMiniBridgeSwapOrderDepositHashDTO
    ): ApiResponse<Boolean>{
        authUtil.getIpAddr(request)

        val order = miniBridgeSwapOrderRepository.findByIdOrNull(dto.orderId)
            ?: throw BusinessException(ResultEnum.MINI_BRIDGE_ORDER_NOT_FOUND)

        val existingHashOrder = miniBridgeSwapOrderRepository.findByDepositTxHash(dto.txHash)
        if(existingHashOrder.isNotEmpty()){
            //throw BusinessException(ResultEnum.MINI_BRIDGE_ORDER_HASH_ALREADY_EXISTS)
            ApiResponse.success(true)
        }

        if(order.depositTxHash == null) {
            order.depositTxHash = dto.txHash
            miniBridgeSwapOrderRepository.save(order)
        }

        return ApiResponse.success(true)
    }

    @Operation(summary = "获取交易参数")
    @CrossOrigin
    @ApiCache(key = "mini_bridge_swap_order:get_parameter", dynamicKey = [], expire = 60)
    @GetMapping("/get_parameter")
    fun getParameter(
        request: HttpServletRequest,
    ): ApiResponse<MiniBridgeParameterResponse>{
        authUtil.getIpAddr(request)

        val parameter = miniBridgeSwapOrderService.getParameter()

        return ApiResponse.success(
            MiniBridgeParameterResponse(
                parameter.priceGap.stripTrailingZeros().toPlainString(),
                parameter.maxValue.stripTrailingZeros().toPlainString(),
                parameter.minValue.stripTrailingZeros().toPlainString(),
                parameter.feeValue.stripTrailingZeros().toPlainString(),
            )
        )
    }
}