package io.vault.jasper.response

import io.swagger.annotations.ApiModel
import io.swagger.annotations.ApiModelProperty
import java.math.BigDecimal

@ApiModel("用户直推返佣概况")
data class UserNetworkRebateSummaryResponse(
    @ApiModelProperty("直推地址", required = true)
    val address: String,
    @ApiModelProperty("别名", required = true)
    val alias: String?,
    @ApiModelProperty("返佣比例", required = true)
    val percentage: String,
    @ApiModelProperty("团队贡献权利金", required = true)
    val premiumFee: String,
    @ApiModelProperty("团队为我产生的佣金", required = true)
    val rebate: String
)