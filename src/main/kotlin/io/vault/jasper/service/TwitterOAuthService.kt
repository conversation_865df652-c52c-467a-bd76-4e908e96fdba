package io.vault.jasper.service

import com.fasterxml.jackson.databind.JsonNode
import com.fasterxml.jackson.databind.ObjectMapper
import io.vault.jasper.model.SystemParameter
import io.vault.jasper.model.TwitterAPIParameter
import io.vault.jasper.repository.TwitterAPIParameterRepository
import oauth.signpost.OAuthConsumer
import oauth.signpost.commonshttp.CommonsHttpOAuthConsumer
import oauth.signpost.http.HttpParameters
import org.apache.http.client.methods.HttpPost
import org.apache.http.impl.client.HttpClients
import org.slf4j.LoggerFactory
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.http.HttpEntity
import org.springframework.http.HttpHeaders
import org.springframework.http.HttpMethod
import org.springframework.http.MediaType
import org.springframework.stereotype.Service
import org.springframework.util.LinkedMultiValueMap
import org.springframework.util.MultiValueMap
import org.springframework.web.client.RestTemplate
import org.springframework.web.util.UriComponentsBuilder

@Service
class TwitterOAuthService @Autowired constructor(
    private val twitterAPIParameterRepository: TwitterAPIParameterRepository
) {

    private val apiKey: String = "*************************"
    private val apiSecret: String = "qofVNPwa98uYOLeKHX3MdFK6KrZSDyIyKkIVBHlDGCzZHjSuyb"
    private val callbackUrl: String = "https://apiv2.jaspervault.io/twitter/callback"

    private val restTemplate = RestTemplate()

    private val logger = LoggerFactory.getLogger(this::class.java)

    //private val consumer: OAuthConsumer = CommonsHttpOAuthConsumer(apiKey, apiSecret)

    private val objectMapper: ObjectMapper = ObjectMapper()

    fun getRequestToken(): String {
        val httpClient = HttpClients.createDefault()
        val request = HttpPost("https://api.twitter.com/oauth/request_token")

        val consumer = CommonsHttpOAuthConsumer(apiKey, apiSecret)
        // 在请求中添加回调 URL
        val params = HttpParameters()
        params.put("oauth_callback", callbackUrl)
        //params.put("x_auth_access_type", "read")
        consumer.setAdditionalParameters(params)
        consumer.sign(request)  // 使用 OAuthConsumer 对请求进行签名

        //logger.info("Twitter Service consumer parameters : ${consumer.requestParameters.oAuthParameters}")

        val response = httpClient.execute(request)
        val entity = response.entity
        val responseContent = entity.content.bufferedReader().use { it.readText() }

        logger.info("Twitter Service response: $responseContent")
        return parseOAuthToken(responseContent)
    }

    private fun parseOAuthToken(response: String): String {
        // 解析 Twitter 返回的响应字符串，提取 `oauth_token`
        return response.split("&").first { it.startsWith("oauth_token") }.split("=")[1]
    }

    fun getAccessToken(oauthToken: String, oauthVerifier: String): Map<String, String>  {

        val consumer = CommonsHttpOAuthConsumer(apiKey, apiSecret)
        consumer.setTokenWithSecret(oauthToken, "")

        val httpClient = HttpClients.createDefault()
        val request = HttpPost("https://api.twitter.com/oauth/access_token")
        request.addHeader("Content-Type", "application/x-www-form-urlencoded")

        // Sign the request with the oauth_verifier parameter
        val params = "oauth_verifier=$oauthVerifier"
        request.entity = org.apache.http.entity.StringEntity(params)
        consumer.sign(request)

        val response = httpClient.execute(request)
        val entity = response.entity
        val responseContent = entity.content.bufferedReader().use { it.readText() }

        return parseAccessToken(responseContent)
    }

    private fun parseAccessToken(response: String): Map<String, String> {
        return response.split("&")
            .map { it.split("=") }
            .associate { it[0] to it[1] }
    }

    fun getUserInfo(accessToken: String, accessTokenSecret: String): JsonNode {

        val consumer = CommonsHttpOAuthConsumer(apiKey, apiSecret)
        consumer.setTokenWithSecret(accessToken, accessTokenSecret)

        val httpClient = HttpClients.createDefault()
        val request = org.apache.http.client.methods.HttpGet("https://api.twitter.com/1.1/account/verify_credentials.json")

        consumer.sign(request)  // Sign the request with the access token

        val response = httpClient.execute(request)
        val entity = response.entity
        val responseStr =  entity.content.bufferedReader().use { it.readText() }

        val node = objectMapper.readTree(responseStr)
        return node
    }

    fun getFollowerList(accessToken: String, accessTokenSecret: String): JsonNode {

        val consumer = CommonsHttpOAuthConsumer(apiKey, apiSecret)
        consumer.setTokenWithSecret(accessToken, accessTokenSecret)

        val httpClient = HttpClients.createDefault()
        val request = org.apache.http.client.methods.HttpGet("https://api.x.com/1.1/friends/list.json")

        consumer.sign(request)  // Sign the request with the access token

        val response = httpClient.execute(request)
        val entity = response.entity
        val responseStr =  entity.content.bufferedReader().use { it.readText() }

        val node = objectMapper.readTree(responseStr)
        return node
    }

    fun getParameter(): TwitterAPIParameter{

        val params = twitterAPIParameterRepository.findAll()

        return if (params.isEmpty()) {
            val p = TwitterAPIParameter(null)
            twitterAPIParameterRepository.save(p)
        } else {
            params.first()
        }
    }
}
