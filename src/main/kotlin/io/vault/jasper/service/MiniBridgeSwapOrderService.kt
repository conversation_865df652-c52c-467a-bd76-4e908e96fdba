package io.vault.jasper.service

import com.fasterxml.jackson.databind.ObjectMapper
import io.vault.jasper.enums.ChainType
import io.vault.jasper.enums.Symbol
import io.vault.jasper.exception.BusinessException
import io.vault.jasper.exception.ResultEnum
import io.vault.jasper.model.*
import io.vault.jasper.repository.MiniBridgePriceOracleRepository
import io.vault.jasper.repository.MiniBridgeSwapOrderRepository
import io.vault.jasper.repository.MiniBridgeSwapParameterRepository
import io.vault.jasper.service.blockchain.BlockchainServiceFactory
import io.vault.jasper.service.blockchain.EvmService
import org.apache.commons.io.IOUtils
import org.bouncycastle.jce.provider.BouncyCastleProvider
import org.slf4j.LoggerFactory
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.core.io.ClassPathResource
import org.springframework.data.repository.findByIdOrNull
import org.springframework.http.*
import org.springframework.stereotype.Service
import org.springframework.web.client.RestTemplate
import org.web3j.abi.datatypes.Address
import org.web3j.utils.Numeric
import java.io.InputStream
import java.io.StringWriter
import java.math.BigDecimal
import java.math.BigInteger
import java.nio.charset.Charset
import java.nio.charset.StandardCharsets
import java.security.*
import java.security.spec.PKCS8EncodedKeySpec
import java.time.Instant
import java.util.*


@Service
class MiniBridgeSwapOrderService @Autowired constructor(
    private val miniBridgeSwapOrderRepository: MiniBridgeSwapOrderRepository,
    private val miniBridgePriceOracleRepository: MiniBridgePriceOracleRepository,
    private val blockchainServiceFactory: BlockchainServiceFactory,
    private val jasperVaultService: JasperVaultService,
    private val currencyService: CurrencyService,
    private val miniBridgeSwapParameterRepository: MiniBridgeSwapParameterRepository,
){

    private val logger = LoggerFactory.getLogger(this::class.java)

    private val systemDepositAddress = "0x73Fa16C12e91A71c10115772a201930570FB4a48"

    private val objectMapper = ObjectMapper()

    val accessToken = "*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************"

    fun getMarketPrice(
        bidAsset: Symbol,
        quoteAsset: Symbol,
    ): OracleInfo {

        val bidPythId = currencyService.getPriceId(bidAsset)
        val bidAssetPrice = jasperVaultService.getPythPrice(bidPythId)
        val quotePythId = currencyService.getPriceId(quoteAsset)
        val quoteAssetPrice = jasperVaultService.getPythPrice(quotePythId)

        return OracleInfo(
            bidAsset = bidAsset,
            quoteAsset = quoteAsset,
            bidAssetUsdPrice = bidAssetPrice!!,
            quoteAssetUsdPrice = quoteAssetPrice!!
        )
    }

    fun validateToAddress(
        chain: ChainType,
        fromAddress: String,
        toAddress: String
    ): Boolean {

        // 只能转到自己 EOA 或者 目标链 AA 地址
        if(fromAddress.compareTo(toAddress, true) != 0){

            //
            val evmService = blockchainServiceFactory.getBlockchainService(chain) as EvmService
            val aaVaultAddress = evmService.getVaultAddress(fromAddress)

            if(aaVaultAddress.compareTo(toAddress, true) != 0){
                throw BusinessException(ResultEnum.INVALID_MINI_BRIDGE_ORDER_TO_ADDRESS)
            }
        }

        return true
    }

    fun calculateFee(
        order: MiniBridgeSwapOrder
    ) {

        val parameter = getParameter()

        if(order.toAsset == Symbol.USDT){

            order.swapFee = parameter.feeValue
            order.actualWithdrawAmount = order.withdrawAmount.subtract(order.swapFee)
            miniBridgeSwapOrderRepository.save(order)

        } else if(order.toAsset == Symbol.BTC){

            val priceOracleInfo = miniBridgePriceOracleRepository.findByIdOrNull(order.priceId)
                ?: throw BusinessException(ResultEnum.PRICE_ORACLE_NOT_FOUND)

            order.swapFee = parameter.feeValue.divide(priceOracleInfo.price, 18, BigDecimal.ROUND_HALF_UP)
            order.actualWithdrawAmount = order.withdrawAmount.subtract(order.swapFee)
            miniBridgeSwapOrderRepository.save(order)
        }
    }

    fun validateAmount(
        fromAsset: Symbol,
        toAsset: Symbol,
        depositAmount: BigDecimal,
        withdrawAmount: BigDecimal,
        priceOracle: MiniBridgePriceOracle,
    ): Boolean{

        var swapValue = BigDecimal.ZERO
        var actualWithdrawAmount = BigDecimal.ZERO
        val parameter = getParameter()

        if(fromAsset == Symbol.USDT && toAsset == Symbol.USDT){
            swapValue = depositAmount
            actualWithdrawAmount = depositAmount
        } else if(fromAsset == Symbol.USDT && toAsset == Symbol.BTC){

            val actualPrice = priceOracle.price.multiply(BigDecimal.ONE + parameter.priceGap)
            actualWithdrawAmount = depositAmount.divide(actualPrice, 6, BigDecimal.ROUND_HALF_UP)
            swapValue = depositAmount

        } else if(fromAsset == Symbol.BTC && toAsset == Symbol.USDT){

            val actualPrice = priceOracle.price.multiply(BigDecimal.ONE - parameter.priceGap)
            actualWithdrawAmount = depositAmount.multiply(actualPrice).setScale(6, BigDecimal.ROUND_HALF_UP)
            swapValue = withdrawAmount

        } else {
            throw BusinessException(ResultEnum.INVALID_MINI_BRIDGE_ORDER_AMOUNT)
        }

        logger.info("swapVault: $swapValue, actualWithdrawAmount: $actualWithdrawAmount, withdrawAmount: $withdrawAmount")

        if(actualWithdrawAmount.subtract(withdrawAmount).abs() > BigDecimal("0.05")){
            throw BusinessException(ResultEnum.INVALID_MINI_BRIDGE_ORDER_AMOUNT)
        }

        if(swapValue > parameter.maxValue){
            throw BusinessException(ResultEnum.INVALID_MINI_BRIDGE_ORDER_AMOUNT)
        }

        if(swapValue < parameter.minValue){
            throw BusinessException(ResultEnum.INVALID_MINI_BRIDGE_ORDER_AMOUNT)
        }

        return true
    }

    fun checkDepositReceipt(
        order: MiniBridgeSwapOrder
    ): Boolean {

        val existingHashOrders = miniBridgeSwapOrderRepository.findByDepositTxHash(order.depositTxHash!!)
        if(existingHashOrders.size > 1){
            throw BusinessException(ResultEnum.MINI_BRIDGE_ORDER_HASH_ALREADY_EXISTS)
        }

        val blockchainService = blockchainServiceFactory.getBlockchainService(order.fromChain) as EvmService
        val parameter = getParameter()

        val evmUtil = blockchainService.evmUtil
        val txReceipt = evmUtil.web3j.ethGetTransactionReceipt(order.depositTxHash)
            .send().transactionReceipt.get()

        val lastBlock = evmUtil.web3j.ethBlockNumber().send().blockNumber.toLong()

        if(lastBlock - txReceipt.blockNumber.toLong() < parameter.blockConfirmation){
            logger.info("checkDepositReceipt transaction ${order.depositTxHash} not confirmed")
            return false
        }

        if(txReceipt.status != "0x1"){
            throw BusinessException(ResultEnum.MINI_BRIDGE_ORDER_DEPOSIT_TX_FAILED)
        }

        val from = txReceipt.from
        val to = txReceipt.to

        if(order.fromAddress.compareTo(from, true) != 0){
            throw BusinessException(ResultEnum.MINI_BRIDGE_ORDER_DEPOSIT_TX_INVALID_FROM_ADDRESS)
        }

        if(order.fromAsset == Symbol.BTC){

            if(systemDepositAddress.compareTo(to, true) != 0){
                throw BusinessException(ResultEnum.MINI_BRIDGE_ORDER_DEPOSIT_TX_INVALID_TO_ADDRESS)
            }

            val transaction = evmUtil.web3j.ethGetTransactionByHash(order.depositTxHash).send().transaction.get()
            val value = transaction.value
            val orderValue = order.depositAmount.movePointRight(18).toBigInteger()

            if(value != orderValue){
                throw BusinessException(ResultEnum.MINI_BRIDGE_ORDER_DEPOSIT_TX_INVALID_VALUE)
            }
        } else if(order.fromAsset == Symbol.USDT){

            val usdtAddress = currencyService.getCurrencyContract(order.fromChain, Symbol.USDT)
            val usdtDecimal = currencyService.getCurrencyDecimal(order.fromChain, Symbol.USDT)

            if(usdtAddress.compareTo(to, true) != 0){
                throw BusinessException(ResultEnum.MINI_BRIDGE_ORDER_DEPOSIT_TX_INVALID_TO_ADDRESS)
            }

            //Find Log
            var transferToAddress: String? = null
            var transferValue: BigInteger? = null

            txReceipt.logs.forEach { log ->
                if (log.address.compareTo(usdtAddress, ignoreCase = true) != 0) {
                    return@forEach
                }
                val transferFnHash = log.topics.firstOrNull() ?: return@forEach
                if (transferFnHash.compareTo(blockchainService.usdtTransferFnHash, true) != 0) {
                    return@forEach
                }
                val transferTo = try {
                    log.topics[2]
                } catch (e: Exception) {
                    return@forEach
                }
                transferToAddress = Address(transferTo).value
                transferValue = Numeric.toBigInt(log.data)
            }

            if(transferToAddress == null){
                throw BusinessException(ResultEnum.MINI_BRIDGE_ORDER_DEPOSIT_TX_INVALID_TO_ADDRESS)
            }

            logger.info("Transfer to address: $transferToAddress")
            if (transferToAddress!!.compareTo(systemDepositAddress, ignoreCase = true) != 0) {
                throw BusinessException(ResultEnum.MINI_BRIDGE_ORDER_DEPOSIT_TX_INVALID_TO_ADDRESS)
            }

            if(transferValue == null || transferValue != order.depositAmount.movePointRight(usdtDecimal).toBigInteger()){
                throw BusinessException(ResultEnum.MINI_BRIDGE_ORDER_DEPOSIT_TX_INVALID_VALUE)
            }
        }

        return true
    }

    fun getEvmChainName(
        chain: ChainType
    ): String{
        if(chain == ChainType.BITLAYER){
            return "evm_200901"
        }

        return "evm_42161"
    }

    fun sendFordefiRequest(
        chain: ChainType,
        currencyAddress: String,
        decimals: Int,
        to: String,
        amount: BigDecimal,
    ) : String?{

        val bigNumber = amount.movePointRight(decimals).stripTrailingZeros().toPlainString()
        val evmChainName = getEvmChainName(chain)

        val privateKeyFile = ClassPathResource("secret/private_pkcs8.pem").inputStream
        logger.info("PrivateKeyFile: $privateKeyFile")
        val path = "/api/v1/transactions"
        var requestJson = "{" +
            "\"signer_type\": \"api_signer\"," +
            "\"vault_id\": \"b09904e9-cd8b-4030-a72b-106af10923a4\"," +
            "\"note\": \"\"," +
            "\"type\": \"evm_transaction\"," +
            "\"details\": {" +
            "\"type\": \"evm_transfer\"," +
            "\"gas\": {" +
            "\"type\": \"priority\"," +
            "\"priority_level\": \"medium\"" +
            "}," +
            "\"to\": \"$to\"," +
            "\"asset_identifier\": {" +
            "\"type\": \"evm\"," +
            "\"details\": {" +
            "\"type\": \"native\"," +
            "\"chain\": \"$evmChainName\"" +
            "}" +
            "}," +
            "\"value\": {" +
            "\"type\": \"value\"," +
            "\"value\": \"$bigNumber\"" +
            "}" +
            "}" +
            "}"

        if(currencyAddress.compareTo("0xEeeeeEeeeEeEeeEeEeEeeEEEeeeeEeeeeeeeEEeE", true) != 0){
            requestJson = "{" +
                "\"signer_type\": \"api_signer\"," +
                "\"vault_id\": \"b09904e9-cd8b-4030-a72b-106af10923a4\"," +
                "\"note\": \"\"," +
                "\"type\": \"evm_transaction\"," +
                "\"details\": {" +
                "\"type\": \"evm_transfer\"," +
                "\"gas\": {" +
                "\"type\": \"priority\"," +
                "\"priority_level\": \"medium\"" +
                "}," +
                "\"to\": \"$to\"," +
                "\"asset_identifier\": {" +
                "\"type\": \"evm\"," +
                "\"details\": {" +
                "\"type\": \"erc20\"," +
                "\"token\": {" +
                    "\"hex_repr\": \"$currencyAddress\"," +
                    "\"chain\": \"$evmChainName\"" +
                "}" +
                "}" +
                "}," +
                "\"value\": {" +
                "\"type\": \"value\"," +
                "\"value\": \"$bigNumber\"" +
                "}" +
                "}" +
                "}"

        }

        val timestamp = Instant.now().epochSecond.toString()
        val payload = "$path|$timestamp|$requestJson"

        // 读取私钥并签名
        val privateKey = loadPrivateKey(privateKeyFile)
        val signature = signECDSA(payload, privateKey)

        // 发送 HTTP 请求
        val response = sendPostRequest(path, requestJson, accessToken, signature, timestamp)
        logger.info("Response: ${response.body}")

        val data = objectMapper.readTree(response.body)
        return data.get("id").asText()
    }

    fun getFordefiTransactionHashById(
        transactionId: String
    ): String{

        val path = "/api/v1/transactions"
        val response = sendGetRequest(path, transactionId, accessToken)
        logger.info("Get Transaction Response: ${response.body}")
        val data = objectMapper.readTree(response.body)
        return data.get("hash").asText()
    }

    // 读取 ECDSA 私钥
    fun loadPrivateKey(keyFileInputStream: InputStream): PrivateKey {
        Security.addProvider(BouncyCastleProvider())

        val writer = StringWriter()
        IOUtils.copy(keyFileInputStream, writer, Charset.defaultCharset())
        val text = writer.toString()

        //val text = keyFile.readText()
        logger.info("Private Key: $text")
        val keyBytes = text.replace("-----BEGIN PRIVATE KEY-----", "")
            .replace("-----END PRIVATE KEY-----", "")
            .replace("\n", "").decodeBase64()

        val keyFactory = KeyFactory.getInstance("EC", "BC")
        val keySpec = PKCS8EncodedKeySpec(keyBytes)
        return keyFactory.generatePrivate(keySpec)
    }

    // ECDSA 签名
    fun signECDSA(data: String, privateKey: PrivateKey): String {
        val signature = Signature.getInstance("SHA256withECDSA", "BC")
        signature.initSign(privateKey)
        signature.update(data.toByteArray(StandardCharsets.UTF_8))
        return Base64.getEncoder().encodeToString(signature.sign())
    }

    // 发送 HTTP 请求
    fun sendPostRequest(path: String, body: String, token: String, signature: String, timestamp: String): ResponseEntity<String> {
        val restTemplate = RestTemplate()
        val url = "https://api.fordefi.com$path"

        val headers = HttpHeaders().apply {
            contentType = MediaType.APPLICATION_JSON
            set("Authorization", "Bearer $token")
            set("x-signature", signature)
            set("x-timestamp", timestamp)
        }

        val requestEntity = HttpEntity(body, headers)
        return restTemplate.exchange(url, HttpMethod.POST, requestEntity, String::class.java)
    }

    fun sendGetRequest(path: String, id: String, token: String): ResponseEntity<String> {
        val restTemplate = RestTemplate()
        val url = "https://api.fordefi.com$path/$id"
        val headers = HttpHeaders().apply {
            contentType = MediaType.APPLICATION_JSON
            set("Authorization", "Bearer $token")
        }

        val requestEntity = HttpEntity("{}", headers)
        return restTemplate.exchange(url, HttpMethod.GET, requestEntity, String::class.java)
    }

    // Base64 解码扩展函数
    fun String.decodeBase64(): ByteArray = Base64.getDecoder().decode(this)

    fun getParameter(): MiniBridgeSwapParameter {
        val params = miniBridgeSwapParameterRepository.findAll()

        return if (params.isEmpty()) {
            val p = MiniBridgeSwapParameter(null)
            miniBridgeSwapParameterRepository.save(p)
        } else {
            params.first()
        }
    }

}