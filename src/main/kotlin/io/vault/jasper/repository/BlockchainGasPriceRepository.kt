package io.vault.jasper.repository

import io.vault.jasper.enums.ChainType
import io.vault.jasper.model.BlockchainGasPrice
import org.springframework.data.mongodb.repository.MongoRepository
import org.springframework.stereotype.Repository

@Repository
interface BlockchainGasPriceRepository : MongoRepository<BlockchainGasPrice, String> {
    fun findFirstByChain(chain: ChainType): BlockchainGasPrice?
}