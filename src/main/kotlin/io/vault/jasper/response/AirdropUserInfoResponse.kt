package io.vault.jasper.response

import io.swagger.annotations.ApiModel
import io.swagger.annotations.ApiModelProperty
import org.web3j.abi.datatypes.Bool

@ApiModel
data class AirdropUserInfoResponse(

    @ApiModelProperty("钱包地址", required = true)
    val address: String,

    @ApiModelProperty("Twitter是否绑定并验证", required = true)
    val isTwitterVerified: <PERSON><PERSON><PERSON>,

    @ApiModelProperty("是否交易过", required = true)
    val hasTraded: <PERSON><PERSON><PERSON>,
)
