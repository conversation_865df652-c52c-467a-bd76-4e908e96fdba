server:
  port: ${PORT:8111}
  undertow:
    threads:
      io: ${THREAD_IO:8}
spring:
  servlet:
    multipart:
      enabled: false
  data:
    mongodb:
      auto-index-creation: true
#      uri: "${DB_URI}"
      host: ${DB_HOST:localhost}
      port: ${DB_PORT:27017}
      database: ${DB_DATABASE}
#      authentication-database: ${DB_AUTHENTICATION_DATABASE}
#      username: ${DB_USERNAME}
#      password: ${DB_PASSWORD}
  devtools:
    restart:
      enabled: ${DEVTOOLS_RESTART_ENABLED:false}
  mvc:
    pathmatch:
      matching-strategy: ant_path_matcher
  redis:
    host: ${REDIS_HOST:localhost}
    port: ${REDIS_PORT:6379}
    password: ${REDIS_PASSWORD}
    username: ${REDIS_USERNAME:default}
    ssl: ${REDIS_SSL:true}
    database: ${REDIS_DATABASE:0}
    # Redis连接池配置 - 用于优化ApiCacheAspect等组件的Redis连接性能
    lettuce:
      pool:
        # 最大连接数 - 连接池中允许的最大连接数量
        max-active: ${REDIS_POOL_MAX_ACTIVE:20}
        # 最大空闲连接数 - 连接池中保持的最大空闲连接数
        max-idle: ${REDIS_POOL_MAX_IDLE:10}
        # 最小空闲连接数 - 连接池中保持的最小空闲连接数
        min-idle: ${REDIS_POOL_MIN_IDLE:5}
        # 最大等待时间 - 当连接池耗尽时，获取连接的最大等待时间
        max-wait: ${REDIS_POOL_MAX_WAIT:-1ms}
        # 连接回收检查间隔 - 空闲连接回收器运行的时间间隔
        time-between-eviction-runs: ${REDIS_POOL_EVICTION_INTERVAL:30s}
logging:
  file:
    path: log/
  config: classpath:${LOG_CONFIG_FILE:logback-spring.xml}
  level:
    org.springframework:
      data: ${LOGGING_LEVEL:INFO}
      web.filter.CommonsRequestLoggingFilter: DEBUG
      io.vault.jasper.filter.RequestResponseLoggingFilter: ${LOGGING_LEVEL:INFO}
    root: ${LOGGING_LEVEL:INFO}

swagger.enabled: ${SWAGGER_ENABLED:false}

blockchain:
  polygon:
    endpoint: ${POLYGON_ENDPOINT}
    chainId: ${POLYGON_CHAIN_ID}
    chain_id: ${POLYGON_CHAIN_ID}
    options_contract: "${POLYGON_OPTIONS_CONTRACT}"
  arbitrum:
    endpoint: ${ARBITRUM_ENDPOINT}
    chain_id: ${ARBITRUM_CHAIN_ID}
    contract:
      option_module: "${ARBITRUM_CONTRACT_OPTION_MODULE}"
      option_service: "${ARBITRUM_CONTRACT_OPTION_SERVICE}"
      diamond: "${ARBITRUM_CONTRACT_DIAMOND:0x5ffdd96bd604f915520d66c9eddd46dfc1434d71}"
      price_oracle: "${ARBITRUM_CONTRACT_PRICE_ORACLE:0x60E974258eFCCEf5E7B9D579F47F600aC0a7064C}"
      pyth: "${ARBITRUM_CONTRACT_PYTH:0xff1a0f4744e8582DF1aE09D5611b887B6a12925C}"
      vault_factory: "${ARBITRUM_VAULT_FACTORY:0xf6C9Db55D428f2F7aCC72d959AcB40a282B9eFD3}"
      issuance_module: "${ARBITRUM_CONTRACT_ISSUANCE_MODULE:0x9F408f6c9e52c091549F1550e31e8B5621795bE6}"
    arbiscan:
      base_url: https://api.arbiscan.io/api
      api_tokens: 14X89RGV9MZIR8YENBU75VEANHVYJ8KGYK, Y7YF6XN4D5FE7EWTRUN57ME35RC8H99F3Y
  sei:
    endpoint: ${SEI_ENDPOINT:https://evm-rpc-arctic-1.sei-apis.com/}
    chain_id: ${SEI_CHAIN_ID:713715}
    contract:
      option_service: "${SEI_CONTRACT_OPTION_SERVICE:0x210018090C3e532cE99992653Fd93bf952F51189}"
      entry_point: "${SEI_CONTRACT_ENTRY_POINT:0x37b73EaA5206Ad2be33Fc6a7FA6aEE27529b53EF}"
      diamond: "${SEI_CONTRACT_DIAMOND:0x11f98ae1cb23111d32fdeb22824363a79af5c9fd}"
      issuance_module: "${SEI_CONTRACT_ISSUANCE_MODULE:0xeF0Abec11456846964D59141FD6961cC5F7AD010}"
      price_oracle: "${SEI_CONTRACT_PRICE_ORACLE:0x433B49319217994E2c55BBF12AeAcA309425b29C}"
      pyth: "${SEI_CONTRACT_PYTH:0xe9d69CdD6Fe41e7B621B4A688C5D1a68cB5c8ADc}"
      vault_factory: "${SEI_VAULT_FACTORY:0x293D5807C4a1A7C465e26D10C7B5661a37C77d12}"
  base:
    endpoint: "${BASE_ENDPOINT:https://base-mainnet.g.alchemy.com/v2/sbPwNr_CtJrYiaWenoy-UIkE5H4edS9X}"
    chain_id: ${BASE_CHAIN_ID:8453}
    contract:
      vault_factory: "${BASE_VAULT_FACTORY:0x402949029F11bB31cEBde6Ca7E2D8cEccEB00a2F}"
      option_service: "${BASE_CONTRACT_OPTION_SERVICE:0xED15AcDF9e425d9e5A3923781A6431a221319121}"
      diamond: "${BASE_DIAMOND:0xA71d071578bb9d5061FF2804A030a73164158d99}"
      issuance_module: "${BASE_ISSUANCE_MODULE:0x26d11e8C94E342FEcFA7E8826e5fA03a31FB0a3C}"
      lp_vault: "${BASE_LP_VAULT:0x85618fD899dC7aDF24915800Dc1d6C5852866aA1}"
    event_hash:
      liquidity: "${BASE_EVENT_LIQUIDITY:0xabd20c9a288c74637de97fa697977b97134d9a0250c5c86d503e3585915a0ef9}"
    scan_api:
      base_url: https://api.basescan.org/api
      api_tokens: IEQG37FN8S8W1YEDRVKQCXUJMWE9XPQIMZ, 9TER9R8KT1665GD7KWS6ZWEWBZVJCNCYZU
  bitlayer:
    endpoint: "${BITLAYER_ENDPOINT:https://rpc.bitlayer.org/}"
    chain_id: ${BITLAYER_CHAIN_ID:200901}
    contract:
      vault_factory: "${BITLAYER_VAULT_FACTORY:0x7F14d635dcC41AE486B1a7c07C3d38705F0D1eFE}"
      option_service: "${BITLAYER_CONTRACT_OPTION_SERVICE:0xdaC0bE32467348ea71654d2A5aAe7793d065191f}"
      diamond: "${BITLAYER_DIAMOND:0x32CE600C20Ce6C6f68F4F81B547c4edcAB11840d}"
      issuance_module: "${BITLAYER_ISSUANCE_MODULE:0xe7D79A3a17730f340Cc594868a55B80F4c0c3138}"
    scan_api:
      base_url: "${BITLAYER_SCAN_BASE_URL:https://api.btrscan.com/scan/api}"
      api_tokens: ""
  bitlayer_test:
    endpoint: "https://testnet-rpc.bitlayer.org/"
    chain_id: 200810
    contract:
      vault_factory: "0x653069B85aFdd9BadC81b40c1f3e055650419BDf"
      option_service: "0x261268456a9ac8587dA9D07CdE0DCA15253f1D4E"
      diamond: "0x2cca8037e457b59a5edaCa9289c43ec397DD940B"
      issuance_module: "0xE53a40106eB2d255f989f122B6389D94797DbA15"
    scan_api:
      base_url: https://testnet.btrscan.com/scan/api
      api_tokens: ""
  bsc:
    endpoint: "https://bnb-mainnet.g.alchemy.com/v2/********************************"
    chain_id: 56
    contract:
      vault_factory: "0x2De647115Ba285054335b70Ff4528268DA712a63"
      option_service: "0x7FA88042118ff303fDC198d3fCa37B87C7319170"
      diamond: "0xED15AcDF9e425d9e5A3923781A6431a221319121"
      issuance_module: "0xB411D0c748Ab93Abd817ed4059ea824a618A4e37"
    scan_api:
      base_url: https://api.bscscan.com/api
      api_tokens: "**********************************"

jasper_vault_api_url: ${JASPER_VAULT_API_URL}

knife4j:
  enable: ${KNIFE4J_ENABLED:false}
  documents:
    - group: Jasper Vault
      name: API Doc
      locations: classpath:wiki/*
  setting:
    # default lang
    language: en-US
    # footer
    enableFooter: false
    enableFooterCustom: true
    footerCustomContent: Jasper Vault
    # header
    enableHomeCustom: true
    homeCustomLocation: classpath:wiki/README.md
    # models
    enableSwaggerModels: true
    swaggerModelName: Jasper Models

aws:
  ses:
    accessKeyID: ********************
    secretAccessKey: qvy4V9xIqNULTS6j2eyCHvFJTinb+4royrOjlauv

settlement_wallet: "${SETTLEMENT_WALLET}"
api_v1_host: "${API_V1_HOST:https://api.jaspervault.io}"
monitor_switch: ${MONITOR_SWITCH:false}
secret_key: Ve5RRdWp6ez7DWrJ

kol:
  report_wallet_primary_key: "b9edb720bb82b90a332881a7d597cbbf20b9a648abb07d9125cf6015dcdf8268"
  report_contract: "******************************************"
  apply:
    mail_from: <EMAIL>

option_price_host: "${OPTION_PRICE_HOST:https://quotecenterv2.jaspervault.io}"

discord:
  bot_token: ""
  guild_id: "1224530137500880947"
  client:
    id: "1281948507879510126"
    secret: "UFt_-nlX-476GggZxEtYi0UkseJ80G_Q"
    # callback_uri: "https://v2-server-uat.fly.dev/auth/discord/oauth2/callback"
    callback_uri: "${DISCORD_CALLBACK_URL:https://apiv2.jaspervault.io/auth/discord/oauth2/callback}"
    authorization_url: "https://discord.com/api/oauth2/authorize"
    token_url: "https://discord.com/api/oauth2/token"

schedule_task_on: ${SCHEDULE_TASK_ON:false}

marketplace:
  api:
    base-url: "${MARKETPLACE_API_BASE_URL:https://marketpalce-api-test-shmm4.ondigitalocean.app}"