package io.vault.jasper.model

import org.springframework.data.annotation.Id
import org.springframework.data.mongodb.core.index.Indexed
import org.springframework.data.mongodb.core.mapping.Document
import java.math.BigDecimal

@Document("kol_level")
data class KolLevel(
    @Id
    val id: String? = null,
    val order: Int,
    @Indexed(unique = true)
    val name: String,
    var conditions: String?,
    var incentiveRate: BigDecimal
) {}
