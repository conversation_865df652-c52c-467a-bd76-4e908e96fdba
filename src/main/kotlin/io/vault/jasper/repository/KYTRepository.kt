package io.vault.jasper.repository

import io.vault.jasper.model.KYT
import org.springframework.data.mongodb.repository.MongoRepository
import org.springframework.stereotype.Repository
import java.time.LocalDateTime

@Repository
interface KYTRepository : MongoRepository<KYT, String> {
    fun findByAddressIgnoreCase(address: String): KYT?
    fun findByScore(score: Int): List<KYT>
    fun findByUpdatedLessThanAndChannelIsNotNull(updated: LocalDateTime): List<KYT>
    fun findByUpdatedGreaterThan(updated: LocalDateTime): List<KYT>
}