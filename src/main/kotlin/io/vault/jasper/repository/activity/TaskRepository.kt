package io.vault.jasper.repository.activity

import io.vault.jasper.model.activity.Task
import io.vault.jasper.model.activity.TaskType
import org.springframework.data.mongodb.repository.MongoRepository
import org.springframework.stereotype.Repository

@Repository
interface TaskRepository : MongoRepository<Task, String> {
    fun findByTaskType(taskType: TaskType): List<Task>
    
    fun findByIdIn(ids: List<String>): List<Task>
}
