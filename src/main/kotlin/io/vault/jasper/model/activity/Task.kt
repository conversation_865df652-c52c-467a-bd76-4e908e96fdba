package io.vault.jasper.model.activity

import org.springframework.data.annotation.CreatedDate
import org.springframework.data.annotation.Id
import org.springframework.data.annotation.LastModifiedDate
import org.springframework.data.mongodb.core.index.Indexed
import org.springframework.data.mongodb.core.mapping.Document
import org.springframework.data.mongodb.core.mapping.Field
import java.time.LocalDateTime

/**
 * Task model representing a task that users can complete in a campaign
 */
@Document("tasks")
data class Task(
    @Id
    val id: String? = null,

    @Indexed(background = true)
    val title: String,

//    val description: String,
//
//    @Field("icon_url")
//    val iconUrl: String? = null,

    @Field("task_type")
    val taskType: TaskType,

    /**
     * Task-specific configuration parameters stored as a map
     * Different task types will use different parameters
     */
    @Field("task_config")
    val taskConfig: Map<String, Any>,

    @CreatedDate
    val created: LocalDateTime = LocalDateTime.now(),

    @LastModifiedDate
    val updated: LocalDateTime = LocalDateTime.now(),

    @Field("i18n_info")
    var i18nInfo: MutableMap<String, I18nTaskInfo>? = null
)

/**
 * Enum representing different types of tasks
 */
enum class TaskType {
    NEW_USER_REGISTRATION,
    OPTION_ORDER_COMPLETION,
    TWITTER_FOLLOW,
    TWITTER_RETWEET,
    DISCORD_BIND,
    DISCORD_SERVER_JOIN,
    DISCORD_LEVEL_UPGRADE,
    BIND_TWITTER_ACCOUNT
}

/**
 * Constants for task configuration parameters
 */
object TaskConfigParams {
    // NEW_USER_REGISTRATION
    const val REGISTRATION_AFTER_TIME = "registrationAfterTime"

    // OPTION_ORDER_COMPLETION
    const val BID_ASSET = "bidAsset"
    const val MIN_BID_AMOUNT = "minBidAmount"
    const val BID_AMOUNT = "bidAmount"
    const val CHAIN = "chain"
    const val MIN_PREMIUM_FEE_USDT = "minPremiumFeeUsdt"
    const val PRODUCT_TYPE = "productType"
    const val FIRST_ORDER_IN_CAMPAIGN = "firstOrderInCampaign"
    const val ORDER_LINK = "orderLink"

    // TWITTER_FOLLOW
    const val TWITTER_FOLLOW_LINK = "twitterFollowLink"

    // TWITTER_RETWEET
    const val TWITTER_RETWEET_LINK = "twitterRetweetLink"

    // DISCORD_BIND
    // No specific parameters needed

    // DISCORD_SERVER_JOIN
    // No specific parameters needed

    // DISCORD_LEVEL_UPGRADE
    const val MIN_DISCORD_LEVEL = "minDiscordLevel"
}

data class I18nTaskInfo(
    val title: String,
    val description: String,
    val icon: String = "",
)
