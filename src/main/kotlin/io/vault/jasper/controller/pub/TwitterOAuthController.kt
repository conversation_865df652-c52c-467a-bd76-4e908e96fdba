package io.vault.jasper.controller.pub

import EVMAddress
import com.twitter.clientlib.ApiException
import com.twitter.clientlib.TwitterCredentialsBearer
import com.twitter.clientlib.api.TwitterApi
import io.swagger.annotations.ApiOperation
import io.vault.jasper.ApiResponse
import io.vault.jasper.model.UserChannel
import io.vault.jasper.repository.KYTRepository
import io.vault.jasper.repository.RetweetUserIdRepository
import io.vault.jasper.repository.UserRepository
import io.vault.jasper.service.AirDropService
import io.vault.jasper.service.TwitterOAuthService
import io.vault.jasper.utils.AuthUtil
import org.slf4j.LoggerFactory
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.web.bind.annotation.*
import org.springframework.web.servlet.view.RedirectView
import javax.servlet.http.HttpServletRequest


@RestController
@RequestMapping("/twitter")
class TwitterOAuthController @Autowired constructor(
    private val twitterOAuthService: TwitterOAuthService,
    private val authUtil: AuthUtil,
    private val userRepository: UserRepository,
    private val kytRepository: KYTRepository
) {

    private var logger = LoggerFactory.getLogger(this::class.java)

    @GetMapping("/request_token")
    @ApiOperation("Request Twitter authorization code")
    @CrossOrigin
    fun requestToken(
        request: HttpServletRequest,
        @RequestParam("address") @EVMAddress address: String,
    ): ApiResponse<String> {

        authUtil.filterIPBlacklist(request)
        val user = authUtil.auth(request, address)

        val oauthToken = twitterOAuthService.getRequestToken()
        user.twitterOAuthToken = oauthToken
        userRepository.save(user)

        return ApiResponse.success("https://api.twitter.com/oauth/authorize?oauth_token=$oauthToken")
    }

    @GetMapping("/callback")
    @ApiOperation("Request Twitter authorization code callback")
    @CrossOrigin
    fun callback(
        @RequestParam("oauth_token") oauthToken: String,
        @RequestParam("oauth_verifier") oauthVerifier: String,
    ): RedirectView {

        val accessTokenResponse = twitterOAuthService.getAccessToken(oauthToken, oauthVerifier)
        val accessToken = accessTokenResponse["oauth_token"] ?: ""
        val accessTokenSecret = accessTokenResponse["oauth_token_secret"] ?: ""

        // Fetch user info with the obtained access token
        val userInfo = twitterOAuthService.getUserInfo(accessToken, accessTokenSecret)
        val twitterAccount = userInfo.get("id_str").asText()

        // You can now return the userInfo or process it further
        logger.info("TwitterOAuthController callback user Info: $userInfo")
        val user = userRepository.findByTwitterOAuthToken(oauthToken)
        val existingUser = userRepository.findByTwitterAccountId(twitterAccount)
        if(user != null && existingUser == null){
            user.twitterAccountId = twitterAccount
            user.twitterAccountName = userInfo.get("name").asText()
            user.twitterAccountScreenName = userInfo.get("screen_name").asText()

            userRepository.save(user)
        }

        var url = "https://x.com/jaspervault"

        if(user != null) {
            val kyt = kytRepository.findByAddressIgnoreCase(user.address)
            if(kyt != null && kyt.channel == UserChannel.JP){
                url = "https://x.com/jaspervaultjp"
            }
        }

        return RedirectView(url)
    }
}
