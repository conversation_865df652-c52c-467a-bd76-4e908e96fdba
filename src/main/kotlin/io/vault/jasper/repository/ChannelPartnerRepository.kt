package io.vault.jasper.repository

import io.vault.jasper.enums.ChainType
import io.vault.jasper.model.Chain
import io.vault.jasper.model.ChannelPartner
import org.springframework.data.mongodb.repository.MongoRepository
import org.springframework.stereotype.Repository

@Repository
interface ChannelPartnerRepository : MongoRepository<ChannelPartner, String> {

    fun findByApiKey(
        apiKey: String
    ): ChannelPartner?
}