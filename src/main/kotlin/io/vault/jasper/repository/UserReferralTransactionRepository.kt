package io.vault.jasper.repository

import io.vault.jasper.model.UserReferralTransaction
import io.vault.jasper.model.UserReferralTransactionType
import org.springframework.data.mongodb.repository.MongoRepository
import org.springframework.stereotype.Repository

@Repository
interface UserReferralTransactionRepository : MongoRepository<UserReferralTransaction, String> {

    fun findByUserIdAndType(
        userId: String,
        type: UserReferralTransactionType
    ): List<UserReferralTransaction>

    fun findByUserIdAndRefereeUserIdAndType(
        userId: String,
        refereeUserId: String,
        type: UserReferralTransactionType
    ): List<UserReferralTransaction>

    fun findByType(
        type: UserReferralTransactionType
    ): List<UserReferralTransaction>
    
    fun findByOptionOrderId(
        optionOrderId: String
    ): UserReferralTransaction?

    fun countByRefereeAddressIgnoreCase(
        refereeAddress: String
    ): Long
}
