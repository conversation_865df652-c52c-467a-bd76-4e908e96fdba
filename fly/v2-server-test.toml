# fly.toml app configuration file generated for v2-server-test on 2024-11-26T16:40:03+08:00
#
# See https://fly.io/docs/reference/configuration/ for information about how to use this file.
#

app = 'v2-server-test'
primary_region = 'sin'

[build]
  dockerfile = 'Dockerfile'

[http_service]
  internal_port = 8080
  force_https = true
  auto_stop_machines = false
  auto_start_machines = true
  min_machines_running = 0
  processes = ['app']

[[vm]]
  memory = '1gb'
  cpu_kind = 'shared'
  cpus = 1
