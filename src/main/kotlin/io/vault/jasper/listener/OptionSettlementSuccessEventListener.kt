package io.vault.jasper.listener

import io.vault.jasper.event.OptionSettlementSuccessEvent
import io.vault.jasper.repository.OptionOrderRepository
import io.vault.jasper.service.AlertService
import io.vault.jasper.service.activity.CampaignIntegrationService
import io.vault.jasper.service.blockchain.BlockchainServiceFactory
import io.vault.jasper.service.blockchain.EvmService
import org.slf4j.LoggerFactory
import org.springframework.context.event.EventListener
import org.springframework.core.annotation.Order
import org.springframework.data.repository.findByIdOrNull
import org.springframework.stereotype.Component

@Component
class OptionSettlementSuccessEventListener(
    private val blockchainServiceFactory: BlockchainServiceFactory,
    private val alertService: AlertService,
    private val optionOrderRepository: OptionOrderRepository,
    private val campaignIntegrationService: CampaignIntegrationService
) {
    private val logger = LoggerFactory.getLogger(this::class.java)

//    @EventListener
//    @Order(1)
//    fun handleOptionSettlementSuccessEvent(event: OptionSettlementSuccessEvent) {
//        logger.info("Processing option settlement success event for order: campaign task processing ${event.optionOrder.id}, ${event.optionOrder.buyer}")
//
//        val optionOrder = optionOrderRepository.findByIdOrNull(event.optionOrder.id)
//        if (optionOrder == null) {
//            logger.warn("Option order not found for ID: ${event.optionOrder.id}")
//            return
//        }
//
//        try {
//            // Process the option order for activities and rewards
//            campaignIntegrationService.processOptionOrder(optionOrder)
//        } catch (e: Exception) {
//            logger.error("Error processing option settlement success event for order: ${event.optionOrder.id}", e)
//        }
//    }

    @EventListener
    @Order(1)
    fun checkUnderlyingAssetReturn(event: OptionSettlementSuccessEvent) {
        logger.info("Processing option settlement success event for order: checkUnderlyingAssetReturn ${event.optionOrder.id}, ${event.optionOrder.buyer}")
        val optionOrder = event.optionOrder
        logger.info("""
            Checking Underlying Asset Return for Order:
            Order ID: ${optionOrder.id}
            Chain: ${optionOrder.chain}
            Buyer: ${optionOrder.buyer}
            Seller: ${optionOrder.seller}
            Settlement Hash: ${optionOrder.settlementHash}
        """.trimIndent())
        
        // 根据chain获取对应的EvmService
        val evmService = blockchainServiceFactory.getBlockchainService(optionOrder.chain) as? EvmService
            ?: run {
                logger.error("${optionOrder.chain} Service not found or not an EvmService")
                return
            }
        
        // 检查抵押物是否已返还
        val isReturned = evmService.checkUnderlyingAssetReturn(optionOrder)
        
        if (!isReturned) {
            logger.warn("Underlying asset not returned for order: ${optionOrder.id}")
            alertService.emergency("[LP Vault未收到抵押物] order=${optionOrder.id} not return underlying asset to LP Vault")
        } else {
            logger.info("Underlying asset successfully returned for order: ${optionOrder.id}")
        }
    }

    @EventListener
    @Order(2)
    fun checkSettlementFromAddress(event: OptionSettlementSuccessEvent) {
        val optionOrder = event.optionOrder
        logger.info("Processing option settlement success event for order: checkSettlementFromAddress ${event.optionOrder.id}, ${event.optionOrder.buyer}")
        
        // 根据chain获取对应的EvmService
        val evmService = blockchainServiceFactory.getBlockchainService(optionOrder.chain) as? EvmService
            ?: run {
                logger.error("${optionOrder.chain} Service not found or not an EvmService")
                return
            }
        
        // 检查结算交易的from地址
        val settlementHash = optionOrder.settlementHash ?: run {
            logger.error("Settlement hash is null for order: ${optionOrder.id}")
            return
        }
        
        try {
            val tx = evmService.evmUtil.web3j.ethGetTransactionByHash(settlementHash).send().transaction.get()
            val expectedFromAddress = "******************************************"
            
            if (!tx.from.equals(expectedFromAddress, ignoreCase = true)) {
                logger.warn("Settlement transaction from address mismatch for order: ${optionOrder.id}")
                alertService.alert("[清算方法并非由JasperVault钱包执行] Order ID: ${optionOrder.id}, Expected: $expectedFromAddress, Actual: ${tx.from}")
            }
        } catch (e: Exception) {
            logger.error("Failed to check settlement transaction for order: ${optionOrder.id}", e)
        }
    }
} 