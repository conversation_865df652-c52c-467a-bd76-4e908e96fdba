package io.vault.jasper.controller.auth

import EVMAddress
import io.swagger.annotations.Api
import io.swagger.annotations.ApiOperation
import io.vault.jasper.ApiResponse
import io.vault.jasper.annotation.ApiCache
import io.vault.jasper.dto.AuthDTO
import io.vault.jasper.enums.ChainType
import io.vault.jasper.model.TgeCampaignParticipateRecordStatus
import io.vault.jasper.repository.*
import io.vault.jasper.service.AirDropService
import io.vault.jasper.utils.AuthUtil
import org.slf4j.LoggerFactory
import org.springframework.web.bind.annotation.*
import java.time.LocalDateTime
import javax.servlet.http.HttpServletRequest
import javax.validation.Valid
import io.vault.jasper.exception.BusinessException
import io.vault.jasper.exception.ResultEnum

@Api(tags = ["TGE Campaign"])
@RestController
@RequestMapping("/campaign/tge")
class TgeCampaignController(
    private val authUtil: AuthUtil,
    private val airdropService: AirDropService,
    private val tgeRetweetUserIdRepository: TgeRetweetUserIdRepository,
    private val userCampaignInfoRepository: UserCampaignInfoRepository,
    private val tgeCampaignRecordRepository: TgeCampaignRecordRepository,
    private val tgeCampaignConfigRepository: TgeCampaignConfigRepository,
    private val tgeCampaignParticipateRecordRepository: TgeCampaignParticipateRecordRepository,
) {

    private val logger = LoggerFactory.getLogger(this::class.java)

    data class TgeCampaignInfo(
        val totalParticipationCount: Int = 0,
        val isTwitterFollowed: Boolean = false,
        val isTwitterRetweet: Boolean = false,
        val isJoinDiscord: Boolean = false,
        val discordLevel: Int = 0,
        val totalTradeCount: Int = 0,
        val retweetLink: String,
        val totalBtr : Int = 0,
        val totalDistributeBtr: Int = 0,
        val getBtrCount: Int = 0,
        val canClaimTimeStone: Boolean = false,
        val hasClaimedTimeStone: Boolean = false
    )

    @ApiOperation("Get TGE Campaign Information")
    @CrossOrigin
    @ApiCache(key = "tge:index", dynamicKey = ["address"], expire = 2)
    @GetMapping
    fun getTgeCampaignInfo(
        request: HttpServletRequest,
        @RequestParam("address") @EVMAddress address: String
    ): ApiResponse<TgeCampaignInfo> {

        authUtil.filterIPBlacklist(request)
        val user = authUtil.auth(request, address)
        //val user = userRepository.findByAddressIgnoreCase(address) ?: throw BusinessException(ResultEnum.USER_NOT_FOUND)
        val userCampaignInfo = airdropService.getUserCampaignInfo(user)

        // 获取活动配置
        val config = tgeCampaignConfigRepository.findAll()[0]

        // 检查 Twitter 任务是否完成
        if(userCampaignInfo.retweetTgeTime == 0L &&
            config.isInCampaignPeriod(LocalDateTime.now()) &&
            user.twitterAccountId != null) {
            tgeRetweetUserIdRepository.findByRetweetUserId(user.twitterAccountId!!)?.let {
                userCampaignInfo.retweetTgeTime = System.currentTimeMillis()
                userCampaignInfoRepository.save(userCampaignInfo)
            }
        }

        // 检查Discord任务
        val discordLevel = airdropService.getDiscordLevel(user)
        val userRecordsCount = tgeCampaignRecordRepository.countByAddressIgnoreCase(user.address)

        // 检查BTR任务
        if(userCampaignInfo.tgeBitlayerTradeCount == 0 &&
            config.isInCampaignPeriod(LocalDateTime.now()) &&
            config.canDistributeBtr()){
            val count = tgeCampaignRecordRepository.countByAddressIgnoreCaseAndChain(
                user.address,
                ChainType.BITLAYER
            )

            userCampaignInfo.tgeBitlayerTradeCount = count.toInt()
            userCampaignInfoRepository.save(userCampaignInfo)
        }

        var userBtr = 0
        var canClaimTimeStone = false
        var hasClaimedTimeStone = false

        val campaignParticipateRecords = tgeCampaignParticipateRecordRepository.findByAddressIgnoreCase(
            user.address
        )

        if(campaignParticipateRecords.isNotEmpty()){
            val campaignParticipateRecord = campaignParticipateRecords[0]
            if(campaignParticipateRecord.status == TgeCampaignParticipateRecordStatus.CREATED){
                canClaimTimeStone = true
            }

            if(campaignParticipateRecord.status == TgeCampaignParticipateRecordStatus.SUCCESS ||
                campaignParticipateRecord.status == TgeCampaignParticipateRecordStatus.CLAIMED){
                hasClaimedTimeStone = true
            }

            if(campaignParticipateRecord.bitlayerTradeOptionOrderId != null){
                userBtr = config.btrAmountPerOrder
            }
        }

        val info = TgeCampaignInfo(
            totalParticipationCount = config.totalParticipation,
            isTwitterFollowed = (user.twitterAccountId != null),
            isTwitterRetweet = (userCampaignInfo.retweetTgeTime > 0L),
            isJoinDiscord = user.discordInfo?.inGuild ?: false,
            discordLevel = discordLevel,
            totalTradeCount = userRecordsCount.toInt(),
            retweetLink = config.retweetLink,
            totalBtr = config.totalBtr,
            totalDistributeBtr = config.totalDistributeBtr,
            getBtrCount = userBtr,
            canClaimTimeStone = canClaimTimeStone,
            hasClaimedTimeStone = hasClaimedTimeStone
        )

        return ApiResponse.success(info)
    }

    @PostMapping("/claim_time_stone")
    @ApiOperation("Claim Time Stone")
    @CrossOrigin
    fun claimTimeStone(
        request: HttpServletRequest,
        @Valid @RequestBody dto: AuthDTO
    ): ApiResponse<Boolean> {

        authUtil.filterIPBlacklist(request)
        val user = authUtil.auth(request, dto.address)

        val unclaimedRecords = tgeCampaignParticipateRecordRepository.findByAddressIgnoreCaseAndStatusIn(
            user.address,
            listOf(TgeCampaignParticipateRecordStatus.CREATED)
        )
        for(record in unclaimedRecords) {

            record.status = TgeCampaignParticipateRecordStatus.CLAIMED
            tgeCampaignParticipateRecordRepository.save(record)
        }

        return ApiResponse.success(true)
    }
} 