package io.vault.jasper.service

import org.slf4j.LoggerFactory
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.data.mongodb.core.MongoTemplate
import org.springframework.data.redis.core.RedisTemplate
import org.springframework.stereotype.Service

@Service
class RedisService @Autowired constructor(
    private val mongoTemplate: MongoTemplate,
    private val redisTemplate: RedisTemplate<String, Any>
){

    private val logger = LoggerFactory.getLogger(this::class.java)
    //private val redisTemplate: RedisTemplate<String, Any> = RedisTemplate()

    fun get(key: String): Any? {
        return redisTemplate.opsForValue().get(key)
    }

    fun set(key: String, value: Any) {
        redisTemplate.opsForValue().set(key, value)
    }
}