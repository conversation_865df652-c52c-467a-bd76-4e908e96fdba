package io.vault.jasper.controller.auth

import io.swagger.v3.oas.annotations.Operation
import io.swagger.v3.oas.annotations.tags.Tag
import io.vault.jasper.config.StonePieceProcessParameter
import io.vault.jasper.controller.ApiResponse
import io.vault.jasper.service.StonePieceProcessParameterService
import org.slf4j.LoggerFactory
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.web.bind.annotation.*

/**
 * 石头碎片处理参数配置管理 Controller
 */
@RestController
@RequestMapping("/stone-piece-config")
@Tag(name = "Stone Piece Process Configuration", description = "石头碎片处理配置管理")
class StonePieceProcessParameterController @Autowired constructor(
    private val stonePieceProcessParameterService: StonePieceProcessParameterService
) {

    private val logger = LoggerFactory.getLogger(this::class.java)

    @Operation(summary = "获取默认配置")
    @CrossOrigin
    @GetMapping("/default")
    fun getDefaultConfig(): ApiResponse<StonePieceProcessParameter> {
        return try {
            val config = stonePieceProcessParameterService.getDefaultConfig()
            ApiResponse.success(config)
        } catch (e: Exception) {
            logger.error("Error getting default config", e)
            ApiResponse.error("Failed to get default config: ${e.message}")
        }
    }

    @Operation(summary = "根据配置名称获取配置")
    @CrossOrigin
    @GetMapping("/{configName}")
    fun getConfigByName(@PathVariable configName: String): ApiResponse<StonePieceProcessParameter?> {
        return try {
            val config = stonePieceProcessParameterService.getConfigByName(configName)
            if (config != null) {
                ApiResponse.success(config)
            } else {
                ApiResponse.error("Config not found: $configName")
            }
        } catch (e: Exception) {
            logger.error("Error getting config: $configName", e)
            ApiResponse.error("Failed to get config: ${e.message}")
        }
    }

    @Operation(summary = "获取所有启用的配置")
    @CrossOrigin
    @GetMapping("/enabled")
    fun getEnabledConfigs(): ApiResponse<List<StonePieceProcessParameter>> {
        return try {
            val configs = stonePieceProcessParameterService.getEnabledConfigs()
            ApiResponse.success(configs)
        } catch (e: Exception) {
            logger.error("Error getting enabled configs", e)
            ApiResponse.error("Failed to get enabled configs: ${e.message}")
        }
    }

    @Operation(summary = "获取所有活跃的配置")
    @CrossOrigin
    @GetMapping("/active")
    fun getAllActiveConfigs(): ApiResponse<List<StonePieceProcessParameter>> {
        return try {
            val configs = stonePieceProcessParameterService.getAllActiveConfigs()
            ApiResponse.success(configs)
        } catch (e: Exception) {
            logger.error("Error getting active configs", e)
            ApiResponse.error("Failed to get active configs: ${e.message}")
        }
    }

    @Operation(summary = "创建或更新配置")
    @CrossOrigin
    @PostMapping
    fun saveConfig(@RequestBody config: StonePieceProcessParameter): ApiResponse<StonePieceProcessParameter> {
        return try {
            val savedConfig = stonePieceProcessParameterService.saveConfig(config)
            ApiResponse.success(savedConfig)
        } catch (e: Exception) {
            logger.error("Error saving config: ${config.configName}", e)
            ApiResponse.error("Failed to save config: ${e.message}")
        }
    }

    @Operation(summary = "更新配置")
    @CrossOrigin
    @PutMapping("/{configName}")
    fun updateConfig(
        @PathVariable configName: String,
        @RequestParam(required = false) enabled: Boolean?,
        @RequestParam(required = false) nftIds: List<String>?,
        @RequestParam(required = false) cronExpression: String?,
        @RequestParam(required = false) batchSize: Int?,
        @RequestParam(required = false) enableDetailedLogging: Boolean?,
        @RequestParam(required = false) maxRetryCount: Int?,
        @RequestParam(required = false) description: String?
    ): ApiResponse<StonePieceProcessParameter?> {
        return try {
            val updatedConfig = stonePieceProcessParameterService.updateConfig(
                configName = configName,
                enabled = enabled,
                nftIds = nftIds,
                cronExpression = cronExpression,
                batchSize = batchSize,
                enableDetailedLogging = enableDetailedLogging,
                maxRetryCount = maxRetryCount,
                description = description
            )
            if (updatedConfig != null) {
                ApiResponse.success(updatedConfig)
            } else {
                ApiResponse.error("Config not found: $configName")
            }
        } catch (e: Exception) {
            logger.error("Error updating config: $configName", e)
            ApiResponse.error("Failed to update config: ${e.message}")
        }
    }

    @Operation(summary = "启用/禁用配置")
    @CrossOrigin
    @PatchMapping("/{configName}/toggle")
    fun toggleConfig(
        @PathVariable configName: String,
        @RequestParam enabled: Boolean
    ): ApiResponse<StonePieceProcessParameter?> {
        return try {
            val updatedConfig = stonePieceProcessParameterService.toggleConfig(configName, enabled)
            if (updatedConfig != null) {
                ApiResponse.success(updatedConfig)
            } else {
                ApiResponse.error("Config not found: $configName")
            }
        } catch (e: Exception) {
            logger.error("Error toggling config: $configName", e)
            ApiResponse.error("Failed to toggle config: ${e.message}")
        }
    }

    @Operation(summary = "删除配置（软删除）")
    @CrossOrigin
    @DeleteMapping("/{configName}")
    fun deleteConfig(@PathVariable configName: String): ApiResponse<String> {
        return try {
            val success = stonePieceProcessParameterService.deleteConfig(configName)
            if (success) {
                ApiResponse.success("Config deleted successfully: $configName")
            } else {
                ApiResponse.error("Config not found: $configName")
            }
        } catch (e: Exception) {
            logger.error("Error deleting config: $configName", e)
            ApiResponse.error("Failed to delete config: ${e.message}")
        }
    }

    @Operation(summary = "初始化默认配置")
    @CrossOrigin
    @PostMapping("/initialize")
    fun initializeConfig(): ApiResponse<String> {
        return try {
            stonePieceProcessParameterService.initializeConfig()
            ApiResponse.success("Configuration initialized successfully")
        } catch (e: Exception) {
            logger.error("Error initializing config", e)
            ApiResponse.error("Failed to initialize config: ${e.message}")
        }
    }

    @Operation(summary = "检查是否有启用的配置")
    @CrossOrigin
    @GetMapping("/has-enabled")
    fun hasEnabledConfig(): ApiResponse<Boolean> {
        return try {
            val hasEnabled = stonePieceProcessParameterService.hasEnabledConfig()
            ApiResponse.success(hasEnabled)
        } catch (e: Exception) {
            logger.error("Error checking enabled configs", e)
            ApiResponse.error("Failed to check enabled configs: ${e.message}")
        }
    }
}
