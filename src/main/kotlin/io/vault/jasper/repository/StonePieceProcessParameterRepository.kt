package io.vault.jasper.repository

import io.vault.jasper.config.StonePieceProcessParameter
import org.springframework.data.mongodb.repository.MongoRepository
import org.springframework.stereotype.Repository

/**
 * 石头碎片处理参数配置 Repository
 */
@Repository
interface StonePieceProcessParameterRepository : MongoRepository<StonePieceProcessParameter, String> {
    
    /**
     * 根据配置名称查找配置
     */
    fun findByConfigName(configName: String): StonePieceProcessParameter?
    
    /**
     * 查找活跃的配置
     */
    fun findByActiveTrue(): List<StonePieceProcessParameter>
    
    /**
     * 根据配置名称和活跃状态查找配置
     */
    fun findByConfigNameAndActiveTrue(configName: String): StonePieceProcessParameter?
    
    /**
     * 查找启用的配置
     */
    fun findByEnabledTrue(): List<StonePieceProcessParameter>
    
    /**
     * 查找启用且活跃的配置
     */
    fun findByEnabledTrueAndActiveTrue(): List<StonePieceProcessParameter>
}
