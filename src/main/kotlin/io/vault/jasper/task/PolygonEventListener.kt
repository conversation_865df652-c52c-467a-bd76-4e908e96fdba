package io.vault.jasper.task

import io.vault.jasper.blockchain.BlockchainUtilFactory
import io.vault.jasper.enums.ChainType
import io.vault.jasper.repository.BlockchainRepository
import io.vault.jasper.repository.OrderRepository
import org.slf4j.LoggerFactory
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.beans.factory.annotation.Value
import org.springframework.stereotype.Service
import org.web3j.protocol.core.DefaultBlockParameterNumber
import org.web3j.protocol.core.methods.response.EthBlock
import org.web3j.utils.Numeric


@Service
class PolygonEventListener @Autowired constructor(
    private val blockchainUtilFactory: BlockchainUtilFactory,
    private val blockchainRepository: BlockchainRepository,
    private val orderRepository: OrderRepository
) {

    private val logger = LoggerFactory.getLogger(this::class.java)

    @Value("\${blockchain.polygon.options_contract}")
    private lateinit var optionsContract: String

    @Value("\${schedule_task_on}")
    lateinit var scheduleOn: String

    // @Scheduled(fixedDelay = 10 * 1000, initialDelay = 5 * 1000)
    fun run() {
        if(scheduleOn == "false"){
            return
        }
        val id = Numeric.toBigIntNoPrefix("660296b3ed9045408b3e6f7d")
        logger.info("Order ID: $id")
        try {
            listen()
        } catch (e: Exception) {
            logger.error("Error in EventListener", e)
        }
    }

    private fun listen() {
        val blockchainUtil = blockchainUtilFactory.getBlockchainUtil(ChainType.POLYGON)
        val blockchain = blockchainRepository.findFirstByChain(ChainType.POLYGON) ?: return
        val web3 = blockchainUtil.web3j
        val blockNumber = web3.ethBlockNumber().send()
        val newestBlockHeight = blockNumber.blockNumber.toLong()
        val lastBlockNumber = blockchain.blockNumber
        var endBlock: Long? = null
        for (i in 1..100) {
            val bn = lastBlockNumber + i
            if (bn + blockchain.confirmCount > newestBlockHeight) break

            val block = web3.ethGetBlockByNumber(
                DefaultBlockParameterNumber(bn.toBigInteger()),
                true
            ).send()
            block.block.transactions.filter {
                val tx = it.get() as EthBlock.TransactionObject
                tx.to?.compareTo(optionsContract, true) == 0
            }.forEach {
                val tx = it.get() as EthBlock.TransactionObject
                logger.info("Found Diamond Contract Transaction: ${tx.hash} in Block: $bn")
                logger.info("\n")
                val receiptRsp = web3.ethGetTransactionReceipt(tx.hash).send()
                val receipt = receiptRsp.result
                if (receipt.status == "0x1") {
                    logger.info("Receipt success!!")
                    receipt.logs.forEach logsLoop@{ log ->
                        logger.info("Log: $log")
                        val address = log.address
                        val topics = log.topics
                        if (address.compareTo("******************************************", true) == 0) {
                            val function = topics.firstOrNull() ?: return@logsLoop
                            if (function.compareTo(
                                    "0xbcbcccdca6e157df0fa51d3e734040adefe956b6d4d8cd233b4bd6641f64b859",
                                    true
                                ) == 0
                            ) {
                                // AddCallOrder
                                val data = log.data
                                logger.info("Data: $data")
                                val dataNoPrefix = Numeric.cleanHexPrefix(data)
                                val chunkSize = 64
                                val chunks = dataNoPrefix.chunked(chunkSize)
                                logger.info(chunks.toString())
                                val onChainOrderId = Numeric.toBigIntNoPrefix(chunks[0])
                                val strikeAmount = Numeric.toBigIntNoPrefix(chunks[9])
                                val expirationDateTimestamp = Numeric.toBigIntNoPrefix(chunks[10])
                                val holderEoaWallet = Numeric.toHexStringWithPrefix(Numeric.toBigIntNoPrefix(chunks[12]))
                                val writerEoaWallet = Numeric.toHexStringWithPrefix(Numeric.toBigIntNoPrefix(chunks[13]))
                                logger.info("On Chain Order ID: $onChainOrderId\tStrike Amount: $strikeAmount\t" +
                                        "Expiration Date: $expirationDateTimestamp\t" +
                                        "Holder EOA Wallet: $holderEoaWallet\tWriter EOA Wallet: $writerEoaWallet")
                                // val order = orderRepository.findFirstByOnChainOrderId(onChainOrderId.toString()) ?: run {
                                //     logger.info("Order = $onChainOrderId not found")
                                //     return@forEach
                                // }
                                // order.status = OrderStatus.PENDING_SETTLEMENT
                                // orderRepository.save(order)
                            }
                        }
                    }
                }
            }
            endBlock = bn
        }
        if (endBlock != null) {
            blockchain.blockNumber = endBlock
            blockchainRepository.save(blockchain)
        }
    }
}