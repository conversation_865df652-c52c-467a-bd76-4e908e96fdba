package io.vault.jasper.controller.admin

import io.swagger.annotations.Api
import io.swagger.annotations.ApiOperation
import io.vault.jasper.exception.BusinessException
import io.vault.jasper.exception.ResultEnum
import io.vault.jasper.model.activity.Task
import io.vault.jasper.repository.activity.TaskRepository
import io.vault.jasper.ApiResponse
import org.springframework.data.repository.findByIdOrNull
import org.springframework.web.bind.annotation.*

@RestController
@RequestMapping("/api/admin/tasks")
@Api(tags = ["Task Admin API"])
class TaskAdminController(
    private val taskRepository: TaskRepository
) {
    
    @PostMapping
    @ApiOperation("Create a new task")
    fun createTask(@RequestBody task: Task): ApiResponse<Task> {
        return ApiResponse.success(taskRepository.save(task))
    }
    
    @GetMapping("/{id}")
    @ApiOperation("Get a task by ID")
    fun getTask(@PathVariable id: String): ApiResponse<Task> {
        val task = taskRepository.findByIdOrNull(id)
            ?: throw BusinessException(ResultEnum.TASK_NOT_FOUND)
        
        return ApiResponse.success(task)
    }
    
    @GetMapping
    @ApiOperation("Get all tasks")
    fun getAllTasks(): ApiResponse<List<Task>> {
        return ApiResponse.success(taskRepository.findAll())
    }
    
    @PutMapping("/{id}")
    @ApiOperation("Update a task")
    fun updateTask(@PathVariable id: String, @RequestBody task: Task): ApiResponse<Task> {
        if (taskRepository.findByIdOrNull(id) == null) {
            throw BusinessException(ResultEnum.TASK_NOT_FOUND)
        }
        
        return ApiResponse.success(taskRepository.save(task.copy(id = id)))
    }
    
    @DeleteMapping("/{id}")
    @ApiOperation("Delete a task")
    fun deleteTask(@PathVariable id: String): ApiResponse<Boolean> {
        if (taskRepository.findByIdOrNull(id) == null) {
            throw BusinessException(ResultEnum.TASK_NOT_FOUND)
        }
        
        taskRepository.deleteById(id)
        return ApiResponse.success(true)
    }
}
