package io.vault.jasper.response

import io.swagger.annotations.ApiModel
import io.swagger.annotations.ApiModelProperty
import io.vault.jasper.enums.Symbol

@ApiModel
data class MinimalistOptionsFeeRateResponse(
    @ApiModelProperty("标的资产", required = true)
    val underlyingAsset: Symbol,
    @ApiModelProperty("行权资产", required = true)
    val strikeAsset: Symbol,
    @ApiModelProperty("权利金费率", required = true)
    val rate: String,
    @ApiModelProperty("资产抵押数量", required = true)
    val availableUnderlyingAssetAmount: List<String>,
    @ApiModelProperty("到期时间", required = true)
    val expiryHour: Long
)
