package io.vault.jasper.model

import org.springframework.data.annotation.CreatedDate
import org.springframework.data.annotation.Id
import org.springframework.data.annotation.LastModifiedDate
import org.springframework.data.mongodb.core.mapping.Document
import java.time.LocalDateTime

@Document("user_network_push_data_task_parameter")
data class UserNetworkPushDataTaskParameter(
    @Id
    val id: String? = null,
    var userNetworkLastPushTime: LocalDateTime? = null,
    var userCommissionLastPushTime: LocalDateTime? = null,
    var userNetworkTransactionLastPushTime: LocalDateTime = LocalDateTime.of(2024, 10, 1, 0, 0),
    var userNetworkRebateRecordLastPushTime: LocalDateTime? = null,
    var userNetworkPointRecordLastPushTime: LocalDateTime = LocalDateTime.of(2024, 10, 1, 0, 0),
    @CreatedDate
    val created: LocalDateTime = LocalDateTime.now(),
    @LastModifiedDate
    val updated: LocalDateTime = LocalDateTime.now(),
    var jasperQuantHost: String = "http://tss7mv.natappfree.cc",
    var prepareDataSwitch: Boolean = false,
    var pushDataSwitch: Boolean = false,
)