package io.vault.jasper.repository

import io.vault.jasper.model.SyncNFTOptionPool
import io.vault.jasper.model.UserZeroPremiumBenefit
import org.springframework.data.mongodb.repository.MongoRepository
import org.springframework.stereotype.Repository

@Repository
interface SyncNFTOptionPoolRepository : MongoRepository<SyncNFTOptionPool, String> {
    fun findByUserIdAndBenefitId(
        userId: String,
        benefitId: String
    ): SyncNFTOptionPool?
}