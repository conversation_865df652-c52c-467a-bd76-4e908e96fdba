import javax.validation.Constraint
import javax.validation.Payload
import kotlin.reflect.KClass

@Target(AnnotationTarget.VALUE_PARAMETER, AnnotationTarget.FIELD)
@Retention(AnnotationRetention.RUNTIME)
@Constraint(validatedBy = [EVMAddressValidator::class])
annotation class EVMAddress(
    val message: String = "Invalid EVM address",
    val groups: Array<KClass<*>> = [],
    val payload: Array<KClass<out Payload>> = []
)