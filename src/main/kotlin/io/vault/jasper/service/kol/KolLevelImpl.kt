package io.vault.jasper.service.kol

import com.fasterxml.jackson.databind.ObjectMapper
import io.vault.jasper.model.KolLevel
import io.vault.jasper.response.KolLevelDesc
import java.math.BigDecimal

class KolLevelImpl(private val level: KolLevel) : IKolLevel {

    private val minTotalPremium: BigDecimal

    private val minActiveAddresses: String

    private val minUserCount: Int?

    private val maxUserCount: Int?

    init {
        val conditionsNode = ObjectMapper().readTree(level.conditions ?: "")
        minTotalPremium = (conditionsNode["total_premium"]?.asText() ?: "0").toBigDecimal()
        minActiveAddresses = conditionsNode["active_addresses"]?.asText() ?: ""
        minUserCount = conditionsNode["min_users"]?.asInt()
        maxUserCount = conditionsNode["max_users"]?.asInt()
    }

    override fun getName(): String {
        return level.name
    }

    override fun getOrder(): Int {
        return level.order
    }

    override fun getIncentiveRate(): BigDecimal {
        return level.incentiveRate
    }

    override fun matchConditions(vararg pairs: Pair<String, Any>): Boolean {
        val map = pairs.toMap()
        val totalPremium = (map["totalPremium"] as? BigDecimal) ?: BigDecimal.ZERO
        val activeAddresses = (map["activeAddresses"] as? Long) ?: 0L
        if (totalPremium >= minTotalPremium && activeAddresses >= minActiveAddresses.toLong()) {
            return true
        }
        return false
    }

    override fun getDesc(): List<KolLevelDesc> {
        return listOf(
            KolLevelDesc(
                title = "Upgrade Conditions",
                content = "The invited address pays the total premium (USDT) ",
                indicator = minTotalPremium.toPlainString()
            ),
            KolLevelDesc(
                title = "Maintenance Conditions",
                content = "The average daily active trading addresses over 30 days (invited)",
                indicator = minActiveAddresses
            )
        )
    }

    override fun matchInvitedUsers(userCount: Int): Boolean = when {
        minUserCount != null && maxUserCount != null -> userCount in minUserCount..maxUserCount
        minUserCount != null -> userCount >= minUserCount
        maxUserCount != null -> userCount <= maxUserCount
        else -> false
    }
}