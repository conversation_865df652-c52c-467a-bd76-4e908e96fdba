package io.vault.jasper.model

import io.vault.jasper.enums.ChainType
import io.vault.jasper.service.blockchain.EvmService
import org.springframework.data.annotation.Id
import org.springframework.data.mongodb.core.index.Indexed
import org.springframework.data.mongodb.core.mapping.Document
import org.springframework.data.mongodb.core.mapping.Field
import java.time.LocalDateTime

@Document("missing_order")
data class MissingOrder(
    @Id
    val id: String? = null,

    var chain: ChainType = ChainType.ARBITRUM,

    @Indexed(unique = true, background = true)
    @Field("tx_hash")
    val txHash: String,

    var receiptStatus: String? = null,

    var struct: EvmService.OptionOrderStruct? = null,

    var premium: EvmService.OptionPremiumStruct? = null,

    var blockHeight: Long? = null,

    var txTime: LocalDateTime? = null,

    var executedTime: LocalDateTime? = null,

    var ignore: Boolean = true,

    var settleTxHash: String? = null,
)