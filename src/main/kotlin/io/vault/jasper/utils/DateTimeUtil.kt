package io.vault.jasper.utils

import org.slf4j.LoggerFactory
import java.text.SimpleDateFormat
import java.time.*
import java.time.format.DateTimeFormatter
import java.util.*

object DateTimeUtil {

    private var logger = LoggerFactory.getLogger(this::class.java)

    fun convertIsoDateTimeStringToLocalDateTime(isoDateTimeString: String): LocalDateTime =
        ZonedDateTime.parse(isoDateTimeString).withZoneSameInstant(ZoneId.systemDefault()).toLocalDateTime()

    fun convertLocalDateTimeToTimestamp(localDateTime: LocalDateTime): Long =
        localDateTime.atZone(ZoneId.systemDefault()).toInstant().toEpochMilli()

    fun convertMilliTimestampToLocalDateTime(milliTimestamp: Long): LocalDateTime =
        LocalDateTime.ofInstant(Instant.ofEpochMilli(milliTimestamp), ZoneId.systemDefault())

    fun convertTimestampToLocalDateTime(timestamp: Long): LocalDateTime =
        LocalDateTime.ofInstant(Instant.ofEpochSecond(timestamp), ZoneId.systemDefault())

    fun convertToUtcZoneDateTime(localDateTime: LocalDateTime = LocalDateTime.now()): ZonedDateTime =
        localDateTime.atZone(ZoneId.systemDefault()).withZoneSameInstant(ZoneOffset.UTC)

    fun getMidNight(localDateTime: LocalDateTime = LocalDateTime.now()): LocalDateTime =
        localDateTime.withHour(0).withMinute(0).withSecond(0).withNano(0)

    fun formatISOString(localDateTime: LocalDateTime): String = localDateTime.atZone(ZoneOffset.UTC).format(
        DateTimeFormatter.ISO_DATE_TIME
    )

    fun getDailySummaryTime(
        date: LocalDateTime
    ): Triple<String, LocalDateTime, LocalDateTime>{
        val start = date.withHour(0).withMinute(0).withSecond(0)
        val end = date.withHour(23).withMinute(59).withSecond(59)

        //Format local date time to string
        val formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd")
        val dateString = date.format(formatter)

        //logger.info("DateString $dateString Start: $start, End: $end")

        return Triple(dateString, start, end)
    }

    fun getCalculatedDayString(
        day: Int = -1,
        dateString : String? = null
    ): String{

        //每日结算昨天的哈希
        val formatter = SimpleDateFormat("yyyy-MM-dd")
        var date: Date? = null

        if(dateString != null){
            date = formatter.parse(dateString)
        }

        val yesterday = getDateAfterDate(day, date)
        return formatter.format(yesterday)
    }

    fun getDateAfterDate(days: Int, date: Date? = null): Date {

        val cal = Calendar.getInstance()

        if(date != null){
            cal.time = date
        }

        cal.add(Calendar.DAY_OF_MONTH, days)
        return cal.time
    }

    fun convertStringToLocalDateTime(dateString: String, pattern: String = "yyyy-MM-dd HH:mm:ss"): LocalDateTime {
        val formatter = DateTimeFormatter.ofPattern(pattern)

        return LocalDateTime.parse(dateString, formatter)
    }
}