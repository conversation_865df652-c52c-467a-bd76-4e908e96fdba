package io.vault.jasper.repository

import io.vault.jasper.model.OptionStatus
import io.vault.jasper.model.UserPointRecord
import org.springframework.data.mongodb.repository.MongoRepository
import org.springframework.stereotype.Repository
import java.math.BigDecimal
import java.time.LocalDateTime

@Repository
interface UserPointRecordRepository : MongoRepository<UserPointRecord, String> {

    fun findByStatus(status: OptionStatus): List<UserPointRecord>
    fun findTopByOrderByCreatedDesc(): UserPointRecord?
    fun findByOptionOrderId(optionOrderId: String): UserPointRecord?
    fun findByPremiumInUsdtIsNull(): List<UserPointRecord>
}