package io.vault.jasper.model.activity

import io.swagger.annotations.ApiModelProperty
import io.vault.jasper.enums.ChainType
import io.vault.jasper.model.Chain
import org.springframework.data.annotation.CreatedDate
import org.springframework.data.annotation.Id
import org.springframework.data.annotation.LastModifiedDate
import org.springframework.data.mongodb.core.index.Indexed
import org.springframework.data.mongodb.core.mapping.Document
import org.springframework.data.mongodb.core.mapping.Field
import java.math.BigDecimal
import java.time.LocalDateTime

/**
 * Campaign model representing a campaign with multiple tasks and rewards
 */
@Document("campaigns")
data class Campaign(
    @Id
    val id: String? = null,

    @Indexed(background = true, unique = true)
    val name: String,

    val chain: ChainType = ChainType.ARBITRUM,

    @Field("start_time")
    val startTime: LocalDateTime,

    @Field("end_time")
    val endTime: LocalDateTime,

    @Field("task_ids")
    val taskIds: List<String> = listOf(),

    @Field("task_group_ids")
    val taskGroupIds: List<String> = listOf(),

    @Field("total_reward_amount")
    val totalRewardAmount: String,

    @Field("smart_contract_address")
    val smartContractAddress: String? = null,

    @Field("active")
    var active: Boolean = true,

    @Field("participant_count")
    var participantCount: Int = 0,

    @Field("completed_count")
    var completedCount: Int = 0,

    @CreatedDate
    val created: LocalDateTime = LocalDateTime.now(),

    @LastModifiedDate
    val updated: LocalDateTime = LocalDateTime.now(),

    @Field("i18n_info")
    var i18nInfo: MutableMap<String, I18nCampaignInfo>? = null
)

data class I18nCampaignInfo(
    val name: String,
    val description: String,
    @Field("banner_desktop")
    val bannerDesktop: String = "",
    @Field("banner_mobile")
    val bannerMobile: String = "",
    @Field("rule_link")
    val ruleLink: String = "",
)
