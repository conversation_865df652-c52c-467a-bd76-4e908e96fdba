package io.vault.jasper.listener

import io.vault.jasper.enums.ChainType
import io.vault.jasper.enums.Symbol
import io.vault.jasper.event.OptionExecutedEvent
import io.vault.jasper.event.OptionSettlementEvent
import io.vault.jasper.model.OptionOrder
import io.vault.jasper.repository.OptionOrderRepository
import io.vault.jasper.service.*
import io.vault.jasper.service.activity.CampaignIntegrationService
import org.slf4j.LoggerFactory
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.context.event.EventListener
import org.springframework.core.annotation.Order
import org.springframework.data.repository.findByIdOrNull
import org.springframework.stereotype.Component
import java.math.BigDecimal

@Component
class AirdropSettlementEventListener @Autowired constructor(
    private val optionOrderRepository: OptionOrderRepository,
    private val stoneService: StoneService,
    private val campaignIntegrationService: CampaignIntegrationService,
    private val alertService: AlertService,
    private val systemService: SystemService,
    private val currencyService: CurrencyService
) {

    private val logger = LoggerFactory.getLogger(this::class.java)

    @EventListener
    @Order(1)
    fun handleBitStonedEvent(event: OptionSettlementEvent) {
        logger.info("AirdropSettlementEventListener Option execute completed for order: ${event.optionOrderId}")

        val optionOrder = optionOrderRepository.findByIdOrNull(event.optionOrderId) ?: return

        // 处理宝石碎片
        handleStoneOptionOrder(optionOrder)
    }

    private fun handleStoneOptionOrder(optionOrder: OptionOrder) {

        logger.info("AirdropSettlementEventListener Stone trade rebate for option order: ${optionOrder.id}")
        try {

            // Base 和 BSC 暂时不开放送宝石碎片的功能
            if(optionOrder.chain == ChainType.BASE || optionOrder.chain == ChainType.BSC){
                return
            }

            stoneService.createStoneRebateRecord(
                optionOrder
            )
        } catch (e: Exception) {
            logger.error("AirdropSettlementEventListener Stone trade failed, optionOrderId: ${optionOrder.id}", e)
        }
    }

    /**
     * Handle option executed events
     * This is a backup in case the settlement success event is not fired
     */
    @EventListener
    @Order(2)
    fun handleOptionExecutedEvent(event: OptionSettlementEvent) {
        logger.info("Processing option settlement event for order, check for campaign: ${event.optionOrderId}")

        try {
            // Get the option order
            val optionOrder = optionOrderRepository.findByIdOrNull(event.optionOrderId)

            if (optionOrder == null) {
                logger.warn("Option order not found for ID: ${event.optionOrderId}")
                return
            }

            // Process the option order for campaigns and rewards
            campaignIntegrationService.processOptionOrder(optionOrder)
        } catch (e: Exception) {
            logger.error("Error processing option executed event for order: ${event.optionOrderId}", e)
        }
    }

    @EventListener
    @Order(3)
    fun checkBuyerProfitRatio(event: OptionSettlementEvent) {
        logger.info("Checking buyer profit ratio for order: ${event.optionOrderId}")
        
        val optionOrder = optionOrderRepository.findByIdOrNull(event.optionOrderId) ?: return
        
        try {
            val premiumFeePayInUsd = optionOrder.premiumFeePayInUsd ?: run {
                if (optionOrder.premiumFeePayInUsdt == null) return
                val usdtDecimal = currencyService.getCurrencyDecimal(optionOrder.chain, Symbol.USDT)
                optionOrder.premiumFeePayInUsdt!!.movePointLeft(usdtDecimal)
            }
            val buyerProfitInUsd = optionOrder.buyerProfitInUsd ?: return
            
            if (premiumFeePayInUsd <= BigDecimal.ZERO) {
                logger.warn("Premium fee is zero or negative for order: ${optionOrder.id}")
                return
            }
            
            val profitRatio = (buyerProfitInUsd / premiumFeePayInUsd - BigDecimal.ONE) * BigDecimal(100)
            val maxProfitRatio = systemService.getParameter().maxBuyerProfitRatio
            
            if (profitRatio >= maxProfitRatio) {
                logger.warn("Buyer profit ratio exceeds ${maxProfitRatio}% for order: ${optionOrder.id}, ratio: ${profitRatio}%")
                alertService.alert("[买方利润率过高] Buyer profit ratio ${profitRatio}%, orderID=${optionOrder.id}")
            }
        } catch (e: Exception) {
            logger.error("Failed to check buyer profit ratio for order: ${optionOrder.id}", e)
        }
    }
}