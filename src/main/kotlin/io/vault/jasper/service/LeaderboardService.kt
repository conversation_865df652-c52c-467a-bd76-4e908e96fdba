package io.vault.jasper.service

import io.vault.jasper.exception.BusinessException
import io.vault.jasper.exception.ResultEnum
import io.vault.jasper.model.UserPointDailySummary
import io.vault.jasper.model.UserReferralDailySummary
import io.vault.jasper.repository.UserRepository
import io.vault.jasper.utils.DateTimeUtil
import org.slf4j.LoggerFactory
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.data.domain.Sort
import org.springframework.data.mongodb.core.MongoTemplate
import org.springframework.data.mongodb.core.aggregation.Aggregation
import org.springframework.data.mongodb.core.aggregation.AggregationResults
import org.springframework.data.mongodb.core.aggregation.ConvertOperators
import org.springframework.data.mongodb.core.query.Criteria
import org.springframework.stereotype.Service
import java.math.BigDecimal
import java.time.LocalDate
import java.time.LocalDateTime
import java.time.format.DateTimeFormatter


@Service
class LeaderboardService @Autowired constructor(
    private val mongoTemplate: MongoTemplate,
    private val userRepository: UserRepository
){

    private val logger = LoggerFactory.getLogger(this::class.java)

    fun aggregateAndSortByPremiumPoint(
        days: Int?,
        registerDays: Int?,
        limit: Long
    ): List<PremiumPointSummary> {

        var sevenDaysAgoStr = "0000-00-00"

        if(days != null){
            val sevenDaysAgo = LocalDate.now().minusDays(days.toLong())
            sevenDaysAgoStr = sevenDaysAgo.format(DateTimeFormatter.ofPattern("yyyy-MM-dd"))
        }

        val criteria = Criteria.where(UserPointDailySummary::dateString.name).gte(sevenDaysAgoStr)

        if(registerDays != null){
            val registerDate = LocalDateTime.now().minusDays(registerDays.toLong()).withHour(0).withMinute(0).withSecond(0)
            criteria.and(UserPointDailySummary::registerTime.name).gte(registerDate)
        }

        val aggregation: Aggregation = Aggregation.newAggregation(
            Aggregation.match(criteria),
            Aggregation.group(UserPointDailySummary::address.name)
                .sum(UserPointDailySummary::premiumPoint.name).`as`("totalPremiumPoint")
                .sum(UserPointDailySummary::premiumFees.name).`as`("totalPremiumAndFee"),
            Aggregation.sort(Sort.by(Sort.Direction.DESC, "totalPremiumPoint")),
            Aggregation.limit(limit)
        )

        val results: AggregationResults<PremiumPointSummary> = mongoTemplate.aggregate(
            aggregation, UserPointDailySummary::class.java, PremiumPointSummary::class.java
        )

        return results.getMappedResults()
    }

    class PremiumPointSummary {
        var _id: String? = null
        var totalPremiumPoint: BigDecimal? = null
        var totalPremiumAndFee: BigDecimal? = null // Getters and Setters
    }

    fun aggregateAndSortByRefereeVolume(
        days: Int?,
        registerDays: Int?,
        limit: Long
    ): List<RefereeVolumeSummary> {

        var sevenDaysAgoStr = "0000-00-00"

        if(days != null){
            val sevenDaysAgo = LocalDate.now().minusDays(days.toLong())
            sevenDaysAgoStr = sevenDaysAgo.format(DateTimeFormatter.ofPattern("yyyy-MM-dd"))
        }

        val criteria = Criteria.where(UserPointDailySummary::dateString.name).gte(sevenDaysAgoStr)
        if(registerDays != null){
            val registerDate = LocalDateTime.now().minusDays(registerDays.toLong()).withHour(0).withMinute(0).withSecond(0)
            criteria.and(UserPointDailySummary::registerTime.name).gte(registerDate)
        }

        val aggregation: Aggregation = Aggregation.newAggregation(
            Aggregation.match(criteria),
            Aggregation.group(UserReferralDailySummary::address.name)
                .sum(UserReferralDailySummary::refereeVolume.name).`as`("totalRefereeVolume")
                .sum(UserReferralDailySummary::referees.name).`as`("totalReferee"),
            Aggregation.sort(Sort.by(Sort.Direction.DESC, "totalRefereeVolume")),
            Aggregation.limit(limit)
        )

        val results: AggregationResults<RefereeVolumeSummary> = mongoTemplate.aggregate(
            aggregation, UserReferralDailySummary::class.java, RefereeVolumeSummary::class.java
        )

        return results.getMappedResults()
    }

    class RefereeVolumeSummary {
        var _id: String? = null
        var totalReferee: Int? = null
        var totalRefereeVolume: BigDecimal? = null
    }

    fun aggregateAndSortByLoyaltyPoint(
        days: Int?,
        registerDays: Int?,
        limit: Long
    ): List<LoyaltyPointSummary> {

        var sevenDaysAgoStr = "0000-00-00"

        if(days != null){
            val sevenDaysAgo = LocalDate.now().minusDays(days.toLong())
            sevenDaysAgoStr = sevenDaysAgo.format(DateTimeFormatter.ofPattern("yyyy-MM-dd"))
        }

        val criteria = Criteria.where(UserPointDailySummary::dateString.name).gte(sevenDaysAgoStr)
        if(registerDays != null){
            val registerDate = LocalDateTime.now().minusDays(registerDays.toLong()).withHour(0).withMinute(0).withSecond(0)
            criteria.and(UserPointDailySummary::registerTime.name).gte(registerDate)
        }

        val aggregation: Aggregation = Aggregation.newAggregation(
            Aggregation.match(criteria),
            Aggregation.group(UserPointDailySummary::address.name)
                .sum(UserPointDailySummary::loyaltyPoint.name).`as`("totalLoyaltyPoint")
                .sum(UserPointDailySummary::counter.name).`as`("steak"),
            Aggregation.sort(Sort.by(Sort.Direction.DESC, "totalLoyaltyPoint")),
            Aggregation.limit(limit)
        )

        val results: AggregationResults<LoyaltyPointSummary> = mongoTemplate.aggregate(
            aggregation, UserPointDailySummary::class.java, LoyaltyPointSummary::class.java
        )

        return results.getMappedResults()
    }

    class LoyaltyPointSummary {
        var _id: String? = null
        var totalLoyaltyPoint: BigDecimal? = null
        var steak: Int? = null
    }

    fun getUserRefereeTotalVolume(
        address: String
    ): BigDecimal {

        val aggregation: Aggregation = Aggregation.newAggregation(
            Aggregation.match(Criteria.where(UserReferralDailySummary::address.name).`is`(address)),
            Aggregation.group().sum(
                ConvertOperators.valueOf(
                    UserReferralDailySummary::refereeVolume.name
                ).convertToDecimal()).`as`("total")
        )
        val groupResults = mongoTemplate.aggregate(aggregation, UserReferralDailySummary::class.java, Map::class.java)
        val result = groupResults.mappedResults.firstOrNull()
        val total = (result?.get("total")?:"0").toString()

        return BigDecimal(total)
    }

    class Coin98Summary {
        var _id: String? = null
        var coin98Streak: Int? = null
        var premiumFees: BigDecimal? = null
    }

    fun aggregateAndSortByCoin98Streak(
        limit: Long
    ): List<LoyaltyPointSummary> {

        val criteria = Criteria.where(UserPointDailySummary::coin98Streak.name).gte(1)

        val aggregation: Aggregation = Aggregation.newAggregation(
            Aggregation.match(criteria),
            Aggregation.group(UserPointDailySummary::address.name)
                .sum(UserPointDailySummary::premiumFees.name).`as`("premiumFees")
                .sum(UserPointDailySummary::coin98Streak.name).`as`("coin98Streak"),
            Aggregation.sort(Sort.by(Sort.Direction.DESC, "coin98Streak")),
            Aggregation.limit(limit)
        )

        val results: AggregationResults<LoyaltyPointSummary> = mongoTemplate.aggregate(
            aggregation, UserPointDailySummary::class.java, LoyaltyPointSummary::class.java
        )

        return results.getMappedResults()
    }

    fun safePalAggregateAndSortByLoyaltyPoint(
        inviteCode: String,
        startDateString: String,
        endDateString: String,
        limit: Long
    ): List<SafepalLoyaltyPointSummary> {

        logger.info("SafePalAggregateAndSortByLoyaltyPoint startDateString: $startDateString")
        logger.info("SafePalAggregateAndSortByLoyaltyPoint endDateString: $endDateString")
        logger.info("SafePalAggregateAndSortByLoyaltyPoint Invite Code: $inviteCode")

        val criteria = Criteria.where(UserPointDailySummary::dateString.name).gte(startDateString).lte(endDateString)

        val mainUser = userRepository.findByInviteCode(inviteCode)
        if(mainUser == null){
            throw BusinessException(ResultEnum.USER_NOT_FOUND)
        }

        val referees = userRepository.findByInvitedUserId(mainUser.id!!)
        val refereeAddresses = referees.map { it.address }
        criteria.and(UserPointDailySummary::address.name).`in`(refereeAddresses)

        val aggregation: Aggregation = Aggregation.newAggregation(
            Aggregation.match(criteria),
            Aggregation.group(UserPointDailySummary::address.name)
                .sum(UserPointDailySummary::loyaltyPoint.name).`as`("totalLoyaltyPoint")
                .sum(UserPointDailySummary::counter.name).`as`("steak")
                .sum(UserPointDailySummary::premiumFees.name).`as`("totalPremiumFee"),
            Aggregation.sort(
                Sort.by(Sort.Direction.DESC, "steak", "totalLoyaltyPoint", "totalPremiumFee")
            ),
            Aggregation.limit(limit)
        )

        val results: AggregationResults<SafepalLoyaltyPointSummary> = mongoTemplate.aggregate(
            aggregation, UserPointDailySummary::class.java, SafepalLoyaltyPointSummary::class.java
        )

        return results.getMappedResults()
    }

    class SafepalLoyaltyPointSummary {
        var _id: String? = null
        var totalLoyaltyPoint: BigDecimal? = null
        var steak: Int? = null
        var totalPremiumFee: BigDecimal? = null
    }
}