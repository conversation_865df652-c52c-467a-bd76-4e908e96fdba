package io.vault.jasper.interceptor

import org.slf4j.LoggerFactory
import org.springframework.http.HttpMethod
import org.springframework.web.servlet.HandlerInterceptor
import javax.servlet.http.HttpServletRequest
import javax.servlet.http.HttpServletResponse

class AuthInterceptor : HandlerInterceptor {

    private val logger = LoggerFactory.getLogger(this::class.java)

    override fun preHandle(request: HttpServletRequest, response: HttpServletResponse, handler: Any): Boolean {
        if (HttpMethod.OPTIONS.toString() == request.method) {
            response.status = HttpServletResponse.SC_OK
            return true
        }

        val authorization = request.getHeader("Authorization")
        logger.info("Authorization: $authorization")

        return true

        // return super.preHandle(request, response, handler)
    }
}
