server:
  port: 8111
  undertow:
    threads:
      io: 8
spring:
  data:
    mongodb:
      auto-index-creation: true
      host: 127.0.0.1
      port: 27017
      database: jasper
      authentication-database: jasper
  devtools:
    restart:
      enabled: true
  mvc:
    pathmatch:
      matching-strategy: ant_path_matcher
  redis:
    host: localhost
    port: 6379
logging:
  file:
    path: log/
  config: classpath:logback-spring.xml
  level:
    org.springframework.data: DEBUG

swagger.enabled: true

blockchain:
  polygon:
    endpoint: https://purple-chaotic-panorama.matic.quiknode.pro/8cb1f7e12302418d34af9de2230e67974930f58f/
    chainId: 80001
    options_contract: "0xFF7708b167C7173fb25Bdf2C5df5f9924f6cC7eE"

jasper_vault_api_url: "https://apitest.jaspervault.io/api"

knife4j:
  enable: true
  documents:
    - group: <PERSON> Vault
      name: API Doc
      locations: classpath:wiki/*
  setting:
    # default lang
    language: en-US
    # footer
    enableFooter: false
    enableFooterCustom: true
    footerCustomContent: <PERSON> Vault
    # header
    enableHomeCustom: true
    homeCustomLocation: classpath:wiki/README.md
    # models
    enableSwaggerModels: true
    swaggerModelName: Jasper Models