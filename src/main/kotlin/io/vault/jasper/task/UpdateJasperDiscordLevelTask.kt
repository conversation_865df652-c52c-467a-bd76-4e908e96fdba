package io.vault.jasper.task

import com.fasterxml.jackson.databind.ObjectMapper
import com.twitter.clientlib.ApiException
import com.twitter.clientlib.TwitterCredentialsBearer
import com.twitter.clientlib.api.TwitterApi
import io.vault.jasper.model.DiscordLevel
import io.vault.jasper.model.RetweetUserId
import io.vault.jasper.repository.*
import io.vault.jasper.service.TwitterOAuthService
import org.slf4j.LoggerFactory
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.beans.factory.annotation.Value
import org.springframework.context.annotation.Profile
import org.springframework.scheduling.annotation.Scheduled
import org.springframework.stereotype.Service
import org.springframework.web.client.HttpClientErrorException
import org.springframework.web.client.RestTemplate

@Service
@Profile("prod", "test")
class UpdateJasperDiscordLevelTask @Autowired constructor(
    private val discordLevelRepository: DiscordLevelRepository
) {

    private val logger = LoggerFactory.getLogger(this::class.java)

    private val restTemplate = RestTemplate()

    private val objectMapper = ObjectMapper()

    @Value("\${schedule_task_on}")
    lateinit var scheduleOn: String

    // 每3分钟执行一次
    @Scheduled(fixedDelay = 3 * 60 * 1000, initialDelay = 30 * 1000)
    fun run() {
        if(scheduleOn == "false"){
            return
        }
        try {
            exec()
        } catch (e: Exception) {
            logger.error(e.message, e)
        }
    }

    /**
     * 更新Discord等级信息
     */
    private fun exec() {

        logger.info("Update Jasper Discord Level Task Begin.")

        var page = 0
        var count = fetchDiscordLevelInfo(page)
        while(count > 0){
            page++
            count = fetchDiscordLevelInfo(page)

            Thread.sleep(1000)
        }

        // logger.info("Update Jasper Discord Level Task End.")
    }

    private fun fetchDiscordLevelInfo(page: Int): Int{

        val url = "https://mee6.xyz/api/plugins/levels/leaderboard/1224530137500880947?page=$page"

        //logger.info("Requesting discord level url $url")
        val response = try {
            restTemplate.getForObject(url, String::class.java)
        } catch (e: HttpClientErrorException) {

            if (e.statusCode.value() == 404) {
                return 0
            }
            throw Exception("Failed to get discord level info")
        } catch (e: Exception) {
            logger.error(e.message, e)
            throw Exception("Failed to get discord level info")
        }

        //logger.info("Response: $response")

        val responseNode = objectMapper.readTree(response)
        val players = responseNode["players"].asIterable().toList()

        players.forEachIndexed { index, player ->

            val discordId = player["id"].asText()
            val guildId = player["guild_id"].asText()
            val username = player["username"].asText()
            val discriminator = player["discriminator"].asText()
            val avatar = player["avatar"].asText()
            val messageCount = player["message_count"].asInt()
            val monetizeXPBoost = player["monetize_xp_boost"].asInt()
            val xp = player["xp"].asInt()
            val isMonetizeSubscriber = player["is_monetize_subscriber"].asBoolean()
            val detailedXP = player["detailed_xp"].asIterable().toList().map { it.asInt() }
            val level = player["level"].asInt()

            val discordLevel = discordLevelRepository.findByDiscordId(discordId)

            if (discordLevel == null) {
                discordLevelRepository.save(
                    DiscordLevel(
                        avatar = avatar,
                        discriminator = discriminator,
                        guildId = guildId,
                        discordId = discordId,
                        messageCount = messageCount,
                        monetizeXPBoost = monetizeXPBoost,
                        username = username,
                        xp = xp,
                        isMonetizeSubscriber = isMonetizeSubscriber,
                        detailedXP = detailedXP,
                        level = level
                    )
                )
            } else {
                discordLevel.avatar = avatar
                discordLevel.discriminator = discriminator
                discordLevel.guildId = guildId
                discordLevel.discordId = discordId
                discordLevel.messageCount = messageCount
                discordLevel.monetizeXPBoost = monetizeXPBoost
                discordLevel.username = username
                discordLevel.xp = xp
                discordLevel.isMonetizeSubscriber = isMonetizeSubscriber
                discordLevel.detailedXP = detailedXP
                discordLevel.level = level

                discordLevelRepository.save(discordLevel)
            }
        }

        return players.size
    }
}