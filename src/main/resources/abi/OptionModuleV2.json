[{"inputs": [], "stateMutability": "nonpayable", "type": "constructor"}, {"anonymous": false, "inputs": [{"indexed": false, "internalType": "address", "name": "previousAdmin", "type": "address"}, {"indexed": false, "internalType": "address", "name": "newAdmin", "type": "address"}], "name": "Admin<PERSON><PERSON>ed", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "beacon", "type": "address"}], "name": "BeaconUpgraded", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": false, "internalType": "uint8", "name": "version", "type": "uint8"}], "name": "Initialized", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": false, "internalType": "enum IOptionFacet.OrderType", "name": "_orderType", "type": "uint8"}, {"indexed": false, "internalType": "uint64", "name": "_orderID", "type": "uint64"}, {"indexed": false, "internalType": "address", "name": "_writer", "type": "address"}, {"indexed": false, "internalType": "address", "name": "_holder", "type": "address"}, {"indexed": false, "internalType": "address", "name": "_premiumAsset", "type": "address"}, {"indexed": false, "internalType": "uint256", "name": "_amount", "type": "uint256"}], "name": "OptionPremiun", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": false, "internalType": "address", "name": "_pool", "type": "address"}], "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": false, "internalType": "address", "name": "_oracle<PERSON>igner", "type": "address"}], "name": "SetOracleWhiteList", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": false, "internalType": "address", "name": "_priceOracleModule", "type": "address"}], "name": "SetPriceOracle", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "implementation", "type": "address"}], "name": "Upgraded", "type": "event"}, {"inputs": [{"components": [{"internalType": "address", "name": "holder", "type": "address"}, {"internalType": "address", "name": "writer", "type": "address"}, {"internalType": "address", "name": "recipient", "type": "address"}, {"internalType": "uint256", "name": "quantity", "type": "uint256"}, {"internalType": "uint256", "name": "settingsIndex", "type": "uint256"}, {"internalType": "uint256", "name": "productTypeIndex", "type": "uint256"}, {"internalType": "uint256", "name": "oracleIndex", "type": "uint256"}, {"internalType": "address", "name": "nftFreeOption", "type": "address"}, {"components": [{"internalType": "uint256", "name": "id", "type": "uint256"}, {"internalType": "uint256", "name": "chainId", "type": "uint256"}, {"internalType": "uint64", "name": "productType", "type": "uint64"}, {"internalType": "address", "name": "optionAsset", "type": "address"}, {"internalType": "uint256", "name": "strikePrice", "type": "uint256"}, {"internalType": "address", "name": "strikeAsset", "type": "address"}, {"internalType": "uint256", "name": "strikeAmount", "type": "uint256"}, {"internalType": "address", "name": "lockAsset", "type": "address"}, {"internalType": "uint256", "name": "lockAmount", "type": "uint256"}, {"internalType": "uint256", "name": "expireDate", "type": "uint256"}, {"internalType": "uint256", "name": "lockDate", "type": "uint256"}, {"internalType": "uint8", "name": "optionType", "type": "uint8"}, {"internalType": "address", "name": "premiumAsset", "type": "address"}, {"internalType": "uint256", "name": "premiumFee", "type": "uint256"}, {"internalType": "uint256", "name": "timestamp", "type": "uint256"}, {"internalType": "bytes[]", "name": "oracleSign", "type": "bytes[]"}], "internalType": "struct IOptionModuleV2.PremiumOracleSign", "name": "premiumSign", "type": "tuple"}], "internalType": "struct IOptionModuleV2.ManagedOrder", "name": "_info", "type": "tuple"}], "name": "SubmitManagedOrder", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "diamond", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "", "type": "address"}], "name": "feeDiscountWhitlist", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "_vault", "type": "address"}], "name": "getManagedOptionsSettings", "outputs": [{"components": [{"internalType": "bool", "name": "isOpen", "type": "bool"}, {"internalType": "enum IOptionFacet.OrderType", "name": "orderType", "type": "uint8"}, {"internalType": "address", "name": "writer", "type": "address"}, {"internalType": "address", "name": "lockAsset", "type": "address"}, {"internalType": "address", "name": "underlyingAsset", "type": "address"}, {"internalType": "enum IOptionFacet.UnderlyingAssetType", "name": "lockAssetType", "type": "uint8"}, {"internalType": "uint256", "name": "underlyingNftID", "type": "uint256"}, {"internalType": "enum IOptionFacet.LiquidateMode", "name": "liquidateMode", "type": "uint8"}, {"internalType": "address", "name": "strikeAsset", "type": "address"}, {"internalType": "uint256", "name": "maximum", "type": "uint256"}, {"internalType": "enum IOptionFacetV2.PremiumOracleType", "name": "premiumOracleType", "type": "uint8"}, {"internalType": "address[]", "name": "premiumAssets", "type": "address[]"}, {"internalType": "uint64[]", "name": "productTypes", "type": "uint64[]"}, {"internalType": "uint256[]", "name": "premiumFloorAMMs", "type": "uint256[]"}, {"internalType": "uint256[]", "name": "premiumRates", "type": "uint256[]"}, {"internalType": "uint256", "name": "maxUnderlyingAssetAmount", "type": "uint256"}, {"internalType": "uint256", "name": "minUnderlyingAssetAmount", "type": "uint256"}], "internalType": "struct IOptionFacetV2.ManagedOptionsSettings[]", "name": "", "type": "tuple[]"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"components": [{"internalType": "address", "name": "holder", "type": "address"}, {"internalType": "address", "name": "writer", "type": "address"}, {"internalType": "address", "name": "recipient", "type": "address"}, {"internalType": "uint256", "name": "quantity", "type": "uint256"}, {"internalType": "uint256", "name": "settingsIndex", "type": "uint256"}, {"internalType": "uint256", "name": "productTypeIndex", "type": "uint256"}, {"internalType": "uint256", "name": "oracleIndex", "type": "uint256"}, {"internalType": "address", "name": "nftFreeOption", "type": "address"}, {"components": [{"internalType": "uint256", "name": "id", "type": "uint256"}, {"internalType": "uint256", "name": "chainId", "type": "uint256"}, {"internalType": "uint64", "name": "productType", "type": "uint64"}, {"internalType": "address", "name": "optionAsset", "type": "address"}, {"internalType": "uint256", "name": "strikePrice", "type": "uint256"}, {"internalType": "address", "name": "strikeAsset", "type": "address"}, {"internalType": "uint256", "name": "strikeAmount", "type": "uint256"}, {"internalType": "address", "name": "lockAsset", "type": "address"}, {"internalType": "uint256", "name": "lockAmount", "type": "uint256"}, {"internalType": "uint256", "name": "expireDate", "type": "uint256"}, {"internalType": "uint256", "name": "lockDate", "type": "uint256"}, {"internalType": "uint8", "name": "optionType", "type": "uint8"}, {"internalType": "address", "name": "premiumAsset", "type": "address"}, {"internalType": "uint256", "name": "premiumFee", "type": "uint256"}, {"internalType": "uint256", "name": "timestamp", "type": "uint256"}, {"internalType": "bytes[]", "name": "oracleSign", "type": "bytes[]"}], "internalType": "struct IOptionModuleV2.PremiumOracleSign", "name": "premiumSign", "type": "tuple"}], "internalType": "struct IOptionModuleV2.ManagedOrder", "name": "_info", "type": "tuple"}], "name": "handleManagedOrder", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "_diamond", "type": "address"}, {"internalType": "address", "name": "_optionService", "type": "address"}, {"internalType": "address", "name": "_priceOracleModule", "type": "address"}], "name": "initialize", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address[]", "name": "array", "type": "address[]"}, {"internalType": "address", "name": "element", "type": "address"}], "name": "isInArray", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "pure", "type": "function"}, {"inputs": [], "name": "optionService", "outputs": [{"internalType": "contract IOptionService", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "", "type": "address"}], "name": "oracleWhiteList", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "proxiableUUID", "outputs": [{"internalType": "bytes32", "name": "", "type": "bytes32"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "_pool", "type": "address"}, {"internalType": "bool", "name": "_status", "type": "bool"}], "name": "setFeeD<PERSON>untW<PERSON><PERSON>", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"components": [{"internalType": "bool", "name": "isOpen", "type": "bool"}, {"internalType": "enum IOptionFacet.OrderType", "name": "orderType", "type": "uint8"}, {"internalType": "address", "name": "writer", "type": "address"}, {"internalType": "address", "name": "lockAsset", "type": "address"}, {"internalType": "address", "name": "underlyingAsset", "type": "address"}, {"internalType": "enum IOptionFacet.UnderlyingAssetType", "name": "lockAssetType", "type": "uint8"}, {"internalType": "uint256", "name": "underlyingNftID", "type": "uint256"}, {"internalType": "enum IOptionFacet.LiquidateMode", "name": "liquidateMode", "type": "uint8"}, {"internalType": "address", "name": "strikeAsset", "type": "address"}, {"internalType": "uint256", "name": "maximum", "type": "uint256"}, {"internalType": "enum IOptionFacetV2.PremiumOracleType", "name": "premiumOracleType", "type": "uint8"}, {"internalType": "address[]", "name": "premiumAssets", "type": "address[]"}, {"internalType": "uint64[]", "name": "productTypes", "type": "uint64[]"}, {"internalType": "uint256[]", "name": "premiumFloorAMMs", "type": "uint256[]"}, {"internalType": "uint256[]", "name": "premiumRates", "type": "uint256[]"}, {"internalType": "uint256", "name": "maxUnderlyingAssetAmount", "type": "uint256"}, {"internalType": "uint256", "name": "minUnderlyingAssetAmount", "type": "uint256"}], "internalType": "struct IOptionFacetV2.ManagedOptionsSettings[]", "name": "_set", "type": "tuple[]"}, {"internalType": "address", "name": "_vault", "type": "address"}, {"internalType": "uint256[]", "name": "_deleteIndex", "type": "uint256[]"}], "name": "setManagedOptionsSettings", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "contract IOptionService", "name": "_optionService", "type": "address"}], "name": "setOptionService", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "_oracle<PERSON>igner", "type": "address"}, {"internalType": "bool", "name": "_status", "type": "bool"}], "name": "setOracleWhiteList", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "contract IPriceOracle", "name": "_priceOracleModule", "type": "address"}], "name": "setPriceO<PERSON>le", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "bytes", "name": "", "type": "bytes"}], "name": "signData", "outputs": [{"internalType": "bool", "name": "lock", "type": "bool"}, {"internalType": "uint256", "name": "total", "type": "uint256"}, {"internalType": "uint256", "name": "orderCount", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "newImplementation", "type": "address"}], "name": "upgradeTo", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "newImplementation", "type": "address"}, {"internalType": "bytes", "name": "data", "type": "bytes"}], "name": "upgradeToAndCall", "outputs": [], "stateMutability": "payable", "type": "function"}]