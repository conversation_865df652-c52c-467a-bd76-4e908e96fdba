package io.vault.jasper.utils

import org.springframework.beans.BeansException
import org.springframework.context.ApplicationContext

import org.springframework.context.ApplicationContextAware
import org.springframework.stereotype.Component

@Component
class ProfileUtil : ApplicationContextAware {

    @Throws(BeansException::class)
    override fun setApplicationContext(applicationContext: ApplicationContext) {
        context = applicationContext
    }

    companion object {
        private var context: ApplicationContext? = null

        // 获取当前环境参数  exp: dev,prod,test
        val activeProfile: String
            get() {
                val profiles: List<String> = context?.environment?.activeProfiles?.toList() ?: listOf()
                return if (profiles.isNotEmpty()) {
                    profiles[0]
                } else ""
            }
    }
}