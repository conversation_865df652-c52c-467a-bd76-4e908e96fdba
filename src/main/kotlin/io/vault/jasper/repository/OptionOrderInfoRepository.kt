package io.vault.jasper.repository

import io.vault.jasper.model.KYT
import io.vault.jasper.model.OptionOrderInfo
import org.springframework.data.mongodb.repository.MongoRepository
import org.springframework.stereotype.Repository
import java.time.LocalDateTime

@Repository
interface OptionOrderInfoRepository : MongoRepository<OptionOrderInfo, String> {
    fun findByTxHash(txHash: String): OptionOrderInfo?
}