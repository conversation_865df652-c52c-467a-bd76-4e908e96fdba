package io.vault.jasper.task

import io.vault.jasper.model.KolRebateRecord
import io.vault.jasper.model.KolRebateRecordDailySummary
import io.vault.jasper.model.KolStatus
import io.vault.jasper.repository.KolRebateRecordDailySummaryRepository
import io.vault.jasper.repository.KolRepository
import org.slf4j.LoggerFactory
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.beans.factory.annotation.Value
import org.springframework.data.domain.PageRequest
import org.springframework.data.mongodb.core.MongoTemplate
import org.springframework.data.mongodb.core.query.Criteria
import org.springframework.data.mongodb.core.query.Query
import org.springframework.scheduling.annotation.Scheduled
import org.springframework.stereotype.Service
import java.math.BigDecimal
import java.time.LocalDate
import java.time.LocalDateTime
import java.time.ZoneId

@Service
class KolDailySummaryProcessor @Autowired constructor(
    private val kolRepository: KolRepository,
    private val kolRebateRecordDailySummaryRepository: KolRebateRecordDailySummaryRepository,
    private val mongoTemplate: MongoTemplate
) {

    private val logger = LoggerFactory.getLogger(this::class.java)

    @Value("\${schedule_task_on}")
    lateinit var scheduleOn: String

    //@Scheduled(fixedDelay = 5 * 60 * 1000, initialDelay = 15 * 1000)
    fun run() {
        if(scheduleOn == "false"){
            return
        }
        try {
            exec()
        } catch (e: Exception) {
            logger.error(e.message, e)
        }
    }

    //@Scheduled(cron = "0 0 0 * * *", zone = "Asia/Singapore")
    fun schedule() {
        if(scheduleOn == "false"){
            return
        }
        try {
            execSchedule()
        } catch (e: Exception) {
            logger.error(e.message, e)
        }
    }

    //@Scheduled(fixedDelay = 5 * 1000, initialDelay = 10 * 1000)
    fun clean() {
        if (scheduleOn == "false") return

        val pageable = PageRequest.of(0, 1000)
        try {
            val totalSummary = kolRebateRecordDailySummaryRepository.findByActiveUsersAndTotalPremiumsAndTotalRebate(
                0,
                BigDecimal.ZERO,
                BigDecimal.ZERO,
                pageable
            )
            logger.info("Total summary to delete: ${totalSummary.totalElements}")
            totalSummary.content.forEach {
                kolRebateRecordDailySummaryRepository.delete(it)
            }
        } catch (e: Exception) {
            logger.error(e.message, e)
        }
    }

    /**
     * 最后一次更新昨天的统计
     */
    private fun execSchedule() {
        val today = LocalDate.now()
        val todayStart = today.atStartOfDay(ZoneId.systemDefault())
        val yesterdayEndDt = LocalDateTime.of(todayStart.year, todayStart.month, todayStart.dayOfMonth, 0, 0, 0)
        val yesterdayStart = todayStart.minusDays(1)
        val yesterdayStartDt = LocalDateTime.of(yesterdayStart.year, yesterdayStart.month, yesterdayStart.dayOfMonth, 0, 0, 0)
        kolRepository.findByLevelNotAndStatus("0", KolStatus.ACTIVE).forEach { kol ->
            val yesterdaySummary = calculate(kol.id!!, yesterdayStartDt, yesterdayEndDt)
            val yesterdayDate = yesterdayStart.toLocalDate()
            val yesterdayDailySummary = kolRebateRecordDailySummaryRepository.findFirstByKolIdAndRecordDate(
                kol.id,
                yesterdayDate
            ) ?: KolRebateRecordDailySummary(
                kolId = kol.id,
                recordDate = yesterdayDate,
                totalPremiums = BigDecimal.ZERO,
                totalRebate = BigDecimal.ZERO,
                activeUsers = 0
            )
            if (
                yesterdaySummary.totalPremiums.compareTo(BigDecimal.ZERO) == 1 ||
                yesterdaySummary.totalRebate.compareTo(BigDecimal.ZERO) == 1 ||
                yesterdaySummary.activeAddresses > 0
            ) { // 数据不为0时才更新记录
                yesterdayDailySummary.totalPremiums = yesterdaySummary.totalPremiums
                yesterdayDailySummary.totalRebate = yesterdaySummary.totalRebate
                yesterdayDailySummary.activeUsers = yesterdaySummary.activeAddresses
                kolRebateRecordDailySummaryRepository.save(yesterdayDailySummary)
            }
        }
    }

    private fun exec() {
        val today = LocalDate.now()
        val todayStart = today.atStartOfDay(ZoneId.systemDefault())
        val todayStartDt = LocalDateTime.of(todayStart.year, todayStart.month, todayStart.dayOfMonth, 0, 0, 0)
        kolRepository.findByLevelNotAndStatus("0", KolStatus.ACTIVE).forEach { kol ->
            val todayDailySummary = kolRebateRecordDailySummaryRepository.findFirstByKolIdAndRecordDate(kol.id!!, today)
                ?: KolRebateRecordDailySummary(
                    kolId = kol.id,
                    recordDate = today,
                    totalPremiums = BigDecimal.ZERO,
                    totalRebate = BigDecimal.ZERO,
                    activeUsers = 0
                )

            // Today
            val todayEnd = todayStart.plusDays(1)
            val todayEndDt = LocalDateTime.of(todayEnd.year, todayEnd.month, todayEnd.dayOfMonth, 0, 0, 0)
            val todaySummary = calculate(kol.id, todayStartDt, todayEndDt)
            if (
                todaySummary.totalPremiums.compareTo(BigDecimal.ZERO) == 1 ||
                todaySummary.totalRebate.compareTo(BigDecimal.ZERO) == 1 ||
                todaySummary.activeAddresses > 0
            ) {
                todayDailySummary.totalPremiums = todaySummary.totalPremiums
                todayDailySummary.totalRebate = todaySummary.totalRebate
                todayDailySummary.activeUsers = todaySummary.activeAddresses
                kolRebateRecordDailySummaryRepository.save(todayDailySummary)
            }
        }
    }

    data class RecordSummary(val totalRebate: BigDecimal, val totalPremiums: BigDecimal, val activeAddresses: Int)

    private fun calculate(kolId: String, startTime: LocalDateTime, endTime: LocalDateTime): RecordSummary {
        val query = Query()
        query.addCriteria(Criteria.where(KolRebateRecord::kolId.name).`is`(kolId))
            .addCriteria(Criteria.where(KolRebateRecord::created.name).gte(startTime).lt(endTime))
        val rebateRecords = mongoTemplate.find(query, KolRebateRecord::class.java)

        var totalRebate = BigDecimal.ZERO
        var totalPremiums = BigDecimal.ZERO
        val activeAddresses = mutableSetOf<String>()
        rebateRecords.forEach {
            totalRebate += it.incentiveAmount
            totalPremiums += it.premiumFee
            activeAddresses.add(it.buyerAddress)
        }

        return RecordSummary(totalRebate, totalPremiums, activeAddresses.size)
    }
}