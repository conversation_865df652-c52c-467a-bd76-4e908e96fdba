package io.vault.jasper.response

import io.swagger.annotations.ApiModel
import io.swagger.annotations.ApiModelProperty
import io.vault.jasper.enums.ChainType
import io.vault.jasper.enums.OptionDirection
import io.vault.jasper.enums.Symbol
import io.vault.jasper.model.CreatorRole
import io.vault.jasper.model.KytStatus
import io.vault.jasper.model.PremiumAsset
import io.vault.jasper.model.SettlementType

@ApiModel
data class GetOrdersResponse(
    val id: String,
    @ApiModelProperty("挂单创建者角色/挂单类型(买单/卖单)", required = true)
    val creatorRole: CreatorRole,
    @ApiModelProperty("业务类型(CALL,PUT)", required = true)
    val businessType: OptionDirection,
    @ApiModelProperty("是否认证用户", required = true)
    val isCertified: Boolean,
    @ApiModelProperty("用户头像", required = true)
    val avatar: String,
    @ApiModelProperty("用户昵称", required = true)
    val name: String,
    @ApiModelProperty("用户地址", required = true)
    val address: String,
    @ApiModelProperty("链类型", required = true)
    val chainId: ChainType,
    @ApiModelProperty("抵押资产", required = true)
    val underlyingAsset: Symbol,
    @ApiModelProperty("行权资产", required = true)
    val strikeAsset: Symbol,
    @ApiModelProperty("行权数量", required = true)
    val strikePrice: String,
    @ApiModelProperty("Deribit行权价格", required = true)
    val deribitPrice: String,
    @ApiModelProperty("剩余标的资产数量", required = true)
    val amount: String,
    @ApiModelProperty("总标的资产数量", required = true)
    val totalAmount: String,
    @ApiModelProperty("权利金资产", required = true)
    val paywith: List<PremiumAsset>,
    @ApiModelProperty("Deribit权利金价格", required = true)
    val deribitPremiumPrice: String?,
    @ApiModelProperty("权利金数量", required = true)
    val premiumFee: String,
    @ApiModelProperty("结算方式", required = true)
    val acceptList: List<SettlementType>,
    @ApiModelProperty("KYT状态", required = true)
    val kyt: KytStatus,
    @ApiModelProperty("挂单创建时间", required = true)
    val createDate: Long,
    @ApiModelProperty("合约到期日", required = true)
    val expiryDate: Long,
    @ApiModelProperty("锁住的资产", required = false)
    val lockedAsset: String?
)
