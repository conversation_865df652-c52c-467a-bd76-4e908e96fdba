package io.vault.jasper.task

import com.fasterxml.jackson.databind.ObjectMapper
import io.vault.jasper.enums.Symbol
import io.vault.jasper.model.OptionOrder
import io.vault.jasper.model.OptionOrderAction
import io.vault.jasper.model.OptionStatus
import io.vault.jasper.model.UserPointRecord
import io.vault.jasper.repository.*
import io.vault.jasper.service.CurrencyService
import io.vault.jasper.service.OptionOrderService
import io.vault.jasper.service.UserPointService
import io.vault.jasper.service.UserPremiumSummaryService
import io.vault.jasper.utils.DateTimeUtil
import io.vault.jasper.utils.ProfileUtil
import org.slf4j.LoggerFactory
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.beans.factory.annotation.Value
import org.springframework.context.annotation.Profile
import org.springframework.data.mongodb.core.MongoTemplate
import org.springframework.data.mongodb.core.query.Criteria
import org.springframework.data.mongodb.core.query.Query
import org.springframework.data.repository.findByIdOrNull
import org.springframework.http.client.SimpleClientHttpRequestFactory
import org.springframework.scheduling.annotation.Scheduled
import org.springframework.stereotype.Service
import org.springframework.web.client.HttpClientErrorException
import org.springframework.web.client.RestTemplate
import java.math.BigDecimal
import java.time.LocalDateTime
import javax.swing.text.html.Option

@Service
@Profile("test")
class SyncProdSettlementToUatTask @Autowired constructor(
    private val optionOrderRepository: OptionOrderRepository,
    private val mongoTemplate: MongoTemplate,
) {

    private val logger = LoggerFactory.getLogger(this::class.java)

    private val fixCount = 1000

    private val restTemplate: RestTemplate by lazy {
        val rt = RestTemplate()

        val requestFactory = SimpleClientHttpRequestFactory()
        // Set the 30s timeout in milliseconds
        requestFactory.setConnectTimeout(30000)
        requestFactory.setReadTimeout(30000)

        rt.requestFactory = requestFactory
        rt
    }

    private val objectMapper = ObjectMapper()

    @Value("\${schedule_task_on}")
    lateinit var scheduleOn: String

    // 每5分钟执行一次
    @Scheduled(fixedDelay = 60 * 1000, initialDelay = 60 * 1000)
    fun run() {
        if(scheduleOn == "false"){
            return
        }

        try {
            exec()
        } catch (e: Exception) {
            logger.error(e.message, e)
        }

    }

    fun exec(){

        val query = Query()
        query.addCriteria(Criteria.where(OptionOrder::status.name).`is`(OptionStatus.EXECUTED))

        // 设置返回的文档数量限制
        query.limit(fixCount)

        // 使用 mongoTemplate 执行查询
        val results: List<OptionOrder> = mongoTemplate.find(query, OptionOrder::class.java)

        val endDate = LocalDateTime.now().minusMinutes(15)
        results.forEach { optionOrder ->

            if(optionOrder.expiryDate.isAfter(endDate)){
                return@forEach
            }

            val url = "https://apiv2.jaspervault.io/internal/orders/order_info?chain=${optionOrder.chain}&onChainOrderId=${optionOrder.onChainOrderId}"
            logger.info("SyncProdSettlementToUatTask url: $url")
            try {
                val pythResponse = restTemplate.getForObject(url, String::class.java)
                //logger.info("SyncProdSettlementToUatTask response: $pythResponse")

                val pythResponseNode = objectMapper.readTree(pythResponse!!)
                val status = pythResponseNode.get("status")?.asInt()
                    ?: throw Exception("Failed to get status for $url $pythResponse")
                val message = pythResponseNode.get("message")?.asText()

                if(status != 0){
                    throw Exception("Error request $url response $message")
                }

                val prodOrderStatus = pythResponseNode.get("data")?.get("status")?.asText()
                    ?: throw Exception("Failed to get order status for $url $pythResponse")
                val orderStatus = OptionStatus.valueOf(prodOrderStatus)
                if(orderStatus == OptionStatus.SETTLED || orderStatus == OptionStatus.SETTLE_TO_BE_CONFIRMED){

                    val prodOrderSettlementHash = pythResponseNode.get("data")?.get("settlementHash")?.asText()
                        ?: throw Exception("Failed to get order settlement hash for $url $pythResponse")

                    optionOrder.status = OptionStatus.SETTLE_TO_BE_CONFIRMED
                    optionOrder.settlementHash = prodOrderSettlementHash
                } else {
                    optionOrder.status = orderStatus
                }

                optionOrderRepository.save(optionOrder)


            } catch (e: Exception) {
                logger.error(e.message, e)
            }

            Thread.sleep(1000)
        }
    }
}