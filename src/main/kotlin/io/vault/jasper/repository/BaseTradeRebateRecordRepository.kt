package io.vault.jasper.repository


import io.vault.jasper.model.*
import org.springframework.data.mongodb.repository.MongoRepository
import org.springframework.stereotype.Repository

@Repository
interface BaseTradeRebateRecordRepository : MongoRepository<BaseTradeRebateRecord, String> {
    fun findByOptionOrderId(optionOrderId: String): BaseTradeRebateRecord?
    fun findTopByOrderByOptionOrderIdDesc(): BaseTradeRebateRecord?
    fun findByStatus(
        status: BaseTradeRebateRecordStatus
    ): List<BaseTradeRebateRecord>

    fun findByBuyerAddressIgnoreCase(
        buyerAddress: String
    ): List<BaseTradeRebateRecord>

    fun findByStatusAndSettleTxIdIsNotNull(
        status: BaseTradeRebateRecordStatus
    ): List<BaseTradeRebateRecord>

    fun findByBuyerAddressAndStatus(
        buyerAddress: String,
        status: BaseTradeRebateRecordStatus
    ): List<BaseTradeRebateRecord>

    fun findByBuyerAddressIgnoreCaseAndStatus(
        buyerAddress: String,
        status: BaseTradeRebateRecordStatus
    ): List<BaseTradeRebateRecord>

    fun findByBuyerAddressAndStatusInAndSettleTxIdIsNotNull(
        buyerAddress: String,
        statuses: List<BaseTradeRebateRecordStatus>
    ): List<BaseTradeRebateRecord>

    fun findByBuyerAddressIgnoreCaseAndStatusIn(
        buyerAddress: String,
        statuses: List<BaseTradeRebateRecordStatus>
    ): List<BaseTradeRebateRecord>
}