package io.vault.jasper.utils

import org.web3j.protocol.Web3j
import org.web3j.protocol.core.DefaultBlockParameterName
import java.util.*
import javax.crypto.Cipher
import javax.crypto.spec.SecretKeySpec

object BlockchainUtil {
    fun isEvmAddressValid(address: String): Bo<PERSON>an {
        if (address.isBlank()) {
            return false
        }
        val regex = Regex("^[A-Za-z0-9]+$")
        if (address.length >= 3) {
            val ethHexString = address.substring(2)
            if (address.startsWith("0x") && ethHexString.length == 40 && ethHexString.matches(regex)) {
                return true
            }
        }
        return false
    }

    fun isContractAddress(address: String, web3j: Web3j): Boolean {
        val ethGetCode = web3j.ethGetCode(address, DefaultBlockParameterName.LATEST).send()
        return ethGetCode.code != "0x" // 如果返回的是 "0x"，说明不是合约地址
    }
}