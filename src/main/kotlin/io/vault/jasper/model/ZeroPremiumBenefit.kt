package io.vault.jasper.model

import io.vault.jasper.enums.OptionDirection
import io.vault.jasper.enums.Symbol
import org.springframework.data.annotation.CreatedDate
import org.springframework.data.annotation.Id
import org.springframework.data.annotation.LastModifiedDate
import org.springframework.data.mongodb.core.index.Indexed
import org.springframework.data.mongodb.core.mapping.Document
import org.springframework.data.mongodb.core.mapping.Field
import java.time.LocalDateTime

@Document("zero_premium_benefit")
data class ZeroPremiumBenefit(
    @Id
    val id: String? = null,

    @Field("activity_id")
    var activityId: String? = null,

    val name: String? = null,

    val underlyingAsset: Symbol,

    val amount: String,

    val optionDirection: OptionDirection? = null,

    val expiryInHour: String = "8",

    val times: Int,

    var status: BenefitStatus = BenefitStatus.ACTIVE,

    @CreatedDate
    val created: LocalDateTime = LocalDateTime.now(),

    @LastModifiedDate
    val updated: LocalDateTime = LocalDateTime.now()
)

enum class BenefitStatus {
    ACTIVE, INACTIVE
}
