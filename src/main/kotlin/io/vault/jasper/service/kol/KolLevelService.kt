package io.vault.jasper.service.kol

import io.vault.jasper.exception.BusinessException
import io.vault.jasper.exception.ResultEnum
import io.vault.jasper.repository.KolLevelRepository
import org.slf4j.LoggerFactory
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.stereotype.Service

@Service
class KolLevelService @Autowired constructor(
    private val kolLevelRepository: KolLevelRepository
) {

    private var logger = LoggerFactory.getLogger(this::class.java)

    fun getKolLevel(name: String): IKolLevel {
        val l = kolLevelRepository.findFirstByName(name) ?: throw BusinessException(ResultEnum.KOL_NOT_FOUND)

        return KolLevelImpl(l)
    }
}