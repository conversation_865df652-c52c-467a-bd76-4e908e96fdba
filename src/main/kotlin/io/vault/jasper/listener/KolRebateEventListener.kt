package io.vault.jasper.listener

import io.vault.jasper.enums.Symbol
import io.vault.jasper.event.OptionExecutedEvent
import io.vault.jasper.model.KolRebateRecord
import io.vault.jasper.model.KolStatus
import io.vault.jasper.model.OptionOrder
import io.vault.jasper.repository.*
import io.vault.jasper.service.CurrencyService
import io.vault.jasper.service.OptionOrderService
import io.vault.jasper.service.SystemService
import io.vault.jasper.service.UserNetworkService
import io.vault.jasper.service.kol.KolLevelService
import org.slf4j.LoggerFactory
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.context.event.EventListener
import org.springframework.data.repository.findByIdOrNull
import org.springframework.stereotype.Component
import java.math.BigDecimal

@Component
class KolRebateEventListener @Autowired constructor(
    private val optionOrderService: OptionOrderService,
    private val optionOrderRepository: OptionOrderRepository,
    private val kolRebateRecordRepository: KolRebateRecordRepository,
    private val userRepository: UserRepository,
    private val kolRepository: KolRepository,
    private val systemService: SystemService,
    private val kolLevelService: KolLevelService,
    private val currencyService: CurrencyService,
    private val userNetworkRebateRecordRepository: UserNetworkRebateRecordRepository,
    private val userNetworkRepository: UserNetworkRepository,
    private val userNetworkService: UserNetworkService
) {

    private val logger = LoggerFactory.getLogger(this::class.java)

    @EventListener
    fun handleOptionExecutedEvent(event: OptionExecutedEvent) {
        logger.debug("Option execute completed for order: ${event.orderId}")
        optionOrderRepository.findByIdOrNull(event.orderId)?.let { oo ->
            
            if (oo.buyer != null && oo.premiumFeePay != null) {
                val buyerUser = userRepository.findByAddressIgnoreCase(oo.buyer!!) ?: return

                if(buyerUser.useKolRebate) {
                    // 关闭KOL Rebate 计算
                    //calculateKolRebate(oo)
                } else {
                    calculateUserNetworkRebate(oo)
                }
            }
        }
    }

    private fun calculateKolRebate(oo: OptionOrder) {

        kolRebateRecordRepository.findFirstByOptionOrderId(oo.id!!)?.let {
            logger.info("KOL rebate record already exists for order: ${oo.id}")
            return
        }
        if (oo.buyer != null && oo.premiumFeePay != null) {
            val buyerUser = userRepository.findByAddressIgnoreCase(oo.buyer!!) ?: return
            val parentUserId = buyerUser.invitedUserId ?: return
            val parentUser = userRepository.findByIdOrNull(parentUserId) ?: return
            if (!parentUser.useKolRebate) return
            val parentKol = kolRepository.findFirstByWallet(parentUser.address) ?: return
            if (parentKol.status == KolStatus.ACTIVE) {
                val incentiveAmountBigInt = optionOrderService.calculateKolRebate(oo)
                if (incentiveAmountBigInt != null) {

                    val quoteAsset = currencyService.getOptionQuoteAsset(oo.chain, null)
                    val decimals = currencyService.getCurrencyDecimal(oo.chain, quoteAsset)
                    val incentiveAmount = incentiveAmountBigInt.toBigDecimal().movePointLeft(decimals)
                    val systemParameter = systemService.getParameter()

                    // 权利金费率新的计算方法。 2024-07-03
                    //val markupRate = systemParameter.premiumPriceRatePercentage.movePointLeft(2)
                    //val premiumRate = markupRate.divide(BigDecimal("1").add(markupRate), 18, BigDecimal.ROUND_HALF_UP)

                    // 权利金费率调整。 2025-01-17
                    val premiumRate = systemParameter.premiumPriceRatePercentage.movePointLeft(2)

                    val level = kolLevelService.getKolLevel(parentKol.level)

                    val newRecord = kolRebateRecordRepository.save(KolRebateRecord(
                        optionOrderId = oo.id,
                        buyerAddress = oo.buyer!!,
                        kolId = parentKol.id!!,
                        kolLevel = parentKol.level,
                        asset = Symbol.USDT, // todo 目前权利金固定为USDT
                        incentiveAmount = incentiveAmount,
                        premiumFee = oo.premiumFeePay ?: BigDecimal.ZERO,
                        premiumFeeInUsdt = oo.premiumFeePayInUsdt,
                        premiumFeeRate = premiumRate,
                        incentiveRate = level.getIncentiveRate()
                    ))
                    logger.info("KOL rebate record created: ${newRecord.id}")
                }
            }
        }
    }

    private fun calculateUserNetworkRebate(oo: OptionOrder) {

        userNetworkRebateRecordRepository.findFirstByOptionOrderId(oo.id!!)?.let {
            logger.info("User Network rebate record already exists for order: ${oo.id}")
            return
        }

        if (oo.buyer != null && oo.premiumFeePay != null) {

            val systemParameter = systemService.getParameter()
            val premiumRate = systemParameter.premiumPriceRatePercentage.movePointLeft(2)

            userNetworkService.calculateRebateRecords(
                oo,
                premiumRate
            )
        }
    }
}