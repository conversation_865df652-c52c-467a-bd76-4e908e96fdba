package io.vault.jasper.listener

import io.vault.jasper.event.BlockScanCompletedEvent
import io.vault.jasper.repository.OptionOrderRepository
import io.vault.jasper.service.AlertService
import io.vault.jasper.service.SystemService
import org.slf4j.LoggerFactory
import org.springframework.stereotype.Component
import org.springframework.context.event.EventListener
import org.springframework.scheduling.annotation.Async

@Component
class BlockScanCompletedListener(
    private val optionOrderRepository: OptionOrderRepository,
    private val systemService: SystemService,
    private val alertService: AlertService
) {
    private val logger = LoggerFactory.getLogger(this::class.java)

    @Async
    @EventListener
    fun handleBlockScanCompleted(event: BlockScanCompletedEvent) {
        val startTime = System.currentTimeMillis()
        val chain = event.chain
        val block = event.block
        val blockNumber = block.number.toLong()
        
        // 查询该区块的所有订单
        val orders = optionOrderRepository.findByChainAndBlockHeight(chain, blockNumber)
        
        // 统计总订单数
        val totalOrders = orders.size
        
        // 统计每个交易的订单数
        val ordersPerTransaction = orders.groupBy { it.txHash }
            .mapValues { it.value.size }
        
        // 获取系统参数
        val params = systemService.getParameter()
        val maxOrdersPerBlock = params.maxOrdersPerBlock
        val maxOrdersPerTransaction = params.maxOrdersPerTransaction
        
        // 检查区块订单是否超过阈值
        if (totalOrders > maxOrdersPerBlock) {
            logger.warn("Block $blockNumber has $totalOrders orders, exceeding maxOrdersPerBlock ($maxOrdersPerBlock)")
            alertService.warning("[同一区块订单成交数量超额] Max orders($totalOrders) exceeded $maxOrdersPerBlock in block $blockNumber on chain $chain")
        }
        
        // 检查每个交易的订单数
        ordersPerTransaction.forEach { (txHash, count) ->
            if (count > maxOrdersPerTransaction) {
                logger.warn("Transaction $txHash has $count orders, exceeding maxOrdersPerTransaction ($maxOrdersPerTransaction)")
                alertService.warning("[同一交易订单成交数量超额] Transaction $txHash has $count orders, exceeding maxOrdersPerTransaction ($maxOrdersPerTransaction) in block $blockNumber on chain $chain")
            }
        }

        // 记录统计信息
        //logger.info("Block $blockNumber statistics: totalOrders=$totalOrders, ordersPerTransaction=$ordersPerTransaction")

        val endTime = System.currentTimeMillis()
        val duration = endTime - startTime
        if (duration > 100) {
            logger.info("handleBlockScanCompleted function in ${duration}ms")
        }
    }
} 