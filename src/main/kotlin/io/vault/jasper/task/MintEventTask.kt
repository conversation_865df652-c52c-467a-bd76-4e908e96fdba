package io.vault.jasper.task

import com.fasterxml.jackson.databind.ObjectMapper
import io.vault.jasper.enums.ChainType
import io.vault.jasper.model.MintEvent
import io.vault.jasper.repository.MintEventRepository
import io.vault.jasper.service.SystemService
import io.vault.jasper.service.UserService
import io.vault.jasper.service.blockchain.ArbitrumService
import io.vault.jasper.service.blockchain.BlockchainServiceFactory
import io.vault.jasper.service.blockchain.EvmService
import org.slf4j.LoggerFactory
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.beans.factory.annotation.Value
import org.springframework.scheduling.annotation.Scheduled
import org.springframework.stereotype.Service
import org.springframework.web.client.RestTemplate

@Service
class MintEventTask @Autowired constructor(
    private val mintEventRepository: MintEventRepository,
    private val blockchainServiceFactory: BlockchainServiceFactory,
    private val userService: UserService,
    private val systemService: SystemService,
    private val arbitrumService: ArbitrumService
) {

    private val logger = LoggerFactory.getLogger(this::class.java)

    private val arbitrumScanUrl = "https://api.arbiscan.io/api"

    private val restTemplate = RestTemplate()

    private val objectMapper = ObjectMapper()

    @Value("\${schedule_task_on}")
    lateinit var scheduleOn: String

    //@Scheduled(fixedDelay = 5 * 1000, initialDelay = 5 * 1000)
    fun run() {
        if(scheduleOn == "false"){
            return
        }
        val switch = systemService.getParameter().mintEventTaskSwitch
        if (!switch) {
            logger.info("MintEventTask 开关: OFF")
            return
        }
        try {
            exec()
        } catch (e: Exception) {
            logger.error(e.message)
        }
        try {
            val oldContract = "0x5e8A3955A8366b4eF892Ef77e1f0a6074A6d62a6"
            supplementMintEvents(oldContract)
        } catch (e: Exception) {
            logger.error(e.message)
        }
        Thread.sleep(5000)
        try {
            val newContract = "0x2f2Cb9dfCF09D8Ae6e6290c43eFA83B45F931B88"
            supplementMintEvents(newContract)
        } catch (e: Exception) {
            logger.error(e.message)
        }
    }

    private fun exec() {
        val events = mintEventRepository.findByNftCodeIsNullAndReceiptStatusIsNull()
        logger.info("Mint event task begin. Found ${events.size} events.")
        events.forEach { event ->
            try {
                val blockchainService = blockchainServiceFactory.getBlockchainService(event.chain) ?: return@forEach
                if (blockchainService !is EvmService) return@forEach
                val web3j = blockchainService.evmUtil.web3j
                // val dataStruct = getMintEventData(web3j, event.txHash)
                val dataStruct = blockchainService.getMintEventData(event.txHash)
                if (dataStruct != null) {
                    event.nftCode = dataStruct.nftCode
                    event.fromAddress = dataStruct.fromAddress
                    event.receiptStatus = dataStruct.receiptStatus
                    event.blockHeight = dataStruct.blockHeight
                    event.txTimestamp = dataStruct.txTimestamp
                    event.userId2 = event.userId
                    event.userId = null
                    mintEventRepository.save(event)
                }
            } catch (e: Exception) {
                logger.error("Failed to process mint event ${event.txHash}", e)
            }
        }
    }

    /**
     * 补充Mint事件
     */
    private fun supplementMintEvents(contract: String) {
        var page = 1
        do {
            val url = "${arbitrumScanUrl}?module=account&action=txlist&address=${contract}&sort=asc&page=${page}&offset=100"
            val response = restTemplate.getForObject(url, String::class.java)
            val resultNode = objectMapper.readTree(response)
            val dataList = resultNode["result"].asIterable().toList()
            if (dataList.isEmpty()) break
            val total = dataList.size
            dataList.forEachIndexed { index, transaction ->
                logger.info("进度: $index / $total")
                val txHash = transaction["hash"].asText()
                mintEventRepository.findFirstByTxHash(txHash) ?: run {
                    val mintEventData = arbitrumService.getMintEventData(txHash)
                    if (mintEventData != null) {
                        val user = userService.getByAddress(mintEventData.fromAddress!!)
                            ?: userService.register(mintEventData.fromAddress!!)
                        val newMintEvent = mintEventRepository.save(MintEvent(
                            chain = ChainType.ARBITRUM,
                            txHash = txHash,
                            // userId = user.id!!,
                            userId2 = user.id,
                            receiptStatus = mintEventData.receiptStatus,
                            fromAddress = mintEventData.fromAddress,
                            nftCode = mintEventData.nftCode,
                            blockHeight = mintEventData.blockHeight,
                            txTimestamp = mintEventData.txTimestamp
                        ))
                        logger.info("补充合约 $contract 的MintEvent: ${newMintEvent.txHash}")
                    } else {
                        logger.info("交易=$txHash 不是MintEvent")
                    }
                }
            }
            page += 1
            Thread.sleep(5000)
        } while (true)
    }
}