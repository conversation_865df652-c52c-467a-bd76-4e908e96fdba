package io.vault.jasper.task

import io.vault.jasper.enums.ChainType
import io.vault.jasper.service.blockchain.AsyncEvmService
import io.vault.jasper.utils.ProfileUtil
import org.slf4j.LoggerFactory
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.beans.factory.annotation.Value
import org.springframework.context.annotation.Profile
import org.springframework.scheduling.annotation.Scheduled
import org.springframework.stereotype.Service

@Service
@Profile("prod", "test")
class CheckMissingOrdersTask @Autowired constructor(
    private val asyncEvmService: AsyncEvmService
) {

    private val logger = LoggerFactory.getLogger(this::class.java)

    @Value("\${schedule_task_on}")
    lateinit var scheduleOn: String

    @Scheduled(fixedDelay = 60 * 1000, initialDelay = 5 * 1000)
    fun run() {

        if(scheduleOn == "false"){
            return
        }

        val supportChains = when(ProfileUtil.activeProfile) {
            "prod" -> listOf(ChainType.ARBITRUM, ChainType.BASE, ChainType.BITLAYER, ChainType.BSC)
            else -> ChainType.values().toList()
        }
        val futures = supportChains.map { asyncEvmService.checkMissingOrders(it) }
        futures.map {
            try {
                it.get()
            } catch (e: Exception) {
                logger.error("checkMissingOrders error", e)
            }
        }
    }
}