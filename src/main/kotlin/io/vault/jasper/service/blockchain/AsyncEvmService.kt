package io.vault.jasper.service.blockchain

import com.fasterxml.jackson.databind.ObjectMapper
import io.vault.jasper.blockchain.BlockchainUtilFactory
import io.vault.jasper.enums.ChainType
import io.vault.jasper.enums.OptionDirection
import io.vault.jasper.model.*
import io.vault.jasper.repository.BlockchainRepository
import io.vault.jasper.repository.OptionOrderRepository
import io.vault.jasper.repository.OrderRepository
import io.vault.jasper.repository.PendingOptionOrderRepository
import io.vault.jasper.service.*
import io.vault.jasper.utils.DateTimeUtil
import io.vault.jasper.utils.ProfileUtil
import org.slf4j.LoggerFactory
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.data.repository.findByIdOrNull
import org.springframework.scheduling.annotation.Async
import org.springframework.scheduling.annotation.AsyncResult
import org.springframework.stereotype.Service
import org.web3j.utils.Numeric
import java.math.BigInteger
import java.time.LocalDateTime
import java.util.concurrent.Future
import kotlin.math.max

@Service
class AsyncEvmService @Autowired constructor(
    private val blockchainRepository: BlockchainRepository,
    private val blockchainServiceFactory: BlockchainServiceFactory,
    private val blockchainUtilFactory: BlockchainUtilFactory,
    private val chainCfgService: BlockchainConfigService,
    private val optionOrderRepository: OptionOrderRepository,
    private val pendingOptionOrderRepository: PendingOptionOrderRepository,
    private val subgraphService: SubgraphService,
    private val optionOrderService: OptionOrderService,
    private val orderRepository: OrderRepository,
    private val currencyService: CurrencyService,
    private val jasperVaultService: JasperVaultService,
    private val blockchainService: BlockchainService,
    private val marketplaceApiService: MarketplaceApiService,
    private val systemService: SystemService
) {

    private var logger = LoggerFactory.getLogger(this::class.java)

    private val objectMapper = ObjectMapper()

    @Async
    fun checkMissingOrders(chainType: ChainType): Future<Unit> {
        val chainCfg = blockchainRepository.findFirstByChain(chainType) ?: return AsyncResult(Unit)
        val evmService = blockchainServiceFactory.getBlockchainService(chainType) ?: return AsyncResult(Unit)
        if (evmService !is EvmService) return AsyncResult(Unit)
        val scanApi = try {
            blockchainUtilFactory.getScanApi(chainType)
        } catch (e: Exception) {
            return AsyncResult(Unit)
        }

        val fromBlock = chainCfg.checkMissingOrdersBlockHeight
        val lastBlock = evmService.evmUtil.web3j.ethBlockNumber().send().blockNumber.toLong()
        val toBlock = lastBlock - chainCfg.confirmCount
        var lastRecordBlock = fromBlock
        listOf(evmService.callOptionFnHash, evmService.putOptionFnHash).forEach fnHash@{ fnHash ->
            for (page in 1..10) {
                val dataList = try {
                    scanApi.getEventLogsByAddressAndTopics(
                        address = evmService.addOrderLogAddress,
                        topicsFirst = fnHash,
                        fromBlock = fromBlock,
                        toBlock = toBlock,
                        page = page
                    )
                } catch (e: Exception) {
                    logger.warn("Failed to get event logs by address and topics", e)
                    null
                }

                if (dataList == null || dataList.isEmpty) break
                dataList.forEach loopData@{ data ->
                    val blockNumber = Numeric.toBigInt(data["blockNumber"].asText()).toLong()
                    lastRecordBlock = max(lastRecordBlock, blockNumber)
                    val txHash = data["transactionHash"].asText()
                    if (optionOrderRepository.existsByChainAndTxHash(chainType, txHash)) return@loopData
                    if (pendingOptionOrderRepository.existsByChainAndTxHash(chainType, txHash)) return@loopData
                    val blockTimestamp = Numeric.toBigInt(data["timeStamp"].asText()).toLong()

                    val pendingOrder = PendingOptionOrder(
                        chain = chainType,
                        txHash = txHash,
                        txBlockHeight = blockNumber,
                        blockTime = DateTimeUtil.convertTimestampToLocalDateTime(blockTimestamp),
                        message = "Created by CheckMissingOrdersTask"
                    )
                    logger.info("发现缺失的期权交易: $txHash - $chainType\t${pendingOrder.blockTime}")
                    try {
                        pendingOptionOrderRepository.save(pendingOrder)
                    } catch (e: Exception) {
                        logger.error("Failed to save pending option order = $chainType - $txHash", e)
                    }
                }
                Thread.sleep(1000)
            }
        }
        chainCfgService.updateField(chainType, Blockchain::checkMissingOrdersBlockHeight.name, lastRecordBlock)

        return AsyncResult(Unit)
    }

    private fun getSettlementHashFromOrderID(oOrder: OptionOrder): String? {

        // 如果是测试环境，直接返回
        if(ProfileUtil.activeProfile == "dev"){
            return null
        }

        // 到Subgraph 查询结算 Hash
        val hash = try {
            subgraphService.getSettlementFromOrderId(oOrder.chain, oOrder.onChainOrderId!!)
        } catch (subgraphE: Exception) {
            logger.info("${oOrder.chain} 链上订单 ${oOrder.onChainOrderId} 查询Subgraph 失败 ${subgraphE.message}")
            null
        }

        if (!hash.isNullOrBlank()){
            logger.info("${oOrder.chain} 链上订单 ${oOrder.onChainOrderId} 查询subgraph hash 为 $hash")
            return hash
        } else {
            logger.info("${oOrder.chain} 链上订单 ${oOrder.onChainOrderId} 查询subgraph hash 为空")
            return null
        }
    }

    @Async
    fun settleOrder(oOrder: OptionOrder, settlementUrl: String? = null): Future<Unit> {
        var mutableOptionOrder = oOrder
        val startTime = LocalDateTime.of(2024, 9, 1, 0, 0, 0)
        val upgradeTime = LocalDateTime.of(2024, 12, 9, 3, 0, 0)
        if (mutableOptionOrder.expiryDate.isBefore(startTime)) return AsyncResult(Unit)

        logger.info("EVM Settlement: (${mutableOptionOrder.chain})已到期的期权合约: ${mutableOptionOrder.id} upgrade time $upgradeTime")

        val alreadyHash = getSettlementHashFromOrderID(mutableOptionOrder)
        if (alreadyHash != null) {
            logger.info("EVM Settlement: (${mutableOptionOrder.chain})已到期的期权合约: ${mutableOptionOrder.id} ${mutableOptionOrder.onChainOrderId} 到 Subgraph 查询结算 Hash: $alreadyHash")
            optionOrderService.updateFields(
                mapOf(
                    OptionOrder::settlementHash.name to alreadyHash,
                    OptionOrder::status.name to OptionStatus.SETTLE_TO_BE_CONFIRMED
                ),
                mutableOptionOrder.id!!
            )
            return AsyncResult(Unit)
        }

        logger.info("EVM Settlement: (${mutableOptionOrder.chain})已到期的期权合约: ${mutableOptionOrder.id} ${mutableOptionOrder.onChainOrderId} 需要结算")
        val evmService = blockchainServiceFactory.getBlockchainService(mutableOptionOrder.chain) ?: run {
            logger.error("${mutableOptionOrder.chain} Service not found, Skipped")
            return AsyncResult(Unit)
        }
        if (evmService !is EvmService) {
            logger.error("${mutableOptionOrder.chain} Settlement Not supported yet, Skipped")
            return AsyncResult(Unit)
        }
        if (mutableOptionOrder.limitOrder) {
            val chainId = evmService.evmUtil.chainId
            val liquidateStatus = marketplaceApiService.checkLiquidateStatus(
                chainId = chainId,
                orderId = mutableOptionOrder.onChainOrderId!!.toLong()
            )
            
            if (liquidateStatus == null || !liquidateStatus.isAutoLiquidateStatus) {
                // 检查是否超过现在1小时，超过1小时后清算
                val now = LocalDateTime.now()
                val timeout = systemService.getParameter().limitOrderSettlementTimeOutInMinutes
                if (now.isAfter(mutableOptionOrder.expiryDate.plusMinutes(timeout.toLong()))) { // 清算
                    mutableOptionOrder.liquidityType = 0
                } else {
                    logger.info("Skip processing limit order ${mutableOptionOrder.orderId} due to liquidate status check")

                    return AsyncResult(Unit)
                }
            } else {
                // 更新订单的清算模式
                mutableOptionOrder.liquidityType = liquidateStatus.LiquidateType
            }

            optionOrderService.updateFields(
                mapOf(OptionOrder::liquidityType.name to mutableOptionOrder.liquidityType),
                mutableOptionOrder.id!!
            )?.let { o ->
                mutableOptionOrder = o
            }
        }
        // 开始结算
        logger.info("EVM Settlement: (${mutableOptionOrder.chain})Start to settle option order: ${mutableOptionOrder.id}")
        val order = orderRepository.findByIdOrNull(mutableOptionOrder.orderId) ?: run {
            val msg = "Order not found: ${mutableOptionOrder.orderId}"
            logger.warn(msg)
            optionOrderService.updateFields(
                mapOf(
                    OptionOrder::status.name to OptionStatus.SETTLE_FAILED,
                    OptionOrder::errorMsg.name to msg
                ),
                mutableOptionOrder.id!!
            )
            return AsyncResult(Unit)
        }
        try {
            val onChainOrderId = mutableOptionOrder.onChainOrderId ?: throw Exception("On chain order ID not found")
            var tokens = listOf<String>()
            // 1. 获取实时价格，并将价格更新到链上
            logger.info("EVM Settlement: (${mutableOptionOrder.chain})Get and set price for order: ${mutableOptionOrder.id}")
            val usdPrice = try {
                //Degen 是利差结算，需要调用getAndSetPrice
                if(mutableOptionOrder.orderType == OrderType.DEGEN) {
                    val bidAssetPythId = try {

                        if(mutableOptionOrder.chain == ChainType.BITLAYER || mutableOptionOrder.chain == ChainType.BITLAYER_TEST) {
                            currencyService.getAproId(mutableOptionOrder.bidAsset!!)
                        } else {
                            currencyService.getPriceId(mutableOptionOrder.bidAsset!!)
                        }

                    } catch (e: Exception) {
                        null
                    }

                    val quoteAssetPythId = try {

                        var quoteAsset = mutableOptionOrder.quoteAsset

                        if(quoteAsset == null) {
                            quoteAsset = currencyService.getOptionQuoteAsset(
                                mutableOptionOrder.chain,
                                mutableOptionOrder.bidAsset!!,
                            )
                        }

                        if(mutableOptionOrder.chain == ChainType.BITLAYER || mutableOptionOrder.chain == ChainType.BITLAYER_TEST) {
                            currencyService.getAproId(quoteAsset)
                        } else {
                            currencyService.getPriceId(quoteAsset)
                        }

                    } catch (e: Exception) {
                        null
                    }
                    tokens = listOfNotNull(bidAssetPythId, quoteAssetPythId)

                    // 如果是 PUT, Token 顺序要反过来
                    if(order.optionsType == OptionDirection.PUT) {
                        tokens = listOfNotNull(quoteAssetPythId, bidAssetPythId)
                    }

                    try {
                        jasperVaultService.getPythPriceAtDate(
                            currencyService.getPriceId(order.bidAsset!!),
                            mutableOptionOrder.expiryDate
                        )
                    } catch (e: Exception) {
                        null
                    }
                } else {

                    try {
                        jasperVaultService.getPythPriceAtDate(
                            currencyService.getPriceId(order.bidAsset!!),
                            mutableOptionOrder.expiryDate
                        )
                    } catch (e: Exception) {
                        null
                    }
                }
            } catch (e: Exception) {
                val msg = "Failed to get and set price for order: ${mutableOptionOrder.id}, try again later"
                // oOrder.errorMsg = msg
                optionOrderService.updateFields(mapOf(OptionOrder::errorMsg.name to msg), mutableOptionOrder.id!!)
                logger.error(msg, e)
                return AsyncResult(Unit)
//            } ?: run {
//                logger.info("${order.bidAsset} get and set price not found")
//                //return AsyncResult(Unit)
            }

            if(usdPrice != null) {
                mutableOptionOrder = optionOrderService.updateFields(
                    mapOf(
                        OptionOrder::marketPriceAtSettlement.name to usdPrice.stripTrailingZeros().toPlainString()
                    ),
                    mutableOptionOrder.id!!,
                    returnUpdated = true
                )!!
            }

            val liquidateMode = run {
                val message = order.contractContent?.get("message")
                if (message != null) {
                    val msgObj = objectMapper.readTree(message)
                    msgObj["liquidateMode"].asInt().toBigInteger()
                } else {
                    mutableOptionOrder.liquidateMode?.toBigInteger()
                } ?: throw Exception("Liquidate mode not found")
            }
            val liquidityType = when {
                mutableOptionOrder.limitOrder -> mutableOptionOrder.liquidityType!!
                mutableOptionOrder.liquidityType == 0 -> BigInteger.ZERO // 清算
                mutableOptionOrder.liquidateMode == "2" -> BigInteger.ONE // 实物交割
                mutableOptionOrder.orderType == OrderType.DEGEN -> BigInteger("2") // 2024-07-31 修改，Degen 的订单总是返回2，即利差结算
                else -> BigInteger.ZERO // SWAP时总是返回0，即不行权。行权动作由前端用户发起。
            }

            // 3. 结算
            logger.info("EVM Settlement: (${mutableOptionOrder.chain})Liquidate option order: ${mutableOptionOrder.id}")
            val blockchainCfg = blockchainRepository.findFirstByChain(evmService.chainType)
                ?: throw Exception("Blockchain config not found: ${evmService.chainType}")
            var network = evmService.chainType.toString().lowercase() // 结算接口需要的网络参数
            if (blockchainCfg.uat) {
                network += "_uat"
            }
            val orderType = when (order.optionsType) {
                OptionDirection.CALL -> 0
                OptionDirection.PUT -> 1
                else -> throw Exception("option type must not be null")
            }

            val expiryInHour = when {
                mutableOptionOrder.limitOrder -> "25"
                else -> mutableOptionOrder.expiryInHour ?: "2"
            }
            var totalPriceTime: Int? = null

            if(mutableOptionOrder.created < upgradeTime){
                totalPriceTime = 1
            }

            val settlementHash = try {
                blockchainService.liquidateOption2(
                    network = network,
                    tokens = tokens,
                    expirationDate = DateTimeUtil.convertLocalDateTimeToTimestamp(mutableOptionOrder.expiryDate) / 1000,
                    orderType = orderType,
                    orderID = onChainOrderId.toLong(),
                    liquidateType = liquidityType.toInt(),
                    expiryInHour = expiryInHour,
                    totalPriceTime = totalPriceTime,
                    specialUrl = settlementUrl
                )
            } catch (e: Exception) {
                logger.error("(${mutableOptionOrder.chain})Failed to liquidate option order: ${mutableOptionOrder.id}, try again later", e)
                logger.info("Failed to liquidate option order: ${mutableOptionOrder.id}, error msg: ${e.message}")
                // try again later
                if(e.message != null &&
                    (e.message!!.contains("execution reverted: OptionService:optionOrder not exist") ||
                            e.message!!.contains("error code: 524"))
                ) {

                    logger.info("Begin to get hash from subgraph for option order: ${mutableOptionOrder.id}")
                    // 到Subgraph 查询结算 Hash
                    val hash = try {
                        subgraphService.getSettlementFromOrderId(mutableOptionOrder.chain, mutableOptionOrder.onChainOrderId!!)
                    } catch (subgraphE: Exception) {
                        logger.info("${mutableOptionOrder.chain} 链上订单 ${mutableOptionOrder.id} ${mutableOptionOrder.onChainOrderId} 查询Subgraph 失败 ${subgraphE.message}")
                        throw e
                    }

                    if (!hash.isNullOrBlank()){
                        logger.info("${mutableOptionOrder.chain} 链上订单 ${mutableOptionOrder.id} ${mutableOptionOrder.onChainOrderId} 查询subgraph hash 为 $hash")
                        hash
                    } else {
                        logger.info("${mutableOptionOrder.chain} 链上订单 ${mutableOptionOrder.id} ${mutableOptionOrder.onChainOrderId} 查询subgraph hash 为空")
                        return AsyncResult(Unit)
                    }

                } else {
                    return AsyncResult(Unit)
                }
            }
            logger.info("EVM Settlement: OptionOrder=${mutableOptionOrder.id}\torder id=${mutableOptionOrder.onChainOrderId}\tSettlement hash: $settlementHash")

            // oOrder.settlementHash = settlementHash
            optionOrderService.updateFields(
                mapOf(
                    OptionOrder::settlementHash.name to settlementHash,
                    OptionOrder::status.name to OptionStatus.SETTLE_TO_BE_CONFIRMED
                ),
                mutableOptionOrder.id!!
            )
        } catch (e: Exception) {
            logger.error("Failed to settle option order: ${mutableOptionOrder.id}", e)
            optionOrderService.updateFields(
                mapOf(
                    OptionOrder::status.name to OptionStatus.SETTLE_FAILED,
                    OptionOrder::errorMsg.name to e.message
                ),
                mutableOptionOrder.id!!
            )
            order.status = OrderStatus.FAILED
            order.errorMsg = e.message
            orderRepository.save(order)
        }
        return AsyncResult(Unit)
    }

}