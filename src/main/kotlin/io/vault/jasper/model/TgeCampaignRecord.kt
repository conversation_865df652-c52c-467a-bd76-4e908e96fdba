package io.vault.jasper.model

import io.vault.jasper.enums.ChainType
import org.springframework.data.annotation.Id
import org.springframework.data.mongodb.core.index.Indexed
import org.springframework.data.mongodb.core.mapping.Document
import org.springframework.data.mongodb.core.mapping.Field
import java.math.BigDecimal
import java.time.LocalDateTime

@Document("tge_campaign_record")
data class TgeCampaignRecord(
    @Id
    val id: String? = null,
    
    @Field("address")
    @Indexed(background = true)
    val address: String,

    @Field("option_order_id")
    @Indexed(background = true)
    val optionOrderId: String,

    val chain: ChainType? = null,
    
    @Field("created")
    val created: LocalDateTime = LocalDateTime.now()
) 