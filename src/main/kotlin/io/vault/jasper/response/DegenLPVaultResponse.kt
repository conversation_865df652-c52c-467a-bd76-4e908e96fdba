package io.vault.jasper.response

import io.swagger.annotations.ApiModel
import io.swagger.annotations.ApiModelProperty
import io.vault.jasper.enums.ChainType
import io.vault.jasper.enums.OptionDirection
import io.vault.jasper.enums.Symbol
import io.vault.jasper.model.OrderType
import java.math.BigDecimal

@ApiModel("Degen LPVault 信息")
data class DegenLPVaultResponse(
    @ApiModelProperty("资产", required = true)
    val symbol: Symbol,
    @ApiModelProperty("LP Vault 资产", required = true)
    val vaults: List<DegenLPVaultData>
)

data class DegenLPVaultData(
    @ApiModelProperty("LP Vault 资产", required = true)
    val optionSymbol: Symbol,
    @ApiModelProperty("时间周期", required = true)
    val expiryInHour: String,
    @ApiModelProperty("Call LP Vault 地址", required = true)
    val callAddress: String?,
    @ApiModelProperty("Put LP Vault 地址", required = true)
    val putAddress: String?,
    @ApiModelProperty("Call 当前流动性，USDT为单位", required = false)
    val callAvailableLiquidity: String? = null,
    @ApiModelProperty("Put 当前流动性，USDT为单位", required = false)
    val putAvailableLiquidity: String? = null,
    @ApiModelProperty("Call 当前OI, 以标的物资产为单位", required = false)
    val callOpenInterest: String? = null,
    @ApiModelProperty("Put 当前OI, 以标的物资产为单位", required = false)
    val putOpenInterest: String? = null,
    @ApiModelProperty("Call iv", required = false)
    val callIV: String? = null,
    @ApiModelProperty("Put iv", required = false)
    val putIV: String? = null,
    @ApiModelProperty("Call Utilization", required = false)
    val callUtilization: String? = null,
    @ApiModelProperty("Put Utilization", required = false)
    val putUtilization: String? = null,
    @ApiModelProperty("Call Total Value Locked", required = false)
    val callTvl: String? = null,
    @ApiModelProperty("Put Total Value Locked", required = false)
    val putTvl: String? = null,
    @ApiModelProperty("Call Premium Rate", required = false)
    val callPremiumRate: String = "1",
    @ApiModelProperty("Put Premium Rate", required = false)
    val putPremiumRate: String = "1",
    @ApiModelProperty("Call Premium Floor Rate", required = false)
    val callPremiumFloorRate: String = "1",
    @ApiModelProperty("Put Premium Floor Rate", required = false)
    val putPremiumFloorRate: String = "1",
    @ApiModelProperty("Call Maximum", required = false)
    val callMaximum: String = "1",
    @ApiModelProperty("Put Maximum", required = false)
    val putMaximum: String = "1",
    @ApiModelProperty("Call Offer ID", required = false)
    val callOfferID: String = "1",
    @ApiModelProperty("Put Offer ID", required = false)
    val putOfferID: String = "1",
    @ApiModelProperty("Call Setting Index", required = false)
    val callSettingIndex: String = "-1",
    @ApiModelProperty("Put Setting Index", required = false)
    val putSettingIndex: String = "-1",
    @ApiModelProperty("Call Product Index", required = false)
    val callProductIndex: String = "-1",
    @ApiModelProperty("Put Product Index", required = false)
    val putProductIndex: String = "-1",
)