package io.vault.jasper.model

import io.vault.jasper.enums.ChainType
import org.springframework.data.annotation.CreatedDate
import org.springframework.data.annotation.Id
import org.springframework.data.annotation.LastModifiedDate
import org.springframework.data.mongodb.core.index.Indexed
import org.springframework.data.mongodb.core.mapping.Document
import org.springframework.data.mongodb.core.mapping.Field
import java.math.BigDecimal
import java.time.LocalDateTime

enum class AirDropType{
    SOCIAL_TASK,
    INVITATION_TASK,
    TRADING_TASK
}

@Document("airdrop_transaction")
data class AirdropTransaction(
    @Id
    val id: String? = null,
    @Field("user_id")
    @Indexed(background = true)
    val userId: String,
    @Indexed(background = true)
    val address: String,
    @Indexed(background = true)
    var type: AirDropType = AirDropType.SOCIAL_TASK,
    var amount: BigDecimal = BigDecimal.ZERO,
    @CreatedDate
    val created: LocalDateTime = LocalDateTime.now(),
    @LastModifiedDate
    val updated: LocalDateTime = LocalDateTime.now(),
    @Field("referral_user_id")
    @Indexed(background = true)
    var referralUserId: String? = null,
    @Field("referral_address")
    @Indexed(background = true)
    var referralAddress: String? = null,
    @Field("has_claimed_free_trade")
    @Indexed(background = true)
    var hasClaimedFreeTrade: Boolean = false
)