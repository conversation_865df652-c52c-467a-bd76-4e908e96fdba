package io.vault.jasper.response

import io.swagger.annotations.ApiModel
import io.swagger.annotations.ApiModelProperty
import io.vault.jasper.enums.ChainType
import io.vault.jasper.enums.OptionDirection
import io.vault.jasper.enums.Symbol
import io.vault.jasper.model.OptionStatus
import io.vault.jasper.model.TransactionMethod

@ApiModel
data class HistoryResponse(

    @ApiModelProperty("交易Hash", required = true)
    val txHash: String,

    @ApiModelProperty("交易方式", required = true)
    val method: TransactionMethod,

    @ApiModelProperty("Token", required = true)
    val token: String,

    @ApiModelProperty("数量", required = true)
    val amount: String,

    @ApiModelProperty("创建时间戳", required = true)
    val created: Long,

)
