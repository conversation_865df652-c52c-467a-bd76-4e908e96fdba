package io.vault.jasper.service


import org.slf4j.LoggerFactory
import org.springframework.beans.factory.annotation.Value
import org.springframework.stereotype.Component
import software.amazon.awssdk.auth.credentials.AwsBasicCredentials
import software.amazon.awssdk.auth.credentials.StaticCredentialsProvider
import software.amazon.awssdk.core.SdkBytes
import software.amazon.awssdk.regions.Region
import software.amazon.awssdk.services.ses.SesClient
import software.amazon.awssdk.services.ses.model.RawMessage
import software.amazon.awssdk.services.ses.model.SendRawEmailRequest
import software.amazon.awssdk.services.ses.model.SesException
import java.io.ByteArrayOutputStream
import java.nio.ByteBuffer
import java.util.*
import javax.activation.DataHandler
import javax.activation.DataSource
import javax.annotation.PostConstruct
import javax.mail.Message
import javax.mail.Session
import javax.mail.internet.InternetAddress
import javax.mail.internet.MimeBodyPart
import javax.mail.internet.MimeMessage
import javax.mail.internet.MimeMultipart
import javax.mail.util.ByteArrayDataSource

@Component
class MailManager {

    private val logger = LoggerFactory.getLogger(this::class.java)

    @Value("\${kol.apply.mail_from}")
    private lateinit var mailFrom: String

    @Value("\${aws.ses.accessKeyID}")
    private lateinit var sesAccessKeyID: String

    @Value("\${aws.ses.secretAccessKey}")
    private lateinit var sesSecretAccessKey: String

    private val sesRegion = Region.AP_SOUTHEAST_1

    private lateinit var sesClient: SesClient

    @PostConstruct
    private fun init() {
        val credentials = AwsBasicCredentials.create(sesAccessKeyID, sesSecretAccessKey)
        sesClient = SesClient.builder().region(sesRegion)
            .credentialsProvider(StaticCredentialsProvider.create(credentials)).build()
    }

    fun sendSesMail(
        recipients: String,
        subject: String,
        content: String,
        fileContent: ByteArray? = null,
        fileType: String? = null,
        filename: String? = null,
        isHtml: Boolean = false
    ): Pair<String?, String?> {
        val session: Session = Session.getDefaultInstance(Properties())
        val message = MimeMessage(session)

        message.setSubject(subject, "UTF-8")
        val from = mailFrom
        message.setFrom(InternetAddress(from))
        message.setRecipients(Message.RecipientType.TO, InternetAddress.parse(recipients))

        val msgBody = MimeMultipart("alternative")

        val wrap = MimeBodyPart()

        val textPart = MimeBodyPart()
        textPart.setContent(content, "text/plain; charset=UTF-8")

        val htmlPart = MimeBodyPart()
        val htmlContent = if (isHtml) {
            content
        } else {
            content.replace("\n", "<br>")
        }
        htmlPart.setContent(htmlContent, "text/html; charset=UTF-8")
        msgBody.addBodyPart(textPart)
        msgBody.addBodyPart(htmlPart)
        wrap.setContent(msgBody)

        val msg = MimeMultipart("mixed")
        message.setContent(msg)

        msg.addBodyPart(wrap)

        if (fileContent != null && fileType != null && filename != null) {
            // Define the attachment.
            val att = MimeBodyPart()
            val fds: DataSource = ByteArrayDataSource(fileContent, fileType)
            att.dataHandler = DataHandler(fds)
            att.fileName = filename

            // Add the attachment to the message.
            msg.addBodyPart(att)
        }

        try {
            logger.info("Attempting to send an email to $recipients through Amazon SES using the AWS SDK for Java...")
            val outputStream = ByteArrayOutputStream()
            message.writeTo(outputStream)
            val buf: ByteBuffer = ByteBuffer.wrap(outputStream.toByteArray())
            val arr = ByteArray(buf.remaining())
            buf.get(arr)
            val data = SdkBytes.fromByteArray(arr)
            val rawMessage = RawMessage.builder().data(data).build()
            val rawEmailRequest = SendRawEmailRequest.builder().rawMessage(rawMessage).build()

            val response = sesClient.sendRawEmail(rawEmailRequest)

            logger.info("Email message Sent $recipients (res: ${response})")
            return Pair(response.toString(), response.messageId())
        } catch (e: SesException) {
            logger.error(e.message, e)
            return Pair(e.message, null)
        }
    }
}