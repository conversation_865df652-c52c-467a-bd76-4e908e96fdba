package io.vault.jasper.repository

import io.vault.jasper.model.*
import org.springframework.data.mongodb.repository.MongoRepository
import org.springframework.stereotype.Repository

@Repository
interface AirDropFreeTradeRecordRepository : MongoRepository<AirdropFreeTradeRecord, String> {

    fun findByStatus(status: AirdropFreeTradeRecordStatus): List<AirdropFreeTradeRecord>
    fun findByUserIdAndStatus(
        userId: String,
        status: AirdropFreeTradeRecordStatus
    ): List<AirdropFreeTradeRecord>
}