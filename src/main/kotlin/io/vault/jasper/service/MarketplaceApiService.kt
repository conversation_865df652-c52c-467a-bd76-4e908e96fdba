package io.vault.jasper.service

import com.fasterxml.jackson.annotation.JsonAutoDetect
import com.fasterxml.jackson.annotation.JsonCreator
import com.fasterxml.jackson.annotation.JsonProperty
import com.fasterxml.jackson.databind.ObjectMapper
import org.slf4j.LoggerFactory
import org.springframework.beans.factory.annotation.Value
import org.springframework.stereotype.Service
import org.springframework.web.client.RestTemplate
import org.springframework.http.client.SimpleClientHttpRequestFactory

@Service
class MarketplaceApiService() {

    private var logger = LoggerFactory.getLogger(this::class.java)

    private val restTemplate: RestTemplate = RestTemplate().apply {
        requestFactory = SimpleClientHttpRequestFactory().apply {
            setConnectTimeout(10000) // 连接超时 10 秒
            setReadTimeout(10000)    // 读取超时 10 秒
        }
    }

    @Value("\${marketplace.api.base-url}")
    private lateinit var baseUrl: String

    fun checkLiquidateStatus(chainId: Long, orderId: Long, version: Int = 0): LiquidateStatusData? {
        val request = LiquidateStatusRequest(chainId, orderId, version)
        val url = "$baseUrl/order/getOrderLiquidateStatus"
        val response = restTemplate.postForObject(
            url,
            request,
            String::class.java
        )
        logger.info("Liquidate status request: $url\t$request")
        logger.info("checkLiquidateStatus response: $response")
        // change string to LiquidateStatusResponse
        val objectMapper = ObjectMapper()
        val rsp = objectMapper.readValue(response, LiquidateStatusResponse::class.java)
        
        return rsp?.data
    }

    fun checkOrderDistance(
        chainId: Long,
        orderId: Long
    ): OrderDistanceData? {
        val request = OrderDistanceRequest(chainId, orderId)
        val url = "$baseUrl/order/getOrderDistance"
        val response = restTemplate.postForObject(
            url,
            request,
            String::class.java
        )

        // change string to LiquidateStatusResponse
        val objectMapper = ObjectMapper()
        val rsp = try {
            objectMapper.readValue(response, OrderDistanceResponse::class.java)
        } catch (e: Exception) {
            //logger.info("Order Distance status request: $url\t$request")
            logger.error("Failed to parse response: $url\t$request\t$response", e)
            return null
        }

        Thread.sleep(500)

        return rsp?.data
    }
}

// @JsonAutoDetect(fieldVisibility = JsonAutoDetect.Visibility.ANY)
data class LiquidateStatusRequest(
    val chainId: Long,
    val orderId: Long,
    val version: Int
) {
    override fun toString(): String {
        return ObjectMapper().writeValueAsString(this)
    }
}

data class OrderDistanceRequest(
    val chainId: Long,
    val orderId: Long
) {
    override fun toString(): String {
        return ObjectMapper().writeValueAsString(this)
    }
}

data class LiquidateStatusResponse @JsonCreator constructor(
    @JsonProperty("code") val code: Int,
    @JsonProperty("data") val data: LiquidateStatusData,
    @JsonProperty("msg") val msg: String
)

data class LiquidateStatusData @JsonCreator constructor(
    @JsonProperty("isAutoLiquidateStatus") val isAutoLiquidateStatus: Boolean,
    @JsonProperty("LiquidateType") val LiquidateType: Int
)

data class OrderDistanceResponse @JsonCreator constructor(
    @JsonProperty("code") val code: Int,
    @JsonProperty("data") val data: OrderDistanceData,
    @JsonProperty("msg") val msg: String
)

data class OrderDistanceData @JsonCreator constructor(
    @JsonProperty("orderId") val orderId: Long,
    @JsonProperty("chainId") val chainId: Long,
    @JsonProperty("distance") val distance: Double,
    @JsonProperty("groupId") val groupId: String?,
)