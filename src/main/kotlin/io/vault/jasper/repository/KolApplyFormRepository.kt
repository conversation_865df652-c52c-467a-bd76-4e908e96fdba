package io.vault.jasper.repository

import io.vault.jasper.model.KolApplyForm
import io.vault.jasper.model.KolApplyStatus
import org.springframework.data.mongodb.repository.MongoRepository
import org.springframework.stereotype.Repository

@Repository
interface KolApplyFormRepository : MongoRepository<KolApplyForm, String> {
    fun findFirstByWalletAndStatusIn(wallet: String, statusIn: List<KolApplyStatus>): KolApplyForm?
    fun findFirstByWalletIgnoreCaseAndStatusIn(wallet: String, statusIn: List<KolApplyStatus>): KolApplyForm?
    fun findByStatus(status: KolApplyStatus): List<KolApplyForm>
    fun findFirstByWallet(wallet: String): KolApplyForm?
}