package io.vault.jasper.controller.pub

import EVMAddress
import io.swagger.annotations.Api
import io.swagger.annotations.ApiOperation
import io.vault.jasper.ApiResponse
import io.vault.jasper.annotation.ApiCache
import io.vault.jasper.dto.CampaignCompleteTaskDTO
import io.vault.jasper.exception.BusinessException
import io.vault.jasper.exception.ResultEnum
import io.vault.jasper.mapper.CampaignMapper
import io.vault.jasper.model.activity.CampaignRewardRecord
import io.vault.jasper.model.activity.TaskType
import io.vault.jasper.model.activity.UserCampaignProgress
import io.vault.jasper.repository.activity.TaskRepository
import io.vault.jasper.response.CampaignResponse
import io.vault.jasper.service.activity.CampaignRewardService
import io.vault.jasper.service.activity.CampaignService
import io.vault.jasper.utils.AuthUtil
import org.springframework.data.repository.findByIdOrNull
import org.springframework.web.bind.annotation.CrossOrigin
import org.springframework.web.bind.annotation.GetMapping
import org.springframework.web.bind.annotation.PostMapping
import org.springframework.web.bind.annotation.RequestBody
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RequestParam
import org.springframework.web.bind.annotation.RestController
import javax.servlet.http.HttpServletRequest
import javax.validation.Valid

@RestController
@RequestMapping("/campaigns")
@Api(tags = ["Campaigns API"])
class CampaignsController(
    private val campaignService: CampaignService,
    private val campaignRewardService: CampaignRewardService,
    private val authUtil: AuthUtil,
    private val taskRepository: TaskRepository
) {

//    @GetMapping
//    @CrossOrigin
//    @ApiCache(key = "campaigns:get_active_campaign", dynamicKey = ["address"], expire = 5)
//    @ApiOperation("Get all active campaigns with user progress")
//    fun getActiveCampaigns(
//        request: HttpServletRequest,
//        @RequestParam("address") @EVMAddress address: String
//    ): ApiResponse<List<CampaignResponse>> {
//
//        authUtil.filterIPBlacklist(request)
//        val user = authUtil.auth(request, address)
//
//        //Refresh all task progress with user
//        campaignService.processUserIncompletedTasksInCampaign(user)
//
//        val campaignsWithProgress = campaignService.getCampaignsForUser(user)
//
//        val response = campaignsWithProgress.map { (campaign, progress) ->
//            val tasks = campaignService.getTasksForCampaign(campaign.id!!)
//            val taskGroups = if (campaign.taskGroupIds.isNotEmpty()) {
//                campaignService.getTaskGroupsForCampaign(campaign.id)
//            } else {
//                listOf()
//            }
//            val campaignRewards = campaignRewardService.getRewardsForCampaign(campaign.id)
//            val rewards = campaignRewardService.getRewardRecordsForUserInCampaign(user.id!!, campaign.id)
//
//            CampaignMapper.toCampaignResponse(
//                campaign = campaign,
//                tasks = tasks,
//                taskGroups = taskGroups,
//                campaignRewards = campaignRewards,
//                userProgress = progress,
//                rewards = rewards
//            )
//        }
//
//        return ApiResponse.success(response)
//    }

    @GetMapping("/get")
    @CrossOrigin
    @ApiOperation("Get a campaign by ID with user progress")
    //@ApiCache(key = "campaigns:get_campaign", dynamicKey = ["id", "address"], expire = 5)
    fun getCampaign(
        @RequestParam("id") id: String,
        request: HttpServletRequest,
        @RequestParam("address") @EVMAddress address: String?
    ): ApiResponse<CampaignResponse> {

        authUtil.filterIPBlacklist(request)

        val campaign = campaignService.getCampaignById(id)
            ?: throw BusinessException(ResultEnum.CAMPAIGN_NOT_FOUND)

        var progress = UserCampaignProgress(
            userId = "",
            address = "",
            campaignId = id
        )
        var rewards = listOf<CampaignRewardRecord>()

        if (address != null){
            val user = authUtil.auth(request, address)

            //Refresh all task progress with user
            progress = campaignService.getUserCampaignProgress(user.id!!, user.address,id)
            campaignService.processUserIncompletedTasksInCampaign(
                user,
                campaign,
                progress
            )
            rewards = campaignRewardService.getRewardRecordsForUserInCampaign(user.id, id)
        }

        val tasks = campaignService.getTasksForCampaign(id)
        val taskGroups = if (campaign.taskGroupIds.isNotEmpty()) {
            campaignService.getTaskGroupsForCampaign(id)
        } else {
            listOf()
        }
        val campaignRewards = campaignRewardService.getRewardsForCampaign(id)

        val response = CampaignMapper.toCampaignResponse(
            campaign = campaign,
            tasks = tasks,
            taskGroups = taskGroups,
            campaignRewards = campaignRewards,
            userProgress = progress,
            rewards = rewards
        )

        return ApiResponse.success(response)
    }

    @PostMapping("/tasks/complete")
    @ApiOperation("Mark a task as completed")
    @CrossOrigin
    fun completeTask(
        request: HttpServletRequest,
        @Valid @RequestBody(required = true) dto: CampaignCompleteTaskDTO
    ): ApiResponse<UserCampaignProgress> {

        authUtil.filterIPBlacklist(request)
        val user = authUtil.auth(request, dto.address)

        val task = taskRepository.findByIdOrNull(dto.taskId)
            ?: throw BusinessException(ResultEnum.TASK_NOT_FOUND)

        if(task.taskType != TaskType.TWITTER_RETWEET && task.taskType != TaskType.TWITTER_FOLLOW){
            throw BusinessException(ResultEnum.TASK_NOT_COMPLETED)
        }

        val progress = campaignService.completeTask(
            user,
            dto.campaignId,
            dto.taskId,
            mapOf("completed" to true)
        )

        return ApiResponse.success(progress)
    }
}