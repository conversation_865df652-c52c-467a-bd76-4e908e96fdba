package io.vault.jasper.controller.pub

import EVMAddress
import io.swagger.annotations.ApiOperation
import io.vault.jasper.ApiResponse
import io.vault.jasper.annotation.ApiCache
import io.vault.jasper.enums.ChainType
import io.vault.jasper.enums.Symbol
import io.vault.jasper.model.*
import io.vault.jasper.repository.*
import io.vault.jasper.response.*
import io.vault.jasper.service.*
import io.vault.jasper.utils.AuthUtil
import io.vault.jasper.utils.DateTimeUtil
import org.slf4j.LoggerFactory
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.web.bind.annotation.*
import java.math.BigDecimal
import java.util.*
import javax.servlet.http.HttpServletRequest

@RestController
@RequestMapping("/jp_campaign")
class JPCampaignController @Autowired constructor(
    private val authUtil: AuthUtil,
    private val optionOrderRepository: OptionOrderRepository,
    private val userJPCampaignInfoRepository: UserJPCampaignInfoRepository,
    private val jpCampaignService: JPCampaignService,
    private val jpFirstTradeRebateRecordRepository: JPFirstTradeRebateRecordRepository,
    private val orderRepository: OrderRepository,
    private val stoneService: StoneService
) {

    private var logger = LoggerFactory.getLogger(this::class.java)

    @ApiOperation("Request JP Campaign Info of User")
    @CrossOrigin
    @ApiCache(key = "jp_campaign:info", dynamicKey = ["address"], expire = 10)
    @GetMapping("/info")
    fun info(
        request: HttpServletRequest,
        @RequestParam("address") @EVMAddress address: String,
    ): ApiResponse<AirdropInfoResponse> {

        authUtil.filterIPBlacklist(request)
        val user = authUtil.auth(request, address)

        val jpCampaignParameter = jpCampaignService.getParameter()
        val jpCampaignInfo = jpCampaignService.getJPCampaignInfo(user)

        var canTrade = user.created.isAfter(jpCampaignParameter.startDate)

        var rebateCount = jpCampaignService.getFirstTradeRebateCount()
        if(rebateCount >= 10000){
            rebateCount = 10000
        }

        val firstTradeRebateRecords = jpFirstTradeRebateRecordRepository.findByBuyerAddressIgnoreCase(
            user.address
        )

        var firstTradePNL = "0"
        var firstTradeIs2H = false
        var firstTradeProfitRate = "0"
        var firstTradePremium = "0"
        var firstTradePremiumAsset = Symbol.USDT
        var firstTradeAmount = "0"
        var tradeInfo: ExecutedOrderResponse? = null

        if(firstTradeRebateRecords.isNotEmpty()) {
            canTrade = false
            val rebateRecord = firstTradeRebateRecords[0]
            firstTradePNL = rebateRecord.netProfit.movePointLeft(6).stripTrailingZeros().toPlainString()
            firstTradeIs2H = true

            var roi = BigDecimal.ZERO
            if(rebateRecord.premiumFee.compareTo(BigDecimal.ZERO) != 0){
                roi = rebateRecord.profit.divide(rebateRecord.premiumFee, 8, BigDecimal.ROUND_HALF_UP)
            }

            firstTradeProfitRate = roi.stripTrailingZeros().toPlainString()
            firstTradeAmount = rebateRecord.rebateAmount.stripTrailingZeros().toPlainString()
        }

        val firstTrade = optionOrderRepository.findFirstByChainAndBuyerIgnoreCase(
            ChainType.ARBITRUM,
            user.address
        )

        logger.info("JP Campaign First trade: $firstTrade")
        if(firstTrade != null && firstTrade.expiryInHour != null && firstTrade.bidAsset != null && firstTrade.bidAmount != null) {

            if(firstTrade.expiryInHour == "0.5" && firstTrade.bidAsset == Symbol.WBTC && firstTrade.bidAmount!!.compareTo(BigDecimal("0.01")) == 0) {
                firstTradeIs2H = true
            }

            firstTradePremium = firstTrade.premiumFeePay?.stripTrailingZeros()?.toPlainString() ?: "0"
            firstTradePremiumAsset = if (firstTrade.premiumAsset?.asset != null) Symbol.valueOf(firstTrade.premiumAsset!!.asset) else Symbol.USDT

            //val order = orderRepository.findByIdOrNull(firstTrade.orderId)
            //if(order != null) {
                var lockDateTimeStamp = DateTimeUtil.convertLocalDateTimeToTimestamp(firstTrade.expiryDate)
                if(firstTrade.lockDate != null){
                    lockDateTimeStamp = DateTimeUtil.convertLocalDateTimeToTimestamp(firstTrade.lockDate)
                }

                val stonePieces = stoneService.getNftIdFromOptionOrder(firstTrade)
                tradeInfo = ExecutedOrderResponse(
                        id = firstTrade.id!!,
                        orderId = firstTrade.orderId,
                        orderType = firstTrade.orderType,
                        chain = firstTrade.chain,
                        onChainOrderId = firstTrade.onChainOrderId,
                        buyerAddress = firstTrade.buyer,
                        buyerVault = firstTrade.buyerVault,
                        buyerVaultSalt = firstTrade.buyerVaultSalt,
                        sellerAddress = firstTrade.seller,
                        businessType = firstTrade.direction!!,

                        bidAsset = firstTrade.bidAsset,
                        bidAmount = firstTrade.bidAmount?.stripTrailingZeros()?.toPlainString(),

                        underlyingAsset = firstTrade.underlyingAsset,
                        underlyingAssetAddress = firstTrade.underlyingAssetAddress,
                        underlyingAssetAmount = firstTrade.amount.stripTrailingZeros().toPlainString(),

                        strikeAsset = firstTrade.strikeAsset,
                        strikeAmount = firstTrade.strikeAmount.stripTrailingZeros().toPlainString(),
                        strikePrice = firstTrade.strikePrice!!.stripTrailingZeros().toPlainString(),
                        actualStrikeAmount = firstTrade.actualStrikeAmount?.stripTrailingZeros()?.toPlainString(),

                        premiumFeesAsset = if (firstTrade.premiumAsset?.asset != null) Symbol.valueOf(firstTrade.premiumAsset!!.asset) else Symbol.USDT,
                        premiumFee = firstTrade.premiumFeePay?.stripTrailingZeros()?.toPlainString(),
                        premiumFeeInUsdt = firstTrade.premiumFeePayInUsdt?.stripTrailingZeros()?.toPlainString(),
                        premiumFeeShouldPay = firstTrade.premiumFeeShouldPay?.stripTrailingZeros()?.toPlainString(),
                        premiumFeeShouldPayInUsdt = firstTrade.premiumFeeShouldPayInUsdt?.stripTrailingZeros()?.toPlainString(),

                        createDate = DateTimeUtil.convertLocalDateTimeToTimestamp(firstTrade.created),
                        expiryDate = DateTimeUtil.convertLocalDateTimeToTimestamp(firstTrade.expiryDate),
                        lockDate = lockDateTimeStamp,
                        status = firstTrade.status,
                        degen = firstTrade.jvault,
                        marketPriceAtSettlement = firstTrade.marketPriceAtSettlement,
                        settlementHash = firstTrade.settlementHash,
                        buyerProfit = firstTrade.buyerProfit?.stripTrailingZeros()?.toPlainString(),
                        expiryInHour = firstTrade.expiryInHour,
                        usedMoonlightBox = firstTrade.usedMoonlightBox,
                        usedRealityStone = firstTrade.usedRealityStone ?: false,
                        usedPowerStone = firstTrade.usedPowerStone ?: false,
                        usedSpaceStone = firstTrade.usedSpaceStone ?: false,
                        usedTimeStone = firstTrade.usedTimeStone ?: false,
                        stoneActivityNftId = firstTrade.stoneActivityNftId,
                        txHash = firstTrade.txHash,
                        premiumFeeLog = firstTrade.premiumFeeLog,
                        awardStonePieceNftIds = stonePieces
                )
            //}
        }

        val hasJoinDiscord = user.discordInfo?.inGuild ?: false
        if(jpCampaignInfo.joinDiscordTime == 0L && hasJoinDiscord) {
            jpCampaignInfo.joinDiscordTime = Date().time
            userJPCampaignInfoRepository.save(jpCampaignInfo)
        }

        if(jpCampaignInfo.followTwitterTime == 0L && user.twitterAccountId != null) {
            jpCampaignInfo.twitterAccountId = user.twitterAccountId
            jpCampaignInfo.followTwitterTime = Date().time
            userJPCampaignInfoRepository.save(jpCampaignInfo)
        }

        val isTwitterFollowed = (user.twitterAccountId != null && jpCampaignInfo.followTwitterTime > 0)
        val isTwitterRetweet = jpCampaignInfo.retweetTime > 0

        return ApiResponse.success(
            AirdropInfoResponse(
                address = user.address,
                isTwitterFollowed = isTwitterFollowed,
                isTwitterRetweet = isTwitterRetweet,
                isJoinDiscord = hasJoinDiscord,
                firstTradePNL = firstTradePNL,
                firstTradeIs2HETH = firstTradeIs2H,
                retweetId = jpCampaignParameter.retweetId,
                canTrade = canTrade,
                firstTradeRebateCount = rebateCount,
                jpTradeInfo = tradeInfo,
                firstTradeAmount = firstTradeAmount,
                firstTradeGrossProfitRate = firstTradeProfitRate,
                firstTradePremium = firstTradePremium,
                firstTradePremiumAsset = firstTradePremiumAsset
            )
        )
    }

    @ApiOperation("返回推特帖子链接")
    @CrossOrigin
    @ApiCache(key = "jp_campaign:retweet_link", dynamicKey = ["address"], expire = 60)
    @GetMapping("/retweet_link")
    fun retweetBase(
        request: HttpServletRequest,
        @RequestParam("address") @EVMAddress address: String,
    ): ApiResponse<String> {

        authUtil.filterIPBlacklist(request)
        val user = authUtil.auth(request, address)

        val userCampaignInfo = jpCampaignService.getJPCampaignInfo(user)
        if(userCampaignInfo.retweetTime == 0L) {
            userCampaignInfo.retweetTime = Date().time
            userJPCampaignInfoRepository.save(userCampaignInfo)
        }

        val parameter = jpCampaignService.getParameter()
        return ApiResponse.success(parameter.retweetId)
    }
}
