package io.vault.jasper.model

import io.vault.jasper.enums.ChainType
import org.springframework.data.annotation.Id
import org.springframework.data.annotation.LastModifiedDate
import org.springframework.data.mongodb.core.mapping.Document
import java.time.LocalDateTime

@Document
data class Currency(
    @Id
    val id: String? = null,
    val symbol: String,
    val chains: MutableList<ChainType>,
    var addresses: MutableMap<ChainType, String>?,
    var decimals: MutableMap<ChainType, Int>?,
    var pythId: String?, // PYTH oracle id
    var aproId: String?, // APRO oracle id
    /**
     * pyth ID精度
     */
    var pythDecimals: Int = 8,
    val logo: String? = null,
    @LastModifiedDate
    val updated: LocalDateTime = LocalDateTime.now()
)