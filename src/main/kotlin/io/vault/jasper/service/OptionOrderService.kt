package io.vault.jasper.service

import io.vault.jasper.enums.ChainType
import io.vault.jasper.enums.OptionDirection
import io.vault.jasper.enums.Symbol
import io.vault.jasper.model.*
import io.vault.jasper.model.OptionStatus.*
import io.vault.jasper.repository.*
import io.vault.jasper.service.blockchain.EvmService
import io.vault.jasper.service.kol.KolLevelService
import org.slf4j.LoggerFactory
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.data.mongodb.core.FindAndModifyOptions
import org.springframework.data.mongodb.core.MongoTemplate
import org.springframework.data.mongodb.core.aggregation.Aggregation
import org.springframework.data.mongodb.core.aggregation.ConvertOperators
import org.springframework.data.mongodb.core.query.Criteria
import org.springframework.data.mongodb.core.query.Query
import org.springframework.data.mongodb.core.query.Update
import org.springframework.data.repository.findByIdOrNull
import org.springframework.stereotype.Service
import java.math.BigDecimal
import java.math.BigInteger
import java.time.LocalDateTime

@Service
class OptionOrderService @Autowired constructor(
    private val currencyService: CurrencyService,
    private val mongoTemplate: MongoTemplate,
    private val systemService: SystemService,
    private val userRepository: UserRepository,
    private val kolRepository: KolRepository,
    private val kolLevelService: KolLevelService,
    private val optionOrderRepository: OptionOrderRepository,
    private val orderRepository: OrderRepository,
    private val jasperVaultService: JasperVaultService,
    private val systemOptionOrderRebateRecordRepository: SystemOptionOrderRebateRecordRepository,
    private val optionOrderTaskParameterRepository: OptionOrderTaskParameterRepository
){

    private val logger = LoggerFactory.getLogger(this::class.java)

    fun getUserOptionPremiumTotal(
        userAddresses: List<String>,
        chain: ChainType
    ): BigDecimal {

        val criteria = Criteria.where(OptionOrder::status.name).`in`(
            OptionStatus.executedStatusList()
        ).and(OptionOrder::buyer.name).`in`(userAddresses)
            .and(OptionOrder::chain.name).`is`(chain)

        val agg = Aggregation.newAggregation(
            Aggregation.match(criteria),
            Aggregation.group().sum(
                ConvertOperators.valueOf(
                    //OptionOrder::premiumFeePay.name
                    OptionOrder::premiumFeePayInUsdt.name
                ).convertToDecimal()).`as`("total")
        )
        val groupResults = mongoTemplate.aggregate(agg, OptionOrder::class.java, Map::class.java)
        val result = groupResults.mappedResults.firstOrNull()
        val total = (result?.get("total")?:"0").toString()

        val decimal = currencyService.getCurrencyDecimal(chain, Symbol.USDT)
        val readableAmount = BigDecimal(total).divide(BigDecimal(BigInteger.TEN.pow(decimal)), 18, BigDecimal.ROUND_DOWN)
        return readableAmount
    }

    fun getUserOptionVolumeTotal(
        userAddresses: List<String>,
        chain: ChainType? = null,
        start: LocalDateTime? = null,
        end: LocalDateTime? = null
    ): BigDecimal {

        if(userAddresses.size == 0){
            return BigDecimal.ZERO
        }

        val criteria = Criteria.where(Order::status.name).`in`(
            listOf(
                OrderStatus.PENDING_SETTLEMENT,
                OrderStatus.COMPLETED
            )
        ).and(Order::creator.name).`in`(userAddresses)

        if(chain != null){
            criteria.and(Order::chain.name).`is`(chain)
        }

        if(start != null && end != null){
            criteria.and(Order::created.name).gte(start).lt(end)
        }

        val agg = Aggregation.newAggregation(
            Aggregation.match(criteria),
            Aggregation.group().sum(
                ConvertOperators.valueOf(
                    Order::volume.name
                ).convertToDecimal()).`as`("total")
        )
        val groupResults = mongoTemplate.aggregate(agg, Order::class.java, Map::class.java)
        val result = groupResults.mappedResults.firstOrNull()
        val total = (result?.get("total")?:"0").toString()
        return BigDecimal(total)
    }

    fun calculateOrderVolume(order: Order): BigDecimal{

        //价格是18位精度
        val strikePrice = order.strikePrice.divide(BigDecimal(BigInteger.TEN.pow(18)), 18, BigDecimal.ROUND_DOWN)
        val bidAmount = order.bidAmount?:BigDecimal.ZERO

        val volume = bidAmount.multiply(strikePrice).setScale(18, BigDecimal.ROUND_DOWN)
        logger.debug("Calculate Order Volume strikePrice: $strikePrice\tbidAmount: $bidAmount\tvolume: $volume")
        return volume
    }

    fun calculateOptionOrderVolume(oo: OptionOrder): BigDecimal{

        //价格是18位精度
        val strikePrice = oo.strikePrice!!.divide(BigDecimal(BigInteger.TEN.pow(18)), 18, BigDecimal.ROUND_DOWN)
        val bidAmount = oo.bidAmount?:BigDecimal.ZERO

        val volume = bidAmount.multiply(strikePrice).setScale(18, BigDecimal.ROUND_DOWN)
        //logger.debug("Calculate Option Order Volume strikePrice: $strikePrice\tbidAmount: $bidAmount\tvolume: $volume")
        return volume
    }

    fun calculateKolRebate(oo: OptionOrder): BigInteger? {
        if (oo.premiumFeePay == null) return null
        val buyer = userRepository.findByAddressIgnoreCase(oo.buyer ?: return null) ?: return null
        val parentUserId = buyer.invitedUserId ?: return null
        val parentUser = userRepository.findByIdOrNull(parentUserId) ?: return null
        val parentUserKol = kolRepository.findFirstByWallet(parentUser.address) ?: return null
        if (parentUserKol.status != KolStatus.ACTIVE) return null
        val level = kolLevelService.getKolLevel(parentUserKol.level)

        val actualPremiumFeeInUsdt = oo.premiumFeePayInUsdt ?: BigDecimal.ZERO // 实际支付权利金
        val systemParameter = systemService.getParameter()

        // 2024-07-04 修改为按照权利金费率计算返利
        //val markupRate = systemParameter.premiumPriceRatePercentage.movePointLeft(2)
        //val premiumRate = markupRate.divide(BigDecimal("1").add(markupRate), 18, BigDecimal.ROUND_HALF_UP)

        // 2025-01-17 再次调整权利金费率计算
        val premiumRate = systemParameter.premiumPriceRatePercentage.movePointLeft(2)
        val amount = actualPremiumFeeInUsdt * premiumRate * level.getIncentiveRate()

        return amount.toBigInteger()
    }

    fun getSellerOpenInterest(
        chain: ChainType,
        sellerVault: String,
        lockSymbol: Symbol
    ): BigDecimal {
//        val criteria = Criteria.where(OptionOrder::status.name).`is`(
//            OptionStatus.EXECUTED
//        ).and(OptionOrder::chain.name).`is`(chain)
//            .and(OptionOrder::sellerVault.name).`is`(sellerVault)
//
//        val agg = Aggregation.newAggregation(
//            Aggregation.match(criteria),
//            Aggregation.group().sum(
//                ConvertOperators.valueOf(
//                    OptionOrder::amount.name
//                ).convertToDecimal()).`as`("total")
//        )
//        val groupResults = mongoTemplate.aggregate(agg, OptionOrder::class.java, Map::class.java)
//        val result = groupResults.mappedResults.firstOrNull()
//        val total = (result?.get("total")?:"0").toString()
//
//        val decimal = currencyService.getCurrencyDecimal(chain, lockSymbol)
//        val readableAmount = BigDecimal(total).divide(BigDecimal(BigInteger.TEN.pow(decimal)), 18, BigDecimal.ROUND_DOWN)
//
//        return readableAmount

        val orders = optionOrderRepository.findByChainAndSellerVaultAndStatusIn(
            chain,
            sellerVault,
            listOf(OptionStatus.EXECUTED)
        )

        var totalOI = BigDecimal.ZERO
        for (oo in orders) {

            val decimal = currencyService.getCurrencyDecimal(chain, lockSymbol)
            val readableAmount = oo.amount.divide(BigDecimal(BigInteger.TEN.pow(decimal)), 18, BigDecimal.ROUND_DOWN)

            totalOI += readableAmount.multiply(oo.bidAmount)
        }

        return totalOI
    }

    fun getSellerPremiumEarn(
        chain: ChainType,
        sellerVault: String
    ): BigDecimal {
        val criteria = Criteria.where(OptionOrder::status.name).`in`(
            OptionStatus.executedStatusList()
        ).and(OptionOrder::chain.name).`is`(chain)
            .and(OptionOrder::sellerVault.name).`is`(sellerVault)

        val agg = Aggregation.newAggregation(
            Aggregation.match(criteria),
            Aggregation.group().sum(
                ConvertOperators.valueOf(
                    OptionOrder::premiumFeePayInUsdt.name
                ).convertToDecimal()).`as`("total")
        )
        val groupResults = mongoTemplate.aggregate(agg, OptionOrder::class.java, Map::class.java)
        val result = groupResults.mappedResults.firstOrNull()
        val total = (result?.get("total")?:"0").toString()

        val decimal = currencyService.getCurrencyDecimal(chain, Symbol.USDT) // 权利金以USDT为单位
        val readableAmount = BigDecimal(total).divide(BigDecimal(BigInteger.TEN.pow(decimal)), 18, BigDecimal.ROUND_DOWN)

        return readableAmount
    }

    fun getSellerOptionVolumeTotal(
        chain: ChainType,
        sellerVault: String
    ): BigDecimal {

        val criteria = Criteria.where(OptionOrder::status.name).`in`(
            OptionStatus.executedStatusList()
        ).and(OptionOrder::chain.name).`is`(chain)
            .and(OptionOrder::sellerVault.name).`is`(sellerVault)

        val agg = Aggregation.newAggregation(
            Aggregation.match(criteria),
            Aggregation.group().sum(
                ConvertOperators.valueOf(
                    OptionOrder::volume.name
                ).convertToDecimal()).`as`("total")
        )
        val groupResults = mongoTemplate.aggregate(agg, OptionOrder::class.java, Map::class.java)
        val result = groupResults.mappedResults.firstOrNull()
        val total = (result?.get("total")?:"0").toString()

        return BigDecimal(total)
    }

    /**
     * 返回的 Net profit 是以大数表示
     */
    fun calculateNetProfit(oo: OptionOrder): BigDecimal {

        if(oo.buyerProfit == null){
            return BigDecimal.ZERO
        }

        val quoteAsset = currencyService.getOptionQuoteAsset(oo.chain, null)
        var profitInUsdt = oo.buyerProfit!!
        val usdtDecimal = currencyService.getCurrencyDecimal(oo.chain, quoteAsset)
        if(oo.direction == OptionDirection.CALL){

            var lockAssetSymbol = oo.bidAsset
            if(lockAssetSymbol == null){
                lockAssetSymbol = oo.underlyingAsset
            }
            val decimal = currencyService.getCurrencyDecimal(oo.chain, lockAssetSymbol)
            val settlementPrice = BigDecimal(oo.marketPriceAtSettlement).movePointLeft(18)

            val profitInAsset = oo.buyerProfit!!.movePointLeft(decimal)
            profitInUsdt = profitInAsset.multiply(settlementPrice).movePointRight(usdtDecimal)
        }

        val netProfit = profitInUsdt.subtract(oo.premiumFeePayInUsdt ?: BigDecimal.ZERO)
        return netProfit
    }

    fun calculateOpenInterest(
        orderType: OrderType?,
        chain: ChainType?,
        symbol: Symbol?,
        direction: OptionDirection?,
        expiryInHour: String?,
        seller: String?,
        sellerVault: String?
    ): BigDecimal {

        val criteria = Criteria.where(OptionOrder::status.name).`is`(
            OptionStatus.EXECUTED
        )

        if(orderType != null){
            criteria.and(OptionOrder::orderType.name).`is`(orderType)
        }

        if(chain != null){
            criteria.and(OptionOrder::chain.name).`is`(chain)
        }

        if(symbol != null){
            criteria.and(OptionOrder::bidAsset.name).`is`(symbol)
        }

        if(direction != null){
            criteria.and(OptionOrder::direction.name).`is`(direction)
        }

        if(expiryInHour != null){
            criteria.and(OptionOrder::expiryInHour.name).`is`(expiryInHour)
        }

        if(seller != null){
            criteria.and(OptionOrder::seller.name).regex("(?i)$seller")
        }

        if(sellerVault != null){
            criteria.and(OptionOrder::sellerVault.name).regex("(?i)$sellerVault")
        }

        val agg = Aggregation.newAggregation(
            Aggregation.match(criteria),
            Aggregation.group().sum(
                ConvertOperators.valueOf(
                    OptionOrder::bidAmount.name
                ).convertToDecimal()).`as`("total")
        )
        val groupResults = mongoTemplate.aggregate(agg, OptionOrder::class.java, Map::class.java)
        val result = groupResults.mappedResults.firstOrNull()
        val total = (result?.get("total")?:"0").toString().toBigDecimal()

        return total
    }

    fun getOptionOrderStrikePrice(
        optionOrder: OptionOrder
    ): BigDecimal{

        val order = orderRepository.findByIdOrNull(optionOrder.orderId) ?: return BigDecimal.ZERO
        return order.strikePrice.movePointLeft(18)
    }

    fun calculateAssetValue(
        chain: ChainType,
        asset: Symbol,
        amount: BigDecimal,
        priceInUsd: BigDecimal
    ): BigDecimal{
        val readablePrice = priceInUsd.movePointLeft(18)
        val assetDecimal = currencyService.getCurrencyDecimal(chain, asset)

        val readableAmount = amount.movePointLeft(assetDecimal)
        val assetValueInUsd = readableAmount.multiply(readablePrice).setScale(18, BigDecimal.ROUND_HALF_UP)

        return assetValueInUsd
    }

    fun calculatePremiumInUSDT(
        optionOrder: OptionOrder,
        premiumToCalculate: BigDecimal,
    ): BigDecimal{

        val strikePrice = getOptionOrderStrikePrice(optionOrder)
        val premiumAsset = Symbol.valueOf(optionOrder.premiumAsset!!.asset)
        if(premiumAsset == Symbol.USDT || premiumAsset == Symbol.USDC){
            //return optionOrder.premiumFeePay ?: BigDecimal.ZERO
            return premiumToCalculate
        }

        val premiumAssetDecimal = currencyService.getCurrencyDecimal(optionOrder.chain, premiumAsset)

        val premium = premiumToCalculate.movePointLeft(premiumAssetDecimal)
        val premiumInUsdt = premium.multiply(strikePrice)

        val usdtDecimal = currencyService.getCurrencyDecimal(optionOrder.chain, Symbol.USDT)
        return premiumInUsdt.movePointRight(usdtDecimal).setScale(0, BigDecimal.ROUND_HALF_UP)
    }

    fun updateOptionOrderPriceInUsdAtCreated(
        optionOrder: OptionOrder
    ){
        val updateFields = mutableMapOf<String, Any?>()
        // Get Pyth ID
        val orderBidAsset = optionOrder.bidAsset ?: return
        var orderQuoteAsset = optionOrder.quoteAsset
        if(orderQuoteAsset == null) {
            orderQuoteAsset = currencyService.getOptionQuoteAsset(optionOrder.chain, null)
            // optionOrder.quoteAsset = orderQuoteAsset
            updateFields[OptionOrder::quoteAsset.name] = orderQuoteAsset
        }

        val bidAssetPythId = currencyService.getPriceId(orderBidAsset)
        val quoteAssetPythId = currencyService.getPriceId(orderQuoteAsset)

        val premiumAsset = optionOrder.premiumAsset?.asset ?: "USDT"
        val premiumAssetSymbol = Symbol.valueOf(premiumAsset)
        val premiumAssetPythId = currencyService.getPriceId(premiumAssetSymbol)

        val expiryDate = optionOrder.expiryDate

        val orderLastSeconds = calculateOptionOrderSeconds(optionOrder)
        val startDate = expiryDate.minusSeconds(orderLastSeconds)

        try {
            var bidAssetPriceAtCreated = jasperVaultService.getPythPriceAtDate(
                bidAssetPythId,
                startDate
            )
            if(bidAssetPriceAtCreated == null){
                bidAssetPriceAtCreated = optionOrder.strikePrice
            }

            if(bidAssetPriceAtCreated == null){
                logger.warn("Can not get bid asset price at created ${optionOrder.id} and strike price is null")
                return
            }

            var quoteAssetPriceAtCreated = jasperVaultService.getPythPriceAtDate(
                quoteAssetPythId,
                startDate
            )

            if(quoteAssetPriceAtCreated == null){

                logger.warn("Can not get quote asset price at created ${optionOrder.id} set it to 1")
                quoteAssetPriceAtCreated = BigDecimal(1).movePointRight(18)
            }

            var premiumAssetPriceAtCreated = jasperVaultService.getPythPriceAtDate(
                premiumAssetPythId,
                startDate
            )

            if(premiumAssetPriceAtCreated == null){
                if(premiumAssetPythId == bidAssetPythId){
                    premiumAssetPriceAtCreated = bidAssetPriceAtCreated
                }

                if(premiumAssetPythId == quoteAssetPythId){
                    premiumAssetPriceAtCreated = quoteAssetPriceAtCreated
                }
            }

            updateFields[OptionOrder::bidAssetPriceInUsdAtCreated.name] = bidAssetPriceAtCreated
            updateFields[OptionOrder::quoteAssetPriceInUsdAtCreated.name] = quoteAssetPriceAtCreated
            updateFields[OptionOrder::premiumAssetPriceInUsdAtCreated.name] = premiumAssetPriceAtCreated

            updateFields[OptionOrder::premiumFeePayInUsd.name] = calculateAssetValue(
                optionOrder.chain,
                premiumAssetSymbol,
                optionOrder.premiumFeePay ?: BigDecimal.ZERO,
                premiumAssetPriceAtCreated!!
            )

            updateFields[OptionOrder::premiumFeeShouldPayInUsd.name] = calculateAssetValue(
                optionOrder.chain,
                premiumAssetSymbol,
                optionOrder.premiumFeeShouldPay ?: BigDecimal.ZERO,
                premiumAssetPriceAtCreated
            )

            updateFields(updateFields, optionOrder.id!!)

        } catch (e: Exception){
            logger.error("Failed to get Pyth price for option order: ${optionOrder.id}", e)
        }
    }

    fun updateOptionOrderPriceInUsdAtSettlement(
        optionOrder: OptionOrder
    ){
        val updateFields = mutableMapOf<String, Any?>()
        // Get Pyth ID
        val orderBidAsset = optionOrder.bidAsset ?: return
        var orderQuoteAsset = optionOrder.quoteAsset
        if(orderQuoteAsset == null){
            orderQuoteAsset = currencyService.getOptionQuoteAsset(optionOrder.chain, null)
            // optionOrder.quoteAsset = orderQuoteAsset
            updateFields[OptionOrder::quoteAsset.name] = orderQuoteAsset
        }

        val bidAssetPythId = currencyService.getPriceId(orderBidAsset)
        val quoteAssetPythId = currencyService.getPriceId(orderQuoteAsset)

        val expiryDate = optionOrder.expiryDate

        try {
            var bidAssetPriceAtSettlement = jasperVaultService.getPythPriceAtDate(
                bidAssetPythId,
                expiryDate
            )

            if(bidAssetPriceAtSettlement == null){

                if(optionOrder.marketPriceAtSettlement != null){
                    bidAssetPriceAtSettlement = BigDecimal(optionOrder.marketPriceAtSettlement)
                }
            }

            var quoteAssetPriceAtSettlement = jasperVaultService.getPythPriceAtDate(
                quoteAssetPythId,
                expiryDate
            )

            if(optionOrder.quoteAssetPriceInUsdAtCreated != null){
                quoteAssetPriceAtSettlement = optionOrder.quoteAssetPriceInUsdAtCreated
            }

            if(bidAssetPriceAtSettlement == null || quoteAssetPriceAtSettlement == null){
                logger.warn("Can not get asset price at settlement ${optionOrder.id}, ${bidAssetPriceAtSettlement}, ${quoteAssetPriceAtSettlement}")
                return
            }

            updateFields[OptionOrder::bidAssetPriceInUsdAtSettlement.name] = bidAssetPriceAtSettlement
            updateFields[OptionOrder::quoteAssetPriceInUsdAtSettlement.name] = quoteAssetPriceAtSettlement

            val buyerProfit = optionOrder.buyerProfit ?: BigDecimal.ZERO
            if(buyerProfit > BigDecimal.ZERO){

                if(optionOrder.direction == OptionDirection.CALL){

                    // optionOrder.buyerProfitInUsd = calculateAssetValue(
                    //     optionOrder.chain,
                    //     orderBidAsset,
                    //     buyerProfit,
                    //     bidAssetPriceAtSettlement!!
                    // )
                    updateFields[OptionOrder::buyerProfitInUsd.name] = calculateAssetValue(
                        optionOrder.chain,
                        orderBidAsset,
                        buyerProfit,
                        bidAssetPriceAtSettlement!!
                    )
                } else {

                    // optionOrder.buyerProfitInUsd = calculateAssetValue(
                    //     optionOrder.chain,
                    //     orderQuoteAsset,
                    //     buyerProfit,
                    //     quoteAssetPriceAtSettlement!!
                    // )
                    updateFields[OptionOrder::buyerProfitInUsd.name] = calculateAssetValue(
                        optionOrder.chain,
                        orderQuoteAsset,
                        buyerProfit,
                        quoteAssetPriceAtSettlement!!
                    )
                }
            } else {
                updateFields[OptionOrder::buyerProfitInUsd.name] = BigDecimal.ZERO
            }

            updateFields(updateFields, optionOrder.id!!)

        } catch (e: Exception){
            logger.error("Failed to get Pyth price for option order: ${optionOrder.id}", e)
        }
    }

    fun calculateOptionOrderSeconds(
        optionOrder: OptionOrder
    ): Long{
        val hours = optionOrder.expiryInHour?.toBigDecimal() ?: BigDecimal("2")
        val seconds = hours.multiply(BigDecimal("3600")).toLong()
        return seconds
    }


    fun getOptionOrderCount(
        addresses: List<String>,
        chain: ChainType? = null,
    ): Long {

        val criteria = Criteria.where(OptionOrder::buyer.name).`in`(addresses)
        if(chain != null){
            criteria.and(OptionOrder::chain.name).`is`(chain)
        }

        val agg = Aggregation.newAggregation(
            Aggregation.match(criteria),
            Aggregation.group().count().`as`("total")
        )

        val groupResults = mongoTemplate.aggregate(agg, OptionOrder::class.java, Map::class.java)
        val result = groupResults.mappedResults.firstOrNull()
        val total = (result?.get("total")?:"0").toString().toLong()

        return total
    }

    fun createSystemOptionOrderRebateRecord(
        optionOrder: OptionOrder
    ){

        if(optionOrder.systemBuyer == null){
            return
        }

        // 是否已经创建了订单
        val existingRecord = systemOptionOrderRebateRecordRepository.findByOptionOrderId(
            optionOrder.id!!
        )

        if(existingRecord != null){
            logger.info("Option Order Service System Option Order Already add rebate record ${optionOrder.buyer} ${optionOrder.id}")
            return
        }

        val record = SystemOptionOrderRebateRecord(
            optionOrderId = optionOrder.id,
            chain = optionOrder.chain,
            txHash = optionOrder.txHash!!,
            buyerAddress = optionOrder.buyer!!,
            direction = optionOrder.direction!!,
            bidAmount = optionOrder.bidAmount!!
        )

        systemOptionOrderRebateRecordRepository.save(record)
    }

    fun checkSystemOptionRebate(
        record: SystemOptionOrderRebateRecord
    ){

        val optionOrder = optionOrderRepository.findByIdOrNull(record.optionOrderId)
        if(optionOrder == null){
            logger.info("Option Order Service system option order can not find order record ${record.buyerAddress} ${record.optionOrderId}")
            return
        }

        if(optionOrder.status == OptionStatus.SETTLE_FAILED){
            logger.info("Option Order Service order settle failed ${optionOrder.buyer} ${optionOrder.id}")
            record.status = SystemOptionOrderRebateRecordStatus.SETTLE_FAILED
            systemOptionOrderRebateRecordRepository.save(record)
            return
        }

        if(optionOrder.status != OptionStatus.SETTLED){
            logger.info("Option Order Service Service Not Settled order ${optionOrder.buyer} ${optionOrder.id}")
            return
        }

        val order = orderRepository.findByIdOrNull(optionOrder.orderId)
        if(order == null){
            logger.debug("Option Order Service can not find order record ${optionOrder.buyer} ${optionOrder.id}")
            return
        }

        record.strikePrice = order.strikePrice.movePointLeft(18)
        record.settlementPrice = BigDecimal(optionOrder.marketPriceAtSettlement!!).movePointLeft(18)

        record.premiumFee = optionOrder.premiumFeePay!!
        record.premiumAsset = Symbol.valueOf(optionOrder.premiumAsset!!.asset)
        record.premiumFeeInUsdt = optionOrder.premiumFeePayInUsdt!!

        record.profit = optionOrder.buyerProfit!!

        if(record.direction == OptionDirection.CALL){
            record.profitAsset = optionOrder.bidAsset!!
        } else {
            record.profitAsset = optionOrder.quoteAsset!!
        }

        // 有盈利，发放给用户
        if(record.profit.compareTo(BigDecimal.ZERO) == 1){
            record.rebateAmount =record.profit
            record.status = SystemOptionOrderRebateRecordStatus.CLAIMED
        } else {
            record.status = SystemOptionOrderRebateRecordStatus.SETTLED
            record.settled = true
        }

        systemOptionOrderRebateRecordRepository.save(record)
    }

    /**
     * 更新OptionOrder指定字段
     * @param updateFields 需要更新的字段和值的Map
     * @param orderId 订单ID
     * @param returnUpdated 是否返回更新后的对象
     * @return 更新后的OptionOrder对象或null
     */
    fun updateFields(
        updateFields: Map<String, Any?>,
        orderId: String,
        returnUpdated: Boolean = false
    ): OptionOrder? {
        // 参数验证
        require(updateFields.isNotEmpty()) { "更新字段不能为空" }
        require(orderId.isNotBlank()) { "订单ID不能为空" }

        return runCatching {
            val query = Query(Criteria.where("id").`is`(orderId))

            // 构建更新对象，过滤掉null值
            val update = Update().apply {
                updateFields
                    .filterValues { it != null }
                    .forEach { (key, value) -> set(key, value) }
            }

            when {
                returnUpdated -> mongoTemplate.findAndModify(
                    query,
                    update,
                    FindAndModifyOptions.options().returnNew(true),
                    OptionOrder::class.java
                )
                else -> {
                    mongoTemplate.updateFirst(query, update, OptionOrder::class.java)
                    null
                }
            }
        }.getOrElse { e ->
            logger.error("更新OptionOrder失败, orderId: $orderId, fields: $updateFields", e)
            throw RuntimeException("更新OptionOrder失败", e)
        }
    }

    fun getOptionOrderProduct(
        channel: UserChannel,
        oo: OptionOrder
    ): OptionOrderProduct {
        var product = OptionOrderProduct.DEGEN
        if(channel == UserChannel.MINI_APP){
            product = OptionOrderProduct.MINI_APP
        } else if(channel == UserChannel.MARKET_PLACE){
            product = OptionOrderProduct.MARKET_PLACE
        } else if(channel == UserChannel.BTC_FI || channel == UserChannel.BTC_FI_BACKEND){
            product = OptionOrderProduct.BTC_FI
        } else if(channel == UserChannel.JASPER_VAULT || channel == UserChannel.JP || channel == UserChannel.JASPER_QUANT){

            if(oo.expiryInHour != "0.5"){
                product = OptionOrderProduct.ZERO_DTE
            }
        }

        return product
    }

    /**
     * 获取系统参数对象
     */
    fun getTaskParameter(): OptionOrderTaskParameter {
        val params = optionOrderTaskParameterRepository.findAll()

        return if (params.isEmpty()) {
            val p = OptionOrderTaskParameter(null)
            optionOrderTaskParameterRepository.save(p)
        } else {
            params.first()
        }
    }

    fun calculateSellerVaultPremium(
        chain: ChainType,
        sellerVault: String
    ): BigDecimal {
        val criteria = Criteria.where(OptionOrder::sellerVault.name).regex("(?i)$sellerVault")
            .and(OptionOrder::status.name).`in`(
                listOf(EXECUTED, SETTLED, SETTLE_TO_BE_CONFIRMED)
            )
            .and(OptionOrder::chain.name).`is`(chain)

        val agg = Aggregation.newAggregation(
            Aggregation.match(criteria),
            Aggregation.group().sum(
                ConvertOperators.valueOf(
                    OptionOrder::sellerPremiumFeeInUsdt.name
                ).convertToDecimal()).`as`("total")
        )
        val groupResults = mongoTemplate.aggregate(agg, OptionOrder::class.java, Map::class.java)
        val result = groupResults.mappedResults.firstOrNull()
        val total = (result?.get("total")?:"0").toString().toBigDecimal()

        val usdtDecimal = currencyService.getCurrencyDecimal(chain, Symbol.USDT)
        return total.movePointLeft(usdtDecimal)
    }

    fun updatePremiumFeeDistribution(
        optionOrder: OptionOrder,
        distribution: EvmService.PremiumFeeDistribution
    ): OptionOrder? {
        val sellerPremiumFeeInUsdt = distribution.sellerPremiumFee?.let {
            calculatePremiumInUSDT(optionOrder, it)
        }
        val platformPremiumFeeInUsdt = distribution.platformPremiumFee?.let {
            calculatePremiumInUSDT(optionOrder, it)
        }

        return updateFields(
            mapOf(
                OptionOrder::sellerPremiumFee.name to distribution.sellerPremiumFee,
                OptionOrder::platformPremiumFee.name to distribution.platformPremiumFee,
                OptionOrder::sellerPremiumFeeInUsdt.name to sellerPremiumFeeInUsdt,
                OptionOrder::platformPremiumFeeInUsdt.name to platformPremiumFeeInUsdt
            ),
            optionOrder.id!!
        )
    }

    /**
     * 更新期权订单的损失金额（以美元计）
     */
    fun updateLossInUsd(optionOrder: OptionOrder): OptionOrder? {
        if (optionOrder.buyerProfitInUsd == null) {
            return null
        }

        val targetOrder = if (optionOrder.premiumFeePayInUsd == null) {
            updatePremiumFeePayInUsd(optionOrder) ?: return null
        } else {
            optionOrder
        }

        val lossInUsd = targetOrder.buyerProfitInUsd!!.subtract(targetOrder.premiumFeePayInUsd)
            .min(BigDecimal.ZERO).abs()
        return updateFields(
            mapOf(OptionOrder::lossInUsd.name to lossInUsd),
            targetOrder.id!!,
            true
        )
    }

    /**
     * 更新期权订单的权利金支付金额（以美元计）
     */
    private fun updatePremiumFeePayInUsd(optionOrder: OptionOrder): OptionOrder? {
        val updateFields = mutableMapOf<String, Any?>()

        val premiumAsset = optionOrder.premiumAsset?.asset ?: "USDT"
        val premiumAssetSymbol = Symbol.valueOf(premiumAsset)
        val premiumAssetPythId = currencyService.getPriceId(premiumAssetSymbol)

        val orderLastSeconds = calculateOptionOrderSeconds(optionOrder)
        val startDate = optionOrder.expiryDate.minusSeconds(orderLastSeconds)

        try {
            val premiumAssetPriceAtCreated = jasperVaultService.getPythPriceAtDate(
                premiumAssetPythId,
                startDate
            ) ?: return null

            val premiumFeePayInUsd = calculateAssetValue(
                optionOrder.chain,
                premiumAssetSymbol,
                optionOrder.premiumFeePay ?: BigDecimal.ZERO,
                premiumAssetPriceAtCreated
            )

            updateFields[OptionOrder::premiumFeePayInUsd.name] = premiumFeePayInUsd
            return updateFields(updateFields, optionOrder.id!!, true)

        } catch (e: Exception) {
            logger.error("Failed to get Pyth price for option order: ${optionOrder.id}", e)
            return null
        }
    }

    /**
     * 更新期权订单的买方利润（以美元计）
     */
    fun updateBuyerProfitInUsd(optionOrder: OptionOrder): OptionOrder? {
        val updateFields = mutableMapOf<String, Any?>()

        val orderBidAsset = optionOrder.bidAsset ?: return null
        var orderQuoteAsset = optionOrder.quoteAsset
        if (orderQuoteAsset == null) {
            orderQuoteAsset = currencyService.getOptionQuoteAsset(optionOrder.chain, null)
            updateFields[OptionOrder::quoteAsset.name] = orderQuoteAsset
        }

        val bidAssetPythId = currencyService.getPriceId(orderBidAsset)
        val quoteAssetPythId = currencyService.getPriceId(orderQuoteAsset)

        try {
            val bidAssetPriceAtSettlement = jasperVaultService.getPythPriceAtDate(
                bidAssetPythId,
                optionOrder.expiryDate
            ) ?: return null

            val quoteAssetPriceAtSettlement = jasperVaultService.getPythPriceAtDate(
                quoteAssetPythId,
                optionOrder.expiryDate
            ) ?: optionOrder.quoteAssetPriceInUsdAtCreated ?: return null

            val buyerProfit = optionOrder.buyerProfit ?: BigDecimal.ZERO
            val buyerProfitInUsd = if (buyerProfit > BigDecimal.ZERO) {
                if (optionOrder.direction == OptionDirection.CALL) {
                    calculateAssetValue(
                        optionOrder.chain,
                        orderBidAsset,
                        buyerProfit,
                        bidAssetPriceAtSettlement
                    )
                } else {
                    calculateAssetValue(
                        optionOrder.chain,
                        orderQuoteAsset,
                        buyerProfit,
                        quoteAssetPriceAtSettlement
                    )
                }
            } else {
                BigDecimal.ZERO
            }

            updateFields[OptionOrder::buyerProfitInUsd.name] = buyerProfitInUsd
            updateFields[OptionOrder::bidAssetPriceInUsdAtSettlement.name] = bidAssetPriceAtSettlement
            updateFields[OptionOrder::quoteAssetPriceInUsdAtSettlement.name] = quoteAssetPriceAtSettlement
            return updateFields(updateFields, optionOrder.id!!, true)

        } catch (e: Exception) {
            logger.error("Failed to get Pyth price for option order: ${optionOrder.id}", e)
            return null
        }
    }
}