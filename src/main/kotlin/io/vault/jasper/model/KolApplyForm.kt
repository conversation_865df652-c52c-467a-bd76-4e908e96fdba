package io.vault.jasper.model

import org.springframework.data.annotation.CreatedDate
import org.springframework.data.annotation.Id
import org.springframework.data.mongodb.core.mapping.Document
import java.time.LocalDateTime

@Document("kol_apply_form")
data class KolApplyForm(
    @Id
    val id: String? = null,
    val wallet: String,
    val twitterHandle: String?,
    val discordId: String?,
    val whatDoYouKnowAboutJasperVault: String,
    var status: KolApplyStatus,
    @CreatedDate
    val created: LocalDateTime = LocalDateTime.now()
) {}

enum class KolApplyStatus {
    SUBMITTED,
    IN_REVIEW,
    APPROVED,
    REJECTED
}
