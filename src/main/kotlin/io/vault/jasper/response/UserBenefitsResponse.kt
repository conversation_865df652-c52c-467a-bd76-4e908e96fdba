package io.vault.jasper.response

import io.swagger.annotations.ApiModel
import io.swagger.annotations.ApiModelProperty
import io.vault.jasper.enums.ChainType
import io.vault.jasper.enums.OptionDirection
import io.vault.jasper.enums.Symbol
import io.vault.jasper.model.OptionStatus

@ApiModel
data class UserBenefitsResponse(

    @ApiModelProperty("是否拥有权益", required = true)
    val freePremiumBenefit: Boolean,

    @ApiModelProperty("已使用过的权益", required = true)
    val usedBenefits: List<String>,

    @ApiModelProperty("未使用的权益", required = true)
    val unusedBenefits: List<String>
)