package io.vault.jasper.service.blockchain

import com.fasterxml.jackson.databind.JsonNode
import com.fasterxml.jackson.databind.ObjectMapper
import io.vault.jasper.blockchain.BlockchainUtilFactory
import io.vault.jasper.blockchain.EvmScanApi
import io.vault.jasper.blockchain.IEvmBlockchainUtil
import io.vault.jasper.enums.ChainType
import io.vault.jasper.enums.OptionDirection
import io.vault.jasper.enums.Symbol
import io.vault.jasper.event.*
import io.vault.jasper.exception.BusinessException
import io.vault.jasper.exception.ResultEnum
import io.vault.jasper.model.*
import io.vault.jasper.repository.*
import io.vault.jasper.service.*
import io.vault.jasper.task.AsyncEvmScanner
import io.vault.jasper.utils.DateTimeUtil
import net.osslabz.evm.abi.decoder.AbiDecoder
import net.osslabz.evm.abi.decoder.DecodedFunctionCall
import org.slf4j.LoggerFactory
import org.springframework.context.ApplicationEventPublisher
import org.springframework.core.io.ClassPathResource
import org.springframework.data.repository.findByIdOrNull
import org.web3j.abi.FunctionEncoder
import org.web3j.abi.FunctionReturnDecoder
import org.web3j.abi.TypeReference
import org.web3j.abi.datatypes.Address
import org.web3j.abi.datatypes.Function
import org.web3j.abi.datatypes.Type
import org.web3j.abi.datatypes.generated.Uint256
import org.web3j.abi.datatypes.generated.Uint8
import org.web3j.crypto.Credentials
import org.web3j.crypto.Keys
import org.web3j.crypto.RawTransaction
import org.web3j.crypto.TransactionEncoder
import org.web3j.protocol.core.DefaultBlockParameter
import org.web3j.protocol.core.DefaultBlockParameterName
import org.web3j.protocol.core.methods.response.EthBlock
import org.web3j.protocol.core.methods.response.EthBlock.TransactionObject
import org.web3j.protocol.core.methods.response.Log
import org.web3j.protocol.core.methods.response.Transaction
import org.web3j.protocol.core.methods.response.TransactionReceipt
import org.web3j.utils.Numeric
import java.math.BigDecimal
import java.math.BigInteger
import java.math.RoundingMode
import java.time.LocalDateTime
import java.time.ZoneId
import java.util.concurrent.Future


abstract class EvmService(
    val evmUtil: IEvmBlockchainUtil,
    private val blockchainRepository: BlockchainRepository,
    private val asyncEvmScanner: AsyncEvmScanner,
    private val orderRepository: OrderRepository,
    private val optionOrderRepository: OptionOrderRepository,
    private val optionOrderInfoRepository: OptionOrderInfoRepository,
    private val jasperVaultService: JasperVaultService,
    private val eventPublisher: ApplicationEventPublisher,
    private val lpVaultRepository: LPVaultRepository,
    private val contractEventRepository: ContractEventRepository,
    private val currencyService: CurrencyService,
    private val optionOrderService: OptionOrderService,
    private val blockchainUtilFactory: BlockchainUtilFactory,
    private val scannerApi: EvmScanApi,
    private val subgraphService: SubgraphService,
    private val historyService: HistoryService,
    private val miniBridgeSwapOrderRepository: MiniBridgeSwapOrderRepository,
    private val alertService: AlertService
) : IBlockchainService {

    // 在类的顶部添加常量
    companion object {
        private const val PLATFORM_PREMIUM_ADDRESS = "0xd79e0b8892d5f27307ac8939f9bb998de3adec3c"
    }

    private var logger = LoggerFactory.getLogger(this::class.java)

    protected abstract val vaultFactoryContract: String

    protected abstract val issuanceModuleContract: String

    abstract val addOrderLogAddress: String

    val emptyAddress = "0x0000000000000000000000000000000000000000"

    // todo 实现流程写入Service里，本属性改为protected
    abstract val optionServiceContract: String

    open val lpVaultContract: String? = null

    val callOptionFnHash = "0x063a2c1431ff3d5b9f5f7ec47762e1e1b36050db33322d8c68dd0aaa4bc65c52"

    val putOptionFnHash = "0xa909be4f8aaa525133fd9d1c04763c9ee0e47b3262d62338a18147f745164794"

    val usdtTransferFnHash = "0xddf252ad1be2c89b69c2b068fc378daa952ba7f163c4a11628f55a4df523b3ef"

    open val liquidityEventHash = "0xabd20c9a288c74637de97fa697977b97134d9a0250c5c86d503e3585915a0ef9"

    /**
     * LP vault合约的Issue方法/事件 hash
     */
    val lpVaultIssueEventHash = "0x65adeb76912378393e600cb6f64f3310842a42e1eae86273dc775d0c47a0f2dc"

    /**
     * LP vault合约的Deposit方法/事件 hash
     */
    val lpVaultDepositEventHash = "0x90890809c654f11d6e72a28fa60149770a0d11ec6c92319d6ceb2bb0a4ea1a15"

    /**
     * LP vault合约的Withdraw方法/事件 hash
     */
    val lpVaultWithdrawEventHash = "0xf279e6a1f5e320cca91135676d9cb6e44ca8a08c0b88342bcdb1144f6511b568"

    val transferSingleFnHash = "0xc3d58168c5ae7397731d063d5bbf3d657854427343f4c083240f7aacaa2d0f62"

    val priceFeedUpdateFnHash = "0xd06a6b7f4918494b3719217d1802786c1f5112a6c1d88fe2cfec00b4584f6aec"

    val entryPointAddress = "******************************************"

    /**
     * Redeem Method ID
     */
    val redeemMethodId = "0xd97cb0a3"

    val redeemTopic = "0x69a69a4c14f47079a7068cc82e2b27a5a631f82bb294fea8c3ef1fc7e4b2e42e"

    val omFileIs = ClassPathResource("abi/OptionModuleV2.json").inputStream
    val omFileIsNew = ClassPathResource("abi/OptionModuleV2_NEW.json").inputStream
    val omFileIsV4 = ClassPathResource("abi/OptionModuleV4.json").inputStream
    val vaultFileIs = ClassPathResource("abi/Vault.json").inputStream
    val epFileIs = ClassPathResource("abi/EntryPoint.json").inputStream
    val osFileIs = ClassPathResource("abi/OptionService.json").inputStream
    val usdtFileIs = ClassPathResource("abi/USDT.json").inputStream
    val pythPriceOracleFileIs = ClassPathResource("abi/PythPriceOracle.json").inputStream

    val entryPointAbi = AbiDecoder(epFileIs)
    val executeBatchAbi = AbiDecoder(vaultFileIs)
    val optionModuleAbi = AbiDecoder(omFileIs)
    val optionModuleAbiNew = AbiDecoder(omFileIsNew)
    val optionModuleAbiV4 = AbiDecoder(omFileIsV4)
    val optionServiceAbi = AbiDecoder(osFileIs)
    val usdtAbi = AbiDecoder(usdtFileIs)
    val pythPriceOracleAbi = AbiDecoder(pythPriceOracleFileIs)

    val chainType: ChainType
        get() {
            val input = this::class.simpleName!!
            val regex = Regex("(.*)(?=Service)")
            val matchResult = regex.find(input)

            val chainName = matchResult?.value ?: throw BusinessException(ResultEnum.CHAIN_NOT_FOUND)
            // chainName的值为BitlayerTest，需要转换为BITLAYER_TEST
            val chainName2 = chainName.replace(Regex("([a-z])([A-Z])"), "$1_$2").uppercase()

            return try {
                ChainType.valueOf(chainName2.uppercase())
            } catch (e: Exception) {
                throw BusinessException(ResultEnum.CHAIN_NOT_FOUND)
            }
        }

    /**
     * 重置数据库中记录的最后扫描区块高度，改为当前最新区块高度
     */
    fun resetLastScanBlockHeight() {
        val blockchain = blockchainRepository.findFirstByChain(chainType)
        if (blockchain != null) {
            val blockNumber = try {
                evmUtil.web3j.ethBlockNumber().send().blockNumber.toLong()
            } catch (e: Exception) {
                return
            }
            blockchain.blockNumber = blockNumber
            blockchainRepository.save(blockchain)
        }
    }

    fun getVaultAddress(address: String): String {
        val inputs: List<Type<*>> = listOf(
            Address(address),
            Uint256(1)
        )
        val outputs: MutableList<TypeReference<*>> = ArrayList()
        val typeReference: TypeReference<Address> = object : TypeReference<Address>() {}
        outputs.add(typeReference)
        val callResult = evmUtil.ethCall("getAddress", vaultFactoryContract, inputs, outputs)
            ?: throw Exception("Failed to get address")

        return callResult.first().value as String
    }

    fun getTotalClaimInfo(
        address: String,
        contractAddress: String
    ): BigInteger {
        val inputs: List<Type<*>> = listOf(
            Address(address)
        )
        val outputs: MutableList<TypeReference<*>> = ArrayList()
        val typeReference: TypeReference<Uint256> = object : TypeReference<Uint256>() {}
        outputs.add(typeReference)
        val callResult = evmUtil.ethCall("totalClaimInfo", contractAddress, inputs, outputs)
            ?: throw Exception("Failed to get total claim info")

        return callResult.first().value as BigInteger
    }

    data class OptionOrderStruct(
        val orderId: BigInteger,
        val optionType: OptionDirection,
        val holderVault: String,
        val liquidateMode: BigInteger,
        val writerVault: String,
        val underlyingAssetType: BigInteger?,
        val underlyingAssetAddress: String,
        val underlyingAmount: BigInteger,
        val strikeAssetAddress: String,
        val strikeAmount: BigInteger,
        val expirationDateTimestamp: BigInteger,
        val lockDateTimestamp: BigInteger,
        val holderEoaWallet: String,
        val writerEoaWallet: String,
        val quantity: BigInteger?, // =BidAmount(18位)
        /**
         * holder的AA vault
         */
        val recipient: String?,
        val lockAssetAddress: String
    )

    private fun createOptionOrderStruct(
        log: Log
    ): OptionOrderStruct? {
        val address = log.address
        val topics = log.topics

        //logger.info("Debug 区块扫描保存订单: txHash $debugInfo Log: Address: $address\\tTopics: $topics  add order log address: $addOrderLogAddress")

        // logger.info("Sync Pending orders : Log: Address: $address\tTopics: $topics")
        if (address.compareTo(addOrderLogAddress, true) != 0) return null

        // logger.info("Sync Pending orders : This log is AddOrder log, address=$address")
        val function = topics.firstOrNull() ?: return null
        val optionType = when {
            function.compareTo(callOptionFnHash, true) == 0 -> OptionDirection.CALL
            function.compareTo(putOptionFnHash, true) == 0 -> OptionDirection.PUT
            else -> return null
        }

        //logger.info("Debug 区块扫描保存订单: This log is Add${optionType}Order log, function=$function")
        // logger.info("Sync Pending orders : This log is Add${optionType}Order log, function=$function")
        val data = log.data
        val dataNoPrefix = Numeric.cleanHexPrefix(data)
        val chunkSize = 64
        val chunks = dataNoPrefix.chunked(chunkSize)
        val onChainOrderId = Numeric.toBigIntNoPrefix(chunks[0])
        //val quantity = subgraphService.getOrderById(chainType, onChainOrderId.toString())?.get("quantity")?.asText()?.toBigInteger()
        val optionOrderStruct = OptionOrderStruct(
            orderId = onChainOrderId,
            optionType = optionType,
            holderVault = Keys.toChecksumAddress(Address(chunks[1]).toString()), // holder = buyer
            liquidateMode = Numeric.toBigIntNoPrefix(chunks[2]), // 清算模式
            writerVault = Keys.toChecksumAddress(Address(chunks[3]).toString()), // writer = seller
            underlyingAssetType = null,
            underlyingAssetAddress = Keys.toChecksumAddress(Address(chunks[7]).toString()), // 抵押资产地址
            strikeAssetAddress = Keys.toChecksumAddress(Address(chunks[8]).toString()), // 行权资产地址
            underlyingAmount = Numeric.toBigIntNoPrefix(chunks[9]), // 抵押资产数量
            strikeAmount = Numeric.toBigIntNoPrefix(chunks[10]), // 行权资产数量
            expirationDateTimestamp = Numeric.toBigIntNoPrefix(chunks[11]), // 到期时间戳(秒)
            lockDateTimestamp = Numeric.toBigIntNoPrefix(chunks[12]), // 锁定时间戳(秒)
            holderEoaWallet = Keys.toChecksumAddress(Address(chunks[15]).toString()), // 买家EOA钱包
            writerEoaWallet = Keys.toChecksumAddress(Address(chunks[16]).toString()), // 卖家EOA钱包
            quantity = null,
            recipient = Keys.toChecksumAddress(Address(chunks[5]).toString()),
            lockAssetAddress = Keys.toChecksumAddress(Address(chunks[6]).toString())
        )
        // logger.info("Sync Pending orders : OptionOrderStruct: $optionOrderStruct")

        return optionOrderStruct
    }

    data class TransferStruct(
        val contractAddress: String,
        val from: String,
        val to: String,
        val amount: BigInteger
    )

    data class OptionPremiumStruct(
        val optionType: String,
        val orderId: BigInteger,
        val writer: String,
        val holder: String,
        val premiumAssetAddress: String,
        val premiumAmount: BigInteger,
        val quantity: BigDecimal,
        val strikePrice: BigDecimal,
        val freePremiumAmount: BigInteger,
        val limitOrder: Boolean = false
    )

    fun getOptionOrderDetail(
        txHash: String,
        confirmCount: Int? = null,
        newBlockHeight: BigInteger? = null,
        optionStruct: OptionOrderStruct? = null,
        txReceipt: TransactionReceipt? = null
    ): Pair<OptionOrderStruct?, OptionPremiumStruct?> {

        var receipt = txReceipt

        if(receipt == null) {
            receipt = try {
                evmUtil.web3j.ethGetTransactionReceipt(txHash).sendAsync().get().transactionReceipt.get()
            } catch (e: Exception) {
                // logger.info("Sync Pending orders : Get Option Order Receipt Failed")
                logger.warn(e.message, e)
                throw BusinessException(ResultEnum.GET_TRANSACTION_FAILED)
            }
        }

        val customLastBH = newBlockHeight ?: evmUtil.web3j.ethBlockNumber().send().blockNumber
        val blockchain = blockchainRepository.findFirstByChain(chainType)
            ?: throw BusinessException(ResultEnum.CHAIN_NOT_FOUND)
        val customConfirmCount = (confirmCount ?: blockchain.confirmCount).toBigInteger()
        if (customLastBH - receipt!!.blockNumber < customConfirmCount)
            throw BusinessException(ResultEnum.TRANSACTION_NOT_CONFIRMED)
        if (receipt.status != "0x1") {
            alertService.warning("[出现Error交易] Tx=$txHash in ${chainType.name} is Error")
            throw BusinessException(ResultEnum.TRANSACTION_RECEIPT_FAILED)
        }

        var optionOrderStruct = optionStruct
        var optionPremiumStruct: OptionPremiumStruct? = null

        for (log in receipt.logs) {
            if (optionOrderStruct == null) {
                optionOrderStruct = createOptionOrderStruct(log)
                if (optionOrderStruct != null) continue
            }
            if (optionPremiumStruct == null) {
                optionPremiumStruct = createOptionPremiumFromLog(log)
                if (optionPremiumStruct != null) continue
            }
        }

        return Pair(optionOrderStruct, optionPremiumStruct)
    }

    private fun matchAndUpdateOptionOrder(
        optionStruct: OptionOrderStruct,
        expirationLocalDatetime: LocalDateTime,
        txHash: String,
        optionOrder: OptionOrder? = null
    ): OptionOrder? {
        // 匹配的订单
        val matchOptionsOrder = findMatchOptionOrderByStruct(
            optionStruct,
            expirationLocalDatetime,
            txHash,
            optionOrder
        ) ?: return null
        val updateFields = mutableMapOf<String, Any?>()
        if (matchOptionsOrder.status.notExecuted()) {
            updateFields[OptionOrder::status.name] = OptionStatus.EXECUTED
            updateFields[OptionOrder::txHash.name] = txHash
        }
        updateFields[OptionOrder::onChainOrderId.name] = optionStruct.orderId.toString()
        updateFields[OptionOrder::buyerVault.name] = optionStruct.holderVault
        updateFields[OptionOrder::seller.name] = optionStruct.writerEoaWallet
        updateFields[OptionOrder::sellerVault.name] = optionStruct.writerVault

        val updatedOptionOrder =
            optionOrderService.updateFields(updateFields, matchOptionsOrder.id!!, returnUpdated = true)

        // 修改挂单状态
        val order = orderRepository.findByIdOrNull(matchOptionsOrder.orderId) ?: run {
            logger.error("Order = ${matchOptionsOrder.orderId} not found")
            throw BusinessException(ResultEnum.PENDING_ORDER_NOT_FOUND)
        }
        order.status = OrderStatus.PENDING_SETTLEMENT
        orderRepository.save(order)

        return updatedOptionOrder
    }

    /**
     * 检查PriceFeedUpdate方法，topics 0
     * topics[1]为strikeAsset的pyth id
     * data[1]为价格（美元），需要移动小数点，具体检查token的decimals
     */
    fun getStrikePriceFromLog(txid: String, bidCurrency: Currency): BigDecimal? {
        val currencyPythId = bidCurrency.pythId ?: return null
        val receipt = try {
            evmUtil.web3j.ethGetTransactionReceipt(txid).send().transactionReceipt.get()
        } catch (e: Exception) {
            logger.warn(e.message, e)
            return null
        }

        return receipt.logs.firstOrNull {
            val fnHash = it.topics.firstOrNull()
            val pythId = try {
                it.topics[1]
            } catch (e: Exception) {
                return@firstOrNull false
            }
            fnHash == priceFeedUpdateFnHash && pythId == currencyPythId
        }?.let { log ->
            val dataList = log.data.substring(2).chunked(64)
            val price = try {
                Numeric.toBigInt(dataList[1])
            } catch (e: Exception) {
                return@let null
            }

            price.toBigDecimal().movePointLeft(bidCurrency.pythDecimals)
        }?.movePointRight(18)
    }

    /**
     * 根据合约结构体创建期权订单
     */
    @Synchronized
    fun createOptionOrder(
        txHash: String,
        optionStruct: OptionOrderStruct,
        optionPremium: OptionPremiumStruct,
        txReceipt: TransactionReceipt? = null,
        txBlock: EthBlock.Block? = null
    ): OptionOrder {
        val defaultOrderType = OrderType.DEGEN
        val expirationLocalDatetime = DateTimeUtil.convertTimestampToLocalDateTime(
            optionStruct.expirationDateTimestamp.toLong()
        ) // 合约到期时间
        val lockDateLocalDateTime = DateTimeUtil.convertTimestampToLocalDateTime(
            optionStruct.lockDateTimestamp.toLong()
        ) // 合约锁定时间

        // 计算expiryInHour
        var block = txBlock
        if(block == null){
            block = try {
                val transaction = evmUtil.web3j.ethGetTransactionByHash(txHash).send().transaction.get()
                evmUtil.web3j.ethGetBlockByHash(transaction.blockHash, false).send().block
            } catch (e: Exception) {
                logger.warn(e.message, e)
                throw BusinessException(ResultEnum.GET_BLOCK_FAILED)
            }
        }

        val blockTimestamp = block!!.timestamp.toLong()
        val expiryTimestamp = DateTimeUtil.convertLocalDateTimeToTimestamp(expirationLocalDatetime) / 1000
        //logger.info("BlockTimestamp: $blockTimestamp, ExpirationDate: $expiryTimestamp," +
        //        " Diff=${expiryTimestamp - blockTimestamp}")
        val diff = expiryTimestamp - blockTimestamp
        val hour = diff / 3600
        val seconds = diff % 3600
        val eih = when {
            seconds in 1..1800 -> hour.toDouble() + 0.5
            seconds > 1800 -> (hour + 1).toDouble()
            else -> hour.toDouble()
        }

        val expiryInHour = eih.toString().toBigDecimal().stripTrailingZeros().toPlainString()

        // 需要查找 quantity, strike price, premium asset, premium amount, free premium amount
        //logger.info("Debug 区块扫描保存订单 $txHash ExpiryInHour: $expiryInHour")
        // underlyAsset 即 bidAsset
        val underlyingAssetCurrency =
            currencyService.getCurrencyByAddressAndChain(optionStruct.underlyingAssetAddress, chainType)
            ?: throw BusinessException(ResultEnum.UNDERLYING_ASSET_NOT_FOUND)

        val strikeAssetCurrency =
            currencyService.getCurrencyByAddressAndChain(optionStruct.strikeAssetAddress, chainType)
            ?: throw BusinessException(ResultEnum.STRIKE_ASSET_NOT_FOUND)
        val strikePrice = optionPremium.strikePrice

        //logger.info("Debug 区块扫描保存订单 $txHash Get Strike Price: $strikePrice")
        // 查找并计算bid_amount
        val bidAsset = Symbol.valueOf(underlyingAssetCurrency.symbol)
        val bidDecimals = currencyService.getCurrencyDecimal(chainType, bidAsset)

        val bidAmount = optionPremium.quantity
        val totalAmount = bidAmount.movePointRight(bidDecimals).setScale(0, RoundingMode.FLOOR)

        //logger.info("Debug 区块扫描保存订单 $txHash Get Bid Amount: $bidAmount")

        val premiumCurrency =
            currencyService.getCurrencyByAddressAndChain(optionPremium.premiumAssetAddress, chainType)
                ?: throw BusinessException(ResultEnum.PREMIUM_ASSET_NOT_FOUND)

        val availablePremiumAssets = PremiumAsset(premiumCurrency.symbol, optionPremium.premiumAssetAddress)
        val premiumAmount = optionPremium.premiumAmount.toBigDecimal()

        //logger.info("Debug 区块扫描保存订单 $txHash Get Premium Amount : $premiumAmount")

        // 从 struct 查找 quote asset
        var quoteAssetAddress = optionStruct.strikeAssetAddress
        if(optionStruct.optionType == OptionDirection.PUT){
            quoteAssetAddress = optionStruct.lockAssetAddress
        }

        val quoteAssetSymbolString = currencyService.getCurrencyByAddressAndChain(quoteAssetAddress, chainType)?.symbol
        var quoteAssetSymbol: Symbol? = null

        if(quoteAssetSymbolString == null){
            quoteAssetSymbol = currencyService.getOptionQuoteAsset(chainType, bidAsset)
            quoteAssetAddress = currencyService.getAddressByChainAndSymbol(quoteAssetSymbol, chainType)!!
        } else {
            quoteAssetSymbol = Symbol.valueOf(quoteAssetSymbolString)
        }

        optionOrderRepository.existsByChainAndTxHashAndOnChainOrderId(
            chainType,
            txHash,
            optionStruct.orderId.toString()
        ).let {
            if (it) throw BusinessException(ResultEnum.ORDER_ALREADY_EXISTS) // 不重复插入数据
        }
        // 创建挂单
        var newOrder = orderRepository.save(Order(
            chain = chainType,
            orderType = defaultOrderType,
            creator = optionStruct.holderEoaWallet,
            creatorRole = CreatorRole.BUYER,
            optionsType = optionStruct.optionType,
            expiryInHour = expiryInHour,
            expiryDate = expirationLocalDatetime,
            underlyingAsset = Symbol.valueOf(underlyingAssetCurrency.symbol),
            underlyingAssetAddress = optionStruct.underlyingAssetAddress,
            totalAmount = totalAmount,
            strikeAsset = Symbol.valueOf(strikeAssetCurrency.symbol),
            strikePrice = strikePrice,
            availablePremiumAssets = listOf(availablePremiumAssets),
            premiumFee = premiumAmount,
            settlementTypes = listOf(SettlementType.CASH_SETTLEMENT, SettlementType.ASSET_DELIVERY),
            amount = totalAmount,
            bidAsset = bidAsset,
            bidAmount = bidAmount,
            status = OrderStatus.PENDING_SETTLEMENT,
            quoteAsset = quoteAssetSymbol,
            quoteAssetAddress = quoteAssetAddress
        ))
        // 计算订单的成交量
        val volume = optionOrderService.calculateOrderVolume(newOrder)
        newOrder.volume = volume
        newOrder = orderRepository.save(newOrder)

        // 创建期权订单
        var oOrder = optionOrderRepository.save(OptionOrder(
            chain = newOrder.chain,
            orderId = newOrder.id!!,
            onChainOrderId = optionStruct.orderId.toString(),
            orderType = newOrder.orderType,
            direction = optionStruct.optionType,
            underlyingAsset = Symbol.valueOf(underlyingAssetCurrency.symbol),
            underlyingAssetAddress = optionStruct.underlyingAssetAddress,
            buyer = optionStruct.holderEoaWallet,
            buyerVault = optionStruct.holderVault,
            buyerVaultSalt = null,
            seller = optionStruct.writerEoaWallet,
            sellerVault = optionStruct.writerVault,
            amount = optionStruct.underlyingAmount.toBigDecimal(),
            strikeAsset = Symbol.valueOf(strikeAssetCurrency.symbol),
            strikeAssetAddress = optionStruct.strikeAssetAddress,
            strikeAmount = optionStruct.strikeAmount.toBigDecimal(),
            liquidateMode = optionStruct.liquidateMode.toString(),
            txHash = txHash,
            expiryInHour = newOrder.expiryInHour,
            expiryDate = expirationLocalDatetime,
            lockDate = lockDateLocalDateTime,
            status = OptionStatus.EXECUTED,
            jvault = true,
            actualStrikeAmount = optionStruct.strikeAmount.toBigDecimal(),
            bidAsset = newOrder.bidAsset,
            bidAmount = newOrder.bidAmount,
            premiumAsset = newOrder.availablePremiumAssets.first(),
            premiumFeePay = newOrder.premiumFee,
            premiumFeeInfo = null,
            quoteAsset = newOrder.quoteAsset,
            quoteAssetAddress = newOrder.quoteAssetAddress,
            strikePrice = newOrder.strikePrice,
            limitOrder = optionPremium.limitOrder,
            volume = volume
        ))

        //logger.info("Debug 区块扫描保存订单 $txHash save option order : ${oOrder.id!!}")
        try {

            val premiumFeeShouldPay = BigDecimal(optionPremium.premiumAmount)
            val freePremiumAmount = BigDecimal(optionPremium.freePremiumAmount)
            val userActualPremiumPay = premiumFeeShouldPay - freePremiumAmount

            oOrder.premiumAsset = availablePremiumAssets
            oOrder.premiumFeeShouldPay = premiumFeeShouldPay
            oOrder.premiumFeeShouldPayInUsdt = optionOrderService.calculatePremiumInUSDT(oOrder, premiumFeeShouldPay)
            oOrder.premiumFeePay = userActualPremiumPay
            oOrder.premiumFeePayInUsdt = optionOrderService.calculatePremiumInUSDT(oOrder, userActualPremiumPay)

            oOrder = optionOrderRepository.save(oOrder)

            newOrder.availablePremiumAssets = listOf(availablePremiumAssets)
            newOrder.premiumFee = premiumFeeShouldPay
            orderRepository.save(newOrder)

            historyService.addPremiumFee(oOrder, this)

        } catch (e: Exception) {
            oOrder.premiumFeeLog = e.message
            oOrder = optionOrderRepository.save(oOrder)
        }
        logger.info("Create OptionOrder: ${oOrder.id} ${oOrder.txHash}")

        return oOrder
    }

    private fun getInternalTxListWithHash(txHash: String, times: Int = 1, waitMilSec: Long = 5000): List<Map<String, Any?>> {
        if (times <= 0) throw Exception("Get Internal Tx List Failed with hash=$txHash")
        val scanApi = blockchainUtilFactory.getScanApi(chainType)

        return try {
            scanApi.getInternalTransactionsByTxHash(txHash)
        } catch (e: Exception) {
            logger.warn(e.message)
            Thread.sleep(waitMilSec)
            getInternalTxListWithHash(txHash, times - 1)
        }
    }

    fun saveOptionOrderFromTxHash(
        txHash: String,
        optionOrder: OptionOrder? = null,
        lastBlockHeight: BigInteger? = null,
        newVersion: Boolean = false,
        createdTime: LocalDateTime? = null,
        optionStruct: OptionOrderStruct? = null,
        block: EthBlock.Block? = null
    ) {

        //logger.info("Debug 区块扫描保存订单: $txHash begin")
        val receipt = try {
            evmUtil.web3j.ethGetTransactionReceipt(txHash).sendAsync().get().transactionReceipt.get()
        } catch (e: Exception) {
            //logger.info("Sync Pending orders : Get Option Order Receipt Failed")
            logger.warn(e.message, e)
            throw BusinessException(ResultEnum.GET_TRANSACTION_FAILED)
        }

        val blockchain = blockchainRepository.findFirstByChain(chainType)
        val confirmCount = if (newVersion) 0 else blockchain?.confirmCount
        // val optionStructList = getOptionOrderFromHash(evmUtil.web3j, txHash, lastBlockHeight, confirmCount)
        val (ooStruct, optionPremium) = getOptionOrderDetail(
            txHash,
            confirmCount = confirmCount,
            newBlockHeight = lastBlockHeight,
            optionStruct = optionStruct,
            txReceipt = receipt
        )
        if (ooStruct == null){
            //logger.info("Debug 区块扫描保存订单: $txHash 无法获取 ooStruct， skip")
            return
        }

        if(optionPremium == null){
            //logger.info("Debug 区块扫描保存订单: $txHash 无法获取 optionPremium， skip")
            return
        }

        //logger.info("Debug 区块扫描保存订单: $txHash 获取 ooStruct")

        val expirationLocalDatetime = DateTimeUtil.convertTimestampToLocalDateTime(
            ooStruct.expirationDateTimestamp.toLong()
        ) // 合约到期时间
        val matchOptionsOrder = when {
            optionOrder != null -> {
                matchAndUpdateOptionOrder(
                    ooStruct,
                    expirationLocalDatetime,
                    txHash,
                    optionOrder
                ) ?: return
            }
            else -> createOptionOrder(
                txHash,
                ooStruct,
                optionPremium,
                txReceipt = receipt,
                txBlock = block
            )
        }

        //logger.info("Debug 区块扫描保存订单: $txHash end")

        // 发布订单创建成功事件通知
        eventPublisher.publishEvent(AfterCreateOptionOrderEvent(this, matchOptionsOrder.id!!))
        // 更新订单权利金信息
        eventPublisher.publishEvent(UpdatePremiumFeeEvent(this, matchOptionsOrder.id))

        // 事件通知
        if (matchOptionsOrder.status == OptionStatus.EXECUTED) {
            eventPublisher.publishEvent(OptionExecutedEvent(this, matchOptionsOrder.id))
        }

        val existingOrderIds = mutableListOf(ooStruct.orderId)

        while(true) {

            // 可能有多张订单，继续扫描
            val (ooStruct2, optionPremium2) = getOptionOrderDetailExceptOrderIds(
                txHash,
                existingOrderIds,
                confirmCount = confirmCount,
                newBlockHeight = lastBlockHeight,
                txReceipt = receipt
            )

            if (ooStruct2 == null || optionPremium2 == null) {
                //logger.info("Debug 区块扫描保存订单: $txHash 无法获取 ooStruct2或optionPremium2, skip")

                // 没有第二张订单，不是宝石类订单，可以检查是否btr campaign订单
                eventPublisher.publishEvent(CheckBtrCampaignOrderEvent(this, matchOptionsOrder.id))
                return
            }

            logger.info("区块扫描: $txHash 第 ${existingOrderIds.size + 1} 张订单, 获取 ooStruct2")

            val matchOptionsOrder2 = createOptionOrder(
                txHash,
                ooStruct2,
                optionPremium2,
                txReceipt = receipt,
                txBlock = block
            )

            //logger.info("Debug 区块扫描保存订单: $txHash end")

            // 发布订单创建成功事件通知
            eventPublisher.publishEvent(AfterCreateOptionOrderEvent(this, matchOptionsOrder2.id!!))
            // 更新订单权利金信息
            eventPublisher.publishEvent(UpdatePremiumFeeEvent(this, matchOptionsOrder2.id))

            // 事件通知
            if (matchOptionsOrder.status == OptionStatus.EXECUTED) {
                eventPublisher.publishEvent(OptionExecutedEvent(this, matchOptionsOrder2.id))
            }

            existingOrderIds.add(matchOptionsOrder.onChainOrderId!!.toBigInteger())
        }
    }

    private fun findMatchOptionOrderByStruct(
        optionStruct: OptionOrderStruct,
        expirationLocalDatetime: LocalDateTime,
        txHash: String,
        optionOrder: OptionOrder? = null,
    ): OptionOrder? {

        logger.info("Find Match Option Order By Struct: $optionStruct")
        logger.info("Find Match Option Order By Struct expirationLocalDatetime: $expirationLocalDatetime")
        logger.info("Find Match Option Order By Struct txHash: $txHash")
        logger.info("Find Match Option Order By Struct optionOrder: $optionOrder")

        if(optionOrder != null){

            if(optionOrder.chain == chainType &&
                optionOrder.direction == optionStruct.optionType &&
                optionOrder.buyer == optionStruct.holderEoaWallet &&
                optionOrder.buyerVault == optionStruct.holderVault &&
                optionOrder.strikeAssetAddress == optionStruct.strikeAssetAddress &&
                optionOrder.liquidateMode == optionStruct.liquidateMode.toString() &&
                optionOrder.expiryDate == expirationLocalDatetime &&
                (optionOrder.status == OptionStatus.PENDING || optionOrder.status == OptionStatus.TO_BE_CONFIRMED)){

                return optionOrder
            }
        }

        val optionsOrders =
            optionOrderRepository.findByChainAndDirectionAndBuyerAndBuyerVaultAndStrikeAssetAddressAndLiquidateModeAndExpiryDateAndStatus(
                chainType,
                optionStruct.optionType,
                optionStruct.holderEoaWallet,
                optionStruct.holderVault,
                optionStruct.strikeAssetAddress,
                optionStruct.liquidateMode.toString(),
                expirationLocalDatetime,
                OptionStatus.PENDING
            )
        val filterOrder = optionsOrders.firstOrNull()
        if (filterOrder != null) {
            optionsOrders.drop(1).forEach { droppedOptionOrder ->
                // 旧的相匹配的挂单，标记为取消
                optionOrderService.updateFields(
                    mapOf(
                        OptionOrder::status.name to OptionStatus.CANCELLED,
                        OptionOrder::txHash.name to txHash,
                        OptionOrder::errorMsg.name to "This is older order, the newest matched order is ${filterOrder.id}"
                    ),
                    droppedOptionOrder.id!!
                )
            }
        }
        return filterOrder
    }

    data class LiquidateOptionData(
        val orderType: Int,
        val orderId: BigInteger,
        val type: Int,
        val incomeAmount: BigInteger,
        val slippage: BigInteger
    )

    @Synchronized
    fun saveOptionSettleFromTxHash(hash: String) {
        val receipt = try {
            evmUtil.web3j.ethGetTransactionReceipt(hash).sendAsync().get().transactionReceipt.get()
        } catch (e: Exception) {
            logger.error("Failed to get transaction(${hash}) receipt: ${e.message}", e)
            return
        }
        if (receipt.status != "0x1") {
            logger.info("Transaction(${hash}) failed")
            return
        }
        receipt.logs.forEach { log ->
            if (log.address.compareTo(optionServiceContract,true) != 0) return@forEach
            val fnHash = log.topics.firstOrNull() ?: return@forEach
            if (fnHash.compareTo(liquidityEventHash, true) != 0) return@forEach
            val dataNoPrefix = Numeric.cleanHexPrefix(log.data)
            val chunkSize = 64
            val chunks = dataNoPrefix.chunked(chunkSize)
            // if (chunks.size != 5) return@forEach
            val liquidateOptionData = LiquidateOptionData(
                orderType = Numeric.toBigIntNoPrefix(chunks[0]).toInt(),
                orderId = Numeric.toBigIntNoPrefix(chunks[1]),
                type = Numeric.toBigIntNoPrefix(chunks[2]).toInt(),
                incomeAmount = Numeric.toBigIntNoPrefix(chunks[3]),
                slippage = Numeric.toBigIntNoPrefix(chunks[4])
            )
            optionOrderRepository.findFirstByChainAndOnChainOrderId(chainType, liquidateOptionData.orderId.toString())
                ?.let { oo ->
                    if (oo.status == OptionStatus.SETTLED || oo.settlementHash != null) return@forEach
                    val updateFields = mutableMapOf<String, Any?>()
                    updateFields[OptionOrder::status.name] = OptionStatus.SETTLED
                    updateFields[OptionOrder::settlementHash.name] = hash
                    updateFields[OptionOrder::settlementTime.name] = kotlin.run {
                        val block = try {
                            evmUtil.web3j.ethGetBlockByHash(receipt.blockHash, false).send().block
                        } catch (e: Exception) {
                            logger.error("Failed to get block by hash: ${receipt.blockHash}", e)
                            return@run null
                        }
                        DateTimeUtil.convertTimestampToLocalDateTime(block.timestamp.toLong())
                    }

                    // 更新结算价格
                    orderRepository.findByIdOrNull(oo.orderId)?.let { o ->
                        if (o.bidAsset != null) {
                            val priceId = currencyService.getPriceId(o.bidAsset!!)
                            updateFields[OptionOrder::marketPriceAtSettlement.name] = try {
                                jasperVaultService.getPythPrice(priceId)?.stripTrailingZeros()?.toPlainString()
                            } catch (e: Exception) {
                                logger.error("Failed to get price for $priceId", e)
                                null
                            }
                        }
                    }
                    optionOrderService.updateFields(updateFields, oo.id!!)

                    orderRepository.findByIdOrNull(oo.orderId)?.let { o ->
                        o.status = OrderStatus.COMPLETED
                        orderRepository.save(o)
                    }

                    historyService.addSettlementProfit(oo, this)
                }

            return
        }
    }

    /**
     * log日志转换成可读的地址
     */
    private fun normalizeAddress(address: String): String {
        val trimResult = address.substring(address.length - 40)
        return "0x$trimResult"
    }

    fun saveLpValueWithdrawFromTxHash(transactionObject: TransactionObject) {
        val hash = transactionObject.hash
        val receipt = try {
            evmUtil.web3j.ethGetTransactionReceipt(hash).sendAsync().get().transactionReceipt.get()
        } catch (e: Exception) {
            logger.error("Failed to get transaction(${hash}) receipt: ${e.message}", e)
            return
        }
        if (receipt.status != "0x1") {
            logger.info("Transaction(${hash}) failed")
            return
        }

        val lpEOAAddress = Keys.toChecksumAddress(normalizeAddress(transactionObject.from))
        logger.info("LP Vault Withdraw event from address : $lpEOAAddress")

        val input = transactionObject.input
        if(input.length < 10){
            logger.info("LP Vault Withdraw event Transaction(${hash}) get function failed")
            return
        }
        val method = input.substring(0, 10)
        if(method == "0xcf66c87f"){ // redeemProxy

            logger.info("LP Vault Withdraw event Transaction(${hash}) get function redeem proxy")
            //Find LP Vault Address from Log
            receipt.logs.forEach { log ->
                if (log.address.compareTo(issuanceModuleContract, true) != 0) return@forEach

                val topics = log.topics
                val data = log.data

                if(topics[0] == "0x04c05e676e7c967412887a3192c2a75dcd62c5f3319222119373149eca2c7596"){

                    logger.info("LP Vault Withdraw event found topic")
                    val dataNoPrefix = Numeric.cleanHexPrefix(data)
                    val chunkSize = 64
                    val chunks = dataNoPrefix.chunked(chunkSize)

                    logger.info("LP Vault Withdraw event get data ${chunks.size}")
                    //if (chunks.size != 4) return@forEach

                    val lpVaultAddress = Keys.toChecksumAddress(Address(chunks[0]).toString())

                    logger.info("LP Vault Withdraw event get LP Vault Address ${lpVaultAddress}")
                    val lpVaultRecords = lpVaultRepository.findByLpEoaAddressIgnoreCaseAndLpVaultAddressIgnoreCaseAndStatus(
                        lpEOAAddress,
                        lpVaultAddress,
                        LPVaultStatus.DONE
                    )

                    if(lpVaultRecords.isNotEmpty()){

                        val lpVaultRecord = lpVaultRecords[0]
                        lpVaultRecord.removeVaultHash = hash
                        lpVaultRecord.status = LPVaultStatus.ABONDANED
                        lpVaultRepository.save(lpVaultRecord)

                        // 重新创建一个对应的 LPVault
                        lpVaultRepository.save(
                            LPVault(
                                lpEoaAddress = lpVaultRecord.lpEoaAddress,
                                optionType = lpVaultRecord.optionType,
                                optionSymbol = lpVaultRecord.optionSymbol,
                                lpVaultAddress = lpVaultRecord.lpVaultAddress
                            )
                        )
                    }
                }
                return
            }
        } else {
            logger.info("LP Vault Withdraw event Transaction(${hash}) not redeem proxy transaction")
        }
    }

    /**
     * 扫描区块
     */
    fun scanBlocks() {
        // get coin98 mint contract
        //val coin98Contract = contractEventRepository.findFirstByEventAndChain(EventName.COIN98, chainType)?.contract
        val bitlayerReaderContract = contractEventRepository.findFirstByEventAndChain(EventName.BITLAYER_BINANCE, chainType)?.contract

        //logger.info("Endpoints: ${evmUtil.endpoint}")
        val blockNumber = evmUtil.web3j.ethBlockNumber().send()
        val latestBlockHeight = blockNumber.blockNumber.toLong() // 最新区块高度

        val blockchain = blockchainRepository.findFirstByChain(chainType) ?: let {
            blockchainRepository.save(
                Blockchain(
                    chain = chainType,
                    blockNumber = latestBlockHeight,
                    confirmCount = 12,
//                    gasPrice = Convert.toWei("0.01", Convert.Unit.GWEI).toBigInteger(),
//                    gasLimit = 21000,
//                    contractGasLimit = 1000000
                )
            )
        }
        val lastScannedBlockNumber = blockchain.blockNumber // 最后扫描的区块高度
        var endBlock: Long? = null
        val asyncResult: MutableList<Future<Unit>> = mutableListOf()
        for (i in 1..100) {
            val bn = lastScannedBlockNumber + i
            if (bn + blockchain.confirmCount > latestBlockHeight) {
                //logger.info("No new block(Newest: $latestBlockHeight, Last scan: $lastScannedBlockNumber)")
                break
            }

            val ret = asyncEvmScanner.scanBlock(
                bn,
                latestBlockHeight.toBigInteger(),
                evmUtil,
                this,
                optionServiceContract,
                callOptionFnHash,
                putOptionFnHash,
                issuanceModuleContract,
                bitlayerReaderContract
            )
            asyncResult.add(ret)
            endBlock = bn
        }
        asyncResult.forEach {
            it.get()
        }
        if (endBlock != null) {
            blockchain.blockNumber = endBlock
            blockchainRepository.save(blockchain)
            //logger.info("Update $chainType blockchain block number to $endBlock")
        }
    }

    private fun getContractEventLog(
        eventName: EventName,
        txHash: String
    ): Log? {

        val contractEvent = contractEventRepository.findFirstByEventAndChain(eventName, chainType) ?: return null

        val receipt = try {
            evmUtil.web3j.ethGetTransactionReceipt(txHash).sendAsync().get().transactionReceipt.get()
        } catch (e: Exception) {
            // No such element
            logger.error("Failed to get transaction(${txHash}) receipt: ${e.message}", e)
            return null
        }
        if (receipt.status != "0x1") {
            // receipt is fail
            logger.info("Transaction(${txHash}) failed")
            return null
        }

        if (receipt.to.compareTo(contractEvent.contract, true) != 0) {
            // not redeem nft event
            logger.warn("Transaction(${txHash}) is not $eventName event")
            return null
        }
        receipt.logs.forEach { log ->
            if (contractEvent.logAddress.compareTo(log.address, true) == 0) {
                val fnHash = log.topics.firstOrNull()
                if (fnHash != null && contractEvent.fnHash.compareTo(fnHash, true) == 0) {
                    logger.info("Transaction(${txHash}) found $eventName event")
                    return log
                }
            }
        }

        return null
    }

    /**
     * 业务：用户执行RedeemNFT操作，即可获得免权利金的优惠
     */
//    fun getRedeemNftReceiver(txHash: String): String? {
//        val matchLog = getContractEventLog(EventName.REDEEM_NFT, txHash) ?: return null
//        val receiver = matchLog.topics.getOrNull(1) ?: return null
//        val receiverAddress = normalizeAddress(receiver)
//        logger.info("Redeem NFT event: receiver address: $receiverAddress")
//
//        return receiverAddress
//    }

    /**
     * 业务：Coin98活动，监听Mint事件，并返回钱包地址和NFT ID
     */
    fun getCoin98Event(txHash: String): Pair<String, String>? {
        val matchLog = getContractEventLog(EventName.COIN98, txHash) ?: return null
        val minter = matchLog.topics.getOrNull(1) ?: return null
        val minterAddress = normalizeAddress(minter)
        val nftId = matchLog.topics.getOrNull(2) ?: return null
        val nftIdStr = Numeric.toBigInt(nftId).toString()
        logger.info("Coin98 event: receiver address: $minterAddress, NFT ID: $nftIdStr")

        return Pair(minterAddress, nftIdStr)
    }

    /**
     * 业务：币安活动，监听logManagedOrder事件，并返回钱包地址
     */
    fun getBinanceEvent(txHash: String): Log? {
        return getContractEventLog(EventName.BITLAYER_BINANCE, txHash) ?: return null
    }

    override fun getSettlementInfo(
        oo: OptionOrder
    ): Triple<BigDecimal, BigDecimal, BigDecimal>? {

        val receipt = evmUtil.web3j.ethGetTransactionReceipt(oo.settlementHash).sendAsync().get().transactionReceipt.get()
        receipt.logs.forEach { log ->

            val result = parseSettlementInfoFromLog(
                log,
                BigInteger(oo.onChainOrderId!!)
            )
            if(result != null){
                return result
            }
        }
        return null
    }

    private fun getProfitFromLog(buyerAddress: String, settlementTxHash: String, underlyingAssetAddress: String): BigInteger? {
        val buyerVaultAddress = getVaultAddress(buyerAddress)
        val receipt = evmUtil.web3j.ethGetTransactionReceipt(settlementTxHash).sendAsync().get().transactionReceipt.get()
        receipt.logs.forEach { log ->
            if (log.address.compareTo(underlyingAssetAddress, ignoreCase = true) != 0) {
                return@forEach
            }
            val transferFnHash = log.topics.firstOrNull() ?: return@forEach
            if (transferFnHash.compareTo(usdtTransferFnHash, true) != 0) {
                return@forEach
            }
            val transferTo = try {
                log.topics[2]
            } catch (e: Exception) {
                return@forEach
            }
            val transferToAddress = Address(transferTo).value
            logger.info("Transfer to address: $transferToAddress")
            if (transferToAddress.compareTo(buyerVaultAddress, ignoreCase = true) != 0) {
                return@forEach
            }

            return Numeric.toBigInt(log.data)
        }
        return null
    }

    private fun getProfitFromInternalTxs(buyerAddress: String, settlementTxHash: String): BigInteger? {
        val buyerVaultAddress = getVaultAddress(buyerAddress)
        logger.info("Buyer vault address = $buyerVaultAddress")
        val internalTxList = try {
            scannerApi.getInternalTransactionsByTxHash(settlementTxHash)
        } catch (e: Exception) {
            logger.error(e.message, e)
            return null
        }
        internalTxList.forEach {
            if (it["to"].toString().compareTo(buyerVaultAddress, ignoreCase = true) != 0) {
                return@forEach
            }
            logger.info("Matched internal tx: $it")
            return BigInteger(it["value"].toString())
        }

        return null
    }

    override fun getProfit(oo: OptionOrder): BigInteger? {
        val tokenAddress = if (oo.direction == OptionDirection.PUT) {
            oo.quoteAssetAddress ?: let {
                val asset = currencyService.getOptionQuoteAsset(oo.chain, oo.bidAsset!!)
                currencyService.getAddressByChainAndSymbol(asset, oo.chain) ?: return null
            }
        } else {
            oo.underlyingAssetAddress ?: return null
        }
        val decimals = try {
            evmUtil.getTokenDecimals(tokenAddress)
        } catch (e: Exception) {
            null
        }
        logger.info("Contract: ${oo.underlyingAssetAddress}\tDecimals: $decimals")
        return if (decimals != null) {
            getProfitFromLog(
                oo.buyer!!,
                oo.settlementHash!!,
                tokenAddress
            )
        } else {
            getProfitFromInternalTxs(oo.buyer!!, oo.settlementHash!!)
        }
    }

    data class MintEventData(
        var nftCode: String? = null,
        var fromAddress: String? = null,
        var receiptStatus: Boolean? = null,
        var blockHeight: Long? = null,
        var txTimestamp: Long? = null
    )

    fun getMintEventData(txHash: String): MintEventData? {
        var dataStruct: MintEventData? = null
        evmUtil.web3j.ethGetTransactionByHash(txHash).send().transaction.ifPresent { tx ->
            val methodId = try {
                tx.input.substring(0, 10)
            } catch (e: Exception) {
                logger.error("Failed to get method ID for $txHash", e)
                return@ifPresent
            }
            if (methodId.compareTo("0xd85d3d27") != 0) {
                logger.info("此交易不是MintEvent, txHash=$txHash")
                return@ifPresent
            }
            val data = try {
                tx.input.substring(10)
            } catch (e: Exception) {
                logger.error("Failed to get data for $txHash", e)
                return@ifPresent
            }
            val dataList = data.chunked(64)
            // logger.info("Method ID: $methodId")
            // logger.info("Data: $data")
            // logger.info("Data List: $dataList")
            val nftCodeHex = try {
                dataList[2]
            } catch (e: Exception) {
                logger.error("Failed to get NFT Code for $txHash", e)
                return@ifPresent
            }
            val nftCodeByteArray = Numeric.hexStringToByteArray(nftCodeHex)
            // logger.info("NFT Code Bytes: $nftCodeByteArray")
            val nftCode = String(nftCodeByteArray).trim('\u0000')
            // logger.info("NFT Code: $nftCode\tSize = ${nftCode.length}")

            val block =
                evmUtil.web3j.ethGetBlockByNumber(DefaultBlockParameter.valueOf(tx.blockNumber), false).send().block
            val blockTimestamp = block.timestamp.toLong() // 交易时间/即区块生成时间
            dataStruct = MintEventData(
                nftCode = nftCode,
                fromAddress = tx.from,
                blockHeight = tx.blockNumber.toLong(),
                txTimestamp = blockTimestamp
            )
            // event.nftCode = nftCode
            // event.fromAddress = tx.from
            // event.userId2 = event.userId

            val receipt = try {
                evmUtil.web3j.ethGetTransactionReceipt(txHash)
                    .send().transactionReceipt.get()
            } catch (e: Exception) {
                logger.error("Failed to get transaction receipt for $txHash", e)
                return@ifPresent
            }
            dataStruct!!.receiptStatus = receipt.isStatusOK
            // event.receiptStatus = receipt.isStatusOK
            // mintEventRepository.save(event)
        }

        return dataStruct
    }

    fun getPremiumFeeInfo(oo: OptionOrder): Pair<PremiumAsset, BigDecimal>? {
        val premiumStruct = subgraphService.getOptionPremiumByOrderId(oo.chain, (oo.onChainOrderId ?: return null))

        return when (premiumStruct) {
            null -> { // 通过Scan API获取
                val premiumAsset = Symbol.valueOf(oo.premiumAsset!!.asset)
                val currencyAddress = currencyService.getAddressByChainAndSymbol(premiumAsset, oo.chain)
                val premiumFee = when {
                    currencyAddress?.compareTo("0xEeeeeEeeeEeEeeEeEeEeeEEEeeeeEeeeeeeeEEeE", true) == 0 -> {
                        // read in internal api
                        val scanApi = blockchainUtilFactory.getScanApi(oo.chain)
                        val buyerVault1 = getVaultAddress(oo.buyer!!)
                        scanApi.getInternalTransactionsByTxHash(oo.txHash!!).mapNotNull {
                            val from = it["from"] as? String ?: return@mapNotNull null
                            val to = it["to"] as? String ?: return@mapNotNull null
                            if (buyerVault1.compareTo(from, true) == 0
                                && oo.buyerVault?.compareTo(to, true) == 0) {
                                val value = it["value"] as? String ?: return@mapNotNull null
                                BigDecimal(value)
                            } else {
                                null
                            }
                        }.firstOrNull() ?: BigDecimal.ZERO
                    }
                    else -> {
                        val premiumFeePay = getPremiumFeePay(oo.txHash!!, oo.buyer!!, oo.buyerVault!!)
                        premiumFeePay?.toBigDecimal() ?: BigDecimal.ZERO
                    }
                }
                Pair(PremiumAsset(oo.premiumAsset!!.asset, currencyAddress), premiumFee)
            }
            else -> {
                val currency = currencyService.getCurrencyByAddressAndChain(premiumStruct.premiumAsset, oo.chain)
                    ?: throw BusinessException(ResultEnum.PREMIUM_ASSET_NOT_FOUND)
                Pair(PremiumAsset(currency.symbol, premiumStruct.premiumAsset), premiumStruct.amount.toBigDecimal())
            }
        }
    }

    fun getActualPremiumFeeInfo(oo: OptionOrder): Pair<PremiumAsset, BigDecimal> {
        // 通过Scan API获取
        val premiumAsset = Symbol.valueOf(oo.premiumAsset!!.asset)
        val currencyAddress = currencyService.getAddressByChainAndSymbol(premiumAsset, oo.chain)
        val premiumFee = when {
            currencyAddress?.compareTo("0xEeeeeEeeeEeEeeEeEeEeeEEEeeeeEeeeeeeeEEeE", true) == 0 -> {
                // read in internal api
                val scanApi = blockchainUtilFactory.getScanApi(oo.chain)
                val buyerVault1 = getVaultAddress(oo.buyer!!)
                scanApi.getInternalTransactionsByTxHash(oo.txHash!!).mapNotNull {
                    val from = it["from"] as? String ?: return@mapNotNull null
                    val to = it["to"] as? String ?: return@mapNotNull null
                    if (buyerVault1.compareTo(from, true) == 0
                        && oo.buyerVault?.compareTo(to, true) == 0
                    ) {
                        val value = it["value"] as? String ?: return@mapNotNull null
                        BigDecimal(value)
                    } else {
                        null
                    }
                }.firstOrNull() ?: BigDecimal.ZERO
            } else -> {
                val premiumFeePay = getPremiumFeePay(oo.txHash!!, oo.buyer!!, oo.buyerVault!!)
                premiumFeePay?.toBigDecimal() ?: BigDecimal.ZERO
            }
        }

        return Pair(PremiumAsset(oo.premiumAsset!!.asset, currencyAddress), premiumFee)
    }

    /**
     * 查询权利金数量
     */
    fun getPremiumFeePay(
        txHash: String,
        buyerAddress: String,
        buyerVaultAddress: String,
        txReceipt: TransactionReceipt? = null
    ): BigInteger? {

        var receipt = txReceipt

        if(receipt == null) {
            receipt = try {
                evmUtil.web3j.ethGetTransactionReceipt(txHash).sendAsync().get().transactionReceipt.get()
            } catch (e: Exception) {
                logger.error("Failed to get transaction(${txHash}) receipt: ${e.message}", e)
                return null
            }
        }

        val vault1Address = getVaultAddress(buyerAddress)

//        logger.info("Buyer address = $buyerAddress")
//        logger.info("Buyer AA Vault address = $vault1Address")
//        logger.info("Buyer vault address = $buyerVaultAddress")

        receipt!!.logs.forEach { log ->
            try {
                val fromAddress = log.topics.getOrNull(1) ?: return@forEach
                val toAddress = log.topics.getOrNull(2) ?: return@forEach
                val from = Address(fromAddress).value
                val to = Address(toAddress).value
                logger.info("From: $from\tTo: $to")
                if (
                    from.compareTo(vault1Address, ignoreCase = true) == 0
                    && to.compareTo(buyerVaultAddress, true) == 0
                ) {
                    val amount = Numeric.toBigInt(log.data)
                    logger.info("Premium Fee Pay: $amount")
                    return amount
                }
            } catch (e: Exception) {
                logger.error("Failed to get log info premium fee pay $log", e)
            }
        }

        return null
    }

    /**
     * 设置价格
     */
    open fun getAndSetPrice(priceId: String, pythIdList: List<String>): BigDecimal {
        return jasperVaultService.getAndSetPrice(priceId, chainType, pythIdList)
    }

    /**
     * 推送 Free Mint 交易
     */
//    fun settleFreeMintRecords(
//        contractAddress: String,
//        privateKey: String,
//        addressList: List<String>,
//        discountIdList: List<BigInteger>,
//        countList: List<BigInteger>,
//        gasLimit: Int,
//        gasPrice: BigInteger? = null
//    ): String {
//        val inputs: List<Type<*>> = listOf(
//            DynamicArray(Address::class.java, addressList.map { Address(it) }),
//            DynamicArray(Uint256::class.java, discountIdList.map { Uint256(it) }),
//            DynamicArray(Uint256::class.java, countList.map { Uint256(it) })
//        )
//        val outputs: MutableList<TypeReference<*>> = ArrayList()
//        val method = "setDiscountToUser"
//        val function = Function(method, inputs, outputs)
//        val data = FunctionEncoder.encode(function)
//        val credentials = Credentials.create(privateKey)
//        val rawTransaction = RawTransaction.createTransaction(
//            evmUtil.getNonce(credentials.address),
//            gasPrice ?: evmUtil.getGasPrice(""),
//            gasLimit.toBigInteger(),
//            contractAddress,
//            BigInteger.valueOf(0),
//            data
//        )
//        val signMessage = TransactionEncoder.signMessage(rawTransaction, evmUtil.chainId, credentials)
//        val toHexString = Numeric.toHexString(signMessage)
//        val ethCall = evmUtil.web3j.ethSendRawTransaction(toHexString).sendAsync().get()
//        if (ethCall.hasError()) {
//            val errorMsg = ethCall.error.message
//            // max fee per gas less than block base fee: address ******************************************, maxFeePerGas: 10000000 baseFee: 10041000
//            if (errorMsg.startsWith("max fee per gas less than block base fee")) {
//                val regex = Regex("baseFee:\\s*(\\d+)")
//                regex.find(errorMsg)?.let {
//                    val baseFee = it.groupValues[1].toBigInteger()
//                    logger.warn("current baseFee: $baseFee")
//                    val move = baseFee.toString(10).length - 2
//                    val divisor = BigInteger.TEN.pow(move)
//                    val newBaseFee = (baseFee.divide(divisor) + BigInteger.ONE).multiply(divisor)
//                    logger.info("new baseFee: $newBaseFee")
//                    Thread.sleep(3000)
//
//                    return settleFreeMintRecords(
//                        contractAddress,
//                        privateKey,
//                        addressList,
//                        discountIdList,
//                        countList,
//                        gasLimit,
//                        newBaseFee
//                    )
//                }
//            } else {
//                logger.error("Error Message: ${ethCall.error.message}")
//                throw Exception("Failed to send transaction")
//            }
//        }
//        val txHash = ethCall.transactionHash
//        logger.info("Contract: ${contractAddress}\tmethod: $method\t txid: $txHash")
//
//        return txHash
//    }

    /**
     * 推送 Free Mint 交易
     */
    fun settleFreeMintNFTRecords(
        contractAddress: String,
        privateKey: String,
        address: String,
        nftId: BigInteger,
        gasLimit: Int,
        gasPrice: BigInteger? = null
    ): String {
        val inputs: List<Type<*>> = listOf(
            Address(address),
            Uint256(nftId)
        )

        logger.info("Settle Free Mint Records input: $inputs")

        val outputs: MutableList<TypeReference<*>> = ArrayList()
        val method = "addNFTDiscountToUser"
        val function = Function(method, inputs, outputs)
        val data = FunctionEncoder.encode(function)
        val credentials = Credentials.create(privateKey)
        val rawTransaction = RawTransaction.createTransaction(
            evmUtil.getNonce(credentials.address),
            gasPrice ?: evmUtil.getGasPrice(""),
            gasLimit.toBigInteger(),
            contractAddress,
            BigInteger.valueOf(0),
            data
        )
        val signMessage = TransactionEncoder.signMessage(rawTransaction, evmUtil.chainId, credentials)
        val toHexString = Numeric.toHexString(signMessage)
        val ethCall = evmUtil.web3j.ethSendRawTransaction(toHexString).sendAsync().get()
        if (ethCall.hasError()) {
            val errorMsg = ethCall.error.message
            // max fee per gas less than block base fee: address ******************************************, maxFeePerGas: 10000000 baseFee: 10041000
            if (errorMsg.startsWith("max fee per gas less than block base fee")) {
                val regex = Regex("baseFee:\\s*(\\d+)")
                regex.find(errorMsg)?.let {
                    val baseFee = it.groupValues[1].toBigInteger()
                    logger.warn("current baseFee: $baseFee")
                    val move = baseFee.toString(10).length - 2
                    val divisor = BigInteger.TEN.pow(move)
                    val newBaseFee = (baseFee.divide(divisor) + BigInteger.ONE).multiply(divisor)
                    logger.info("new baseFee: $newBaseFee")
                    Thread.sleep(3000)

                    return settleFreeMintNFTRecords(
                        contractAddress,
                        privateKey,
                        address,
                        nftId,
                        gasLimit,
                        newBaseFee
                    )
                }
            } else {
                logger.error("Error Message: ${ethCall.error.message}")
                throw Exception("Failed to send transaction")
            }
        }
        val txHash = ethCall.transactionHash
        logger.info("Contract: ${contractAddress}\tmethod: $method\t txid: $txHash")

        return txHash
    }

    private fun getBidAmount(txHash: String, optionStruct: OptionOrderStruct): BigDecimal {
        // 通过Subgraph获取
        val bidAmountBigInt = try {
            subgraphService.getOrderFromHash(txHash, chainType)?.get("quantity")?.asText()?.toBigDecimal()
        } catch (e: Exception) {
            null
        } ?: run {
            // 从subgraph获取失败，访问scan api
            val internalTxList = try {
                getInternalTxListWithHash(txHash, times = 3)
            } catch (e: Exception) {
                null
            }
            val bidAmountBigIntList = internalTxList?.mapNotNull {
                val from = it["from"] as? String ?: return@mapNotNull null
                val to = it["to"] as? String ?: return@mapNotNull null
                if (from.compareTo(optionStruct.writerVault, true) == 0
                    && to.compareTo(optionStruct.holderVault, true) == 0
                ) {
                    val value = it["value"] as? String ?: return@mapNotNull null
                    BigDecimal(value)
                } else {
                    null
                }
            }
            bidAmountBigIntList?.firstOrNull()
        }

        return bidAmountBigInt ?: throw BusinessException(ResultEnum.BID_AMOUNT_NOT_FOUND)
    }

    fun getOptionOrderDetailExceptOrderIds(
        txHash: String,
        orderIds: List<BigInteger>,
        confirmCount: Int? = null,
        newBlockHeight: BigInteger? = null,
        txReceipt: TransactionReceipt? = null
    ): Pair<OptionOrderStruct?, OptionPremiumStruct?> {
        var receipt = txReceipt

        if(receipt == null) {
            receipt = try {
                evmUtil.web3j.ethGetTransactionReceipt(txHash).sendAsync().get().transactionReceipt.get()
            } catch (e: Exception) {
                // logger.info("Sync Pending orders : Get Option Order Receipt Failed")
                logger.warn(e.message, e)
                throw BusinessException(ResultEnum.GET_TRANSACTION_FAILED)
            }
        }

        val customLastBH = newBlockHeight ?: evmUtil.web3j.ethBlockNumber().send().blockNumber
        val blockchain = blockchainRepository.findFirstByChain(chainType)
            ?: throw BusinessException(ResultEnum.CHAIN_NOT_FOUND)
        val customConfirmCount = (confirmCount ?: blockchain.confirmCount).toBigInteger()
        if (customLastBH - receipt!!.blockNumber < customConfirmCount)
            throw BusinessException(ResultEnum.TRANSACTION_NOT_CONFIRMED)
        if (receipt.status != "0x1") throw BusinessException(ResultEnum.TRANSACTION_RECEIPT_FAILED)

        var optionOrderStruct: OptionOrderStruct? = null
        var optionPremiumStruct: OptionPremiumStruct? = null

        for (log in receipt.logs) {
            val tempOptionOrderStruct = createOptionOrderStruct(log)
            if(tempOptionOrderStruct!= null && !orderIds.contains(tempOptionOrderStruct.orderId)){
                optionOrderStruct = tempOptionOrderStruct
                break
            }
        }

        if(optionOrderStruct == null){
            return Pair(null, null)
        }

        for (log in receipt.logs) {

            val tempOptionPremiumStruct = createOptionPremiumFromLog(log)
            if(tempOptionPremiumStruct != null && tempOptionPremiumStruct.orderId == optionOrderStruct.orderId){
                optionPremiumStruct = tempOptionPremiumStruct
                break
            }
        }

        return Pair(optionOrderStruct, optionPremiumStruct)
    }

    fun getCallOrdersFromStartAndEnd(start: Long, end: Long, pageSize: Int, skip: Int): List<JsonNode>? {
        val query = """
{
    callOrderEntityV2S(
        where: {timestamp_gte: "$start", timestamp_lt: "$end"}
        first: $pageSize
        orderBy: timestamp
        orderDirection: asc
        skip: $skip
    ) {
        orderId
        transactionHash
        timestamp
        holderWallet
        id
        writerWallet
        callOrder {
            expirationDate
            holder
            id
            liquidateMode
            lockAmount
            lockAsset
            lockAssetType
            lockDate
            quantity
            recipient
            strikeAmount
            strikeAsset
            underlyingAsset
            underlyingNftID
            writer
        }
    }
}
"""
        val raw = subgraphService.getSubgraphOrderData(chainType, query)
        //logger.info("[${chainType}] subgraph response: $raw")
        val response = ObjectMapper().readTree(raw ?: return null)

        return response?.get("data")?.get("callOrderEntityV2S")?.asIterable()?.toList()
    }

    fun getPutOrdersFromStartAndEnd(start: Long, end: Long, pageSize: Int, skip: Int): List<JsonNode>? {
        val query = """
{
    putOrderEntityV2S(
        where: {timestamp_gte: "$start", timestamp_lt: "$end"}
        first: $pageSize
        orderBy: timestamp
        orderDirection: asc
        skip: $skip
    ) {
        orderId
        transactionHash
        holderWallet
        id
        timestamp
        writerWallet
        putOrder {
            expirationDate
            holder
            id
            liquidateMode
            lockAmount
            lockAsset
            lockAssetType
            lockDate
            quantity
            recipient
            strikeAmount
            strikeAsset
            underlyingAsset
            underlyingNftID
            writer
        }
    }
}
"""
        val raw = subgraphService.getSubgraphOrderData(chainType, query)
        //logger.info("[${chainType}] subgraph response: $raw")
        val response = ObjectMapper().readTree(raw ?: return null)

        return response?.get("data")?.get("putOrderEntityV2S")?.asIterable()?.toList()
    }

    fun getLastCheckedTimeInSubgraph(): Long? =
        blockchainRepository.findFirstByChain(chainType)?.lastCheckedTimeInSubgraph?.atZone(ZoneId.systemDefault())
            ?.toEpochSecond()

    fun getStoneBurnInfoFromLogForAddress(
        txHash: String,
        address: String
    ): Int? {
        val receipt = try {
            evmUtil.web3j.ethGetTransactionReceipt(txHash).sendAsync().get().transactionReceipt.get()
        } catch (e: Exception) {
            logger.error("Failed to get transaction(${txHash}) receipt: ${e.message}", e)

            return null
        }

        val logs = receipt.logs
        var nftId: Int? = null
        for(log in logs){

            if(log.topics.firstOrNull() == transferSingleFnHash){
                val from = log.topics.getOrNull(2) ?: continue
                val fromAddress = Address(from).value
                if(fromAddress.compareTo(address, true) == 0){
                    val nftIdStr = log.data.substring(2).chunked(64).firstOrNull()
                    nftId = Numeric.toBigIntNoPrefix(nftIdStr).toInt()
                    break
                }
            }
        }

        logger.info("Tx=$txHash\tTransferSingle NFT ID: $nftId")

        return nftId
    }

    /**
     * decode input data
     */
    fun decodeExecutedBatchMethodFromBundlerInputData(
        inputData: String,
        toAddress: String
    ) : Pair<BigDecimal?, Long?> {

        // 使用 Bundler 发送交易
        var orderInputData: String? = null
        var decodedFunctionCall: DecodedFunctionCall? = null

        if(toAddress.lowercase() == entryPointAddress.lowercase()) {
            var opCallData: String? = null
            val decodeEntryPointCall = entryPointAbi.decodeFunctionCall(inputData)

            //logger.info(decodeEntryPointCall.name)

            val opsParameter = decodeEntryPointCall.params

            var opsValueArray: Array<Object>? = null
            for (opsParam in opsParameter) {

                if (opsParam.name == "ops") {
                    opsValueArray = opsParam.value as Array<Object>
                    break;
                }
            }

            for (opsValue in opsValueArray!!) {
                val opsArray = opsValue as Array<Object>

                val ops = opsArray[3] as ByteArray
                opCallData = Numeric.toHexString(ops)

                try {
                    decodedFunctionCall = executeBatchAbi.decodeFunctionCall(opCallData)

                    if (decodedFunctionCall.name == "executeBatch") {
                        break
                    }

                } catch (e: Exception) {
                    //logger.error("Error: ${e.message}")
                    //logger.info("Can not decode executeBatch method from bundler data")
                    return Pair(null, null)
                }
            }

        } else {
            decodedFunctionCall = try {
                executeBatchAbi.decodeFunctionCall(inputData)
            } catch (e: Exception) {
                logger.warn("Could not decode function from input data")
                null
            }
        }

        if (decodedFunctionCall == null) {
            return Pair(null, null)
        }

        decodedFunctionCall.params.forEach {
            // logger.info("name: ${it.name}, value: ${it.value}")
            try {
                val name = it.name
                if(name == "func"){

                    try {
                        val paramValue = it.value as Array<Object>
                        val size = paramValue.size
                        orderInputData = paramValue[size - 1] as String

                    } catch (e: Exception) {
                        // logger.warn("decodedFunctionCall.params value is not Array<Object>, name=${it.name}")
                        //return@forEach
                        orderInputData = it.value as String
                    }
                }
            } catch (e: Exception) {
                logger.error("Error: ${e.message}", e)
            }
        }

        if(orderInputData == null){
            return Pair(null, null)
        }

        var decodeOptionModule: DecodedFunctionCall? = try {
            optionModuleAbiNew.decodeFunctionCall(orderInputData)
        } catch (e: Exception) {
            //logger.info("Can not decode strike price from V2 option module")
            null
        }

        if(decodeOptionModule == null){

            decodeOptionModule = try{
                optionModuleAbiV4.decodeFunctionCall(orderInputData)
            } catch (e: Exception) {
                //logger.info("Can not decode strike price from V4 option module")
                return Pair(null, null)
            }
        }

        if(decodeOptionModule == null){
            return Pair(null, null)
        }

        //logger.info(decodeOptionModule.name
        val infoParameter = decodeOptionModule.params.first()

        val infoValue = infoParameter.value as Array<Object>
        val premiumSign = infoValue[8] as Array<Object>
        val strikePrice = premiumSign[4] as BigInteger
        val timestamp = premiumSign[14] as BigInteger

        val quoteAssetToken = currencyService.getOptionQuoteAsset(chainType, null)
        val decimal = currencyService.getCurrencyDecimal(
            chainType,
            quoteAssetToken
        )

        return Pair(BigDecimal(strikePrice).movePointLeft(decimal), timestamp.toLong())
    }

    /**
     * decode input data
     */
    fun decodeSettlementMethodFromBundlerInputData(
        inputData: String,
        toAddress: String,
        optionServiceAddress: String
    ) : Boolean {

        // 使用 Bundler 发送交易
        if(toAddress.lowercase() == entryPointAddress.lowercase()){
            var opCallData: String? = null
            val decodeEntryPointCall = entryPointAbi.decodeFunctionCall(inputData)

            //logger.info(decodeEntryPointCall.name)

            val opsParameter = decodeEntryPointCall.params

            var opsValueArray: Array<Object>? = null
            for(opsParam in opsParameter){

                if(opsParam.name == "ops"){
                    opsValueArray = opsParam.value as Array<Object>
                    break;
                }
            }

            var decodeAddress: String? = null
            for(opsValue in opsValueArray!!){
                val opsArray = opsValue as Array<Object>

                val ops = opsArray[3] as ByteArray
                opCallData = Numeric.toHexString(ops)

                try {
                    val decodedFunctionCall = executeBatchAbi.decodeFunctionCall(opCallData)

                    if (decodedFunctionCall.name != "executeBatch") {
                        continue
                    }

                    decodedFunctionCall.params.forEach {
                        try {
                            val name = it.name
                            val paramValue = it.value as Array<Object>

                            if (name == "dest") {
                                val size = paramValue.size
                                decodeAddress = paramValue[size - 1] as String
                            }
                        } catch (e: Exception) {
                            logger.error("Error: ${e.message}", e)
                        }
                    }
                } catch (e: Exception) {
                    //logger.error("Error: ${e.message}")
                    //logger.info("Can not decode executeBatch method from bundler data")
                    return false
                }

                if(decodeAddress != null){
                    break
                }
            }

            if(decodeAddress == null){
                return false
            }

            if(decodeAddress!!.compareTo(optionServiceAddress, true) != 0){
                return false
            }

            return true
        }

        return false
    }

    fun createOptionPremiumFromLog(log: Log): OptionPremiumStruct? {
        val fnHash = "0xcdca717e34ff37f724a3263bfa000456073728d92238c3dc47a2fa24ae4bbe3b"
        if (log.topics.firstOrNull()?.compareTo(fnHash, true) != 0) return null

        val decodeOptionModule = optionModuleAbiNew.decodeLogEvent(
            log.topics,
            log.data
        )

        //logger.info(decodeOptionModule.name)
        var orderIdParameter: DecodedFunctionCall.Param? = null
        var infoParameter: DecodedFunctionCall.Param? = null
        var premiumAmountParameter: DecodedFunctionCall.Param? = null
        var freePremiumAmountParameter: DecodedFunctionCall.Param? = null

        decodeOptionModule.params.forEach{
            val name = it.name

            if(name == "_orderID"){
                orderIdParameter = it
            }
            if(name == "_info"){
                infoParameter = it
            }
            if(name == "_premiumAmount"){
                premiumAmountParameter = it
            }
            if(name == "_freePremiumAmount"){
                freePremiumAmountParameter = it
            }
        }

        if(orderIdParameter == null ||
            infoParameter == null ||
            premiumAmountParameter == null ||
            freePremiumAmountParameter == null){
            return null
        }

        val infoValue = infoParameter!!.value as Array<Object>
        val holder = infoValue[0] as String
        val writer = infoValue[1] as String
        val quantity = infoValue[3] as BigInteger
        val premiumSign = infoValue[8] as Array<Object>
        val productType = premiumSign[2] as BigInteger // > 10**9 时，为limit order
        val strikePrice = premiumSign[4] as BigInteger
        val optionType = premiumSign[11] as BigInteger
        val premiumAssetAddress = Numeric.toHexString(premiumSign[12] as ByteArray)

        val quoteAssetToken = currencyService.getOptionQuoteAsset(chainType, null)
        val decimal = currencyService.getCurrencyDecimal(
            chainType,
            quoteAssetToken
        )

        val orderId = orderIdParameter!!.value as BigInteger
        val premiumAmount = premiumAmountParameter!!.value as BigInteger
        val freePremiumAmount = freePremiumAmountParameter!!.value as BigInteger

        val readableStrikePrice = BigDecimal(strikePrice).movePointLeft(decimal)
        val readableQuantity = BigDecimal(quantity).movePointLeft(18)
        val limitOrder = productType.compareTo(BigInteger.TEN.pow(9)) == 1

        return OptionPremiumStruct(
            optionType = if(optionType == BigInteger.ZERO) "CALL" else "PUT",
            orderId = orderId,
            writer = Keys.toChecksumAddress(writer),
            holder = Keys.toChecksumAddress(holder),
            premiumAssetAddress = Keys.toChecksumAddress(premiumAssetAddress),
            premiumAmount = premiumAmount,
            quantity = readableQuantity,
            strikePrice = readableStrikePrice.movePointRight(18),
            freePremiumAmount = freePremiumAmount,
            limitOrder = limitOrder
        )
    }

    fun parseSettlementInfoFromLog(
        log: Log,
        onChainOrderId: BigInteger
    ): Triple<BigDecimal, BigDecimal, BigDecimal>? {
        val fnHash = "0xabd20c9a288c74637de97fa697977b97134d9a0250c5c86d503e3585915a0ef9"
        if (log.topics.firstOrNull()?.compareTo(fnHash, true) != 0) return null

        val decodeOptionModule = optionServiceAbi.decodeLogEvent(
            log.topics,
            log.data
        )

        //logger.info(decodeOptionModule.name)
        var settleResultParameter: DecodedFunctionCall.Param? = null
        var orderIdParameter: DecodedFunctionCall.Param? = null

        decodeOptionModule.params.forEach{
            if(it.name == "_result"){
                settleResultParameter = it
            }

            if(it.name == "_orderID"){
                orderIdParameter = it
            }
        }

        if(orderIdParameter == null){
            return null
        }
        if(settleResultParameter == null){
            return null
        }

        val orderId = orderIdParameter!!.value as BigInteger
        if(onChainOrderId != orderId){
            return null
        }

        val resultValue = settleResultParameter!!.value as Array<Object>
        val profit = resultValue[0] as BigInteger
        val strikeAssetPrice = resultValue[1] as BigInteger
        val lockAssetPrice = resultValue[2] as BigInteger

        return Triple(BigDecimal(profit), BigDecimal(strikeAssetPrice), BigDecimal(lockAssetPrice))
    }

    fun parseStrikePriceInfoFromLog(
        logs: List<Log>,
        onChainOrderId: BigInteger
    ): Pair<BigDecimal, Long>? {
        val fnHash = "0xcdca717e34ff37f724a3263bfa000456073728d92238c3dc47a2fa24ae4bbe3b"

        for(log in logs){
            if (log.topics.firstOrNull()?.compareTo(fnHash, true) != 0) {
                continue
            }

            val decodeOptionModule = try{
                optionModuleAbiNew.decodeLogEvent(
                    log.topics,
                    log.data
                )
            } catch (e: Exception) {
                logger.info("parseStrikePriceInfoFromLog: Can not decode strike price from option module V2 logs")

                try{
                    optionModuleAbiV4.decodeLogEvent(
                        log.topics,
                        log.data
                    )
                } catch (e: Exception) {
                    logger.info("parseStrikePriceInfoFromLog: Can not decode strike price from option module V4 logs")
                    continue
                }
            }

            //logger.info(decodeOptionModule.name)
            var infoParameter: DecodedFunctionCall.Param? = null
            var orderIdParameter: DecodedFunctionCall.Param? = null

            decodeOptionModule.params.forEach{
                if(it.name == "_info"){
                    infoParameter = it
                }

                if(it.name == "_orderID"){
                    orderIdParameter = it
                }
            }

            if(infoParameter == null){
                continue
            }
            if(orderIdParameter == null){
                continue
            }

            val orderId = orderIdParameter!!.value as BigInteger
            if(onChainOrderId != orderId){
                continue
            }

            val infoValue = infoParameter!!.value as Array<Object>
            val premiumSign = infoValue[8] as Array<Object>
            val strikePrice = premiumSign[4] as BigInteger
            val timestamp = premiumSign[14] as BigInteger

            val quoteAssetToken = currencyService.getOptionQuoteAsset(chainType, null)
            val decimal = currencyService.getCurrencyDecimal(
                chainType,
                quoteAssetToken
            )

            return Pair(BigDecimal(strikePrice).movePointLeft(decimal), timestamp.toLong())
        }

        return null
    }

    fun parseSettlementPriceListFromLog(
        log: Log,
        onChainOrderId: BigInteger
    ): List<SettlementPriceInfo>? {

        // ReadHistoryPrice Event
        val fnHash = "0xf22e0f824d4bf1a9e8c31ca9d8f6ea7678b36298add58eec2250d6b52d86bb4f"
        if (log.topics.firstOrNull()?.compareTo(fnHash, true) != 0) return null

        val decodeOracleData = pythPriceOracleAbi.decodeLogEvent(
            log.topics,
            log.data
        )

        var historyParameter: DecodedFunctionCall.Param? = null

        decodeOracleData.params.forEach{
            if(it.name == "historyPrice"){
                historyParameter = it
            }
        }

        if(historyParameter == null){
            return null
        }

        val resultValue = historyParameter!!.value as Array<Object>

        val returnResult = mutableListOf<SettlementPriceInfo>()
        resultValue.forEach {

            val e = it as Array<Object>
            val price = e[0] as BigInteger
            val timestamp = e[1] as BigInteger

            returnResult.add(
                SettlementPriceInfo(
                    price = BigDecimal(price),
                    timestamp = timestamp.toLong()
                )
            )
        }

        return returnResult
    }

    /**
     * 结算空投首单永赚
     */
    fun transferERC20Token(
        toAddress: String,
        amountIn: BigDecimal,
        contractAddress: String,
        fromAddress: String,
        fromAddressPriviteKey: String,
        gasLimit: Int,
        gasPrice: BigInteger? = null,
    ): String {

        val decimals = getTokenDecimals(contractAddress)
        logger.info("发送交易start to send ${amountIn} $contractAddress(decimals: $decimals) from ${fromAddress} to ${toAddress}")
        val amountInt = amountIn.movePointRight(decimals).toBigInteger()
        val methodName = "transfer"
        val amount = Uint256(amountInt)
        //val ethGetTransactionCount =
        //    evmUtil.web3j.ethGetTransactionCount(fromAddress, DefaultBlockParameterName.PENDING).send()

        val inputParameters: List<Type<*>> = listOf(Address(toAddress), amount)

        val outputParameters: MutableList<TypeReference<*>> = java.util.ArrayList()
        val typeReference1: TypeReference<Uint256> = object : TypeReference<Uint256>() {}
        val typeReference2: TypeReference<Uint256> = object : TypeReference<Uint256>() {}
        outputParameters.add(typeReference1)
        outputParameters.add(typeReference2)
        val function = Function(
            methodName, inputParameters, outputParameters
        )

        val data = FunctionEncoder.encode(function)
        val credentials = Credentials.create(fromAddressPriviteKey)
        val rawTransaction = RawTransaction.createTransaction(
            evmUtil.getNonce(credentials.address),
            gasPrice ?: evmUtil.getGasPrice(""),
            gasLimit.toBigInteger(),
            contractAddress,
            BigInteger.valueOf(0),
            data
        )
        val signMessage = TransactionEncoder.signMessage(rawTransaction, evmUtil.chainId, credentials)
        val toHexString = Numeric.toHexString(signMessage)
        val ethCall = evmUtil.web3j.ethSendRawTransaction(toHexString).sendAsync().get()

        val txHash = ethCall.transactionHash
        logger.info("method: $methodName\t txid: $txHash")
        return txHash
    }

    /**
     * 查询代币精度
     *
     * @param contractAddress
     * @return
     */
    fun getTokenDecimals(contractAddress: String): Int {

        val methodName = "decimals"
        val fromAddr = emptyAddress
        val inputParameters: List<Type<*>> = java.util.ArrayList()
        val outputParameters: MutableList<TypeReference<*>> = java.util.ArrayList()
        val typeReference: TypeReference<Uint8> = object : TypeReference<Uint8>() {}
        outputParameters.add(typeReference)
        val function = Function(methodName, inputParameters, outputParameters)
        val data = FunctionEncoder.encode(function)
        val transaction = org.web3j.protocol.core.methods.request.Transaction.createEthCallTransaction(
            fromAddr,
            contractAddress,
            data
        )
        try {
            val ethCall = evmUtil.web3j.ethCall(transaction, DefaultBlockParameterName.LATEST).sendAsync().get()
            val results = FunctionReturnDecoder.decode(ethCall.value, function.outputParameters)

            val decimals = results[0].value.toString().toInt()
            if (decimals > 0) {
                return decimals
            } else {
                throw BusinessException(ResultEnum.TOKEN_DECIMALS_ERROR)
            }
        } catch (e: Exception) {
            logger.warn("Contract=${contractAddress}\t查询代币精度失败: ${e.message}", e)
            throw BusinessException(ResultEnum.TOKEN_DECIMALS_ERROR)
        }
    }

    /**
     * 结算空投首单永赚
     */
    fun adminBurnStone(
        contractAddress: String,
        privateKey: String,
        address: String,
        gasLimit: Int,
        gasPrice: BigInteger? = null,
        nftId: BigInteger = BigInteger.ONE
    ): String {
        val inputs: List<Type<*>> = listOf(
            Address(address),
            Uint256(nftId),
            Uint256(1)
        )
        val outputs: MutableList<TypeReference<*>> = ArrayList()
        val method = "adminBurn"
        val function = Function(method, inputs, outputs)
        val data = FunctionEncoder.encode(function)
        val credentials = Credentials.create(privateKey)
        val rawTransaction = RawTransaction.createTransaction(
            evmUtil.getNonce(credentials.address),
            gasPrice ?: evmUtil.getGasPrice(""),
            gasLimit.toBigInteger(),
            contractAddress,
            BigInteger.valueOf(0),
            data
        )
        val signMessage = TransactionEncoder.signMessage(rawTransaction, evmUtil.chainId, credentials)
        val toHexString = Numeric.toHexString(signMessage)
        val ethCall = evmUtil.web3j.ethSendRawTransaction(toHexString).sendAsync().get()
        if (ethCall.hasError()) {
            val errorMsg = ethCall.error.message
            // max fee per gas less than block base fee: address ******************************************, maxFeePerGas: 10000000 baseFee: 10041000
            if (errorMsg.startsWith("max fee per gas less than block base fee")) {
                val regex = Regex("baseFee:\\s*(\\d+)")
                regex.find(errorMsg)?.let {
                    val baseFee = it.groupValues[1].toBigInteger()
                    logger.debug("current baseFee: $baseFee")
                    val move = baseFee.toString(10).length - 2
                    val divisor = BigInteger.TEN.pow(move)
                    val newBaseFee = (baseFee.divide(divisor) + BigInteger.ONE).multiply(divisor)
                    logger.debug("new baseFee: $newBaseFee")
                    Thread.sleep(3000)

                    return adminBurnStone(
                        contractAddress,
                        privateKey,
                        address,
                        gasLimit,
                        newBaseFee,
                        nftId = nftId
                    )
                }
            } else {
                logger.error("Error Message: ${ethCall.error.message}")
                throw Exception("Failed to send transaction")
            }
        }
        val txHash = ethCall.transactionHash
        logger.info("method: $method\t txid: $txHash")

        return txHash
    }

    data class PremiumFeeDistribution(
        val sellerPremiumFee: BigDecimal?,
        val platformPremiumFee: BigDecimal?
    )

    /**
     * 从交易日志中获取权利金分配信息
     * @param txHash 交易哈希
     * @param buyerVault 买家的vault地址
     * @param sellerVault 卖家的vault地址
     * @param premiumAssetAddress 权利金资产合约地址
     * @return PremiumFeeDistribution 包含卖家和平台获得的权利金数量，如果获取失败则返回null
     */
    fun getPremiumFeeDistribution(
        txHash: String,
        buyerVault: String,
        sellerVault: String,
        premiumAssetAddress: String
    ): PremiumFeeDistribution? {
        try {
            // 如果是原生代币
            if (premiumAssetAddress.equals("******************************************", true)) {
                return getInternalTransactionPremiumFee(txHash, buyerVault, sellerVault)
            }

            // ERC20代币的处理逻辑
            val receipt = evmUtil.web3j.ethGetTransactionReceipt(txHash).send().transactionReceipt.get()
            var sellerPremiumFee: BigDecimal? = null
            var platformPremiumFee: BigDecimal? = null
            
            // 获取合约精度
            // val decimals = getTokenDecimals(premiumAssetAddress)
            
            // 遍历交易日志，查找Transfer事件
            receipt.logs.forEach { log ->
                try {
                    // 检查是否是对应资产的Transfer事件
                    if (log.address.compareTo(premiumAssetAddress, true) != 0) return@forEach
                    
                    val topics = log.topics
                    if (topics.size < 3) return@forEach
                    
                    // 检查事件签名是否是Transfer
                    if (topics[0] != usdtTransferFnHash) return@forEach
                    
                    // 获取from地址
                    val fromAddress = normalizeAddress(topics[1])
                    if (fromAddress.compareTo(buyerVault, true) != 0) return@forEach
                    
                    // 获取to地址
                    val toAddress = normalizeAddress(topics[2])
                    
                    // 获取转账金额并根据精度转换
                    val amount = BigDecimal(Numeric.toBigInt(log.data))
                    
                    // 根据接收地址判断是卖家还是平台的权利金
                    when {
                        toAddress.compareTo(sellerVault, true) == 0 -> {
                            sellerPremiumFee = amount
                        }
                        toAddress.compareTo(PLATFORM_PREMIUM_ADDRESS, true) == 0 -> {
                            platformPremiumFee = amount
                        }
                    }
                } catch (e: Exception) {
                    logger.error("Failed to parse transfer log: ${e.message}")
                }
            }

            return if (sellerPremiumFee == null && platformPremiumFee == null) {
                null
            } else {
                PremiumFeeDistribution(sellerPremiumFee, platformPremiumFee)
            }
        } catch (e: Exception) {
            logger.error("Failed to get premium fee distribution for tx: $txHash", e)
            return null
        }
    }

    /**
     * 从内部交易中获取原生代币的权利金分配信息
     */
    private fun getInternalTransactionPremiumFee(
        txHash: String,
        buyerVault: String,
        sellerVault: String
    ): PremiumFeeDistribution? {
        try {
            // 获取内部交易列表
            val internalTxs = getInternalTxListWithHash(txHash)
            
            var sellerPremiumFee: BigDecimal? = null
            var platformPremiumFee: BigDecimal? = null

            // 遍历内部交易
            internalTxs.forEach { tx ->
                try {
                    // 检查是否是从buyerVault发出的交易
                    val fromAddr = tx["from"] as? String ?: return@forEach
                    val fromAddress = normalizeAddress(fromAddr)
                    if (fromAddress.compareTo(buyerVault, true) != 0) return@forEach

                    // 获取接收地址
                    val toAddr = tx["to"] as? String ?: return@forEach
                    val toAddress = normalizeAddress(toAddr)
                    
                    // 获取转账金额（原生代币精度为18）
                    val value = tx["value"] as? String ?: return@forEach
                    val amount = BigDecimal(value)

                    // 根据接收地址判断是卖家还是平台的权利金
                    when {
                        toAddress.compareTo(sellerVault, true) == 0 -> {
                            sellerPremiumFee = amount
                        }
                        toAddress.compareTo(PLATFORM_PREMIUM_ADDRESS, true) == 0 -> {
                            platformPremiumFee = amount
                        }
                    }
                } catch (e: Exception) {
                    logger.error("Failed to parse internal transaction: ${e.message}")
                }
            }

            return if (sellerPremiumFee == null && platformPremiumFee == null) {
                null
            } else {
                PremiumFeeDistribution(sellerPremiumFee, platformPremiumFee)
            }

        } catch (e: Exception) {
            logger.error("Failed to get internal transactions for tx: $txHash", e)
            return null
        }
    }

    /**
     * 结算空投首单永赚
     */
    fun settleMoonlightBox(
        contractAddress: String,
        privateKey: String,
        address: String,
        gasLimit: Int,
        gasPrice: BigInteger? = null,
        nftId: BigInteger = BigInteger.ONE
    ): String {
        val inputs: List<Type<*>> = listOf(
            Address(address),
            Uint256(nftId),
            Uint256(1)
        )
        val outputs: MutableList<TypeReference<*>> = ArrayList()
        val method = "adminMint"
        val function = Function(method, inputs, outputs)
        val data = FunctionEncoder.encode(function)
        val credentials = Credentials.create(privateKey)
        val rawTransaction = RawTransaction.createTransaction(
            evmUtil.getNonce(credentials.address),
            gasPrice ?: evmUtil.getGasPrice(""),
            gasLimit.toBigInteger(),
            contractAddress,
            BigInteger.valueOf(0),
            data
        )
        val signMessage = TransactionEncoder.signMessage(rawTransaction, evmUtil.chainId, credentials)
        val toHexString = Numeric.toHexString(signMessage)
        val ethCall = evmUtil.web3j.ethSendRawTransaction(toHexString).sendAsync().get()
        if (ethCall.hasError()) {
            val errorMsg = ethCall.error.message
            // max fee per gas less than block base fee: address ******************************************, maxFeePerGas: 10000000 baseFee: 10041000
            if (errorMsg.startsWith("max fee per gas less than block base fee")) {
                val regex = Regex("baseFee:\\s*(\\d+)")
                regex.find(errorMsg)?.let {
                    val baseFee = it.groupValues[1].toBigInteger()
                    logger.debug("current baseFee: $baseFee")
                    val move = baseFee.toString(10).length - 2
                    val divisor = BigInteger.TEN.pow(move)
                    val newBaseFee = (baseFee.divide(divisor) + BigInteger.ONE).multiply(divisor)
                    logger.debug("new baseFee: $newBaseFee")
                    Thread.sleep(3000)

                    return settleMoonlightBox(
                        contractAddress,
                        privateKey,
                        address,
                        gasLimit,
                        newBaseFee,
                        nftId = nftId
                    )
                }
            } else {
                logger.error("Error Message: ${ethCall.error.message}")
                throw Exception("Failed to send transaction")
            }
        }
        val txHash = ethCall.transactionHash
        logger.info("method: $method\t txid: $txHash")

        return txHash
    }

    fun parseERC20TransferToAddressFromInputData(
        inputData: String
    ): Pair<String?, BigInteger?>{

        val usdtCall = usdtAbi.decodeFunctionCall(inputData)

        //logger.info(decodeEntryPointCall.name)

        val opsParameter = usdtCall.params

        var toAddress: String? = null
        var amount: BigInteger? = null
        for(opsParam in opsParameter){

            if(opsParam.name == "recipient"){
                toAddress = opsParam.value as String
            }

            if(opsParam.name == "amount"){
                amount = opsParam.value as BigInteger
            }
        }

        return Pair(toAddress, amount)
    }

    fun saveMiniBridgeSwapOrderDepositHash(
        chain: ChainType,
        from: String,
        asset: Symbol,
        amount: BigInteger,
        txHash: String
    ){

        val existingOrders = miniBridgeSwapOrderRepository.findByDepositTxHash(
            txHash
        )

        if(existingOrders.isNotEmpty()){
            logger.info("EVM Service: swap order txHash $txHash already exists")
            return
        }

        val usdtDecimal = currencyService.getCurrencyDecimal(
            chainType,
            asset
        )

        val amountIn = BigDecimal(amount).movePointLeft(usdtDecimal)

        logger.info("$txHash : search mini bridge swap order $chain, $from, $asset, $amountIn")
        val orders = miniBridgeSwapOrderRepository.findByFromChainAndFromAddressIgnoreCaseAndFromAssetAndDepositAmountAndStatusAndDepositTxHashIsNull(
            chain,
            from,
            asset,
            amountIn,
            MiniBridgeSwapOrderStatus.PENDING_DEPOSIT
        )

        if(orders.isNotEmpty()){
            val order = orders.first()
            logger.info("$txHash : search mini bridge swap order $chain, $from, $asset, $amountIn order id: ${order.id}")
            order.depositTxHash = txHash
            miniBridgeSwapOrderRepository.save(order)
        } else  {
            logger.info("$txHash : search mini bridge swap order $chain, $from, $asset, $amountIn not found")
        }
    }

    /**
     * 检查抵押物返还情况
     * @param optionOrder 期权订单
     * @return Boolean 是否返还成功
     */
    fun checkUnderlyingAssetReturn(optionOrder: OptionOrder): Boolean {
        val buyerVault = optionOrder.buyerVault ?: return false
        val sellerVault = optionOrder.sellerVault ?: return false
        val premiumAsset = optionOrder.premiumAsset ?: return false
        val premiumAssetAddress = premiumAsset.address ?: return false
        val settlementTxHash = optionOrder.settlementHash ?: return false

        // 检查是否是平台币
        val isNativeToken = premiumAssetAddress.equals("0xEeeeeEeeeEeEeeEeEeEeeEEEeeeeEeeeeeeeEEeE", true)

        return if (isNativeToken) {
            // 检查内部交易
            checkInternalTransactionUnderlyingAsset(settlementTxHash, buyerVault, sellerVault)
        } else {
            // 检查ERC20转账事件
            checkERC20TransferUnderlyingAsset(settlementTxHash, buyerVault, sellerVault, premiumAssetAddress)
        }
    }

    /**
     * 检查内部交易中的平台币返还
     */
    private fun checkInternalTransactionUnderlyingAsset(
        txHash: String,
        buyerVault: String,
        sellerVault: String
    ): Boolean {
        try {
            val internalTxs = getInternalTxListWithHash(txHash, times = 2)
            
            return internalTxs.any { tx ->
                try {
                    val fromAddr = tx["from"] as? String ?: return@any false
                    val fromAddress = normalizeAddress(fromAddr)
                    if (fromAddress.compareTo(buyerVault, true) != 0) return@any false

                    val toAddr = tx["to"] as? String ?: return@any false
                    val toAddress = normalizeAddress(toAddr)
                    
                    toAddress.compareTo(sellerVault, true) == 0
                } catch (e: Exception) {
                    logger.error("Failed to parse internal transaction: ${e.message}", e)
                    false
                }
            }
        } catch (e: Exception) {
            logger.error("Failed to get internal transactions for tx: $txHash", e)
            return false
        }
    }

    /**
     * 检查ERC20抵押物返还事件
     */
    private fun checkERC20TransferUnderlyingAsset(
        txHash: String,
        buyerVault: String,
        sellerVault: String,
        premiumAssetAddress: String
    ): Boolean {
        try {
            val receipt = evmUtil.web3j.ethGetTransactionReceipt(txHash).send().transactionReceipt.get()
            
            return receipt.logs.any { log ->
                try {
                    // 检查是否是对应资产的Transfer事件
                    if (log.address.compareTo(premiumAssetAddress, true) != 0) return@any false
                    
                    val topics = log.topics
                    if (topics.size < 3) return@any false
                    
                    // 检查事件签名是否是Transfer
                    if (topics[0] != usdtTransferFnHash) return@any false
                    
                    // 获取from地址
                    val fromAddress = normalizeAddress(topics[1])
                    if (fromAddress.compareTo(buyerVault, true) != 0) return@any false
                    
                    // 获取to地址
                    val toAddress = normalizeAddress(topics[2])
                    
                    toAddress.compareTo(sellerVault, true) == 0
                } catch (e: Exception) {
                    logger.error("Failed to parse transfer log: ${e.message}", e)
                    false
                }
            }
        } catch (e: Exception) {
            logger.error("Failed to get transaction receipt for tx: $txHash", e)
            return false
        }
    }

    /**
     * 检查redeem交易是否符合规则
     */
    fun checkRedeem(tx: Transaction) {
        try {
            // 检查是否是redeem方法调用
            val inputData = Numeric.cleanHexPrefix(tx.input)
            if (!inputData.startsWith(redeemMethodId)) { // redeem方法的函数选择器
                return
            }
            
            // 获取交易收据
            val receipt = evmUtil.web3j.ethGetTransactionReceipt(tx.hash).send().transactionReceipt.get()
            
            // 检查交易状态
            if (receipt.status != "0x1") {
                return
            }
            
            // 检查logs中是否有符合条件的记录
            val fromAddress = tx.from

            receipt.logs.map { log ->
                if (log.topics.firstOrNull() == redeemTopic) {
                    val data = Numeric.cleanHexPrefix(log.data)
                    val chunks = data.chunked(64)
                    if (chunks.size >= 2) {
                        val addressInLog = "0x${chunks[1].substring(24)}" // 提取地址部分
                        val result = addressInLog.equals(fromAddress, ignoreCase = true)
                        if (!result) {
                            // 发出警报
                            alertService.emergency("[Redeem时资金未回到发起交易的钱包] txHash=${tx.hash} fromAddress=$fromAddress, logAddress=$addressInLog")
                        }
                    } else {
                        return@map
                    }
                } else {
                    return@map
                }
            }
        } catch (e: Exception) {
            logger.error("Failed to get redeem info for txHash: ${tx.hash}", e)
            return
        }
    }
}