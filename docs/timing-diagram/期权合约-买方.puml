@startuml
participant BuyerUser as B
participant Server as Srv
participant OptionContract as OC

B -> Srv: 查询当前可购买的订单列表
activate Srv
Srv -> B: 返回订单列表
deactivate Srv

B -> Srv: 选中订单并提交参数及授权签名
activate Srv

Srv -> OC: 向期权合约发起创建请求
activate OC
OC --> Srv: 返回合约创建成功的结果
deactivate OC

Srv -> B: 通知订单已成功创建
deactivate Srv

Srv -> OC: 到期发起结算操作
activate Srv
activate OC
OC -> Srv: 结算完成
deactivate Srv
OC --> B: 到账
deactivate OC
@enduml
