package io.vault.jasper.repository


import io.vault.jasper.model.*
import org.springframework.data.mongodb.repository.MongoRepository
import org.springframework.stereotype.Repository
import java.math.BigDecimal

@Repository
interface BinanceTradeRebateRecordRepository : MongoRepository<BinanceTradeRebateRecord, String> {
    fun findByOptionOrderId(optionOrderId: String): BinanceTradeRebateRecord?
    fun findTopByOrderByOptionOrderIdDesc(): BinanceTradeRebateRecord?
    fun findByStatus(
        status: BinanceTradeRebateRecordStatus
    ): List<BinanceTradeRebateRecord>

    fun findByBuyerAddress(
        buyerAddress: String
    ): List<BinanceTradeRebateRecord>

    fun findByBuyerAddressIgnoreCase(
        buyerAddress: String
    ): List<BinanceTradeRebateRecord>

    fun findByBuyerAddressIgnoreCaseAndStatus(
        buyerAddress: String,
        status: BinanceTradeRebateRecordStatus
    ): List<BinanceTradeRebateRecord>

    fun findByStatusAndSettleNftTxIdIsNotNull(
        status: BinanceTradeRebateRecordStatus
    ): List<BinanceTradeRebateRecord>

    fun findByStatusAndSettleMoonlightBoxTxIdIsNotNull(
        status: BinanceTradeRebateRecordStatus
    ): List<BinanceTradeRebateRecord>

    fun findByStatusInAndSettleNftTxIdIsNotNull(
        statuses: List<BinanceTradeRebateRecordStatus>,
    ): List<BinanceTradeRebateRecord>

    fun findByStatusAndSettleNftTxIdIsNotNullAndSettledNft(
        status: BinanceTradeRebateRecordStatus,
        settled: Boolean
    ): List<BinanceTradeRebateRecord>

    fun findByStatusAndSettleMoonlightBoxTxIdIsNotNullAndSettledMoonlightBox(
        status: BinanceTradeRebateRecordStatus,
        settled: Boolean
    ): List<BinanceTradeRebateRecord>

    fun findBySettlementPrice(
        price: BigDecimal
    ): List<BinanceTradeRebateRecord>
}