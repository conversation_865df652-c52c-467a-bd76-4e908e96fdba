@startuml

actor "卖家用户" as seller
actor "买家用户" as buyer
participant "Dapp" as dapp
participant "中心服务器" as server
participant "智能合约" as contract
participant "区块链" as blockchain

seller -> dapp: 发起挂单
activate dapp
dapp -> server: 创建挂单\n提交挂单信息及签名
activate server
server -> dapp: 挂单成功
deactivate server
dapp -> seller: 挂单成功
deactivate dapp

seller -> dapp: 发起撤单
activate dapp
dapp -> contract: 请求废除老签名的方法
activate contract
contract --> dapp: 操作完成
deactivate contract
dapp -> server: 请求撤单操作
activate server
server -> dapp: 操作成功
deactivate server
dapp -> seller: 操作成功
deactivate dapp

buyer -> dapp: 查询挂单列表
activate dapp
dapp -> server: 当前挂单列表
activate server
server -> dapp: 返回当前挂单列表
deactivate server
dapp -> buyer: 展示挂单列表
deactivate dapp

buyer -> dapp: 选择挂单信息并提交
activate dapp
dapp -> server: 请求挂单详情\n带上用户的签名
activate server
server -> dapp: 返回挂单详情及卖家签名
deactivate server
dapp -> buyer: 请求对交易签名
deactivate dapp

buyer -> dapp: 对交易进行签名
activate dapp
dapp -> contract: 发起交易
activate contract
contract --> dapp: 操作完成\n返回订单ID
deactivate contract
dapp -> server: 提交链上订单ID
activate server
server -> dapp: 订单修改完成
deactivate server
dapp -> buyer: 操作完成
deactivate dapp

loop
server -> blockchain: 查看订单的交易结果
activate server
activate blockchain
blockchain --> server: 返回结果并更新订单
deactivate server
deactivate blockchain
end loop

@enduml
