package io.vault.jasper.blockchain

import com.fasterxml.jackson.databind.JsonNode
import com.fasterxml.jackson.databind.ObjectMapper
import org.slf4j.LoggerFactory
import org.springframework.web.client.RestTemplate

abstract class EvmScanApi {

    private val logger = LoggerFactory.getLogger(this::class.java)

    protected abstract val baseUrl: String

    protected abstract val apiTokens: List<String>

    private var tokenIndex = 0

    private val tokenSize: Int
        get() {
            return apiTokens.size
        }

    private val restTemplate = RestTemplate()

    private val objectMapper = ObjectMapper()

    private fun getApiToken(): String {
        val token = try {
            apiTokens[tokenIndex]
        } catch (e: IndexOutOfBoundsException) {
            ""
        }
        tokenIndex += 1
        if (tokenIndex >= tokenSize) {
            tokenIndex = 0
        }

        return token
    }

    fun getInternalTransactionsByTxHash(txHash: String): List<Map<String, Any?>> {
        var url = "${baseUrl}?module=account&action=txlistinternal&txhash=${txHash}"

        val apiToken = getApiToken()
        if (apiToken.isNotBlank()) {
            url += "&apikey=${apiToken}"
        }
        //logger.info("Scan Api: $url")
        restTemplate.getForObject(url, String::class.java)?.let {
            //logger.info("Get internal transactions by tx hash=$txHash, response: $it")
            val response = objectMapper.readTree(it)
            val status = response["status"].asText().toInt() // 1 for success, others for failure
            val result = response["result"]

            // Sleep for 0.3s for rate limit
            Thread.sleep(300)

            if (status == 1) {
                return objectMapper.readValue(result.toString(), List::class.java) as List<Map<String, Any?>>
            }
            throw RuntimeException("Failed to get internal transactions by tx hash=$txHash, result: $result")
        } ?: throw RuntimeException("Failed to get internal transactions by tx hash=$txHash")
    }

    fun getEventLogsByAddressAndTopics(
        address: String,
        topicsFirst: String,
        fromBlock: Long,
        toBlock: Long,
        page: Int,
        offset: Int = 1000
    ): JsonNode? {
        var url = "${baseUrl}?module=logs&action=getLogs&fromBlock=${fromBlock}&toBlock=${toBlock}&address=${address}" +
                "&topic0=${topicsFirst}&page=${page}&offset=${offset}"
        val apiToken = getApiToken()
        if (apiToken.isNotBlank()) {
            url += "&apikey=${apiToken}"
        }
        restTemplate.getForObject(url, String::class.java)?.let {
            // logger.info("Get event logs by address=$address, topicsFirst=$topicsFirst, page=$page, response: $it")
            val response = objectMapper.readTree(it)
            val status = response["status"].asText().toInt() // 1 for success, others for failure
            val result = response["result"]
            if (status == 1) {
                return result
            } else {
                //logger.info("Status!=1, response=$it")
                return null
            }
            // throw RuntimeException("Failed to get event logs by address=$address, topicsFirst=$topicsFirst, page=$page, result: $result")
        } ?: throw RuntimeException("Failed to get event logs by address=$address, topicsFirst=$topicsFirst, page=$page")
    }
}