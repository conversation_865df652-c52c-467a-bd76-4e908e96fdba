package io.vault.jasper.service

import io.vault.jasper.enums.ChainType
import io.vault.jasper.enums.Symbol
import io.vault.jasper.model.*
import io.vault.jasper.repository.BlockchainRepository
import io.vault.jasper.repository.BtrCampaignConfigRepository
import io.vault.jasper.repository.BtrCampaignRecordRepository
import io.vault.jasper.repository.UserRepository
import org.slf4j.LoggerFactory
import org.springframework.stereotype.Service
import java.time.LocalDateTime
import javax.annotation.PostConstruct
import java.math.BigDecimal
import org.springframework.data.mongodb.core.MongoTemplate
import org.springframework.data.mongodb.core.query.Criteria
import org.springframework.data.mongodb.core.query.Query
import org.springframework.data.mongodb.core.query.Update

@Service
class BtrCampaignService(
    private val btrCampaignConfigRepository: BtrCampaignConfigRepository,
    private val btrCampaignRecordRepository: BtrCampaignRecordRepository,
    private val blockchainRepository: BlockchainRepository,
    private val mongoTemplate: MongoTemplate,
    private val userRepository: UserRepository
) {
    private val logger = LoggerFactory.getLogger(this::class.java)

    @PostConstruct
    fun init() {
        // 检查是否已存在配置
        if (btrCampaignConfigRepository.count() == 0L) {
            // 创建新配置
            val config = BtrCampaignConfig(
                startTime = LocalDateTime.of(2025, 3, 3, 0, 0, 0),
                endTime = LocalDateTime.of(2025, 3, 23, 0, 0, 0),
                totalBtrAmount = BigDecimal("50000"),
                distributedBtrAmount = BigDecimal.ZERO,
                btrAmountPerOrder = BigDecimal("2"),
                enabled = true
            )
            
            btrCampaignConfigRepository.save(config)
            logger.info("BTR Campaign: Created initial campaign config")
        }
    }

    @Synchronized
    fun handleOptionOrder(optionOrder: OptionOrder) {
        // 检查是否是 Bitlayer 链的订单
        if (optionOrder.chain != ChainType.BITLAYER) {
            logger.info("BTR Campaign: Option order ${optionOrder.id} is not on Bitlayer chain")
            return
        }

        // 检查是否是免费订单
        if (optionOrder.premiumFree == true) {
            logger.info("BTR Campaign: Option order ${optionOrder.id} is a premium free order")
            return
        }

        if (optionOrder.premiumFeePay?.compareTo(optionOrder.premiumFeeShouldPay) != 0) {
            logger.info("BTR Campaign: Option order ${optionOrder.id} is a trading credit order.")
            return
        }

        // 检查 bidAsset 是否为 BTC
        if (optionOrder.bidAsset != Symbol.BTC) {
            logger.info("BTR Campaign: Option order ${optionOrder.id} bid asset is not BTC")
            return
        }

        // 检查 bidAmount 是否大于等于 0.01
        if (optionOrder.bidAmount == null || optionOrder.bidAmount!!.compareTo(BigDecimal("0.01")) == -1) {
            logger.info("BTR Campaign: Option order ${optionOrder.id} bid amount is less than 0.01 BTC")
            return
        }

        // 检查是否已经处理过
        if (btrCampaignRecordRepository.existsByOptionOrderId(optionOrder.id!!)) {
            logger.info("BTR Campaign: Option order ${optionOrder.id} already processed")
            return
        }

        // 获取活动配置
        val config = btrCampaignConfigRepository.findFirstByEnabled(true) ?: run {
            logger.info("BTR Campaign: No active campaign found")
            return
        }

        // 检查是否在活动期间
        if (!config.isInCampaignPeriod(optionOrder.created)) {
            logger.info("BTR Campaign: Option order ${optionOrder.id} not in campaign period")
            return
        }

        // 检查是否是普通订单 (stoneActivityNftId 为 null)
        if (optionOrder.stoneActivityNftId != null) {
            logger.info("BTR Campaign: Option order ${optionOrder.id} is not a normal order")
            return
        }

        // 检查是否还有剩余 BTR
        if (!config.canDistributeBtr()) {
            logger.info("BTR Campaign: No more BTR available")
            return
        }

        // 创建奖励记录
        val record = BtrCampaignRecord(
            address = optionOrder.buyer!!,
            optionOrderId = optionOrder.id,
            btrAmount = config.btrAmountPerOrder
        )
        btrCampaignRecordRepository.save(record)

        // 更新已发放总量
        val query = Query(Criteria.where("_id").`is`(config.id))
        val newDistributedAmount = config.distributedBtrAmount.add(config.btrAmountPerOrder)
        val update = Update().set("distributed_btr_amount", newDistributedAmount)
        mongoTemplate.updateFirst(query, update, BtrCampaignConfig::class.java)

        logger.info("BTR Campaign: Distributed ${config.btrAmountPerOrder} BTR to user ${optionOrder.buyer} for order ${optionOrder.id}")
    }
} 