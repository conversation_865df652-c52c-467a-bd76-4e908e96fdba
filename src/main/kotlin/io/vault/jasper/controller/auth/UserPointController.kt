package io.vault.jasper.controller.auth

import EVMAddress
import io.swagger.annotations.ApiOperation
import io.vault.jasper.ApiResponse
import io.vault.jasper.annotation.ApiCache
import io.vault.jasper.dto.AuthDTO
import io.vault.jasper.dto.KolApplyDTO
import io.vault.jasper.enums.Symbol
import io.vault.jasper.exception.BusinessException
import io.vault.jasper.exception.ResultEnum
import io.vault.jasper.model.*
import io.vault.jasper.repository.*
import io.vault.jasper.response.*
import io.vault.jasper.service.UserPointService
import io.vault.jasper.service.UserService
import io.vault.jasper.service.kol.KolLevelService
import io.vault.jasper.utils.AuthUtil
import io.vault.jasper.utils.DateTimeUtil
import org.slf4j.LoggerFactory
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.data.domain.PageImpl
import org.springframework.data.domain.PageRequest
import org.springframework.data.domain.Sort
import org.springframework.data.mongodb.core.MongoTemplate
import org.springframework.data.mongodb.core.query.Criteria
import org.springframework.data.mongodb.core.query.Query
import org.springframework.data.repository.findByIdOrNull
import org.springframework.validation.annotation.Validated
import org.springframework.web.bind.annotation.*
import java.math.BigDecimal
import java.time.LocalDate
import java.time.ZoneId
import java.time.format.DateTimeFormatter
import java.util.*
import javax.servlet.http.HttpServletRequest
import javax.validation.Valid
import javax.validation.constraints.Min


@RestController
@RequestMapping("/point")
class UserPointController @Autowired constructor(
    private val userPointDailySummaryRepository: UserPointDailySummaryRepository,
    private val authUtil: AuthUtil,
    private val userPointService: UserPointService,
    private val mongoTemplate: MongoTemplate,
) {

    private var logger = LoggerFactory.getLogger(this::class.java)

    @ApiOperation("用户积分总览")
    @CrossOrigin
    @PostMapping("/info")
    fun info(
        request: HttpServletRequest,
        @Valid @RequestBody dto: AuthDTO
    ): ApiResponse<UserPointInfoResponse> {

        authUtil.filterIPBlacklist(request)
        authUtil.auth(request, dto.address)

        val yesterDayPoint = userPointService.getPointsInDays(
            dto.address,
            1
        )

        val pointsIn7Days = userPointService.getPointsInDays(
            dto.address,
            7
        )

        val pointsIn30Days = userPointService.getPointsInDays(
            dto.address,
            30
        )

        val totalPoint = userPointService.sumTotalPointByAddress(
            dto.address,
            UserPointDailySummary::premiumPoint.name
        )

        val response = UserPointInfoResponse(
            address = dto.address,
            totalPoint = totalPoint.stripTrailingZeros().toPlainString(),
            yesterdayPoint = yesterDayPoint.stripTrailingZeros().toPlainString(),
            pointsIn7Days = pointsIn7Days.stripTrailingZeros().toPlainString(),
            pointsIn30Days = pointsIn30Days.stripTrailingZeros().toPlainString()
        )

        return ApiResponse.success(response)
    }

    @ApiOperation("用户 Loyalty 积分总览")
    @CrossOrigin
    @PostMapping("/loyalty_info")
    fun loyaltyInfo(
        request: HttpServletRequest,
        @Valid @RequestBody dto: AuthDTO
    ): ApiResponse<UserLoyaltyPointInfoResponse> {

        authUtil.filterIPBlacklist(request)
        authUtil.auth(request, dto.address)

        val totalPoint = userPointService.sumTotalPointByAddress(
            dto.address,
            UserPointDailySummary::loyaltyPoint.name
        )

        val allRecords = userPointDailySummaryRepository.findByAddress(dto.address)

        val today = LocalDate.now()
        val dateFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd")
        val todayStr = dateFormatter.format(today)

        var currentStreak = 0
        val currentStreakRecord = userPointDailySummaryRepository.findByAddressAndDateString(
            dto.address,
            todayStr
        )

        if(currentStreakRecord != null){
            currentStreak = currentStreakRecord.streak
        } else {
            val yesterday = LocalDate.now().minusDays(1)
            val yesterdayStr = dateFormatter.format(yesterday)

            val yesterdayStreakRecord = userPointDailySummaryRepository.findByAddressAndDateString(
                dto.address,
                yesterdayStr
            )

            if(yesterdayStreakRecord != null) {
                currentStreak = yesterdayStreakRecord.streak
            }
        }

        var longStreak = 0
        val longStreakRecord = userPointDailySummaryRepository.findTopByAddressOrderByStreakDesc(
            dto.address
        )

        if(longStreakRecord != null){
            longStreak = longStreakRecord.streak
        }

        val response = UserLoyaltyPointInfoResponse(
            address = dto.address,
            totalPoint = totalPoint.stripTrailingZeros().toPlainString(),
            totalTradingDays = allRecords.size,
            currentStreakDays = currentStreak,
            maxStreakDays = longStreak
        )

        return ApiResponse.success(response)
    }

    @ApiOperation("用户 Trading Point 积分列表")
    @CrossOrigin
    @ApiCache(key = "point:point_list", dynamicKey = ["address", "page", "pageSize"], expire = 60)
    @GetMapping("/point_list")
    fun pointList(
        request: HttpServletRequest,
        @RequestParam @EVMAddress address: String,
        @RequestParam @Min(1) page: Int,
        @RequestParam @Min(1) pageSize: Int,
    ): ApiResponse<PageResponse<UserPointListResponse>> {

        authUtil.filterIPBlacklist(request)
        authUtil.auth(request, address)

        val pageable = PageRequest.of(page - 1, pageSize, Sort.by(Sort.Order.desc(UserPointRecord::created.name)))
        val query = Query()
        query.addCriteria(
            Criteria.where(UserPointRecord::address.name).`is`(address)
        )

        val totalCount = mongoTemplate.count(query, UserPointRecord::class.java)
        query.with(pageable)
        val responseResult = mongoTemplate.find(query, UserPointRecord::class.java)
        val pageObj = PageImpl(responseResult, pageable, totalCount)
        val responseList = responseResult.mapNotNull {

            var premium = it.premiumInUsdt
            if(premium.compareTo(BigDecimal.ZERO) == 0){
                premium = it.premium
            }

            UserPointListResponse(
                address = it.address,
                point = it.premiumPoint.stripTrailingZeros().toPlainString(),
                premiumAndFee = premium.stripTrailingZeros().toPlainString(),
                created = DateTimeUtil.convertLocalDateTimeToTimestamp(it.created)
            )
        }

        return ApiResponse.success(PageResponse(
            page = page,
            pageSize = pageSize,
            totalPage = pageObj.totalPages,
            total = totalCount,
            hasNext = pageObj.hasNext(),
            data = responseList
        ))
    }

    @ApiOperation("用户 Loyalty Point 积分列表")
    @CrossOrigin
    @ApiCache(key = "point:loyalty_point_list", dynamicKey = ["address", "page", "pageSize"], expire = 60)
    @GetMapping("/loyalty_point_list")
    fun loyaltyPointList(
        request: HttpServletRequest,
        @RequestParam address: String,
        @RequestParam @Min(1) page: Int,
        @RequestParam @Min(1) pageSize: Int,
    ): ApiResponse<PageResponse<UserPointListResponse>> {

        authUtil.filterIPBlacklist(request)
        authUtil.auth(request, address)

        val pageable = PageRequest.of(page - 1, pageSize, Sort.by(Sort.Order.desc(UserPointDailySummary::dateString.name)))
        val query = Query()
        query.addCriteria(
            Criteria.where(UserPointDailySummary::address.name).`is`(address)
        )

        val totalCount = mongoTemplate.count(query, UserPointDailySummary::class.java)
        query.with(pageable)
        val responseResult = mongoTemplate.find(query, UserPointDailySummary::class.java)
        val pageObj = PageImpl(responseResult, pageable, totalCount)
        val responseList = responseResult.mapNotNull {
            UserPointListResponse(
                address = it.address,
                dateString = it.dateString,
                loyaltyPoint = it.loyaltyPoint.stripTrailingZeros().toPlainString(),
                streak = it.streak
            )
        }

        return ApiResponse.success(PageResponse(
            page = page,
            pageSize = pageSize,
            totalPage = pageObj.totalPages,
            total = totalCount,
            hasNext = pageObj.hasNext(),
            data = responseList
        ))
    }
}