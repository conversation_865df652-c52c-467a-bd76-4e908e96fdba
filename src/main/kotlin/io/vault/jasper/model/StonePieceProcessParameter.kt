package io.vault.jasper.model

import org.springframework.data.annotation.CreatedDate
import org.springframework.data.annotation.Id
import org.springframework.data.annotation.LastModifiedDate
import org.springframework.data.mongodb.core.mapping.Document
import org.springframework.data.mongodb.core.mapping.Field
import java.time.LocalDateTime

/**
 * 石头碎片处理定时任务参数配置
 */
@Document("stone_piece_process_parameter")
data class StonePieceProcessParameter(
    @Id
    val id: String? = null,
    
    /**
     * 任务开关，默认关闭
     */
    var enabled: Boolean = false,
    
    /**
     * 需要处理的NFT ID列表
     */
    @Field("nft_ids")
    var nftIds: List<String> = emptyList(),
    
    @CreatedDate
    val created: LocalDateTime = LocalDateTime.now(),
    
    @LastModifiedDate
    val updated: LocalDateTime = LocalDateTime.now()
)
