package io.vault.jasper.response

import io.swagger.annotations.ApiModel
import io.swagger.annotations.ApiModelProperty
import io.vault.jasper.model.UserNetworkGrade
import java.math.BigDecimal

@ApiModel("用户网络信息")
data class UserNetworkInfoResponse(
    @ApiModelProperty("用户地址", required = true)
    val address: String,
    @ApiModelProperty("邀请码", required = true)
    val inviteCode: String,
    @ApiModelProperty("邀请人的邀请码", required = true)
    val parentInviteCode: String,
    @ApiModelProperty("返佣比例", required = true)
    val percentage: String,
    @ApiModelProperty("用户等级", required = true)
    val grade: UserNetworkGrade?,
    @ApiModelProperty("直推用户数", required = true)
    val directMemberCount: Int,
    @ApiModelProperty("团队用户数", required = true)
    val teamMemberCount: Int,
    @ApiModelProperty("自身业绩", required = true)
    val selfPremium: String,
    @ApiModelProperty("直推业绩", required = true)
    val directPremium: String,
    @ApiModelProperty("团队业绩", required = true)
    val teamPremium: String,
    @ApiModelProperty("别名", required = false)
    val alias: String?,
    @ApiModelProperty("累计从下面团队领取的佣金", required = false)
    val teamRebate: String?,
    @ApiModelProperty("累计为上级的佣金", required = false)
    val parentRebate: String?,
    @ApiModelProperty("注册时间", required = false)
    val created: Long,
    @ApiModelProperty("能否设置下一级代理", required = false)
    val canSetAgent: Boolean
)