package io.vault.jasper.service

import io.vault.jasper.exception.BusinessException
import io.vault.jasper.exception.ResultEnum
import io.vault.jasper.model.*
import io.vault.jasper.repository.KolRepository
import io.vault.jasper.repository.UserCampaignInfoRepository
import io.vault.jasper.repository.UserNetworkRepository
import io.vault.jasper.repository.UserRepository
import io.vault.jasper.service.kol.KolLevelService
import org.slf4j.LoggerFactory
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.data.mongodb.core.MongoTemplate
import org.springframework.data.mongodb.core.query.Criteria
import org.springframework.data.mongodb.core.query.Query
import org.springframework.data.repository.findByIdOrNull
import org.springframework.stereotype.Service
import org.web3j.crypto.Keys
import java.math.BigDecimal
import java.time.LocalDateTime
import java.util.concurrent.ThreadLocalRandom

@Service
class UserService @Autowired constructor(
    private val userRepository: UserRepository,
    private val userPremiumSummaryService: UserPremiumSummaryService,
    private val kolRepository: KolRepository,
    private val kolLevelService: KolLevelService,
    private val referralInfoService: ReferralInfoService,
    private val mongoTemplate: MongoTemplate,
    private val userNetworkRepository: UserNetworkRepository,
    private val airDropService: AirDropService,
    private val userNetworkService: UserNetworkService
){

    private val logger = LoggerFactory.getLogger(this::class.java)

    fun bindInvitor(address: String, inviteCode: String) {
        val user = userRepository.findByAddressIgnoreCase(address)
        if (user == null) {
            throw BusinessException(ResultEnum.USER_NOT_FOUND)
        }

        if(user.invitedUserId != null) {
            throw BusinessException(ResultEnum.USER_ALREADY_INVITED)
        }

        val invitor = userRepository.findByInviteCode(inviteCode)
        if (invitor == null) {
            throw BusinessException(ResultEnum.INVITOR_NOT_FOUND)
        }

        if(invitor.id == user.id){
            throw BusinessException(ResultEnum.INVITOR_CAN_NOT_BE_YOURSELF)
        }

        if(invitor.created > user.created){
            throw BusinessException(ResultEnum.INVITOR_MUST_OLDER)
        }

        user.invitedUserId = invitor.id!!
        userRepository.save(user)

        //Build User Network
        val invitorNetwork = userNetworkRepository.findByUserId(invitor.id)
        if(invitorNetwork != null){

            logger.info("Invitor ${invitorNetwork.address} network is not null for new user ${user.address}")

            val userNetwork = UserNetwork(
                userId = user.id!!,
                address = user.address,
                invitedUserId = invitor.id,
                invitedNetworkId = invitorNetwork.id!!,
                inviteCode = user.inviteCode,
                level = invitorNetwork.level + 1,
            )

            userNetwork.checkKolRebate = true
            userNetworkRepository.save(userNetwork)

            userNetworkService.updateMemberCount(userNetwork)

            user.useKolRebate = false
            userRepository.save(user)
        } else {
            logger.info("Invitor network is null for user ${user.address}")
        }

        // 更新邀请人的每日推荐统计
        referralInfoService.updateReferralCountAtDate(invitor)

        // 更新邀请人的总推荐数
        referralInfoService.updateUserReferralCount(invitor)

        //Update Airdrop campaign user count
        airDropService.updateUserCampaignReferralCount(invitor)
    }

    fun getRefereesAddress(user: User): List<String> {
        return userRepository.findByInvitedUserId(user.id!!).map { it.address }
    }

    fun getRefereesAddressAfterTime(
        user: User,
        afterTime: LocalDateTime
    ): List<String> {
        return userRepository.findByInvitedUserIdAndCreatedBetween(
            user.id!!,
            afterTime,
            LocalDateTime.now()
        ).map { it.address }
    }

    fun getRefereesCount(
        user: User,
        afterTime: LocalDateTime? = null,
    ): Long {

        val query = Query()
        query.addCriteria(Criteria.where(User::invitedUserId.name).`is`(user.id!!))

        if (afterTime != null) {
            query.addCriteria(Criteria.where(User::created.name).gte(afterTime))
        }

        return mongoTemplate.count(query, User::class.java)
    }

    fun generateInviteCode(): String{

        var tryCount = 10;
        var new_invite_code = ""

        while (tryCount > 0) {

            tryCount --

            val length = 6
            val characters = "abcdefghigkmnprstuvwxyz1234567890"
            new_invite_code = ""

            for (i in 1..length) {
                val index = ThreadLocalRandom.current().nextInt(0, characters.length)
                new_invite_code += characters.substring(index, index+1)
            }

            logger.info("The new invite code is : $new_invite_code")

            val existLeft = userRepository.findByInviteCode(new_invite_code)
            if(existLeft != null){
                continue
            }

            break
        }

        if(tryCount == 0){
            throw BusinessException(ResultEnum.INVITE_CODE_NOT_FOUND)
        }

        return new_invite_code
    }

    fun findUserParent(user: User): User? {
        if(user.invitedUserId == null){
            return null
        }
        return userRepository.findByIdOrNull(user.invitedUserId)
    }

    fun register(
        address: String,
        referralCode: String? = null
    ): User {

        //新用户注册
        var user = userRepository.findByAddressIgnoreCase(address)
        if(user == null) {

            val checksumAddress = Keys.toChecksumAddress(address)

            val inviteCode = generateInviteCode()
            user = userRepository.save(
                User(
                    address = checksumAddress,
                    inviteCode = inviteCode,
                )
            )

            // 更新用户权利金汇总
            try {
                userPremiumSummaryService.updateUserPremiumSummary(user)
            } catch (e: Exception) {
                logger.error("Update user premium summary failed", e)
            }

            // 绑定邀请人
            if(referralCode != null) {
                bindInvitor(checksumAddress, referralCode)
            }

            // 设置最低等级的kol，邀请人获取 12% 的分佣
            val level0 = kolLevelService.getKolLevel("0")
            kolRepository.save(
                Kol(
                    wallet = checksumAddress,
                    level = level0.getName(),
                    status = KolStatus.ACTIVE
                )
            )
        }

        return user!!
    }

    fun getById(id: String): User? = userRepository.findByIdOrNull(id)

    fun getByAddress(address: String): User? = userRepository.findByAddressIgnoreCase(address)

    fun getByDiscordSession(session: String): User? = userRepository.findFirstByDiscordSession(session)
}