package io.vault.jasper.model

import io.vault.jasper.enums.Symbol
import org.springframework.data.annotation.CreatedDate
import org.springframework.data.annotation.Id
import org.springframework.data.annotation.LastModifiedDate
import org.springframework.data.mongodb.core.index.CompoundIndex
import org.springframework.data.mongodb.core.index.Indexed
import org.springframework.data.mongodb.core.mapping.Document
import java.math.BigDecimal
import java.math.BigInteger
import java.time.LocalDateTime

@Document("user_network_rebate_record")
@CompoundIndex(
    name = "option_order_id_network_id",
    def = "{'optionOrderId' : 1, 'userNetworkId': 1}",
    background = true, unique = true
)
data class UserNetworkRebateRecord(
    @Id
    val id: String? = null,
    @Indexed(background = true)
    val optionOrderId: String,
    @Indexed(background = true)
    val buyerAddress: String,
    @Indexed(background = true)
    val userId: String,
    @Indexed(background = true)
    val userNetworkId: String,
    @Indexed(background = true)
    val userAddress: String,
    val asset: Symbol,
    /**
     * 实际返佣
     */
    var incentiveAmount: BigDecimal,
    val premiumFee: BigDecimal,
    val premiumFeeInUsdt: BigDecimal,
    val premiumFeeRate: BigDecimal,
    val incentiveRate: BigDecimal,
    val actualIncentiveRate: BigDecimal? = null,
    var status: UserNetworkRebateRecordStatus = UserNetworkRebateRecordStatus.PENDING,
    var settleTxId: String? = null,
    @CreatedDate
    val created: LocalDateTime = LocalDateTime.now(),
    @LastModifiedDate
    val updated: LocalDateTime = LocalDateTime.now(),
    var grade2Address: String? = null,
    var grade1Address: String? = null,
    var grade0Address: String? = null
) {}

enum class UserNetworkRebateRecordStatus {
    PENDING,
    SETTLED
}