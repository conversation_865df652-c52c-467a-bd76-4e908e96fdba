stages:
  - deploy

v2-server-uat:
  stage: deploy
  only:
    - uat
  when: manual
  variables:
    IMG_NAME: registry.fly.io/$CI_JOB_NAME:$CI_COMMIT_SHORT_SHA
    FLY_FILE: fly/$CI_JOB_NAME.toml
  script:
    - docker build -t $IMG_NAME .
    - docker push $IMG_NAME
    - fly deploy --detach -i "$IMG_NAME" -c $FLY_FILE

v2-server-test:
  stage: deploy
  only:
    - develop
  when: manual
  variables:
    IMG_NAME: registry.fly.io/$CI_JOB_NAME:$CI_COMMIT_SHORT_SHA
    FLY_FILE: fly/$CI_JOB_NAME.toml
  script:
    - docker build -t $IMG_NAME .
    - docker push $IMG_NAME
    - fly deploy --detach -i "$IMG_NAME" -c $FLY_FILE

deploy_prod:
  stage: deploy
  only:
    - main
  when: manual
  variables:
    ENV_NAME: latest
    DO_APPID: "7af9cb0b-227b-4524-acd1-6263fadf1cec"
    BASE_IMG_NAME: registry.digitalocean.com/jaspervault/v2/server

    IMG_NAME: $BASE_IMG_NAME:$ENV_NAME
    COMMIT_IMG_NAME: $BASE_IMG_NAME:$CI_COMMIT_SHORT_SHA
  script:
    - docker build -t $COMMIT_IMG_NAME .
    - docker push $COMMIT_IMG_NAME
    - docker tag $COMMIT_IMG_NAME $IMG_NAME
    - docker push $IMG_NAME
    - doctl version
    # 若长时间无法部署成功可以打开。(运行更新前先执行修改环境变量，达到另类强制Kill服务的方法。)
    - doctl apps spec get $DO_APPID --format yaml > config.yaml
    - bash do_force_update.sh
    - doctl apps update $DO_APPID --spec config.yaml --wait

    - echo -e "doctl apps create-deployment $DO_APPID --wait --force-rebuild"
    - doctl apps create-deployment $DO_APPID --wait --force-rebuild
