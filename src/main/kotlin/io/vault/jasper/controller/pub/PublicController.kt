package io.vault.jasper.controller.pub

import io.swagger.v3.oas.annotations.Operation
import io.vault.jasper.ApiResponse
import io.vault.jasper.annotation.ApiCache
import io.vault.jasper.enums.ChainType
import io.vault.jasper.enums.Symbol
import io.vault.jasper.exception.BusinessException
import io.vault.jasper.exception.ResultEnum
import io.vault.jasper.model.OptionOrder
import io.vault.jasper.model.OptionStatus
import io.vault.jasper.model.UserPointDailySummary
import io.vault.jasper.repository.BlockchainRepository
import io.vault.jasper.repository.OptionOrderRepository
import io.vault.jasper.repository.OrderRepository
import io.vault.jasper.repository.UserRepository
import io.vault.jasper.response.PageResponse
import io.vault.jasper.response.TransactionHistoryResponse
import io.vault.jasper.service.SystemService
import io.vault.jasper.utils.AuthUtil
import io.vault.jasper.utils.DateTimeUtil
import org.slf4j.LoggerFactory
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.data.domain.PageImpl
import org.springframework.data.domain.PageRequest
import org.springframework.data.domain.Sort
import org.springframework.data.mongodb.core.MongoTemplate
import org.springframework.data.mongodb.core.aggregation.Aggregation
import org.springframework.data.mongodb.core.aggregation.ConvertOperators
import org.springframework.data.mongodb.core.query.Criteria
import org.springframework.data.mongodb.core.query.Query
import org.springframework.data.repository.findByIdOrNull
import org.springframework.web.bind.annotation.*
import java.math.BigDecimal
import javax.servlet.http.HttpServletRequest


@RestController
@RequestMapping("/public")
class PublicController @Autowired constructor(
    private val orderRepository: OrderRepository,
    private val blockchainRepository: BlockchainRepository,
    private val mongoTemplate: MongoTemplate,
    private val authUtil: AuthUtil,
    private val systemService: SystemService,
    private val userRepository: UserRepository,
    private val optionOrderRepository: OptionOrderRepository
) {

    private var logger = LoggerFactory.getLogger(this::class.java)

    @Operation(summary = "交易历史")
    @CrossOrigin
    @ApiCache(key = "public:transaction_history", dynamicKey = ["orderId", "chainList", "afterOrderId", "pageSize"], expire = 10)
    @GetMapping("/transaction_history")
    fun transactionHistory(
        @RequestParam(required = false) orderId: String? = null,
        @RequestParam(required = false) chainList: List<ChainType>? = null,
        @RequestParam(required = false) afterOrderId: String? = null,
        @RequestParam(required = false) pageSize: Int? = null,
        request: HttpServletRequest
    ): ApiResponse<PageResponse<TransactionHistoryResponse>> {
        authUtil.filterIPBlacklist(request)
        val p = 1
        val ps = if(pageSize == null || pageSize < 1) 20 else pageSize
        val pageable = PageRequest.of(p - 1, ps, Sort.Direction.DESC, OptionOrder::id.name)

        val query = Query()
        query.addCriteria(Criteria.where(OptionOrder::status.name).`in`(
            listOf(OptionStatus.EXECUTED, OptionStatus.SETTLED)
        ))

        if(!orderId.isNullOrBlank()){
            query.addCriteria(Criteria.where(OptionOrder::orderId.name).gt(orderId))
        }

        if(afterOrderId != null){
            query.addCriteria(Criteria.where(OptionOrder::orderId.name).lt(afterOrderId))
        }

        if(chainList != null){
            query.addCriteria(Criteria.where(OptionOrder::chain.name).`in`(chainList))
        }

        val total = mongoTemplate.count(query, OptionOrder::class.java)
        query.with(pageable)
        val resultSet = mongoTemplate.find(query, OptionOrder::class.java)
        val pageResult = PageImpl(resultSet, pageable, total)
        val content = pageResult.content.mapNotNull {
            val order = orderRepository.findByIdOrNull(it.orderId) ?: return@mapNotNull null
            TransactionHistoryResponse(
                id = order.id!!,
                chain = it.chain,
                creatorAddress = order.creator,
                businessType = order.optionsType!!,
                creatorVaultAddress = it.buyerVault!!,
                sellerAddress = it.seller!!,
                sellerVaultAddress = it.sellerVault!!,

                bidAsset = order.bidAsset,
                bidAmount = order.bidAmount?.stripTrailingZeros()?.toPlainString(),

                underlyingAsset = it.underlyingAsset,
                underlyingAssetAddress = it.underlyingAssetAddress,
                underlyingAssetAmount = it.amount.stripTrailingZeros().toPlainString(),

                strikeAsset = it.strikeAsset,
                strikeAmount = it.strikeAmount.stripTrailingZeros().toPlainString(),
                strikePrice = order.strikePrice.stripTrailingZeros().toPlainString(),

                premiumFeesAsset = if (it.premiumAsset != null) Symbol.valueOf(it.premiumAsset!!.asset) else null,
                premiumFee = it.premiumFeePay?.stripTrailingZeros()?.toPlainString(),

                createDate = DateTimeUtil.convertLocalDateTimeToTimestamp(order.created),
                expiryDate = DateTimeUtil.convertLocalDateTimeToTimestamp(it.expiryDate),
                status = it.status,
                degen = it.jvault,
                expiryInHour = it.expiryInHour
            )
        }

        return ApiResponse.success(PageResponse(
            data = content,
            total = pageResult.totalElements,
            page = p,
            pageSize = ps,
            hasNext = pageResult.hasNext(),
            totalPage = pageResult.totalPages
        ))
    }

    @Operation(summary = "系统状态接口")
    @CrossOrigin
    @ApiCache(key = "public:status", expire = 5)
    @GetMapping("/status")
    fun status(
        request: HttpServletRequest
    ): ApiResponse<String>{
        authUtil.getIpAddr(request)
        val systemParam = systemService.getParameter()
        return ApiResponse.success(systemParam.systemStatus)
    }

    @Operation(summary = "获取某条链最新的区块高度")
    @CrossOrigin
    @ApiCache(key = "public:block_number", dynamicKey = ["chain"], expire = 5)
    @GetMapping("/block_number")
    fun blockNumber(
        request: HttpServletRequest,
        @RequestParam chain: ChainType
    ): ApiResponse<Pair<String, String>>{
        authUtil.getIpAddr(request)
        val blockchain = blockchainRepository.findFirstByChain(chain)

        if(blockchain == null){
            throw BusinessException(ResultEnum.NOT_FOUND)
        }

        val blockchainNumber = blockchain.blockNumber.toString()
        val diff = blockchain.warningDiffCount

        return ApiResponse.success(Pair(blockchainNumber, diff.toString()))
    }

    @Operation(summary = "Galxe JVIP 验证")
    @CrossOrigin
    @GetMapping("/galxe/jvip_verify")
    fun jvipVerify(
        @RequestParam(required = false) address: String? = null,
        @RequestParam(required = false) addressWithout0x: String? = null,
        @RequestParam(required = false) socialId: String? = null,
        request: HttpServletRequest
    ): Int {
        authUtil.filterIPBlacklist(request)
        
        // 如果只有 socialId，直接返回 0
        if (socialId != null && address == null && addressWithout0x == null) {
            return 0
        }

        // 处理地址参数
        val userAddress = when {
            address != null -> address
            addressWithout0x != null -> "0x$addressWithout0x"
            else -> throw BusinessException(ResultEnum.BAD_REQUEST)
        }

        // 查询该用户的所有日汇总的 premiumFees 总和
        val agg = Aggregation.newAggregation(
            Aggregation.match(Criteria.where(UserPointDailySummary::address.name).regex(userAddress, "i")),
            Aggregation.group()
                .sum(ConvertOperators.valueOf(UserPointDailySummary::premiumFees.name).convertToDecimal())
                .`as`("total")
        )

        val groupResults = mongoTemplate.aggregate(agg, UserPointDailySummary::class.java, Map::class.java)
        val result = groupResults.mappedResults.firstOrNull()
        val totalPremium = (result?.get("total") ?: "0").toString().toBigDecimal()
        logger.info("Address=$userAddress\tTotal premium: $totalPremium")

        // 如果总和大于等于 1000，返回 1，否则返回 0
        return if (totalPremium >= BigDecimal("1000")) 1 else 0
    }

    @Operation(summary = "Galxe user 验证")
    @CrossOrigin
    @GetMapping("/galxe/user_verify")
    fun userVerify(
        @RequestParam(required = false) address: String? = null,
        @RequestParam(required = false) addressWithout0x: String? = null,
        @RequestParam(required = false) socialId: String? = null,
        request: HttpServletRequest
    ): Int {
        authUtil.filterIPBlacklist(request)

        // 如果只有 socialId，直接返回 0
        if (socialId != null && address == null && addressWithout0x == null) {
            return 0
        }

        // 处理地址参数
        val userAddress = when {
            address != null -> address
            addressWithout0x != null -> "0x$addressWithout0x"
            else -> throw BusinessException(ResultEnum.BAD_REQUEST)
        }

        // 检查用户是否在option_order表中有成功的订单（作为买家）
        val hasSuccessfulOrder = optionOrderRepository.existsByBuyerIgnoreCaseAndStatusIn(
            userAddress,
            listOf(OptionStatus.EXECUTED, OptionStatus.SETTLED)
        )

        return if (hasSuccessfulOrder) 1 else 0
    }
}