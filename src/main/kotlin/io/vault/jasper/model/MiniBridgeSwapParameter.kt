package io.vault.jasper.model

import io.vault.jasper.enums.ChainType
import io.vault.jasper.enums.Symbol
import org.springframework.data.annotation.CreatedDate
import org.springframework.data.annotation.Id
import org.springframework.data.annotation.LastModifiedDate
import org.springframework.data.mongodb.core.index.Indexed
import org.springframework.data.mongodb.core.mapping.Document
import org.springframework.data.mongodb.core.mapping.Field
import org.springframework.data.mongodb.core.mapping.FieldType
import java.math.BigDecimal
import java.time.LocalDateTime

@Document("mini_bridge_swap_parameter")
data class MiniBridgeSwapParameter(
    @Id
    val id: String? = null,

    @Field("price_gap", targetType = FieldType.DECIMAL128)
    var priceGap: BigDecimal = BigDecimal("0.001"),

    @Field("max_value", targetType = FieldType.DECIMAL128)
    var maxValue: BigDecimal = BigDecimal("100"),

    @Field("min_value", targetType = FieldType.DECIMAL128)
    val minValue: BigDecimal = BigDecimal("0.1"),

    @Field("fee_value", targetType = FieldType.DECIMAL128)
    val feeValue: BigDecimal = BigDecimal("0.01"),

    @Field("price_oracle_expiry_time")
    val priceOracleExpiryTime: Long = 30,

    @Field("block_confirmation")
    val blockConfirmation: Long = 4L,

    @Field("enable_swap")
    val enableSwap: Boolean = false,

    @CreatedDate
    val created: LocalDateTime = LocalDateTime.now(),

    @LastModifiedDate
    val updated: LocalDateTime = LocalDateTime.now(),
)
