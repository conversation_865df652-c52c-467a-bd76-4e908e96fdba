package io.vault.jasper.controller.auth

import EVMAddress
import io.swagger.annotations.ApiOperation
import io.vault.jasper.ApiResponse
import io.vault.jasper.annotation.ApiCache
import io.vault.jasper.dto.KolApplyDTO
import io.vault.jasper.exception.BusinessException
import io.vault.jasper.exception.ResultEnum
import io.vault.jasper.model.*
import io.vault.jasper.repository.*
import io.vault.jasper.response.*
import io.vault.jasper.service.kol.KolLevelService
import org.slf4j.LoggerFactory
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.data.domain.PageRequest
import org.springframework.data.domain.Sort
import org.springframework.validation.annotation.Validated
import org.springframework.web.bind.annotation.*
import java.time.LocalDate
import java.time.ZoneId
import javax.validation.Valid


@RestController
@RequestMapping("/kol")
class KolController @Autowired constructor(
    private val kolLevelRepository: KolLevelRepository,
    private val kolRepository: KolRepository,
    private val kolApplyFormRepository: KolApplyFormRepository,
    private val kolLevelService: KolLevelService,
    private val kolRebateRecordDailySummaryRepository: KolRebateRecordDailySummaryRepository
) {

    private var logger = LoggerFactory.getLogger(this::class.java)

    @ApiOperation("KOL等级列表")
    @CrossOrigin
    @GetMapping("/level/list")
    @ApiCache(expire = 3600)
    fun levelList(): ApiResponse<List<KolLevelInfoResponse>> {
        val sort = Sort.by(Sort.Order.asc(KolLevel::order.name))

        val allLevels = kolLevelRepository.findAll(sort)
        val levelList = mutableListOf<KolLevelInfoResponse>()
        for(level in allLevels){

            if(level.name == "0"){
                continue
            }

            val levelInfo = kolLevelService.getKolLevel(level.name)
            levelList.add(KolLevelInfoResponse(
                name = level.name,
                desc = levelInfo.getDesc(),
                incentiveRate = level.incentiveRate.movePointRight(2).stripTrailingZeros().toPlainString()
            ))
        }

        return ApiResponse.success(levelList)
    }

    @ApiOperation("KOL信息")
    @CrossOrigin
    @GetMapping("/info")
    @ApiCache(key = "kol:info", dynamicKey = ["address"], expire = 300)
    fun info(
        @RequestParam @EVMAddress address: String
    ): ApiResponse<KolResponse> {
        val kol = kolRepository.findFirstByWallet(
            address
        ) ?: throw BusinessException(ResultEnum.KOL_NOT_FOUND)

        return ApiResponse.success(KolResponse(
            id = kol.id!!,
            wallet = kol.wallet,
            level = kol.level,
            referralCode = kol.referralCode,
            incentive = kol.incentive.stripTrailingZeros().toPlainString(),
            status = kol.status
        ))
    }

    @ApiOperation("KOL申请表单")
    @CrossOrigin
    @PostMapping("/apply")
    fun apply(@RequestBody @Valid dto: KolApplyDTO): ApiResponse<Boolean> {
        kolApplyFormRepository.findFirstByWalletAndStatusIn(
            dto.walletAddress,
            listOf(KolApplyStatus.SUBMITTED, KolApplyStatus.IN_REVIEW)
        )?.let {
            throw BusinessException(ResultEnum.KOL_APPLY_IS_UNDER_REVIEW)
        }
        kolApplyFormRepository.findFirstByWalletAndStatusIn(
            dto.walletAddress,
            listOf(KolApplyStatus.APPROVED)
        )?.let {
            throw BusinessException(ResultEnum.YOU_ARE_ALREADY_A_KOL)
        }
        if (!dto.twitterHandle.startsWith("@")) {
            throw BusinessException(ResultEnum.INVALID_TWITTER_HANDLE)
        }
        if (!dto.walletAddress.startsWith("0x") || dto.walletAddress.length != 42) {
            throw BusinessException(ResultEnum.INVALID_WALLET_ADDRESS)
        }

        kolApplyFormRepository.save(KolApplyForm(
            wallet = dto.walletAddress,
            twitterHandle = dto.twitterHandle,
            discordId = dto.discordId,
            whatDoYouKnowAboutJasperVault = dto.whatDoYouKnowAboutJasperVault,
            status = KolApplyStatus.SUBMITTED
        ))

        return ApiResponse.success(true)
    }

    @ApiOperation("KOL申请状态")
    @CrossOrigin
    @GetMapping("/apply/status")
    @ApiCache(key = "kol:apply:status", dynamicKey = ["address"], expire = 60)
    fun applyStatus(@RequestParam address: String): ApiResponse<KolApplyStatusResponse?> {
        kolApplyFormRepository.findFirstByWallet(address)?.let {
            return ApiResponse.success(KolApplyStatusResponse(
                address = it.wallet,
                status = it.status,
                created = it.created.toInstant(ZoneId.systemDefault().rules.getOffset(it.created)).epochSecond
            ))
        } ?: return ApiResponse.success(null)
    }

    @ApiOperation("Rebate Records")
    @CrossOrigin
    @GetMapping("/rebateRecord")
    @ApiCache(key = "kol:rebateRecord", dynamicKey = ["address", "lastDays", "page", "pageSize"], expire = 60)
    fun rebateRecord(
        @RequestParam @EVMAddress address: String,
        @RequestParam lastDays: Int?,
        @RequestParam page: Int,
        @RequestParam pageSize: Int
    ): ApiResponse<PageResponse<KolRebateRecordResponse>> {
        val kol = kolRepository.findFirstByWallet(address) ?: run {
            return ApiResponse.success(PageResponse(
                page = page,
                pageSize = pageSize,
                totalPage = 0,
                total = 0,
                hasNext = false,
                data = emptyList()
            ))
        }
        val pageRequest = PageRequest.of(page - 1, pageSize, Sort.by(Sort.Order.desc(KolRebateRecordDailySummary::recordDate.name)))
        val pageObj = if (lastDays == null) {
            kolRebateRecordDailySummaryRepository.findByKolId(kol.id!!, pageRequest)
        } else {
            // 获取今天的LocalDate对象
            val today = LocalDate.now()
            // 往前减N日
            val startDate = today.minusDays(lastDays.toLong())
            kolRebateRecordDailySummaryRepository.findByKolIdAndRecordDateIsGreaterThanEqual(kol.id!!, startDate, pageRequest)
        }

        return ApiResponse.success(PageResponse(
            page = page,
            pageSize = pageSize,
            totalPage = pageObj.totalPages,
            total = pageObj.totalElements,
            hasNext = pageObj.hasNext(),
            data = pageObj.content.map {
                KolRebateRecordResponse(
                    date = it.recordDate.atStartOfDay(ZoneId.systemDefault()).toInstant().toEpochMilli(),
                    dailyTotalRebate = it.totalRebate.stripTrailingZeros().toPlainString(),
                    dailyActive = it.activeUsers
                )
            }
        ))
    }
}