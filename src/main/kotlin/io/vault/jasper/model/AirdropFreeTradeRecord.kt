package io.vault.jasper.model

import org.springframework.data.annotation.CreatedDate
import org.springframework.data.annotation.Id
import org.springframework.data.annotation.LastModifiedDate
import org.springframework.data.mongodb.core.index.Indexed
import org.springframework.data.mongodb.core.mapping.Document
import org.springframework.data.mongodb.core.mapping.Field
import java.time.LocalDateTime

enum class AirdropFreeTradeRecordStatus{
    CRETATED, PENDING, CLAIMED
}

@Document("airdrop_free_trade_record")
data class AirdropFreeTradeRecord(
    @Id
    val id: String? = null,
    @Field("user_id")
    @Indexed(background = true)
    val userId: String,
    @Indexed(background = true)
    val address: String,
    @Indexed(background = true)
    var status: AirdropFreeTradeRecordStatus = AirdropFreeTradeRecordStatus.CRETATED,
    @CreatedDate
    val created: LocalDateTime = LocalDateTime.now(),
    @LastModifiedDate
    val updated: LocalDateTime = LocalDateTime.now(),
    @Field("tx_hash")
    var txHash: String? = null,
    @Field("airdrop_transaction_ids")
    @Indexed(background = true)
    var airdropTransactionIds: String? = null
)