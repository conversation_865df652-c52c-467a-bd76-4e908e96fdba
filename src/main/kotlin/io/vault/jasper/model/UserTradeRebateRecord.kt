package io.vault.jasper.model

import io.vault.jasper.enums.ChainType
import io.vault.jasper.enums.OptionDirection
import io.vault.jasper.enums.Symbol
import org.springframework.data.annotation.CreatedDate
import org.springframework.data.annotation.Id
import org.springframework.data.annotation.LastModifiedDate
import org.springframework.data.mongodb.core.index.Indexed
import org.springframework.data.mongodb.core.mapping.Document
import org.springframework.data.mongodb.core.mapping.Field
import org.springframework.data.mongodb.core.mapping.FieldType
import java.math.BigDecimal
import java.time.LocalDateTime

enum class UserTradeRebateStatus {
    CREATED,  // 已创建
    PENDING,  // 待结算
    SETTLED   // 已结算（已添加到用户的trading credits中）
}

@Document("user_trade_rebate_record")
data class UserTradeRebateRecord(
    @Id
    val id: String? = null,
    @Field("option_order_id")
    @Indexed(unique = true, background = true)
    val optionOrderId: String,
    @Field("buyer_address")
    @Indexed(background = true)
    val buyerAddress: String,
    @Field("buyer_user_id")
    @Indexed(background = true)
    val buyerUserId: String,
    @Field("invitor_user_id")
    @Indexed(background = true)
    val invitorUserId: String? = null,
    @Field("invitor_address")
    @Indexed(background = true)
    val invitorAddress: String? = null,
    var direction: OptionDirection,
    val chain: ChainType,
    @Field("premium_fee")
    var premiumFee: BigDecimal = BigDecimal.ZERO,
    @Field("premium_asset")
    var premiumAsset: Symbol = Symbol.USDT,
    @Field("premium_fee_in_usdt", targetType = FieldType.DECIMAL128)
    var premiumFeeInUsdt: BigDecimal = BigDecimal.ZERO,
    @Field("rebate_rate", targetType = FieldType.DECIMAL128)
    var rebateRate: BigDecimal = BigDecimal("0.01"), // 默认返利率1%
    @Field("rebate_amount", targetType = FieldType.DECIMAL128)
    var rebateAmount: BigDecimal = BigDecimal.ZERO,
    var status: UserTradeRebateStatus = UserTradeRebateStatus.CREATED,
    @Field("settle_tx_id")
    var settleTxId: String? = null, // 结算交易ID
    @CreatedDate
    val created: LocalDateTime = LocalDateTime.now(),
    @LastModifiedDate
    val updated: LocalDateTime = LocalDateTime.now()
)
