package io.vault.jasper.response

import io.swagger.annotations.ApiModel
import io.swagger.annotations.ApiModelProperty
import java.math.BigDecimal

@ApiModel("邀请人获取SPoint信息")
data class ReferralSPointResponse(
    @ApiModelProperty("被邀请人地址", required = true)
    val address: String,
    @ApiModelProperty("被邀请人是否交易过", required = true)
    val traded: <PERSON>olean,
    @ApiModelProperty("权利金金额", required = true)
    val spoint: String
)