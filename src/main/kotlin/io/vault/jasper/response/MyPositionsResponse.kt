package io.vault.jasper.response

import io.swagger.annotations.ApiModel
import io.swagger.annotations.ApiModelProperty
import io.vault.jasper.enums.ChainType
import io.vault.jasper.enums.OptionDirection
import io.vault.jasper.enums.Symbol
import io.vault.jasper.model.OptionStatus
import io.vault.jasper.model.OrderType

@ApiModel
data class MyPositionsResponse(

    @ApiModelProperty("总利润")
    val totalNetProfit: String,

    @ApiModelProperty("总权利金")
    val totalPremium: String,

    @ApiModelProperty("分页数据")
    val pageContent: PageResponse<ExecutedOrderResponse>
)
