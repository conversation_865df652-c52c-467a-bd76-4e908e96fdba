package io.vault.jasper.repository

import io.vault.jasper.model.UserNetwork
import io.vault.jasper.model.UserNetworkGrade
import org.springframework.data.domain.Sort
import org.springframework.data.mongodb.repository.MongoRepository
import org.springframework.stereotype.Repository

@Repository
interface UserNetworkRepository : MongoRepository<UserNetwork, String> {

    fun findByAddressIgnoreCase(address: String): UserNetwork?
    fun findByUserId(userId: String): UserNetwork?
    fun findByCheckKolRebateIsNull(): List<UserNetwork>
    fun findByInvitedNetworkId(invitedNetworkId: String): List<UserNetwork>
    fun findByGrade2NetworkIdIsNullAndGrade(
        grade: UserNetworkGrade,
    ): List<UserNetwork>
    fun findByInviteCode(inviteCode: String): UserNetwork?
    fun countByInvitedNetworkId(
        invitedNetworkId: String
    ): Long
}