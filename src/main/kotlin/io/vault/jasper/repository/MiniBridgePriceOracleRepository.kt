package io.vault.jasper.repository

import io.vault.jasper.enums.ChainType
import io.vault.jasper.enums.Symbol
import io.vault.jasper.model.MiniBridgePriceOracle
import io.vault.jasper.model.MiniBridgeSwapOrder
import io.vault.jasper.model.OptionOrder
import io.vault.jasper.model.OptionStatus
import org.springframework.data.mongodb.repository.MongoRepository
import org.springframework.stereotype.Repository
import java.time.LocalDateTime

@Repository
interface MiniBridgePriceOracleRepository : MongoRepository<MiniBridgePriceOracle, String> {

    fun findFirstByBidAssetAndQuoteAssetOrderByCreatedDesc(
        bidAsset: Symbol,
        quoteAsset: Symbol
    ): MiniBridgePriceOracle?
}