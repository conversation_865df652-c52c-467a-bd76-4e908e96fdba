package io.vault.jasper.repository

import io.vault.jasper.model.UserNetworkTeamRebateDailySummary
import org.springframework.data.mongodb.repository.MongoRepository
import org.springframework.stereotype.Repository

@Repository
interface UserNetworkTeamRebateDailySummaryRepository : MongoRepository<UserNetworkTeamRebateDailySummary, String> {

    fun findByDateStringAndUserAddressAndParentAddress(
        dateString: String,
        userAddress: String,
        parentAddress: String
    ): UserNetworkTeamRebateDailySummary?
}