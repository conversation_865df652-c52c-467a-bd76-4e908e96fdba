package io.vault.jasper.service

object Blacklist {
    private val blacklist: MutableSet<String> = HashSet()

    fun getBlackListSize(): Int {
        return blacklist.size
    }

    fun add(ipAddressOrRange: String) {
        blacklist.add(ipAddressOrRange)
    }

    fun contains(ipAddress: String): Boolean {
        for (entry in blacklist) {
            if (isIpInRange(ipAddress, entry)) {
                return true
            }
        }
        return false
    }

    private fun isIpInRange(ipAddress: String, range: String): Boolean {
        if (!range.contains("/")) {
            return ipAddress == range
        }
        val parts = range.split("/".toRegex()).dropLastWhile { it.isEmpty() }.toTypedArray()
        val base = parts[0]
        val prefixLength = parts[1].toInt()
        val baseParts = base.split("\\.".toRegex()).dropLastWhile { it.isEmpty() }.toTypedArray()
        val ipParts = ipAddress.split("\\.".toRegex()).dropLastWhile { it.isEmpty() }.toTypedArray()
        for (i in 0 until prefixLength / 8) {
            if (baseParts[i] != ipParts[i]) {
                return false
            }
        }
        val bits = prefixLength % 8
        val mask = 0xff00 shr bits
        return (baseParts[prefixLength / 8].toInt() and mask) == (ipParts[prefixLength / 8].toInt() and mask)
    }
}