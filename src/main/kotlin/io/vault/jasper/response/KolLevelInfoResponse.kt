package io.vault.jasper.response

import io.swagger.annotations.ApiModel
import io.swagger.annotations.ApiModelProperty
import io.vault.jasper.enums.ChainType
import io.vault.jasper.enums.OptionDirection
import io.vault.jasper.enums.Symbol
import io.vault.jasper.model.OptionStatus

@ApiModel
data class KolLevelInfoResponse(

    @ApiModelProperty("等级名称", required = true)
    val name: String,

    @ApiModelProperty("等级描述", required = true)
    val desc: List<KolLevelDesc>,

    @ApiModelProperty("返佣百分比(不含%符号)", required = true)
    val incentiveRate: String
)

data class KolLevelDesc(
    @ApiModelProperty("描述标题", required = true)
    val title: String,

    @ApiModelProperty("描述内容", required = true)
    val content: String,

    @ApiModelProperty("指标内容", required = true)
    val indicator: String
)
