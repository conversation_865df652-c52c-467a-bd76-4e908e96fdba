package io.vault.jasper.repository

import io.vault.jasper.enums.ChainType
import io.vault.jasper.model.History
import io.vault.jasper.model.MoonlightRebateRecord
import io.vault.jasper.model.MoonlightRebateRecordStatus
import io.vault.jasper.model.TransactionMethod
import org.springframework.data.domain.Page
import org.springframework.data.domain.Pageable
import org.springframework.data.mongodb.repository.MongoRepository
import org.springframework.stereotype.Repository
import java.math.BigInteger

@Repository
interface MoonlightRebateRecordRepository : MongoRepository<MoonlightRebateRecord, String> {

    fun findByOptionOrderId(
        optionOrderId: String
    ): MoonlightRebateRecord?

    fun findByBuyerAddressIgnoreCase(
        buyerAddress: String
    ): List<MoonlightRebateRecord>

    fun findByStatus(
        status: MoonlightRebateRecordStatus
    ): List<MoonlightRebateRecord>

    fun findByStatusAndSettleTxIdIsNotNull(
        status: MoonlightRebateRecordStatus
    ): List<MoonlightRebateRecord>

    fun findByBuyerAddressIgnoreCaseAndStatusAndSettleTxIdIsNotNull(
        buyerAddress: String,
        status: MoonlightRebateRecordStatus
    ): List<MoonlightRebateRecord>

    fun findByBuyerAddressIgnoreCaseAndSbtNFTIdAndStatusAndSettleTxIdIsNotNull(
        buyerAddress: String,
        sbtNFTId: BigInteger,
        status: MoonlightRebateRecordStatus
    ): List<MoonlightRebateRecord>

    fun findByBuyerAddressIgnoreCaseAndStatus(
        buyerAddress: String,
        status: MoonlightRebateRecordStatus
    ): List<MoonlightRebateRecord>

    fun findByBuyerAddressIgnoreCaseAndStatusIn(
        buyerAddress: String,
        statuses: List<MoonlightRebateRecordStatus>
    ): List<MoonlightRebateRecord>

    fun findByBuyerAddressIgnoreCaseAndSbtNFTIdAndStatusIn(
        buyerAddress: String,
        sbtNFTId: BigInteger,
        statuses: List<MoonlightRebateRecordStatus>
    ): List<MoonlightRebateRecord>

//    fun findByChainAndBuyerAddressIgnoreCaseAndSbtNFTIdAndStatusIn(
//        chain: ChainType,
//        buyerAddress: String,
//        sbtNFTId: BigInteger,
//        statuses: List<MoonlightRebateRecordStatus>
//    ): List<MoonlightRebateRecord>

    fun findByChainAndBuyerAddressIgnoreCaseAndSbtNFTIdAndStatusIn(
        chain: ChainType,
        buyerAddress: String,
        sbtNFTId: BigInteger,
        statuses: List<MoonlightRebateRecordStatus>
    ): List<MoonlightRebateRecord>

    fun findByChainAndBuyerAddressIgnoreCaseAndSbtNFTIdInAndStatusIn(
        chain: ChainType,
        buyerAddress: String,
        sbtNFTIds: List<BigInteger>,
        statuses: List<MoonlightRebateRecordStatus>
    ): List<MoonlightRebateRecord>

    fun findBySbtNFTIdAndStatus(
        sbtNFTId: BigInteger,
        status: MoonlightRebateRecordStatus
    ): List<MoonlightRebateRecord>

    fun findBySbtNFTIdAndStatusIn(
        sbtNFTId: BigInteger,
        statuses: List<MoonlightRebateRecordStatus>
    ): List<MoonlightRebateRecord>
}