package io.vault.jasper.service

import com.fasterxml.jackson.databind.JsonNode
import com.fasterxml.jackson.databind.ObjectMapper
import io.vault.jasper.enums.ChainType
import io.vault.jasper.repository.ChainRepository
import io.vault.jasper.repository.UserRepository
import org.slf4j.LoggerFactory
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.http.HttpEntity
import org.springframework.http.HttpHeaders
import org.springframework.http.HttpMethod
import org.springframework.http.MediaType
import org.springframework.stereotype.Service
import org.springframework.web.client.RestTemplate
import java.math.BigInteger

@Service
class SubgraphService @Autowired constructor(
    private val userRepository: UserRepository,
    private val chainRepository: ChainRepository
){

    private val logger = LoggerFactory.getLogger(this::class.java)

    private val restTemplate: RestTemplate = RestTemplate()

    private val objectMapper = ObjectMapper()

    fun getSubgraphOrderData(
        chainType: ChainType,
        subgraphQuery: String
    ): String? {

        val chain = chainRepository.findByChain(chainType) ?: throw Exception("Chain not found")
        val subgraphUrl = chain.subgraphUrl ?: throw Exception("Subgraph URL not set")

        // 设置请求头
        val headers = HttpHeaders().apply {
            contentType = MediaType.APPLICATION_JSON
        }

        // GraphQL 查询体
        val requestBody = mapOf("query" to subgraphQuery)
        val requestBodyJson = objectMapper.writeValueAsString(requestBody)

        val request = HttpEntity(requestBodyJson, headers)

        // 发起 POST 请求
        val response = restTemplate.exchange(
            subgraphUrl,
            HttpMethod.POST,
            request,
            String::class.java
        )

        return response.body
    }

    fun getOrderFromHash(
        hash: String,
        chainType: ChainType = ChainType.ARBITRUM
    ): JsonNode?{
        val query = """
query OptionPremium {
    optionPremiums(where: { transactionHash: "$hash" }) {
        id
        orderType
        orderID
        writer
        holder
        premiumAsset
        amount
        transactionHash
        timestamp
    }
}
"""
        val premiumResponse = getSubgraphOrderData(chainType, query)

        logger.info("response: $premiumResponse")

        if(premiumResponse != null){
            val jsonNode = objectMapper.readTree(premiumResponse)
            val orderId = jsonNode["data"]["optionPremiums"][0]["orderID"].asText()
            val orderType = jsonNode["data"]["optionPremiums"][0]["orderType"].asInt()
            logger.info("Order ID: $orderId")

            var order: JsonNode? = null
            if(orderType == 0){
                order = getCallOrderFromOrderId(orderId, chainType)?.get("callOrder")
            } else if(orderType == 1){
                order = getPutOrderFromOrderId(orderId, chainType)?.get("putOrder")
            }

            return order
        }

        return null
    }

    fun getCallOrderFromOrderId(
        orderId: String,
        chainType: ChainType = ChainType.ARBITRUM
    ): JsonNode?{
        val query = """
        query CallOrderEntity {
    callOrderEntityV2S(where: { orderId: "$orderId" }) {
        id
        orderId
        holderWallet
        writerWallet
        transactionHash
        timestamp
        callOrder {
            id
            holder
            liquidateMode
            writer
            lockAssetType
            recipient
            lockAsset
            underlyingAsset
            strikeAsset
            lockAmount
            strikeAmount
            expirationDate
            lockDate
            underlyingNftID
            quantity
        }
    }
}
"""
        val callOrderResponse = getSubgraphOrderData(chainType, query)

        logger.info("Call Order Response: $callOrderResponse")

        if(callOrderResponse != null){
            val jsonNode = objectMapper.readTree(callOrderResponse)
            //val orderId = jsonNode["data"]["callOrderEntityV2S"][0]["orderId"].asText()
            // return jsonNode["data"]["callOrderEntityV2S"][0]["callOrder"]
            return jsonNode["data"]["callOrderEntityV2S"]?.get(0)
        }

        return null
    }

    fun getPutOrderFromOrderId(
        orderId: String,
        chainType: ChainType = ChainType.ARBITRUM
    ): JsonNode?{
        val query = """
        query PutOrderEntity {
    putOrderEntityV2S(where: { orderId: "$orderId" }) {
        id
        orderId
        holderWallet
        writerWallet
        transactionHash
        timestamp
        putOrder {
            id
            holder
            liquidateMode
            writer
            lockAssetType
            recipient
            lockAsset
            underlyingAsset
            strikeAsset
            lockAmount
            strikeAmount
            expirationDate
            lockDate
            underlyingNftID
            quantity
        }
    }
}
"""
        val putOrderResponse = getSubgraphOrderData(chainType, query)

        logger.info("Put Order Response: $putOrderResponse")

        if(putOrderResponse != null){
            val jsonNode = objectMapper.readTree(putOrderResponse)
            //val orderId = jsonNode["data"]["putOrderEntityV2S"][0]["orderId"].asText()
            // return jsonNode["data"]["putOrderEntityV2S"][0]["putOrder"]
            return jsonNode["data"]["putOrderEntityV2S"]?.get(0)
        }

        return null
    }

    fun getOrderById(chainType: ChainType, orderId: String): JsonNode? {
        return getCallOrderFromOrderId(orderId, chainType)?.get("callOrder")
            ?: getPutOrderFromOrderId(orderId, chainType)?.get("putOrder")
    }

    fun getSettlementFromOrderId(chainType: ChainType, orderId: String): String? {
        val query = """
query {
  optionSettlements(where: {orderID: "$orderId"}) {
    orderID
    transactionHash
  }
}
        """.trim()
        logger.info("Get settlement hash request: $chainType $orderId")
        val response = getSubgraphOrderData(chainType, query)
        logger.info("Get settlement hash Response: $response")
        val rspNode = objectMapper.readTree(response)

        return rspNode?.get("data")?.get("optionSettlements")?.firstOrNull()?.get("transactionHash")?.asText()
    }

    data class SubgraphOptionPremium(
        val orderID: String,
        val premiumAsset: String,
        val amount: String,
        val transactionHash: String
    )

    /**
     * 根据订单ID获取权利金信息
     */
    fun getOptionPremiumByOrderId(chainType: ChainType, orderId: String): SubgraphOptionPremium? {
        val query = """
query MyQuery {
  optionPremiums(where: {orderID: "$orderId"}) {
    orderID
    orderType
    premiumAsset
    amount
    transactionHash
  }
}
        """.trim()
        val response = getSubgraphOrderData(chainType, query)
        logger.info("Get option premium Response: $response")
        val rspNode = objectMapper.readTree(response)
        val dataStruct = rspNode?.get("data")?.get("optionPremiums")?.firstOrNull() ?: return null

        return SubgraphOptionPremium(
            dataStruct.get("orderID").asText(),
            dataStruct.get("premiumAsset").asText(),
            dataStruct.get("amount").asText(),
            dataStruct.get("transactionHash").asText()
        )
    }
}