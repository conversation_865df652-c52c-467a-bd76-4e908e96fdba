package io.vault.jasper.model

import io.vault.jasper.enums.ChainType
import io.vault.jasper.enums.OptionDirection
import io.vault.jasper.enums.Symbol
import org.springframework.data.annotation.CreatedDate
import org.springframework.data.annotation.Id
import org.springframework.data.annotation.LastModifiedDate
import org.springframework.data.mongodb.core.index.Indexed
import org.springframework.data.mongodb.core.mapping.Document
import org.springframework.data.mongodb.core.mapping.Field
import java.math.BigDecimal
import java.time.LocalDateTime

/**
 * Degen LP Vault 列表
 */

@Document("degen_lp_vault")
data class DegenLPVault(
    @Id
    val id: String? = null,
    @Field("order_type")
    val orderType: OrderType = OrderType.DEGEN,
    val chain: ChainType = ChainType.ARBITRUM, // 对应的链
    @Indexed(background = true)
    var address: String, // Vault 地址，大小写敏感
    @Field("option_type")
    var optionType: OptionDirection = OptionDirection.CALL, // Vault 期权类型
    @Field("option_symbol")
    var optionSymbol: Symbol = Symbol.ETH, // Vault 期权标的
    @Field("expire_in_hour")
    var expireInHour: String = "8",
    @Field("product_type")
    var productType: Int = 0,
    @Field("available_liquidity")
    var availableLiquidity: BigDecimal = BigDecimal.ZERO,
    @Field("available_liquidity_in_usdt")
    var availableLiquidityInUsdt: BigDecimal = BigDecimal.ZERO,
    var token: Symbol? = null,
    @Field("token_address")
    var tokenAddress: String? = null,
    var premium: BigDecimal = BigDecimal.ZERO, // 一份期权的权利金价格
    @CreatedDate
    val created: LocalDateTime = LocalDateTime.now(),
    @LastModifiedDate
    val updated: LocalDateTime = LocalDateTime.now(),
    var apy: BigDecimal = BigDecimal.ZERO,
    var iv: BigDecimal = BigDecimal.ZERO,
    @Field("open_interest")
    var openInterest: BigDecimal = BigDecimal.ZERO,
    @Field("open_interest_in_usdt")
    var openInterestInUsdt: BigDecimal = BigDecimal.ZERO,
    var utilization: BigDecimal = BigDecimal.ZERO,
    var tvl: BigDecimal = BigDecimal.ZERO,
    @Field("readable_market_price")
    var readableMarketPrice: BigDecimal = BigDecimal.ZERO,
    @Field("premium_rate")
    var premiumRate: BigDecimal = BigDecimal.ONE,
    @Field("premium_floor_percentage")
    var premiumFloorPercentage: BigDecimal = BigDecimal.ONE,
    @Field("maximum")
    var maximum: BigDecimal = BigDecimal.ONE,
    @Field("offer_id")
    var offerId: String = "0",
    @Field("setting_index")
    var settingIndex: String = "-1",
    @Field("product_index")
    var productIndex: String = "-1",
    @Field("last_update_setting_time")
    var lastUpdateSettingTime: LocalDateTime? = null,
)