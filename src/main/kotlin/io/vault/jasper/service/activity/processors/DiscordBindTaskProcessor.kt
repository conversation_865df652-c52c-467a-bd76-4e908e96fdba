package io.vault.jasper.service.activity.processors

import io.vault.jasper.model.User
import io.vault.jasper.model.activity.Task
import io.vault.jasper.model.activity.TaskType
import io.vault.jasper.service.activity.TaskProcessor
import org.springframework.stereotype.Component

/**
 * Processor for DISCORD_BIND task type
 */
@Component
class DiscordBindTaskProcessor : TaskProcessor {
    override fun getTaskType(): TaskType = TaskType.DISCORD_BIND
    
    override fun isTaskCompleted(user: User, task: Task, context: Map<String, Any>): Pair<Boolean, Map<String, Any>> {
        // Check if the user has Discord info
        val isCompleted = user.discordInfo != null
        
        val completionData = if (isCompleted) {
            mapOf(
                "discordUsername" to (user.discordInfo?.username ?: ""),
                "discordUserId" to (user.discordInfo?.userId ?: "")
            )
        } else {
            emptyMap()
        }
        
        return Pair(isCompleted, completionData)
    }
}
