package io.vault.jasper.repository

import io.vault.jasper.model.UserZeroPremiumBenefit
import org.springframework.data.mongodb.repository.MongoRepository
import org.springframework.stereotype.Repository

@Repository
interface UserZeroPremiumBenefitRepository : MongoRepository<UserZeroPremiumBenefit, String> {
    fun findByUserId(userId: String): List<UserZeroPremiumBenefit>
    fun findByUserIdAndBenefitId(userId: String, benefitId: String): List<UserZeroPremiumBenefit>
    fun findByUserIdAndBenefitIdInAndUsed(userId: String, benefitIdIn: List<String>, used: Boolean): List<UserZeroPremiumBenefit>
    fun existsByUserIdAndBenefitIdIn(userId: String, benefitIds: List<String>): Boolean
    fun findByUsed(used: Boolean): List<UserZeroPremiumBenefit>
}