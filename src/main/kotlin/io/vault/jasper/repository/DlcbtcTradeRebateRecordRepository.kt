package io.vault.jasper.repository


import io.vault.jasper.model.*
import org.springframework.data.mongodb.repository.MongoRepository
import org.springframework.stereotype.Repository

@Repository
interface DlcbtcTradeRebateRecordRepository : MongoRepository<DlcbtcTradeRebateRecord, String> {
    fun findByOptionOrderId(optionOrderId: String): DlcbtcTradeRebateRecord?
    fun findTopByOrderByOptionOrderIdDesc(): DlcbtcTradeRebateRecord?
    fun findByStatus(
        status: DlcbtcTradeRebateRecordStatus
    ): List<DlcbtcTradeRebateRecord>

    fun findByStatusAndSettled(
        status: DlcbtcTradeRebateRecordStatus,
        settled: Boolean
    ): List<DlcbtcTradeRebateRecord>

    fun findByStatusAndSettleTxIdIsNotNullAndSettled(
        status: DlcbtcTradeRebateRecordStatus,
        settled: Boolean
    ): List<DlcbtcTradeRebateRecord>

    fun findByBuyerAddressIgnoreCase(
        buyerAddress: String
    ): List<DlcbtcTradeRebateRecord>

    fun findByStatusAndSettleTxIdIsNotNull(
        status: DlcbtcTradeRebateRecordStatus
    ): List<DlcbtcTradeRebateRecord>

    fun findByBuyerAddressAndStatus(
        buyerAddress: String,
        status: DlcbtcTradeRebateRecordStatus
    ): List<DlcbtcTradeRebateRecord>

    fun findByBuyerAddressIgnoreCaseAndStatus(
        buyerAddress: String,
        status: DlcbtcTradeRebateRecordStatus
    ): List<DlcbtcTradeRebateRecord>

    fun findByBuyerAddressAndStatusInAndSettleTxIdIsNotNull(
        buyerAddress: String,
        statuses: List<DlcbtcTradeRebateRecordStatus>
    ): List<DlcbtcTradeRebateRecord>

    fun findByBuyerAddressIgnoreCaseAndStatusIn(
        buyerAddress: String,
        statuses: List<DlcbtcTradeRebateRecordStatus>
    ): List<DlcbtcTradeRebateRecord>
}