package io.vault.jasper.model

import org.springframework.data.annotation.CreatedDate
import org.springframework.data.annotation.Id
import org.springframework.data.mongodb.core.index.Indexed
import org.springframework.data.mongodb.core.mapping.Document
import org.springframework.data.mongodb.core.mapping.Field
import java.time.LocalDateTime

@Document("tge_retweet_user_id")
data class TgeRetweetUserId(
    @Id
    val id: String? = null,
    @Field("jasper_tweet_id")
    val jasperTweetId: String,
    @Field("retweet_user_id")
    @Indexed(background = true)
    val retweetUserId: String,
    @Field("retweet_username")
    @Indexed(background = true)
    val retweetUsername: String,
    @Field("retweet_user_screen_name")
    @Indexed(background = true)
    val retweetUserScreenName: String,
    @CreatedDate
    val created: LocalDateTime = LocalDateTime.now(),
)