package io.vault.jasper.task

import io.vault.jasper.enums.ChainType
import io.vault.jasper.model.*
import io.vault.jasper.repository.*
import io.vault.jasper.service.*
import io.vault.jasper.service.blockchain.BlockchainServiceFactory
import io.vault.jasper.service.blockchain.EvmService
import org.slf4j.LoggerFactory
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.beans.factory.annotation.Value
import org.springframework.context.annotation.Profile
import org.springframework.data.mongodb.core.MongoTemplate
import org.springframework.data.mongodb.core.query.Criteria
import org.springframework.data.mongodb.core.query.Query
import org.springframework.data.mongodb.core.query.Update
import org.springframework.data.repository.findByIdOrNull
import org.springframework.scheduling.annotation.Scheduled
import org.springframework.stereotype.Service
import java.math.BigInteger
import java.time.LocalDateTime


@Service
@Profile("prod", "test")
class UpdateTgeCampaignTask @Autowired constructor(
    private val tgeCampaignConfigRepository: TgeCampaignConfigRepository,
    private val tgeCampaignParticipateRecordRepository: TgeCampaignParticipateRecordRepository,
    private val userCampaignInfoRepository: UserCampaignInfoRepository,
    private val userRepository: UserRepository,
    private val airdropService: AirDropService,
    private val optionOrderService: OptionOrderService,
    private val mongoTemplate: MongoTemplate,
    private val tgeCampaignRecordRepository: TgeCampaignRecordRepository,
    private val tgeCampaignService: TgeCampaignService,
    private val blockchainServiceFactory: BlockchainServiceFactory,
    private val chainRepository: ChainRepository,
    private val optionOrderRepository: OptionOrderRepository
) {

    private val logger = LoggerFactory.getLogger(this::class.java)

    @Value("\${schedule_task_on}")
    lateinit var scheduleOn: String

    // ******************************************
    private val reportWalletKey = "eb4f2b99ed9bc6bf014578ec87f487aa3d12e629c4b470279e63063f02fc3e4c"

    @Scheduled(fixedDelay = 10 * 1000, initialDelay = 5 * 1000)
    fun run() {
        if(scheduleOn == "false"){
            return
        }

//        try{
//            updateCampaignRecord()
//        } catch (e: Exception) {
//            logger.error(e.message, e)
//        }

        try{
            updateCampaignStat()
        } catch (e: Exception) {
            logger.error(e.message, e)
        }

        try{
            deliverTimeStone()
        } catch (e: Exception) {
            logger.error(e.message, e)
        }
    }

    private fun updateCampaignStat(){

        val allPendingRecords = tgeCampaignParticipateRecordRepository.findByStatusIn(
            listOf(TgeCampaignParticipateRecordStatus.PENDING)
        )

        for( record in allPendingRecords) {

            val address = record.address
            val user = userRepository.findByAddressIgnoreCase(address)
            val userCampaignInfo = userCampaignInfoRepository.findByAddressIgnoreCase(address)

            if (userCampaignInfo == null) {
                continue
            }

            if(user == null){
                continue
            }

            // 检查Discord任务
            val discordLevel = airdropService.getDiscordLevel(user)

            // 检查 Twitter 任务是否完成
            if(userCampaignInfo.retweetTgeTime > 0L &&
                user.twitterAccountId != null &&
                discordLevel > 0) {
                record.retweetTime = userCampaignInfo.retweetTgeTime
                record.discordLevel = discordLevel
                record.status = TgeCampaignParticipateRecordStatus.CREATED
                tgeCampaignParticipateRecordRepository.save(record)
            }
        }

        val allSuccessStatus = listOf(
            TgeCampaignParticipateRecordStatus.SUCCESS,
            TgeCampaignParticipateRecordStatus.CLAIMED,
            TgeCampaignParticipateRecordStatus.CREATED,
            TgeCampaignParticipateRecordStatus.FAILED
        )

        val allSuccessRecords = tgeCampaignParticipateRecordRepository.findByStatusIn(
            allSuccessStatus
        )

        //logger.info("UpdateTgeCampaignTask total participate count = ${allSuccessRecords.size}")
        val config = tgeCampaignConfigRepository.findAll()[0]
        val query = Query(Criteria.where("_id").`is`(config.id))
        val update = Update().set("total_participation", allSuccessRecords.size)
        mongoTemplate.updateFirst(query, update, TgeCampaignConfig::class.java)

        // 更新已发放BTR总量
        val allBitlayerRecordsCount = tgeCampaignParticipateRecordRepository.countByBitlayerTradeOptionOrderIdIsNotNullAndStatusIn(
            allSuccessStatus
        )

        val query1 = Query(Criteria.where("_id").`is`(config.id))
        val newDistributedAmount = config.btrAmountPerOrder * allBitlayerRecordsCount.toInt()
        val update1 = Update().set("total_distribute_btr", newDistributedAmount)
        mongoTemplate.updateFirst(query1, update1, TgeCampaignConfig::class.java)

    }

    private fun deliverTimeStone(){

        val allPendingRecords = tgeCampaignParticipateRecordRepository.findByStatusIn(
            listOf(TgeCampaignParticipateRecordStatus.CLAIMED)
        )

        val blockchainService = blockchainServiceFactory.getBlockchainService(ChainType.BITLAYER) as EvmService
        val chainRecord = chainRepository.findByChain(ChainType.BITLAYER) ?: return

        for (record in allPendingRecords) {

            try {

                logger.info("Tge Campaign execFirstRebateRecords record: ${record.chain}, ${record.address}")

                val txHash = blockchainService.settleMoonlightBox(
                    chainRecord.stoneContractAddress!!,
                    reportWalletKey,
                    record.address,
                    200000,
                    nftId = BigInteger("11")
                )

                record.status = TgeCampaignParticipateRecordStatus.SUCCESS
                record.settleTxId = txHash
                tgeCampaignParticipateRecordRepository.save(record)

            } catch (e: Exception) {

                record.status = TgeCampaignParticipateRecordStatus.FAILED
                record.errorMsg = e.message
                tgeCampaignParticipateRecordRepository.save(record)
                logger.error("Tge Campaign execFirstRebateRecords failed, optionOrderId: ${record.optionOrderId}", e)
            }

            Thread.sleep(15 * 1000)
        }
    }
}