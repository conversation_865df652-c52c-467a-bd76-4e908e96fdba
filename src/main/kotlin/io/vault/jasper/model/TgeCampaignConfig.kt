package io.vault.jasper.model

import org.springframework.data.annotation.Id
import org.springframework.data.mongodb.core.mapping.Document
import org.springframework.data.mongodb.core.mapping.Field
import java.math.BigDecimal
import java.time.LocalDateTime

@Document("tge_campaign_config")
data class TgeCampaignConfig(
    @Id
    val id: String? = null,

    @Field("start_time")
    val startTime: LocalDateTime,

    @Field("end_time")
    val endTime: LocalDateTime,

    @Field("total_participation")
    var totalParticipation: Int = 0,

    @Field("total_distribute_btr")
    val totalDistributeBtr: Int = 0,

    @Field("total_btr")
    val totalBtr: Int = 80000,

    @Field("btr_amount_per_order")
    val btrAmountPerOrder: Int = 3,

    @Field("enabled")
    var enabled: Boolean = false,

    @Field("retweet_link")
    val retweetLink: String = "https://x.com/Jaspervault/status/1832969774160339168",
) {

    fun isInCampaignPeriod(timestamp: LocalDateTime): Boolean {
        return timestamp in startTime..endTime
    }

    fun canDistributeBtr(): Boolean {
        return totalBtr - totalDistributeBtr > btrAmountPerOrder
    }
} 