package io.vault.jasper.listener

import io.vault.jasper.event.CheckTgeCampaignOrderEvent
import io.vault.jasper.repository.OptionOrderRepository
import io.vault.jasper.service.BtrCampaignService
import io.vault.jasper.service.TgeCampaignService
import org.slf4j.LoggerFactory
import org.springframework.context.event.EventListener
import org.springframework.data.repository.findByIdOrNull
import org.springframework.stereotype.Component

@Component
class CheckTgeCampaignOrderEventListener(
    private val optionOrderRepository: OptionOrderRepository,
    private val tgeCampaignService: TgeCampaignService
) {
    private val logger = LoggerFactory.getLogger(this::class.java)

    @EventListener
    fun handleTgeCampaign(event: CheckTgeCampaignOrderEvent) {
        val optionOrder = optionOrderRepository.findByIdOrNull(event.optionOrderId) ?: return
        try {
            tgeCampaignService.handleOptionOrder(optionOrder)
        } catch (e: Exception) {
            logger.error("Failed to handle TGE campaign for option order ${optionOrder.id}", e)
        }
    }
} 