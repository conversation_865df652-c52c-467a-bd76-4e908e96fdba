package io.vault.jasper.repository

import io.vault.jasper.enums.ChainType
import io.vault.jasper.model.Chain
import io.vault.jasper.model.Gala3Address
import org.springframework.data.mongodb.repository.MongoRepository
import org.springframework.stereotype.Repository

@Repository
interface Gala3AddressRepository : MongoRepository<Gala3Address, String> {
    fun findByAddressIgnoreCase(address: String): Gala3Address?
}