package io.vault.jasper.task

import com.twitter.clientlib.ApiException
import com.twitter.clientlib.TwitterCredentialsBearer
import com.twitter.clientlib.api.TwitterApi
import io.vault.jasper.model.EchoooRetweetUserId
import io.vault.jasper.model.TgeRetweetUserId
import io.vault.jasper.repository.*
import io.vault.jasper.service.TwitterOAuthService
import org.slf4j.LoggerFactory
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.beans.factory.annotation.Value
import org.springframework.context.annotation.Profile
import org.springframework.scheduling.annotation.Scheduled
import org.springframework.stereotype.Service

@Service
@Profile("prod")
class UpdateTgeRetweetInfoTask @Autowired constructor(
    private val twitterOAuthService: TwitterOAuthService,
    private val twitterAPIParameterRepository: TwitterAPIParameterRepository,
    //private val echoooRetweetUserIdRepository: EchoooRetweetUserIdRepository,
    private val tgeRetweetUserIdRepository: TgeRetweetUserIdRepository
) {

    private val logger = LoggerFactory.getLogger(this::class.java)

    @Value("\${schedule_task_on}")
    lateinit var scheduleOn: String

    // 每3分钟执行一次
    @Scheduled(fixedDelay = 3 * 60 * 1000, initialDelay = 30 * 1000)
    fun run() {
        if(scheduleOn == "false"){
            return
        }
        try {
            exec()
        } catch (e: Exception) {
            logger.error(e.message, e)
        }
    }

    /**
     * 更新每条订单记录
     */
    private fun exec() {

        logger.info("Update TGE Retweet Info Task Begin.")

        fetchRetweetInfo()

        logger.info("Update TGE Retweet Info Task End.")
    }

    private fun fetchRetweetInfo(){

        logger.info("Update TGE Retweet Info fetch retweet info Begin.")

        val parameter = twitterOAuthService.getParameter()
        if(parameter.taskSwitch == false){
            logger.info("Update Jasper Airdrop NFT Retweet Info Task Switch is off.")
            return
        }

        val TWITTER_BEARER_TOKEN = parameter.bearToken
        val apiInstance = TwitterApi(TwitterCredentialsBearer(TWITTER_BEARER_TOKEN))

        val id = parameter.retweetId
        val maxResults = parameter.pageCount
        val userFields: Set<String> = HashSet<String>() // Set<String> | A comma separated list of User fields to display.
        val expansions: Set<String> = HashSet<String>()
        val tweetFields: Set<String> = HashSet<String>()
        try {
            var request = apiInstance.users().tweetsIdRetweetingUsers(id)
                .maxResults(maxResults)
                .userFields(userFields)
                .expansions(expansions)
                .tweetFields(tweetFields)

            if(!parameter.paginationToken.isNullOrBlank()){
                request = request.paginationToken(parameter.paginationToken)
            }

            val result = request.execute()
            //logger.info("Update TGE Retweet Info User Info get result $result")

            if(result.data.isNullOrEmpty()){
                logger.info("Update TGE Retweet Info User Info is empty")
                parameter.paginationToken = null
                twitterAPIParameterRepository.save(parameter)
                return
            }

            var reachRecordEnd = false
            result?.data?.forEach {
                val twitterAccountId = it.id

                val retweetUserId = tgeRetweetUserIdRepository.findByRetweetUserId(twitterAccountId)
                if (retweetUserId != null) {
                    reachRecordEnd = true
                    logger.debug("TwitterOAuthController User $twitterAccountId already retweeted")
                } else {
                    tgeRetweetUserIdRepository.save(
                        TgeRetweetUserId(
                            retweetUserId = twitterAccountId,
                            jasperTweetId = id,
                            retweetUsername = it.username,
                            retweetUserScreenName = it.name
                        )
                    )
                }
            }

            result.meta?.let {

                logger.info("Update TGE Retweet Info User Info meta: ${it.resultCount}")
                logger.info("Update TGE Retweet Info User Info meta: ${it.nextToken}")

                if(result.meta!!.resultCount!!.toInt() < maxResults){
                    reachRecordEnd = true
                }

                if(reachRecordEnd){
                    logger.info("Update TGE Retweet Info User Info reach the end page, read from start page")
                    parameter.paginationToken = null
                } else {
                    parameter.paginationToken = it.nextToken
                }

                twitterAPIParameterRepository.save(parameter)
            }

        } catch (e: ApiException) {
            logger.error("Exception when calling UsersApi#tweetsIdRetweetingUsers")
            logger.error("Status code: " + e.code)
            logger.error("Reason: " + e.responseBody)
            logger.error("Response headers: " + e.responseHeaders)
        }

        logger.info("Update TGE Retweet Info fetch retweet info End.")
    }
}