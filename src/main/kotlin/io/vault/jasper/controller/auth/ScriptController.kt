package io.vault.jasper.controller.auth

import com.fasterxml.jackson.databind.ObjectMapper
import io.swagger.v3.oas.annotations.Operation
import io.vault.jasper.ApiResponse
import io.vault.jasper.dto.*
import io.vault.jasper.enums.ChainType
import io.vault.jasper.event.AfterCreateOptionOrderEvent
import io.vault.jasper.event.OptionExecutedEvent
import io.vault.jasper.event.UpdatePremiumFeeEvent
import io.vault.jasper.exception.BusinessException
import io.vault.jasper.exception.ResultEnum
import io.vault.jasper.model.OptionStatus
import io.vault.jasper.model.activity.*
import io.vault.jasper.model.MoonlightRebateRecord
import io.vault.jasper.model.MoonlightRebateRecordStatus
import io.vault.jasper.repository.*
import io.vault.jasper.repository.activity.*
import io.vault.jasper.repository.MoonlightRebateRecordRepository
import io.vault.jasper.service.activity.*
import io.vault.jasper.service.*
import io.vault.jasper.service.blockchain.*
import okhttp3.Address
import org.slf4j.LoggerFactory
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.context.ApplicationContext
import org.springframework.beans.factory.annotation.Value
import org.springframework.context.ApplicationEventPublisher
import org.springframework.web.bind.annotation.*
import springfox.documentation.annotations.ApiIgnore
import java.math.BigDecimal
import java.math.BigInteger
import java.time.LocalDateTime


@RestController
@RequestMapping("/script")
@ApiIgnore
class ScriptController @Autowired constructor(
    private val applicationContext: ApplicationContext,
    private val optionOrderRepository: OptionOrderRepository,
    private val blockchainServiceFactory: BlockchainServiceFactory,
    private val jasperVaultService: JasperVaultService,
    private val currencyService: CurrencyService,
    private val optionOrderService: OptionOrderService,
    private val eventPublisher: ApplicationEventPublisher,
) {

    private var logger = LoggerFactory.getLogger(this::class.java)

    @Value("\${settlement_wallet}")
    private lateinit var settlementWalletKey: String


    @Operation(summary = "处理石头碎片失败记录")
    @CrossOrigin
    @GetMapping("/handle_stone_piece_failed_records")
    fun handleStonePieceFailedRecords(
        nftId: String
    ): ApiResponse<String> {
        try {
            val moonlightRebateRecordRepository = applicationContext.getBean(MoonlightRebateRecordRepository::class.java)

            // 1. 根据 nftId 找出所有状态为 SETTLE_FAILED 的记录
            val nftIdBigInteger = BigInteger(nftId)
            val failedRecords = moonlightRebateRecordRepository.findBySbtNFTIdAndStatus(
                nftIdBigInteger,
                MoonlightRebateRecordStatus.SETTLE_FAILED
            )

            if (failedRecords.isEmpty()) {
                return ApiResponse.success("No failed records found for nftId: $nftId")
            }

            logger.info("Found ${failedRecords.size} failed records for nftId: $nftId")

            // 2. 按 buyerAddress 分组
            val recordsByAddress = failedRecords.groupBy { it.buyerAddress }
            logger.info("Found ${recordsByAddress.size} unique buyer addresses")

            val allNewRecords = mutableListOf<MoonlightRebateRecord>()
            val allDeletedRecordIds = mutableListOf<String>()
            var totalProcessedOrderIds = 0

            // 3. 对每个 buyerAddress 分组进行处理
            recordsByAddress.forEach { (buyerAddress, addressRecords) ->
                logger.info("Processing ${addressRecords.size} records for address: $buyerAddress")

                // 提取该地址下所有 stoneRelatedOptionOrderIds 并去重
                val allOrderIds = mutableSetOf<String>()
                addressRecords.forEach { record ->
                    record.stoneRelatedOptionOrderIds?.let { orderIds ->
                        allOrderIds.addAll(orderIds)
                    }
                }

                if (allOrderIds.isEmpty()) {
                    logger.info("No stone related option order IDs found for address: $buyerAddress")
                    return@forEach
                }

                logger.info("Extracted ${allOrderIds.size} unique order IDs for address: $buyerAddress")
                totalProcessedOrderIds += allOrderIds.size

                // 重新生成记录，每7个订单ID为一组生成一条记录
                val orderIdsList = allOrderIds.toList()
                val templateRecord = addressRecords.first()

                var maxPieceCount = 7
                if(templateRecord.expiryInHour == "0.5" && templateRecord.bidAmount.compareTo(BigDecimal("0.01")) == 0){
                    maxPieceCount = 3
                }

                // 将订单ID按每maxPieceCount个分组
                val orderIdGroups = orderIdsList.chunked(maxPieceCount)

                orderIdGroups.forEachIndexed { index, orderGroup ->
                    val actualPieceCount = orderGroup.size

                    // 当 stonePieceCount = maxPieceCount 时，新生成的记录 status 为 CLAIMED，否则为 EXECUTED
                    val newStatus = if (actualPieceCount == maxPieceCount) {
                        MoonlightRebateRecordStatus.CLAIMED
                    } else {
                        MoonlightRebateRecordStatus.EXECUTED
                    }

                    // 使用该地址的第一个失败记录作为模板创建新记录
                    val newRecord = MoonlightRebateRecord(
                        optionOrderId = templateRecord.optionOrderId,
                        buyerAddress = templateRecord.buyerAddress,
                        direction = templateRecord.direction,
                        bidAmount = templateRecord.bidAmount,
                        bidAsset = templateRecord.bidAsset,
                        strikePrice = templateRecord.strikePrice,
                        settlementPrice = templateRecord.settlementPrice,
                        premiumFee = templateRecord.premiumFee,
                        premiumAsset = templateRecord.premiumAsset,
                        premiumFeeInBtc = templateRecord.premiumFeeInBtc,
                        premiumFeeInUsdt = templateRecord.premiumFeeInUsdt,
                        profit = templateRecord.profit,
                        profitAsset = templateRecord.profitAsset,
                        profitInBtc = templateRecord.profitInBtc,
                        profitInUsdt = templateRecord.profitInUsdt,
                        netProfit = templateRecord.netProfit,
                        rebateAmount = templateRecord.rebateAmount,
                        jumpGalaThreeLinkTime = templateRecord.jumpGalaThreeLinkTime,
                        status = newStatus,
                        settleTxId = null, // 重置结算交易ID
                        expiryInHour = templateRecord.expiryInHour,
                        sbtNFTId = templateRecord.sbtNFTId,
                        stonePieceCount = actualPieceCount,
                        stoneRelatedOptionOrderIds = orderGroup.toMutableList(),
                        chain = templateRecord.chain,
                        errorMsg = null // 清除错误信息
                    )

                    allNewRecords.add(newRecord)
                    logger.info("Prepared record ${index + 1} for address $buyerAddress with ${actualPieceCount} pieces (status: $newStatus)")
                }

                // 收集该地址下要删除的旧记录ID
                val addressDeletedRecordIds = addressRecords.map { it.id!! }
                allDeletedRecordIds.addAll(addressDeletedRecordIds)
            }

            // 4. 批量插入所有新记录
            val savedRecords = moonlightRebateRecordRepository.saveAll(allNewRecords)
            logger.info("Created ${savedRecords.size} new records")

            // 5. 批量删除所有旧记录
            moonlightRebateRecordRepository.deleteAllById(allDeletedRecordIds)
            logger.info("Deleted ${allDeletedRecordIds.size} old failed records")

            return ApiResponse.success(
                "Successfully processed stone piece failed records for nftId: $nftId. " +
                "Processed ${recordsByAddress.size} addresses, " +
                "created ${savedRecords.size} new records from ${totalProcessedOrderIds} order IDs, " +
                "deleted ${allDeletedRecordIds.size} old records."
            )

        } catch (e: Exception) {
            logger.error("Error processing stone piece failed records for nftId: $nftId", e)
            return ApiResponse.error("Failed to process stone piece failed records: ${e.message}")
        }
    }

    @Operation(summary = "创建活动")
    @CrossOrigin
    @GetMapping("/create_campaign")
    fun createCampaign(): ApiResponse<String> {
        // Create tasks for the campaign
        val tasks = createCampaignTasks()

        // Create task groups for the campaign
        val taskGroups = createCampaignTaskGroups(tasks)

        // Create the campaign
        val campaign = createJasperVaultCampaign(tasks, taskGroups)

        // Create the campaign reward
        val reward = createCampaignReward(campaign.id!!)

        return ApiResponse.success("Campaign created successfully with ID: ${campaign.id}")
    }

    /**
     * Create tasks for the Jasper Vault campaign
     */
    private fun createCampaignTasks(): List<Task> {
        val taskRepository = applicationContext.getBean(TaskRepository::class.java)

        // 1. New User Registration Task
        val newUserRegistrationTask = Task(
            title = "Check the New User Eligibility",
            taskType = TaskType.NEW_USER_REGISTRATION,
            taskConfig = mapOf(
                TaskConfigParams.REGISTRATION_AFTER_TIME to "2025-05-22T00:00:00"
            ),
            i18nInfo = mutableMapOf(
                "en" to I18nTaskInfo(
                    title = "Check the New User Eligibility",
                    description = "This campaign is limited to new users only (i.e., wallet address never traded on Jasper Vault before).",
                    icon = "https://assets.jaspervault.io/icons/registration.png"
                ),
                "zh" to I18nTaskInfo(
                    title = "僅限新用戶",
                    description = "該活動限新用戶（即从未在Jasper Vault交易过的錢包地址）",
                    icon = "https://assets.jaspervault.io/icons/registration.png"
                )
            )
        )

        // 2. Twitter Follow Task
        val twitterFollowTask = Task(
            title = "Follow Jasper Vault Official X",
            taskType = TaskType.TWITTER_FOLLOW,
            taskConfig = mapOf(
                TaskConfigParams.TWITTER_FOLLOW_LINK to "https://x.com/jaspervault"
            ),
            i18nInfo = mutableMapOf(
                "en" to I18nTaskInfo(
                    title = "Follow Jasper Vault Official X",
                    description = "Click the link to follow the Jasper Vault official X account.",
                    icon = "https://assets.jaspervault.io/icons/twitter.png"
                ),
                "zh" to I18nTaskInfo(
                    title = "關注Jasper Vault官方X",
                    description = "點擊即可關注 Jasper Vault 官方 X 帳號。",
                    icon = "https://assets.jaspervault.io/icons/twitter.png"
                )
            )
        )

        // 3. Twitter Retweet Task
        val twitterRetweetTask = Task(
            title = "Like and Retweet the Campaign Post",
            taskType = TaskType.TWITTER_RETWEET,
            taskConfig = mapOf(
                TaskConfigParams.TWITTER_RETWEET_LINK to "https://x.com/jaspervault/status/1924682446454603875"
            ),
            i18nInfo = mutableMapOf(
                "en" to I18nTaskInfo(
                    title = "Like and Retweet the Campaign Post",
                    description = "Like and retweet this campaign post in your X account.",
                    icon = "https://assets.jaspervault.io/icons/retweet.png"
                ),
                "zh" to I18nTaskInfo(
                    title = "點贊轉發活動推文",
                    description = "在您的 X 賬號上點贊並轉推本次活動推文。",
                    icon = "https://assets.jaspervault.io/icons/retweet.png"
                )
            )
        )

        // 4. Bind Twitter Account Task
        val twitterBindTask = Task(
            title = "Bind X Account",
            taskType = TaskType.BIND_TWITTER_ACCOUNT,
            taskConfig = mapOf(),
            i18nInfo = mutableMapOf(
                "en" to I18nTaskInfo(
                    title = "Bind X Account",
                    description = "Click \"Connect\" to bind your X account. ",
                    icon = "https://assets.jaspervault.io/icons/twitter.png"
                ),
                "zh" to I18nTaskInfo(
                    title = "綁定 X 賬號",
                    description = "點擊“Connect”以綁定您的 X 帳號.",
                    icon = "https://assets.jaspervault.io/icons/twitter.png"
                )
            )
        )

        // 5. Discord Bind Task
        val discordBindTask = Task(
            title = "Authorize your Discord account",
            taskType = TaskType.DISCORD_BIND,
            taskConfig = mapOf(),
            i18nInfo = mutableMapOf(
                "en" to I18nTaskInfo(
                    title = "Authorize your Discord account",
                    description = "Click \"Connect\" to connect your Discord account.",
                    icon = "https://assets.jaspervault.io/icons/discord.png"
                ),
                "zh" to I18nTaskInfo(
                    title = "授權您的 Discord 賬號",
                    description = "點擊\"Connect\"以綁定您的 Discord 賬號。",
                    icon = "https://assets.jaspervault.io/icons/discord.png"
                )
            )
        )

        // 6. Discord Server Join Task
        val discordServerJoinTask = Task(
            title = "Join Jasper Vault Official Discord",
            taskType = TaskType.DISCORD_SERVER_JOIN,
            taskConfig = mapOf(),
            i18nInfo = mutableMapOf(
                "en" to I18nTaskInfo(
                    title = "Join Jasper Vault Official Discord",
                    description = "Join Jasper Vault Official Discord",
                    icon = "https://assets.jaspervault.io/icons/discord-server.png"
                ),
                "zh" to I18nTaskInfo(
                    title = "加入Jasper Vault官方Discord社區",
                    description = "加入Jasper Vault官方Discord社區",
                    icon = "https://assets.jaspervault.io/icons/discord-server.png"
                )
            )
        )

        // 7. Discord Level Upgrade Task
        val discordLevelUpgradeTask = Task(
            title = "Complete Level 1 verification",
            taskType = TaskType.DISCORD_LEVEL_UPGRADE,
            taskConfig = mapOf(
                TaskConfigParams.MIN_DISCORD_LEVEL to "1"
            ),
            i18nInfo = mutableMapOf(
                "en" to I18nTaskInfo(
                    title = "Complete Level 1 verification",
                    description = "Complete Level 1 verification (Send at least 3 messages in any channel).",
                    icon = "https://assets.jaspervault.io/icons/level-up.png"
                ),
                "zh" to I18nTaskInfo(
                    title = "完成1級驗證",
                    description = "完成 1 級驗證（在任意頻道發送至少 3 條消息）",
                    icon = "https://assets.jaspervault.io/icons/level-up.png"
                )
            )
        )

        // 8. Option Order Completion Task
        val optionOrderCompletionTask = Task(
            title = "First Trade - 30-minute 0.2 ETH size",
            taskType = TaskType.OPTION_ORDER_COMPLETION,
            taskConfig = mapOf(
                TaskConfigParams.BID_ASSET to "ETH",
                TaskConfigParams.BID_AMOUNT to "0.2",
                TaskConfigParams.FIRST_ORDER_IN_CAMPAIGN to true,
                TaskConfigParams.CHAIN to "ARBITRUM",
                TaskConfigParams.ORDER_LINK to "https://app.jaspervault.io/pro/#/leverage"
            ),
            i18nInfo = mutableMapOf(
                "en" to I18nTaskInfo(
                    title = "Place the First Trade - 30 mins * 0.2 ETH option",
                    description = "The first trade must be a 30-minute * 0.2 ETH options order.",
                    icon = "https://assets.jaspervault.io/icons/order.png"
                ),
                "zh" to I18nTaskInfo(
                    title = "首筆下單 - 30分鐘 0.2 ETH期權",
                    description = "首次交易需為30分鐘 0.2個ETH期權訂單。",
                    icon = "https://assets.jaspervault.io/icons/order.png"
                )
            )
        )

        // Save all tasks
        val savedTasks = mutableListOf<Task>()
        savedTasks.add(taskRepository.save(newUserRegistrationTask))
        savedTasks.add(taskRepository.save(twitterFollowTask))
        savedTasks.add(taskRepository.save(twitterRetweetTask))
        savedTasks.add(taskRepository.save(twitterBindTask))
        savedTasks.add(taskRepository.save(discordBindTask))
        savedTasks.add(taskRepository.save(discordServerJoinTask))
        savedTasks.add(taskRepository.save(discordLevelUpgradeTask))
        savedTasks.add(taskRepository.save(optionOrderCompletionTask))

        logger.info("Created ${savedTasks.size} tasks for Jasper Vault campaign")

        return savedTasks
    }

    /**
     * Create task groups for the Jasper Vault campaign
     */
    private fun createCampaignTaskGroups(tasks: List<Task>): List<TaskGroup> {
        val taskGroupService = applicationContext.getBean(TaskGroupService::class.java)
        val taskGroupRepository = applicationContext.getBean(TaskGroupRepository::class.java)

        // Create task groups with i18n information
        val newUserTaskGroup = TaskGroup(
            title = "Check the New User Eligibility",
            i18nInfo = mutableMapOf(
                "en" to I18nTaskGroupInfo(
                    title = "Check the New User Eligibility",
                    description = "This campaign is limited to new users only (i.e., wallet address never traded on Jasper Vault before).",
                    icon = "https://assets.jaspervault.io/icons/registration.png"
                ),
                "zh" to I18nTaskGroupInfo(
                    title = "僅限新用戶",
                    description = "該活動限新用戶（即从未在Jasper Vault交易过的錢包地址）",
                    icon = "https://assets.jaspervault.io/icons/registration.png"
                )
            )
        )

        val firstTradeTaskGroup = TaskGroup(
            title = "Place the First Trade - 30 mins * 0.2 ETH option",
            i18nInfo = mutableMapOf(
                "en" to I18nTaskGroupInfo(
                    title = "Place the First Trade - 30 mins * 0.2 ETH option",
                    description = "The first trade must be a 30-minute * 0.2 ETH options order.",
                    icon = "https://assets.jaspervault.io/icons/trading.png",
                    extraNote= "* Complete additional 3 steps to claim premium loss compensation."
                ),
                "zh" to I18nTaskGroupInfo(
                    title = "首筆下單 - 30分鐘 0.2 ETH期權",
                    description = "首次交易需為，30分鐘 0.2個ETH期權訂單。",
                    icon = "https://assets.jaspervault.io/icons/trading.png",
                    extraNote= "* 完成額外3步來領取權利金損失補償。"
                )
            )
        )

        val xSocialMediaTaskGroup = TaskGroup(
            title = "Follow @Jaspervault on X",
            i18nInfo = mutableMapOf(
                "en" to I18nTaskGroupInfo(
                    title = "Follow @Jaspervault on X",
                    description = "Click \"Connect\" to connect your X account. And click it once again to follow the Jasper Vault official X account.",
                    icon = "https://assets.jaspervault.io/icons/social-media.png"
                ),
                "zh" to I18nTaskGroupInfo(
                    title = "關注Jasper Vault官方X  @Jaspervault",
                    description = "點擊“Connect”以綁定您的 X 賬號，再次點擊即可關注 Jasper Vault 官方 X 賬號。",
                    icon = "https://assets.jaspervault.io/icons/social-media.png"
                )
            )
        )

        val retweetTaskGroup = TaskGroup(
            title = "Like and Retweet the Campaign Post",
            i18nInfo = mutableMapOf(
                "en" to I18nTaskGroupInfo(
                    title = "Like and Retweet the Campaign Post",
                    description = "Like and retweet this campaign post in your X account.",
                    icon = "https://assets.jaspervault.io/icons/retweet.png"
                ),
                "zh" to I18nTaskGroupInfo(
                    title = "點贊轉發活動推文",
                    description = "在您的 X 賬號上點贊並轉推本次活動推文。",
                    icon = "https://assets.jaspervault.io/icons/retweet.png"
                )
            )
        )

        val discordSocialMediaTaskGroup = TaskGroup(
            title = "Join Jasper Vault Official Discord",
            i18nInfo = mutableMapOf(
                "en" to I18nTaskGroupInfo(
                    title = "Join Jasper Vault Official Discord",
                    description = "\"1. Authorize your discord account: Discord Authorization\n" +
                            "2. Join Jasper Vault’s official Discord:Discord  Invitation\n" +
                            "3. Complete Level 1 verification (Send at least 3 messages in any channel).\"",
                    icon = "https://assets.jaspervault.io/icons/discord.png"
                ),
                "zh" to I18nTaskGroupInfo(
                    title = "加入Jasper Vault官方Discord社區",
                    description = "\"1. 授權您的 Discord 賬號：Discord 授權\n" +
                            "2. 加入 Jasper Vault 官方 Discord：Discord 邀請鏈接\n" +
                            "3. 完成 1 級驗證（在任意頻道發送至少 3 條消息）\"",
                    icon = "https://assets.jaspervault.io/icons/discord.png"
                )
            )
        )

        // Save task groups
        val savedNewUserTaskGroup = taskGroupService.createTaskGroup(newUserTaskGroup)
        val savedFirstTradeTaskGroup = taskGroupService.createTaskGroup(firstTradeTaskGroup)
        val savedXSocialMediaTaskGroup = taskGroupService.createTaskGroup(xSocialMediaTaskGroup)
        val savedRetweetTaskGroup = taskGroupService.createTaskGroup(retweetTaskGroup)
        val savedDiscordSocialMediaTaskGroup = taskGroupService.createTaskGroup(discordSocialMediaTaskGroup)

        // Find all tasks by type
        val newUserRegistrationTask = tasks.find { it.taskType == TaskType.NEW_USER_REGISTRATION }
        val optionOrderCompletionTask = tasks.find { it.taskType == TaskType.OPTION_ORDER_COMPLETION }
        val twitterBindTask = tasks.find { it.taskType == TaskType.BIND_TWITTER_ACCOUNT }
        val twitterFollowTask = tasks.find { it.taskType == TaskType.TWITTER_FOLLOW }
        val twitterRetweetTask = tasks.find { it.taskType == TaskType.TWITTER_RETWEET }
        val discordBindTask = tasks.find { it.taskType == TaskType.DISCORD_BIND }
        val discordServerJoinTask = tasks.find { it.taskType == TaskType.DISCORD_SERVER_JOIN }
        val discordLevelUpgradeTask = tasks.find { it.taskType == TaskType.DISCORD_LEVEL_UPGRADE }

        // 1. New User Registration Task Group
        val newUserTaskIds = mutableListOf<String>()
        if (newUserRegistrationTask != null) {
            newUserTaskIds.add(newUserRegistrationTask.id!!)
        }

        // Update new user task group with task IDs
        val updatedNewUserTaskGroup = savedNewUserTaskGroup.copy(taskIds = newUserTaskIds)
        val finalNewUserTaskGroup = taskGroupRepository.save(updatedNewUserTaskGroup)

        // 2. First Trade Task Group
        val firstTradeTaskIds = mutableListOf<String>()
        if (optionOrderCompletionTask != null) {
            firstTradeTaskIds.add(optionOrderCompletionTask.id!!)
        }

        // Update first trade task group with task IDs
        val updatedFirstTradeTaskGroup = savedFirstTradeTaskGroup.copy(taskIds = firstTradeTaskIds)
        val finalFirstTradeTaskGroup = taskGroupRepository.save(updatedFirstTradeTaskGroup)

        // 3. X Social Media Tasks: Bind X Account, Follow Jasper Twitter
        val xSocialMediaTaskIds = mutableListOf<String>()
        if (twitterBindTask != null) {
            xSocialMediaTaskIds.add(twitterBindTask.id!!)
        }
        if (twitterFollowTask != null) {
            xSocialMediaTaskIds.add(twitterFollowTask.id!!)
        }

        // Update X social media task group with task IDs
        val updatedXSocialMediaTaskGroup = savedXSocialMediaTaskGroup.copy(taskIds = xSocialMediaTaskIds)
        val finalXSocialMediaTaskGroup = taskGroupRepository.save(updatedXSocialMediaTaskGroup)

        // 4. Retweet Task Group
        val retweetTaskIds = mutableListOf<String>()
        if (twitterRetweetTask != null) {
            retweetTaskIds.add(twitterRetweetTask.id!!)
        }

        // Update retweet task group with task IDs
        val updatedRetweetTaskGroup = savedRetweetTaskGroup.copy(taskIds = retweetTaskIds)
        val finalRetweetTaskGroup = taskGroupRepository.save(updatedRetweetTaskGroup)

        // 5. Discord Social Media Tasks: Bind Discord Account, Join Jasper Discord Server, Upgrade to Lv1
        val discordSocialMediaTaskIds = mutableListOf<String>()
        if (discordBindTask != null) {
            discordSocialMediaTaskIds.add(discordBindTask.id!!)
        }
        if (discordServerJoinTask != null) {
            discordSocialMediaTaskIds.add(discordServerJoinTask.id!!)
        }
        if (discordLevelUpgradeTask != null) {
            discordSocialMediaTaskIds.add(discordLevelUpgradeTask.id!!)
        }

        // Update Discord social media task group with task IDs
        val updatedDiscordSocialMediaTaskGroup = savedDiscordSocialMediaTaskGroup.copy(taskIds = discordSocialMediaTaskIds)
        val finalDiscordSocialMediaTaskGroup = taskGroupRepository.save(updatedDiscordSocialMediaTaskGroup)

        logger.info("Created task groups for Jasper Vault campaign")

        return listOf(
            finalNewUserTaskGroup,
            finalFirstTradeTaskGroup,
            finalXSocialMediaTaskGroup,
            finalRetweetTaskGroup,
            finalDiscordSocialMediaTaskGroup
        )
    }

    /**
     * Create the Jasper Vault campaign
     */
    private fun createJasperVaultCampaign(tasks: List<Task>, taskGroups: List<TaskGroup>): Campaign {
        val campaignService = applicationContext.getBean(CampaignService::class.java)

        val campaign = Campaign(
            name = "Ride the ETH - First Trade Always Wins",
            startTime = LocalDateTime.parse("2025-05-26T13:00:00"),
            endTime = LocalDateTime.parse("2025-06-09T09:00:00"),
            taskIds = tasks.map { it.id!! },
            taskGroupIds = taskGroups.map { it.id!! },
            totalRewardAmount = "3000",
            smartContractAddress = "******************************************",
            active = true
        )

        val i18nInfo = mutableMapOf<String, I18nCampaignInfo>()
        i18nInfo["en"] = I18nCampaignInfo(
            name = "Ride the ETH - First Trade Always Wins",
            description = "Your first 30 min 0.2 ETH options trade is covered. If you lose, we’ll refund the loss.",
            bannerMobile = "https://assets.jaspervault.io/campaign/banner.png",
            bannerDesktop = "https://assets.jaspervault.io/campaign/banner.png",
            ruleLink = "https://jaspervault.medium.com/ee466769d077"
        )
        i18nInfo["zh"] = I18nCampaignInfo(
            name = "乘風以太 - 首單永賺",
            description = "新用戶首單30分鐘 0.2 ETH期權，贏可提走；若虧損，完成3步社媒任務即可補償虧損。",
            bannerMobile = "https://assets.jaspervault.io/campaign/banner.png",
            bannerDesktop = "https://assets.jaspervault.io/campaign/banner.png",
            ruleLink = "https://jaspervault.medium.com/ee466769d077"
        )

        campaign.i18nInfo = i18nInfo

        val savedCampaign = campaignService.createCampaign(campaign)
        logger.info("Created Jasper Vault campaign with ID: ${savedCampaign.id}")

        return savedCampaign
    }

    /**
     * Create the campaign reward
     */
    private fun createCampaignReward(campaignId: String): CampaignReward {
        val campaignRewardService = applicationContext.getBean(CampaignRewardService::class.java)

        val campaignReward = CampaignReward(
            campaignId = campaignId,
            rewardType = RewardType.ORDER_LOSS_COMPENSATION,
            rewardConfig = mapOf(
                RewardConfigParams.SMART_CONTRACT_ADDRESS to "******************************************",
                RewardConfigParams.GEN_REWARD_RECORD_FOR_ZERO_REWARD to "true"
            ),
            totalRewardAmount = BigDecimal("3000"),
            i18nInfo = mutableMapOf(
                "en" to I18nCampaignRewardInfo(
                    title = "Premium Loss Compensation",
                    description = "Rewards"
                ),
                "zh" to I18nCampaignRewardInfo(
                    title = "權利金虧損補償",
                    description = "獎勵"
                )
            )
        )

        val savedReward = campaignRewardService.createCampaignReward(campaignReward)
        logger.info("Created campaign reward with ID: ${savedReward.id}")

        return savedReward
    }

    @Operation(summary = "铸造测试")
    @CrossOrigin
    @GetMapping("/mint_test")
    fun mintTest(
        chain: ChainType,
//        hash: String,
        onChainOrderId: String

    ) {

        val optionOrder = optionOrderRepository.findByChainAndOnChainOrderId(
            chain,
            onChainOrderId
        )

        if(optionOrder == null){
            logger.info("OptionOrder not found")
            return
        }

        val expiryDate = optionOrder.expiryDate

        val orderLastSeconds = optionOrderService.calculateOptionOrderSeconds(optionOrder)
        val startDate = expiryDate.minusSeconds(orderLastSeconds)

        val quoteAssetPythId = currencyService.getPriceId(optionOrder.quoteAsset!!)
        val price = jasperVaultService.getPythPriceAtDate(
            quoteAssetPythId,
            startDate,
            30
        )

        logger.info("$price")


//        val evmService = blockchainServiceFactory.getBlockchainService(chain) as EvmService
//        // val tx = evmService.evmUtil.web3j.ethGetTransactionByHash(hash).send().transaction.get()
//        val receipt = evmService.evmUtil.web3j.ethGetTransactionReceipt(hash).send().transactionReceipt.get()
//
//        val result = evmService.parseStrikePriceInfoFromLog(
//            receipt.logs,
//            onChainOrderId.toBigInteger()
//        )
//
//        logger.info("${result?.first}, ${result?.second}")
        //jasperVaultService.stopKMS()
    }

    @Operation(summary = "补充合约交易")
    @CrossOrigin
    @PostMapping("/addOptionOrder")
    fun addOptionOrder(@RequestBody dto: AddOptionOrderDTO): ApiResponse<String> {
        val blockchainService = blockchainServiceFactory.getBlockchainService(dto.chain)
        if (!blockchainServiceFactory.isEvmService(blockchainService)) {
            throw BusinessException(ResultEnum.CHAIN_NOT_SUPPORTED)
        }

        var inputAmount: BigDecimal? = null
        if(dto.inputBidAmount != null){
            inputAmount = BigDecimal(dto.inputBidAmount)
        }

        val evmService = blockchainService as EvmService
        val (optionStruct, optionPremium) = evmService.getOptionOrderDetail(dto.txHash)
        if (optionStruct == null) throw BusinessException(ResultEnum.ORDER_NOT_FOUND)
        if (optionPremium == null) throw BusinessException(ResultEnum.ORDER_NOT_FOUND)
        val oOrder = evmService.createOptionOrder(
            dto.txHash,
            optionStruct,
            optionPremium
        )

        return ApiResponse.success(oOrder.id!!)
    }

    @Operation(summary = "补充合约交易2")
    @CrossOrigin
    @PostMapping("/addOptionOrderExceptOrderId")
    fun addOptionOrderExceptOrderId(@RequestBody dto: AddOptionOrderDTO): ApiResponse<List<String>> {
        val blockchainService = blockchainServiceFactory.getBlockchainService(dto.chain)
        if (!blockchainServiceFactory.isEvmService(blockchainService)) {
            throw BusinessException(ResultEnum.CHAIN_NOT_SUPPORTED)
        }

        val evmService = blockchainService as EvmService
        val exceptOrderIds = mutableListOf<BigInteger>()
        val resultOrderIds = mutableListOf<String>()

        while(true) {

            val (optionStruct, optionPremium) = evmService.getOptionOrderDetailExceptOrderIds(
                dto.txHash, exceptOrderIds
            )
            if (optionStruct == null){
                break
            }

            if (optionPremium == null){
                break
            }

            val oOrder = evmService.createOptionOrder(
                dto.txHash,
                optionStruct,
                optionPremium
            )

            // 发布订单创建成功事件通知
            eventPublisher.publishEvent(AfterCreateOptionOrderEvent(this, oOrder.id!!))
            // 更新订单权利金信息
            eventPublisher.publishEvent(UpdatePremiumFeeEvent(this, oOrder.id))

            // 事件通知
            if (oOrder.status == OptionStatus.EXECUTED) {
                eventPublisher.publishEvent(OptionExecutedEvent(this, oOrder.id))
            }

            resultOrderIds.add(oOrder.id!!)
            exceptOrderIds.add(oOrder.onChainOrderId!!.toBigInteger())
        }

        return ApiResponse.success(resultOrderIds)
    }
}
