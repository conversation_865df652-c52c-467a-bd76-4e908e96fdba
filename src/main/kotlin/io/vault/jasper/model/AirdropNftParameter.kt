package io.vault.jasper.model

import io.vault.jasper.enums.ChainType
import org.springframework.data.annotation.Id
import org.springframework.data.mongodb.core.mapping.Document
import org.springframework.data.mongodb.core.mapping.Field
import java.math.BigDecimal
import java.time.LocalDateTime

@Document("airdrop_nft_parameter")
data class AirdropNftParameter(
    @Id
    val id: String? = null,
    @Field("start_date")
    var startDate: LocalDateTime = LocalDateTime.now(),
    @Field("free_mint_chain")
    var freeMintChain: ChainType = ChainType.BASE,
    @Field("free_mint_contract_address")
    var freeMintContractAddress: String = "0xFd4A69a3E4B7d45B4577481B69f05846C84A2F4C",
    @Field("free_mint_task_switch")
    var freeMintTaskSwitch: Boolean = true, // 推送Free Mint任务的开关
    @Field("tweet_id")
    var tweetId: String = "1825800581090545926",
)