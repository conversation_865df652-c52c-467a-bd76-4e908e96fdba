package io.vault.jasper.response

import io.swagger.annotations.ApiModel
import io.swagger.annotations.ApiModelProperty
import io.vault.jasper.enums.ChainType
import io.vault.jasper.model.KolStatus

@ApiModel
data class KolResponse(

    @ApiModelProperty("KOL ID", required = true)
    val id: String,

    @ApiModelProperty("钱包地址", required = true)
    val wallet: String,

    @ApiModelProperty("等级", required = true)
    val level: String,

    @ApiModelProperty("推荐码", required = true)
    val referralCode: String?,

    @ApiModelProperty("当前返佣余额", required = true)
    val incentive: String,

    @ApiModelProperty("KOL状态", required = true)
    val status: KolStatus
)
