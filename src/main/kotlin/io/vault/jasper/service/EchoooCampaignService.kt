package io.vault.jasper.service

import io.vault.jasper.enums.ChainType
import io.vault.jasper.enums.OptionDirection
import io.vault.jasper.enums.Symbol
import io.vault.jasper.model.*
import io.vault.jasper.repository.*
import org.slf4j.LoggerFactory
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.data.repository.findByIdOrNull
import org.springframework.stereotype.Service
import java.math.BigDecimal
import java.time.LocalDateTime
import java.util.*

@Service
class EchoooCampaignService @Autowired constructor(
    private val echoooTradeRebateRecordRepository: EchoooTradeRebateRecordRepository,
    private val echoooCampaignParameterRepository: EchoooCampaignParameterRepository,
    private val optionOrderRepository: OptionOrderRepository,
    private val userRepository: UserRepository,
    private val orderRepository: OrderRepository,
    private val discordLevelRepository: DiscordLevelRepository,
    private val currencyService: CurrencyService,
    private val airDropService: AirDropService,
    private val userPointRecordRepository: UserPointRecordRepository,
    private val userPointService: UserPointService,
    private val optionOrderInfoRepository: OptionOrderInfoRepository
){

    private val logger = LoggerFactory.getLogger(this::class.java)

    /**
     * 获取空投参数对象
     */
    fun getCampaignParameter(): EchoooCampaignParameter {
        val params = echoooCampaignParameterRepository.findAll()

        return if (params.isEmpty()) {
            val p = EchoooCampaignParameter(null)
            echoooCampaignParameterRepository.save(p)
        } else {
            params.first()
        }
    }

    /**
     * 判断是否活动内首单
     */
    fun isFirstOrderInCampaign(
        optionOrder: OptionOrder
    ): Boolean{

        if(optionOrder.status != OptionStatus.SETTLED &&
            optionOrder.status != OptionStatus.EXECUTED &&
            optionOrder.status != OptionStatus.SETTLE_FAILED){
            return false
        }

        val parameter = getCampaignParameter()

        // 符合 0.5H，0.2 ETH 使用 BTC 或者 USDT 支付期权费 规格的订单
        if(optionOrder.bidAsset == null || optionOrder.bidAsset != Symbol.ETH){
            logger.info("EchoooCampaignService Service Not ETH ${optionOrder.buyer} ${optionOrder.id}")
            return false
        }

        if(optionOrder.expiryInHour == null || optionOrder.expiryInHour != "0.5"){
            logger.info("EchoooCampaignService Service Not 0.5 hours ${optionOrder.buyer} ${optionOrder.id}")
            return false
        }

        if(optionOrder.bidAmount == null || optionOrder.bidAmount!!.compareTo(parameter.firstTradeDegenQuantity) != 0){
            logger.info("EchoooCampaignService Service Not 0.2 ETH ${optionOrder.buyer} ${optionOrder.id}")
            return false
        }

        // 活动开始后的第一单 DEGEN ETH
        val firstOptionOrder = optionOrderRepository.findFirstByChainAndBidAssetAndExpiryInHourAndBidAmountAndBuyerIgnoreCaseAndCreatedGreaterThan(
            ChainType.ARBITRUM,
            Symbol.ETH,
            optionOrder.expiryInHour,
            optionOrder.bidAmount!!,
            optionOrder.buyer!!,
            parameter.startDate
        )

        if(firstOptionOrder == null){
            logger.info("EchoooCampaignService can not get option orders from db ${optionOrder.buyer} ${optionOrder.id}")
            return false
        }

        if(firstOptionOrder.id!! != optionOrder.id!!){
            logger.info("EchoooCampaignService first order id not match ${optionOrder.buyer} ${optionOrder.id}")
            return false
        }

        // 看看是否从 Echooo 过来的订单
        val optionOrderInfo = optionOrderInfoRepository.findByTxHash(
            optionOrder.txHash!!
        )

        if(optionOrderInfo == null || optionOrderInfo.channel != UserChannel.ECHOOO){
            logger.info("EchoooCampaignService Service Not From Echooo ${optionOrder.buyer} ${optionOrder.id}")
            return false
        }

//        val user = userRepository.findByAddressIgnoreCase(optionOrder.buyer!!)
//        if(user == null){
//            return false
//        }

        return true
    }

    fun createTradeRebateRecord(
        optionOrder: OptionOrder
    ){

        val parameter = getCampaignParameter()
        val rebateCount = getFirstTradeRebateCount()
        if( rebateCount >= parameter.firstTradeRebateCount){
            logger.info("EchoooCampaignService first trade rebate task reach max count")
            return
        }

        val now = LocalDateTime.now()
        if(now.isAfter(parameter.endDate)){
            logger.info("EchoooCampaignService first trade rebate task $now reach end date ${parameter.endDate}")
            return
        }

        // 是否已经创建了订单
        val existingRecord = echoooTradeRebateRecordRepository.findByOptionOrderId(
            optionOrder.id!!
        )

        if(existingRecord != null){
            logger.info("EchoooCampaignService Service Already add rebate record ${optionOrder.buyer} ${optionOrder.id}")
            return
        }

        // 是否有同样地址的订单
        val existingAddressRecords = echoooTradeRebateRecordRepository.findByBuyerAddressIgnoreCase(
            optionOrder.buyer!!
        )

        if(existingAddressRecords.isNotEmpty()){
            logger.info("EchoooCampaignService Service Already same address rebate record ${optionOrder.buyer} ${optionOrder.id}")
            return
        }

        if(!isFirstOrderInCampaign(optionOrder)){
            logger.info("EchoooCampaignService Service Not First Order ${optionOrder.buyer} ${optionOrder.id}")
            return
        }

        val record = EchoooTradeRebateRecord(
            optionOrderId = optionOrder.id,
            chain = ChainType.ARBITRUM,
            txHash = optionOrder.txHash!!,
            buyerAddress = optionOrder.buyer!!,
            direction = optionOrder.direction!!,
            bidAmount = optionOrder.bidAmount!!
        )

        echoooTradeRebateRecordRepository.save(record)
    }

    fun checkEchoooTradeRebate(
        record: EchoooTradeRebateRecord
    ){

        val optionOrder = optionOrderRepository.findByIdOrNull(record.optionOrderId)
        if(optionOrder == null){
            logger.info("EchoooCampaignService Service can not find order record ${record.buyerAddress} ${record.optionOrderId}")
            return
        }

        if(optionOrder.status == OptionStatus.SETTLE_FAILED){
            logger.info("EchoooCampaignService Service order settle failed ${optionOrder.buyer} ${optionOrder.id}")
            record.status = EchoooTradeRebateRecordStatus.SETTLE_FAILED
            echoooTradeRebateRecordRepository.save(record)
            return
        }

        if(optionOrder.status != OptionStatus.SETTLED){
            logger.info("EchoooCampaignService Service Not Settled order ${optionOrder.buyer} ${optionOrder.id}")
            return
        }

        //是否是新用户
        val user = userRepository.findByAddressIgnoreCase(optionOrder.buyer!!)
        if(user == null){
            logger.debug("EchoooCampaignService Service not new user ${optionOrder.buyer} ${optionOrder.id}")
            return
        }

        val order = orderRepository.findByIdOrNull(optionOrder.orderId)
        if(order == null){
            logger.debug("DlcbtcCampaignService Service can not find order record ${optionOrder.buyer} ${optionOrder.id}")
            return
        }

//        record.twitterAccountId = user.twitterAccountId
//        record.twitterTaskFinished = user.twitterTaskFinished
//        record.hasJoinDiscord = user.discordInfo?.inGuild ?: false

        record.strikePrice = order.strikePrice.movePointLeft(18)
        record.settlementPrice = BigDecimal(optionOrder.marketPriceAtSettlement!!).movePointLeft(18)

        val usdtDecimal = currencyService.getCurrencyDecimal(
            ChainType.ARBITRUM, Symbol.USDT
        )
        val ethDecimal = currencyService.getCurrencyDecimal(
            ChainType.ARBITRUM, Symbol.ETH
        )

        record.premiumFee = optionOrder.premiumFeePay!!
        record.premiumAsset = Symbol.valueOf(optionOrder.premiumAsset!!.asset)

//        if(record.premiumAsset == Symbol.ETH){
//            record.premiumFee = optionOrder.premiumFeePay!!
//        } else {
//            val readablePremiumFee = optionOrder.premiumFeePay!!.movePointLeft(usdcDecimal)
//            val readableFeeInBtc = readablePremiumFee.divide(record.strikePrice, btcDecimal, BigDecimal.ROUND_HALF_UP)
//        }

        record.premiumFeeInUsdt = optionOrder.premiumFeePayInUsdt!!

        record.profit = optionOrder.buyerProfit!!

        if(record.direction == OptionDirection.CALL){
            record.profitAsset = Symbol.ETH
            val readableProfit = record.profit.movePointLeft(ethDecimal)
            val readableUsdtProfit = readableProfit.multiply(record.settlementPrice).setScale(usdtDecimal, BigDecimal.ROUND_HALF_UP)
            record.profitInUsdt = readableUsdtProfit.movePointRight(usdtDecimal).setScale(0, BigDecimal.ROUND_HALF_UP)
        } else {
            record.profitAsset = Symbol.USDT
            record.profitInUsdt = optionOrder.buyerProfit!!
//            val readableUsdcProfit = record.profitInUsdt.movePointLeft(usdcDecimal)
//            val readableBtcProfit = readableUsdcProfit.divide(record.settlementPrice, btcDecimal, BigDecimal.ROUND_HALF_UP)
//            record.profitInBtc = readableBtcProfit.movePointRight(btcDecimal).setScale(0, BigDecimal.ROUND_HALF_UP)
        }

        val discordLevel = getDiscordLevel(user)
        record.discordLevel = discordLevel

        record.netProfit = record.profitInUsdt.subtract(record.premiumFeeInUsdt).setScale(0, BigDecimal.ROUND_HALF_UP)
        if(record.netProfit.compareTo(BigDecimal.ZERO) == -1){
            record.rebateAmount = record.netProfit.multiply(BigDecimal("-1"))
            record.status = EchoooTradeRebateRecordStatus.CREATED

//            //获得双倍 杠杆 jpoint
//            val userPointRecord = userPointRecordRepository.findByOptionOrderId(
//                optionOrder.id!!
//            )
//
//            if(userPointRecord != null){
//                userPointRecord.premiumPoint = userPointRecord.premiumPoint.multiply(BigDecimal("2"))
//                userPointRecord.totalPoint = userPointRecord.netProfitPoint.add(userPointRecord.premiumPoint)
//                userPointRecordRepository.save(userPointRecord)
//
//                userPointService.updateUserPointDailySummary(userPointRecord)
//            }

        } else {
            record.status = EchoooTradeRebateRecordStatus.SETTLED
            record.settled = true
        }

        echoooTradeRebateRecordRepository.save(record)
    }

    fun getDiscordLevel(
        user: User
    ): Int {

        val discordId = user.discordInfo?.userId ?: return 0
        val level = discordLevelRepository.findByDiscordId(discordId)
        if(level == null){
            return 0
        }

        return level.level
    }

    fun getFirstTradeRebateCount(): Int {
        return echoooTradeRebateRecordRepository.findAll().size
//        return echoooTradeRebateRecordRepository.findByStatusAndSettleTxIdIsNotNull(
//            EchoooTradeRebateRecordStatus.SETTLED
//        ).size
    }

//    fun getClaimRebateTotalCount(): Int {
//        return dlcbtcTradeRebateRecordRepository.findAll().size
//    }

//    fun getFirstDlcbtcTradesCount(
//        bidAmount: BigDecimal
//    ): Int {
//        return optionOrderRepository.findByChainAndStatusInAndBidAssetAndBidAmountEqualsAndExpiryInHour(
//            ChainType.ARBITRUM,
//            listOf(OptionStatus.SETTLED, OptionStatus.EXECUTED, OptionStatus.SETTLE_FAILED),
//            Symbol.DLCBTC,
//            bidAmount,
//            "2"
//        ).size
//    }
//
//    fun getUnclaimedRebateRecord(
//        user: User
//    ): List<DlcbtcTradeRebateRecord> {
//        return dlcbtcTradeRebateRecordRepository.findByBuyerAddressIgnoreCaseAndStatus(
//            user.address,
//            DlcbtcTradeRebateRecordStatus.CREATED
//        )
//    }
}