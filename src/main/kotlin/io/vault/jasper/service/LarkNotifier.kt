package io.vault.jasper.service

import java.net.HttpURLConnection
import java.net.URL
import java.security.MessageDigest
import javax.crypto.Mac
import javax.crypto.spec.SecretKeySpec
import kotlin.text.Charsets.UTF_8
import org.json.JSONObject
import java.util.*

class LarkNotifier(private val secret: String, private val url: String) {

    private fun genSign(secret: String): Pair<String, String> {
        val timestamp = (System.currentTimeMillis() / 1000).toString()
        // val timestamp = "1730100658"
        // 拼接timestamp和secret
        val stringToSign = "$timestamp\n$secret"
        println(stringToSign)
        // val stringToSignBytes = stringToSign.toByteArray(Charsets.UTF_8)
        // println("stringToSignBytes: $stringToSignBytes")

        // 初始化 HMAC-SHA256
        val mac = Mac.getInstance("HmacSHA256")
        val secretKeySpec = SecretKeySpec(stringToSign.toByteArray(Charsets.UTF_8), "HmacSHA256")
        mac.init(secretKeySpec)

        // 生成 HMAC 代码
        val hmacCode = mac.doFinal(null)

        // 对结果进行 Base64 编码
        val sign = Base64.getEncoder().encodeToString(hmacCode)

        return Pair(timestamp, sign)
    }

    private fun createContentPost(title: String, picKeys: List<String>, text: String?): Map<String, Any> {
        val content = mutableListOf<Any>()
        text?.let {
            content.add(listOf(mapOf("tag" to "text", "text" to it)))
        }
        picKeys.forEach { key ->
            content.add(listOf(mapOf("tag" to "img", "image_key" to key)))
        }
        return mapOf(
            "post" to mapOf(
                "zh_cn" to mapOf(
                    "title" to title,
                    "content" to content
                )
            )
        )
    }

    private fun sendLark(
        conTentPost: Map<String, Any>,
        url: String,
        timestamp: String,
        sign: String,
        msgType: String = "post"
    ): JSONObject {
        val payload = JSONObject(
            mapOf(
                "timestamp" to timestamp,
                "sign" to sign,
                "content" to conTentPost,
                "msg_type" to msgType
            )
        ).toString()
        println("payload: $payload")

        val connection = URL(url).openConnection() as HttpURLConnection
        connection.requestMethod = "POST"
        connection.setRequestProperty("Content-Type", "application/json")
        connection.doOutput = true
        connection.outputStream.use { it.write(payload.toByteArray(UTF_8)) }

        return connection.inputStream.use { inputStream ->
            JSONObject(inputStream.bufferedReader().readText())
        }
    }

    fun sendLark(title: String, text: String? = null): JSONObject {
        val (timestamp, sign) = genSign(secret)
        println("Timestamp: $timestamp, Sign: $sign")
        val conTentPost = createContentPost(title, emptyList(), text)
        return sendLark(conTentPost, url, timestamp, sign)
    }
}

// fun test() {
//     val secret = "NfRIby8qxSvOkAmiizziMb"
//     val url = "https://open.larksuite.com/open-apis/bot/v2/hook/f7dde930-77dd-4ba7-9410-299b28f27278"
//     val notifier = LarkNotifier(secret, url)
//     val result = notifier.sendLark("我是一个标题", "131")
//     println(result.toString(4))
// }