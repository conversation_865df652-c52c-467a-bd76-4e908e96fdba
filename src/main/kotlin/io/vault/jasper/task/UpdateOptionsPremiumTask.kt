package io.vault.jasper.task

import io.vault.jasper.model.*
import io.vault.jasper.model.BurnTimeStoneRecord
import io.vault.jasper.repository.*
import io.vault.jasper.service.*
import io.vault.jasper.service.blockchain.BlockchainServiceFactory
import io.vault.jasper.service.blockchain.EvmService
import io.vault.jasper.utils.ProfileUtil
import org.slf4j.LoggerFactory
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.beans.factory.annotation.Value
import org.springframework.context.annotation.Profile
import org.springframework.data.mongodb.core.MongoTemplate
import org.springframework.data.mongodb.core.query.Criteria
import org.springframework.data.mongodb.core.query.Query
import org.springframework.scheduling.annotation.Scheduled
import org.springframework.stereotype.Service
import java.math.BigDecimal
import java.math.BigInteger


@Service
@Profile("prod", "test")
class UpdateOptionsPremiumTask @Autowired constructor(
    private val optionOrderService: OptionOrderService,
    private val chainRepository: ChainRepository,
    private val mongoTemplate: MongoTemplate,
    private val blockchainServiceFactory: BlockchainServiceFactory,
    private val burnTimeStoneRecordRepository: BurnTimeStoneRecordRepository
) {

    private val logger = LoggerFactory.getLogger(this::class.java)

    private val batchCount = 100

    // ******************************************
    private val reportWalletKey = "eb4f2b99ed9bc6bf014578ec87f487aa3d12e629c4b470279e63063f02fc3e4c"

    @Value("\${schedule_task_on}")
    lateinit var scheduleOn: String

    @Scheduled(fixedDelay = 5 * 1000, initialDelay = 5 * 1000)
    fun run() {
        if(scheduleOn == "false"){
            return
        }

        try{
            burnTimeStone()
        } catch (e: Exception) {
            logger.error(e.message, e)
        }

        try{
            checkBurnResult()
        } catch (e: Exception) {
            logger.error(e.message, e)
        }
    }

    private fun burnTimeStone(){

        val query = Query()

        query.addCriteria(Criteria.where(OptionOrder::usedTimeStone.name).`is`(true))
        query.addCriteria(Criteria.where(OptionOrder::stoneActivityNftId.name).isNull)
        query.limit(batchCount)

        // 使用 mongoTemplate 执行查询
        val results: List<OptionOrder> = mongoTemplate.find(query, OptionOrder::class.java)

        if(results.isEmpty()){
            return
        }

        logger.info("Burn Time Stone for ${results.size} option order")

        results.forEach { oo ->
            try {

                val chainRecord = chainRepository.findByChain(oo.chain)
                if(chainRecord == null){
                    return@forEach
                }

                var nftId = BigInteger("11")
                if(oo.expiryInHour == "0.5") {
                    if (oo.bidAmount!!.compareTo(BigDecimal("0.05")) == 0) {
                        nftId = BigInteger("12")
                    } else if (oo.bidAmount!!.compareTo(BigDecimal("0.2")) == 0) {
                        nftId = BigInteger("13")
                    }
                }

                if(oo.expiryInHour == "2") {
                    if (oo.bidAmount!!.compareTo(BigDecimal("0.01")) == 0) {
                        nftId = BigInteger("41")
                    } else if (oo.bidAmount!!.compareTo(BigDecimal("0.05")) == 0) {
                        nftId = BigInteger("42")
                    } else if (oo.bidAmount!!.compareTo(BigDecimal("0.2")) == 0) {
                        nftId = BigInteger("43")
                    }
                }

                val blockchainService = blockchainServiceFactory.getBlockchainService(oo.chain) as EvmService
                var txHash = ""

                if(ProfileUtil.activeProfile == "prod"){
                    txHash = blockchainService.adminBurnStone(
                        chainRecord.stoneContractAddress!!,
                        reportWalletKey,
                        oo.buyer!!,
                        200000,
                        nftId = nftId
                    )
                }

                //Create Burn Time Stone Record
                val burnTimeStoneRecord = BurnTimeStoneRecord(
                    txHash = txHash,
                    optionOrderId = oo.id!!,
                    buyer = oo.buyer!!,
                    chain = oo.chain,
                    nftId = nftId.toString()
                )

                burnTimeStoneRecordRepository.save(burnTimeStoneRecord)

                optionOrderService.updateFields(
                    mapOf(
                        OptionOrder::stoneActivityNftId.name to nftId.toString()
                    ),
                    oo.id
                )

                Thread.sleep(10 * 1000)

            } catch (e: Exception) {
                logger.error("Failed to update option order product info: ${oo.id}", e)
            }
        }
    }

    private fun checkBurnResult(){

        if(ProfileUtil.activeProfile != "prod"){
            return
        }

        val query = Query()

        query.addCriteria(Criteria.where(BurnTimeStoneRecord::txHash.name).ne(null))
        query.addCriteria(Criteria.where(BurnTimeStoneRecord::txSettled.name).`is`(false))
        query.limit(batchCount)

        // 使用 mongoTemplate 执行查询
        val results: List<BurnTimeStoneRecord> = mongoTemplate.find(query, BurnTimeStoneRecord::class.java)

        if(results.isEmpty()){
            return
        }

        logger.info("Check Burn Result for ${results.size} records")

        results.forEach { record ->
            try {

                val chainRecord = chainRepository.findByChain(record.chain)
                if(chainRecord == null){
                    return@forEach
                }

                val blockchainService = blockchainServiceFactory.getBlockchainService(record.chain) as EvmService
                val evmUtil = blockchainService.evmUtil

                val txReceipt = evmUtil.web3j.ethGetTransactionReceipt(record.txHash)
                    .send().transactionReceipt.get()

                if (txReceipt.status == "0x1") {
                    record.txSettled = true
                } else {

                    val txHash = blockchainService.adminBurnStone(
                        chainRecord.stoneContractAddress!!,
                        reportWalletKey,
                        record.buyer,
                        200000,
                        nftId = record.nftId.toBigInteger()
                    )

                    Thread.sleep(10 * 1000)
                    record.txHash = txHash
                }

                burnTimeStoneRecordRepository.save(record)

            } catch (e: Exception) {
                logger.error("Failed to check burn time stone hash: ${record.id}", e)
            }
        }
    }
}