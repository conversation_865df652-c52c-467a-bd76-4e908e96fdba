package io.vault.jasper.service

import io.vault.jasper.model.*
import io.vault.jasper.repository.HistoryRepository
import io.vault.jasper.repository.OrderRepository
import io.vault.jasper.service.blockchain.EvmService
import io.vault.jasper.utils.DateTimeUtil
import org.slf4j.LoggerFactory
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.data.repository.findByIdOrNull
import org.springframework.stereotype.Service
import org.web3j.protocol.core.DefaultBlockParameter
import java.math.BigDecimal
import java.time.LocalDateTime

@Service
class HistoryService @Autowired constructor(
    private val historyRepository: HistoryRepository,
    private val orderRepository: OrderRepository
) {

    private val logger = LoggerFactory.getLogger(this::class.java)

    private fun getTxTime(txHash: String, service: EvmService): LocalDateTime? {
        return try {
            val blockNumber = service.evmUtil.web3j.ethGetTransactionByHash(txHash).send().transaction.get().blockNumber
            val block = service.evmUtil.web3j.ethGetBlockByNumber(DefaultBlockParameter.valueOf(blockNumber), false).send().block

            DateTimeUtil.convertTimestampToLocalDateTime(block.timestamp.toLong())
        } catch (e: Exception) {
            null
        }
    }

    /**
     * 最多两笔支付记录：权利金+Credit金（指定合约支付）/免单权利金
     */
    fun addPremiumFee(oo: OptionOrder, service: EvmService) {
        if (oo.status !in listOf(OptionStatus.EXECUTED, OptionStatus.SETTLED)) return
        if (oo.premiumFree == true) {
            historyRepository.existsByChainAndBuyerAndTxHashAndMethodAndAsset(
                oo.chain,
                oo.buyer!!,
                oo.txHash!!,
                TransactionMethod.PAY,
                HistoryToken.FREE_ORDER.name
            ).let { if (!it) {
                val o = orderRepository.findByIdOrNull(oo.orderId) ?: return
                historyRepository.save(History(
                    chain = oo.chain,
                    optionOrderId = oo.id!!,
                    buyer = oo.buyer!!,
                    method = TransactionMethod.PAY,
                    asset = HistoryToken.FREE_ORDER.name,
                    amount = o.premiumFee,
                    txHash = oo.txHash!!,
                    created = getTxTime(oo.txHash!!, service) ?: oo.created
                ))
            }}
        } else {
            val created = getTxTime(oo.txHash!!, service)
            // 保存权利金支付记录
            historyRepository.existsByChainAndBuyerAndTxHashAndMethodAndAsset(
                oo.chain,
                oo.buyer!!,
                oo.txHash!!,
                TransactionMethod.PAY,
                (oo.premiumAsset?.asset ?: return)
            ).let { if (!it && oo.premiumFeePay != null) {
                historyRepository.save(History(
                    chain = oo.chain,
                    optionOrderId = oo.id!!,
                    buyer = oo.buyer!!,
                    method = TransactionMethod.PAY,
                    asset = oo.premiumAsset!!.asset,
                    amount = oo.premiumFeePay!!,
                    txHash = oo.txHash!!,
                    created = created ?: oo.created
                ))
            }}

            // 保存Credit金支付记录
            historyRepository.existsByChainAndBuyerAndTxHashAndMethodAndAsset(
                oo.chain,
                oo.buyer!!,
                oo.txHash!!,
                TransactionMethod.PAY,
                HistoryToken.CREDITS.name
            ).let { if (!it) {
                // 检查logs是否有Trading Credits记录
                val creditAmount = try {
                    service.evmUtil.web3j.ethGetTransactionReceipt(oo.txHash!!).send().transactionReceipt.get().logs
                        .firstNotNullOfOrNull {
                            // todo Check if trading credits are paid
                            BigDecimal.ZERO
                        } ?: BigDecimal.ZERO
                } catch (e: Exception) {
                    BigDecimal.ZERO
                }
                if (creditAmount.compareTo(BigDecimal.ZERO) != 0) {
                    historyRepository.save(History(
                        chain = oo.chain,
                        optionOrderId = oo.id!!,
                        buyer = oo.buyer!!,
                        method = TransactionMethod.PAY,
                        asset = HistoryToken.CREDITS.name,
                        amount = creditAmount,
                        txHash = oo.txHash!!,
                        created = created ?: oo.created
                    ))
                }
            }}
        }
    }

    fun addSettlementProfit(oo: OptionOrder, service: EvmService) {
        if (oo.status != OptionStatus.SETTLED) return
        historyRepository.existsByChainAndBuyerAndTxHashAndMethodAndAsset(
            oo.chain,
            oo.buyer!!,
            oo.txHash!!,
            TransactionMethod.RECEIVE,
            (oo.premiumAsset?.asset ?: return)
        ).let { if (!it && oo.buyerProfit != null) {
            historyRepository.save(History(
                chain = oo.chain,
                optionOrderId = oo.id!!,
                buyer = oo.buyer!!,
                method = TransactionMethod.RECEIVE,
                asset = oo.premiumAsset!!.asset,
                amount = oo.buyerProfit!!,
                txHash = oo.txHash!!,
                created = getTxTime(oo.settlementHash!!, service) ?: oo.settlementTime ?: LocalDateTime.now()
            ))
        }}
    }
}