package io.vault.jasper.controller.auth

import io.swagger.annotations.ApiOperation
import io.vault.jasper.ApiResponse
import io.vault.jasper.annotation.ApiCache
import io.vault.jasper.model.UserPremiumSummary
import io.vault.jasper.repository.UserPointDailySummaryRepository
import io.vault.jasper.response.PageResponse
import io.vault.jasper.response.ReferralPremiumResponse
import io.vault.jasper.service.LeaderboardService
import io.vault.jasper.service.LeaderboardService.PremiumPointSummary
import io.vault.jasper.service.UserPremiumSummaryService
import io.vault.jasper.service.UserService
import io.vault.jasper.utils.AuthUtil
import io.vault.jasper.utils.DateTimeUtil
import org.slf4j.LoggerFactory
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.data.domain.PageImpl
import org.springframework.data.domain.PageRequest
import org.springframework.data.domain.Sort
import org.springframework.data.mongodb.core.MongoTemplate
import org.springframework.data.mongodb.core.query.Criteria
import org.springframework.data.mongodb.core.query.Query
import org.springframework.web.bind.annotation.*
import java.math.BigDecimal
import java.text.SimpleDateFormat
import java.time.format.DateTimeFormatter
import java.util.*
import javax.servlet.http.HttpServletRequest
import javax.validation.constraints.Min


@RestController
@RequestMapping("/leaderboard")
class LeaderboardController @Autowired constructor(
    private val authUtil: AuthUtil,
    private val leaderboardService: LeaderboardService,
) {

    private var logger = LoggerFactory.getLogger(this::class.java)

    private var leaderboardLimit = 50

    @ApiOperation("交易积分排行榜")
    @CrossOrigin
    @ApiCache(key = "leaderboard:trading_point_list", dynamicKey = ["days", "registerDays"], expire = 60)
    @GetMapping("/trading_point_list")
    fun tradingPointList(
        request: HttpServletRequest,
        days: Int? = null,
        registerDays: Int? = null
    ): ApiResponse<List<PremiumPointSummary>> {

        authUtil.filterIPBlacklist(request)

        val list = leaderboardService.aggregateAndSortByPremiumPoint(
            days,
            registerDays,
            leaderboardLimit.toLong()
        )

        return ApiResponse.success(list)
    }

    @ApiOperation("loyalty point 排行榜")
    @CrossOrigin
    @ApiCache(key = "leaderboard:loyalty_point_list", dynamicKey = ["days", "registerDays"], expire = 60)
    @GetMapping("/loyalty_point_list")
    fun loyaltyPointList(
        request: HttpServletRequest,
        days: Int? = null,
        registerDays: Int? = null
    ): ApiResponse<List<LeaderboardService.LoyaltyPointSummary>> {

        authUtil.filterIPBlacklist(request)

        val list = leaderboardService.aggregateAndSortByLoyaltyPoint(
            days,
            registerDays,
            leaderboardLimit.toLong()
        )

        return ApiResponse.success(list)
    }

    @ApiOperation("邀请排行榜")
    @CrossOrigin
    @ApiCache(key = "leaderboard:referral_list", dynamicKey = ["days", "registerDays"], expire = 60)
    @GetMapping("/referral_list")
    fun referralList(
        request: HttpServletRequest,
        days: Int? = null,
        registerDays: Int? = null
    ): ApiResponse<List<LeaderboardService.RefereeVolumeSummary>> {

        authUtil.filterIPBlacklist(request)

        val list = leaderboardService.aggregateAndSortByRefereeVolume(
            days,
            registerDays,
            leaderboardLimit.toLong()
        )

        return ApiResponse.success(list)
    }
}