package io.vault.jasper.service

import com.fasterxml.jackson.databind.JsonNode
import com.fasterxml.jackson.databind.ObjectMapper
import io.vault.jasper.blockchain.ArbitrumBlockchainUtil
import io.vault.jasper.enums.OptionDirection
import io.vault.jasper.enums.Symbol
import io.vault.jasper.model.KYT
import io.vault.jasper.model.UTMInfo
import io.vault.jasper.model.UserChannel
import io.vault.jasper.repository.KYTRepository
import org.slf4j.LoggerFactory
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.beans.factory.annotation.Value
import org.springframework.http.HttpEntity
import org.springframework.http.HttpHeaders
import org.springframework.http.HttpMethod
import org.springframework.stereotype.Service
import org.springframework.web.client.HttpClientErrorException
import org.springframework.web.client.RestTemplate
import org.web3j.crypto.Keys
import java.math.BigDecimal
import java.math.BigInteger

@Service
class MistTrackService @Autowired constructor(
    private val kytRepository: KYTRepository
){

    private val logger = LoggerFactory.getLogger(this::class.java)

    private val restTemplate = RestTemplate()

    private val objectMapper = ObjectMapper()

    private val mistTrackApiKeys = listOf(
        "4dxy7ihnZNHfR1cKmDPzSbueCV5k2ATQ",
        "RXTMoamOnWDyhQYtA4ELzxNKb6lCZueH",
        "LFHln3sAEyY1cBbwiqrZvpRx4IzuQt7X"
    )

    private val beosinAppId = "fd5cf2f60f63b1bf"
    private val beosinAppSecret = "07911d4a9ec0823436373534353934c7"

    private var currentKytKeyIndex = 0

    private val highRiskScore = 75

    fun isAddressHighRisk(
        address: String,
        channel: UserChannel? = null,
        ipAddress: String? = null,
        utmInfo: UTMInfo? = null
    ): Boolean {

        var kytScore = 0
        val checkSumAddress = Keys.toChecksumAddress(address)

        try {

            var kyt = kytRepository.findByAddressIgnoreCase(checkSumAddress)

            if(kyt != null){

                if(kyt.channel == null) {
                    kyt.channel = channel
                }

                if(kyt.utmInfo == null){
                    kyt.utmInfo = utmInfo
                }
            }

            if(kyt == null){
                kyt = KYT(
                    address = checkSumAddress,
                    score = 0,
                    channel = channel,
                    utmInfo = utmInfo
                )
            }

            kyt.ipAddress = ipAddress
            kytRepository.save(kyt)

            kytScore = kyt.score

        }catch (e: Exception){
            logger.error("Failed to get kyt score for $address", e)
        }

        return kytScore >= highRiskScore
    }

//    fun getKytScore(
//        address: String,
//        channel: UserChannel?,
//        coin: String,
//        readFromCache: Boolean = true
//    ): Int {
//
//        //Check if the address is in the KYT database
//        var kyt = kytRepository.findByAddressIgnoreCase(address)
//
//        if(kyt != null && kyt.channel == null){
//            kyt.channel = channel
//            kyt = kytRepository.save(kyt)
//        }
//
//        if(readFromCache) {
//            if (kyt != null) {
//                return kyt.score
//            }
//        } else {
//            // 数据库里面的记录已经是高风险，直接返回，不需要再请求慢雾
//            if(kyt != null && kyt.score >= highRiskScore){
//                return kyt.score
//            }
//        }
//
//        // If the address is not in the database, create a new record
//        if(kyt == null){
//            kyt = kytRepository.save(
//                KYT(
//                    address = address,
//                    score = 0,
//                    channel = channel
//                )
//            )
//        }
//
//        val score = getScoreFromMistTrack(address, coin)
//
//        // Cache the KYT score
//        kyt!!.score = score
//        kytRepository.save(kyt)
//
//        Thread.sleep(500) // Sleep for 0.5s
//        return score
//    }

    fun getScoreFromMistTrack(address: String, coin: String): Int {

        val apiKey = mistTrackApiKeys[currentKytKeyIndex]
        currentKytKeyIndex = (currentKytKeyIndex + 1) % mistTrackApiKeys.size

        val url = "https://openapi.misttrack.io/v1/risk_score?coin=$coin&address=$address&api_key=$apiKey"

        //logger.info("Requesting KYT score for $address url $url")
        val response = try {
            restTemplate.getForObject(url, String::class.java)
        } catch (e: HttpClientErrorException) {
            // logger.error(e.message, e)
            if (e.statusCode.value() == 404) {
                return 0
            }
            throw Exception("Failed to get kyt for $address")
        } catch (e: Exception) {
            logger.error(e.message, e)
            throw Exception("Failed to get kyt for $address")
        }

        logger.info("Response: $response")

        val responseNode = objectMapper.readTree(response)
        val score =
            responseNode.get("data")?.get("score")?.asInt() ?: throw Exception("Failed to get score for $address")

        return score
    }

    fun getScoreFromBeosin(
        address: String
    ): Int {

        val url = "https://api.beosin.com/api/v3/kyt/address/risk?chainId=42161&address=$address"

        val headers = HttpHeaders()
        headers.add("APPID", beosinAppId)
        headers.add("APP-SECRET", beosinAppSecret)

        val entity = HttpEntity(null, headers)

        logger.info("Requesting KYT score for $address url $url")
        val response = try {
            restTemplate.exchange(url, HttpMethod.GET, entity, String::class.java)
        } catch (e: HttpClientErrorException) {
            // logger.error(e.message, e)
            if (e.statusCode.value() == 404) {
                return 0
            }
            throw Exception("Failed to get kyt for $address")
        } catch (e: Exception) {
            logger.error(e.message, e)
            throw Exception("Failed to get kyt for $address")
        }

        logger.info("Response: $response")

        val responseNode = objectMapper.readTree(response.body)
        val score =
            responseNode.get("data")?.get("score")?.asInt() ?: throw Exception("Failed to get score for $address")

        return score
    }
}