package io.vault.jasper.model

import org.springframework.data.annotation.CreatedDate
import org.springframework.data.annotation.Id
import org.springframework.data.annotation.LastModifiedDate
import org.springframework.data.mongodb.core.index.Indexed
import org.springframework.data.mongodb.core.mapping.Document
import org.springframework.data.mongodb.core.mapping.Field
import org.springframework.data.mongodb.core.mapping.FieldType
import java.math.BigDecimal
import java.time.LocalDateTime

@Document("user_network_team_rebate_daily_summary")
data class UserNetworkTeamRebateDailySummary(
    @Id
    val id: String? = null,
    @Field("date_string")
    val dateString: String,
    @Indexed(background = true)
    @Field("user_id")
    val userId: String,
    @Indexed(background = true)
    @Field("user_network_id")
    val userNetworkId: String,
    @Field("invited_network_id")
    @Indexed(background = true)
    var invitedNetworkId: String,
    @Indexed(background = true)
    @Field("user_address")
    val userAddress: String,
    @Field("parent_address")
    val parentAddress: String,
    @Field("to_parent_team_premium",targetType = FieldType.DECIMAL128)
    var toParentTeamPremium: BigDecimal,
    @Field("to_parent_team_rebate",targetType = FieldType.DECIMAL128)
    var toParentTeamRebate: BigDecimal,
    @CreatedDate
    val created: LocalDateTime = LocalDateTime.now(),
    @LastModifiedDate
    val updated: LocalDateTime = LocalDateTime.now()
) {}