package io.vault.jasper.controller.pub

import com.fasterxml.jackson.databind.ObjectMapper
import com.fasterxml.jackson.databind.node.ArrayNode
import io.swagger.annotations.ApiOperation
import io.swagger.v3.oas.annotations.Operation
import io.vault.jasper.ApiResponse
import io.vault.jasper.blockchain.BlockchainUtilFactory
import io.vault.jasper.enums.ChainType
import io.vault.jasper.enums.Symbol
import io.vault.jasper.exception.BusinessException
import io.vault.jasper.exception.ParticleException
import io.vault.jasper.exception.ResultEnum
import io.vault.jasper.model.OptionOrder
import io.vault.jasper.model.OptionStatus
import io.vault.jasper.model.SafepalTaskRecord
import io.vault.jasper.model.UserPremiumSummary
import io.vault.jasper.repository.*
import io.vault.jasper.response.PageResponse
import io.vault.jasper.response.TaskOnVerificationResponse
import io.vault.jasper.response.TransactionHistoryResponse
import io.vault.jasper.service.BlockchainService
import io.vault.jasper.service.JasperVaultService
import io.vault.jasper.service.OptionOrderService
import io.vault.jasper.service.SystemService
import io.vault.jasper.utils.AuthUtil
import io.vault.jasper.utils.BlockchainUtil
import io.vault.jasper.utils.DateTimeUtil
import org.slf4j.LoggerFactory
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.context.annotation.Profile
import org.springframework.data.domain.PageImpl
import org.springframework.data.domain.PageRequest
import org.springframework.data.domain.Sort
import org.springframework.data.mongodb.core.MongoTemplate
import org.springframework.data.mongodb.core.query.Criteria
import org.springframework.data.mongodb.core.query.Query
import org.springframework.data.repository.findByIdOrNull
import org.springframework.http.ResponseEntity
import org.springframework.web.bind.annotation.*
import org.springframework.web.client.RestTemplate
import org.web3j.abi.TypeReference
import org.web3j.abi.datatypes.Address
import org.web3j.abi.datatypes.Type
import org.web3j.abi.datatypes.generated.Uint256
import java.math.BigDecimal
import java.math.BigInteger
import java.util.*
import javax.servlet.http.HttpServletRequest
import javax.servlet.http.HttpServletResponse


@RestController
@RequestMapping("/particle")
class ParticleController @Autowired constructor(
    private val authUtil: AuthUtil
) {

    private var logger = LoggerFactory.getLogger(this::class.java)

    private val jasperContractWhiteList = listOf(
        "0x5FF137D4b0FDCD49DcA30c7CF57E578a026d2789", //"EntryPoint":
        "0x1a2536E1DD062466C20bdAFd5DF5d6e0e2F4FCd3", //"DiamondCutFacet":
        "0xfE1718b86BfA03Bd52d8e31F8F6ea37F44eD8E44", // "DiamondLoupeFacet":
        "0xf6b3299a9E2be4eD3859Eb9B3DF9831FBC45261e", // "OwnershipFacet":
        "0x32CE600C20Ce6C6f68F4F81B547c4edcAB11840d", //"Diamond":
        "0x34328baf6BC36609D10944a5B6Cd4b580CE59D79", // "PlatformFacet":
        "0xc8bf340180C72bf17E3002e252A35B0BEB7cACa5", //"VaultFacet":
        "0x8e81aC9afC2a158a7550Ec616A999b55A65E18A2", // "OptionFacet":
        "0x90d7b52487997Dca7e4B4F991BA66404321D7e3e", // "OptionFacetV2":
        "0xfce8C16411d205D87B993E18308ee9BF59874e39", // "PaymasterFacet":
        "0x61Ac9e947918F1da8B4103C9F89E084Fe491d3d1", // "IssuanceFacet":
        "0x9F781FDa6Cdf7a339aF514d0F5E16f6b2b2E5836", // "VaultPaymaster":
        "0xBea48CA3AaC57D570a9054CC374D2F01c2Bc48Ed", // "VaultManageModule":
        "0x88EEd20e8a5Fb4bE8A4B53CDeeFC67BB91047f2D", // "TradeModule":
        "0xe7D79A3a17730f340Cc594868a55B80F4c0c3138", // "IssuanceModule":
        "0xeF0Abec11456846964D59141FD6961cC5F7AD010", // "Manager":
        "0x8690759b04916466B20C5b67d6ccF9743B3f99de", // "PriceOracle":
        "0xdaC0bE32467348ea71654d2A5aAe7793d065191f", // "OptionService":
        "0xE632D208e2F1561414E4a8442f8e20614Faa6CE6", // "OptionModule":
        "0xEcb1233E463Aa6cf6Ac0970D5643935A929fFad9", // "OptionModuleV2":
        "0x1B44c8d71DFC49e42e224c6D4EB7FDe00498dd3f", // "UniswapV2ExchangeAdapter":
        "0x94d0d676dDF1eFf0f234F5Dfe7371100acF2BC8C", // "UniswapV3ExchangeAdapter":
        "0xe1099a130A41B7a45Bc0e107714f98b18Fded40B", // "PythOracleAdapter":
        "0x6C7e7176e594eD05b506930bd2e06370E8f5cb70", // "ChainLinkOracleAdapter":
        "0xc8d8A3BdEEEdE5D03e5D80c8C82aF163C9Af923a", // "TradingCreditPool":
        "0x4D2c31293A1Ca7a411fF5EaD6B272F96b1fDB860", // "JVTB":
        "0xbfc2bce6c3e071986927264F889B07dD6a2D8dE2", // "NFTFreeOptionPool":
        "0x7F14d635dcC41AE486B1a7c07C3d38705F0D1eFE", // "VaultFactoryV2":
        "0x7F14d635dcC41AE486B1a7c07C3d38705F0D1eFE", // "VaultFactory":
        "0x5d23B93D82010535B8d6E9bCdDE45b2E8e6a8E12", // "JSBT"
        "0xF2a7CDcaf85F1C56a2C99D5907db1d8BCB0C5aDd", // Mint NFT
        "0xD8462650Dd1EcA4885Cb3dE1ca171d87ac9fdC6b", // Swap Point Service
        "0xfBB60762a576Ab2FbAFfbe14c735273BB0d91399", // NFT Token
        "0x65eC8711814A1b2B48F4fDdbf2C391c3d7e05764", // "OptionModuleV4":
    )

    private val arbitrumContractWhiteList = listOf(
        "0x5FF137D4b0FDCD49DcA30c7CF57E578a026d2789", //"EntryPoint"
        "0x462482E07D7b2AfFAD9d934544bfFB24Dbcb019A", //"DiamondCutFacet"
        "0x0E0C448Ddf771AefBFf1e4AA62978E8EDC8FE40F", // "DiamondLoupeFacet"
        "0x2837e9124C53046857172bd517b5557fA3B1Fa97", // "OwnershipFacet"
        "0x5FfDD96bD604f915520d66C9edDd46dFc1434d71", // "Diamond"
        "0x6974577C1bDFc31Af5c511ed2A3C9f8d4f9a0Bf8", // "PlatformFacet"
        "0x8113F1988d18B3785A4d8Cda0E33A8BCa390cd0f", // "VaultFacet"
        "0x337dA5BF466d3cAd7aAdf775482CEff4B693bB57", // "OptionFacet"
        "0xab0079C4D518B6Edc212C2a2adA120c2ADdaF6d1", // "OptionFacetV2"
        "0xe10a54788e39E8Cd9c275Bff5c6A941D48F5d9C7", // "PaymasterFacet"
        "0xa5Db2700E2CC1E007d9F50261ecb04339d712E3A", // "IssuanceFacet"
        "0x5abBc8a4BB1B2a93Bee73f14292c59d47C0012Ab", // "VaultPaymaster"
        "0x9F408f6c9e52c091549F1550e31e8B5621795bE6", // "VaultManageModule"
        "0xacEB04e585842F79a596D9866F9F27378fb73b98", // "TradeModule"
        "0xAed71826eDbf82897c8d41A898ECd7D8F2c13044", // "IssuanceModule"
        "0xAE5eB39df913D14EE5B102C16f4EE38Bc0369da0", // "UniswapV3ExchangeAdapter":
        "0xf6C9Db55D428f2F7aCC72d959AcB40a282B9eFD3", // "VaultFactory"
        "0x2c462313B8ea0cc6EC96C4A05Cd14AE24cf971E7", // "LeverageFacet"
        "0xB593be092508589bD7a48BfEeEF790Cce1Ef8993", // "LeverageModule"
        "0x35Ce6751A37c8E9b70ec6F7ce106Eda44C36C1E7", // "ChainLinkOracleAdapter"
        "0x17C9bbBF495FEFb45D3Af9EB05D8F3B1E0214126", // "PythOracleAdapter":
        "0x60E974258eFCCEf5E7B9D579F47F600aC0a7064C", // "PriceOracle"
        "0x9817acDA4B20cd5dbFb9b9505027f343884cd1fd", // "IssuanceFacet":
        "0x653069B85aFdd9BadC81b40c1f3e055650419BDf", // "BusinessFacet":
        "0xA74F863f5Ba44FEC59423E0Ed6282F6A998BC50c", // "BusinessService"
        "0xE0488Bc177a85BbC58aB8439C7B73Ca459789159", // "OptionFacet":
        "0x720a7b1A11B1A14b3E7E12811DfF2cF382997892", // "OptionService":
        "0x9545bd54A0B1Cf4F56290AA823367346A51E0B74", // "ERC20TOKEN":
        "0x835d6Fe77fa1437f951F6F95b2B7524Aa0B8c2e6", // "OptionModule":
        "0x7d36Ffa0192cb2f8e7cac438895F16e18c154b35", // "NFTFreeOptionPool":
        "0x2f2Cb9dfCF09D8Ae6e6290c43eFA83B45F931B88", // "JVTB":
        "0xCFE9340CF648Ff7623e6c7B1C7fE2f902F390612", // "OptionModuleV2":
        "0x2d6DC9A15079C11a277f6871897fa90D19Ff66d6", // "OptionFacetV2":
        "0x882d3F58c41C1Fa1a2dF86A5FA46E512F50685CF", // "TradingCreditPool":
        "0x205cD1f0E4e14Ee32e273dbfc6837eb8dE553eDe", // "AirdropTradeClaim":
        "0xEE09D1f76bdDcd79e95787aCae581CBbEE2F01AA", // "BitlayerTradeClaim":
        "0x819257ff4e9C6499701FEcE0021d1D2944476A31", // "AirdropArbClaim":
        "0x0408a702dC6a0Ace0aee72eDdA18d678c57358bf", // "OptionModuleV2Handle":
        "0xdffB4320F24b21839e88DE4CA1267698e58D9c75", // "OptionLiquidateService":
        "0xC7Ed6C3ee23165b27bc4fcbcB727fB2004230d7c", // "OptionLiquidateHelper":
        "0xAb473B3289E297D53e4b63FdBCb46e9381dD68F5", // "Reader":
        "0x6E5aD859877aEb3ffC984A91D90bf7281C14A41F", // "EchoooTradeClaim":
        "0xa9f9398D3357F544b17Faa30FeB100B52DA9Bbdf", // "JSBT"
        "0xD364261EB9ee191faD71f635896328194EA7a488", // "OptionModuleV4":
    )

    private val objectMapper = ObjectMapper()

    private val particleProjectId = "47ad2d9c-2271-4c27-a4e1-6856db7ffc76"

    private val particleArbitrumProjectId = "923331c8-dbfe-4ca3-93f8-8c7eb46baf6a"

    @ApiOperation("Hook After Sign")
    @CrossOrigin
    @PostMapping("/hook-after-paymaster-sign")
    fun afterSign(
        request: HttpServletRequest,
        @RequestBody postBody: String
    ): String {

        authUtil.filterIPBlacklist(request)
        return "true"
    }

    @ApiOperation("Hook Before Sign")
    @CrossOrigin
    @PostMapping("/hook-before-paymaster-sign")
    fun beforeSign(
        request: HttpServletRequest,
        @RequestBody postBody: String
    ): String {

        authUtil.filterIPBlacklist(request)

        logger.info("Hook before paymaster Sign: $postBody")

        var result = "true"
        try {
            val jsonNode = objectMapper.readTree(postBody)
            val projectId = jsonNode["projectUuid"].asText()
            val chainId = jsonNode["chainId"].asText()

            var verifyProjectId = particleProjectId
            if(chainId == "42161"){
                verifyProjectId = particleArbitrumProjectId
            }

            var verifyContractList = jasperContractWhiteList
            if(chainId == "42161"){
                verifyContractList = arbitrumContractWhiteList
            }

            logger.info("Hook before paymaster Sign chain id: $chainId")

            if(!projectId.equals(verifyProjectId)){
                result = "false"
                logger.info("Hook before paymaster Sign Project ID not match $projectId")
            }

            val txs = jsonNode["parsed"]["txs"] as ArrayNode
            
            for(tx in txs){
                val to = tx["to"].asText()
                if(verifyContractList.contains(to)){
                    continue
                }

                logger.info("Hook before paymaster Sign Contract Not White List $to")
                result = "false"
                break
            }

        } catch (e: Exception) {
            logger.error("Hook before paymaster Sign error: $e")
        }

        if(result == "false"){
            logger.info("Hook before paymaster Sign response : $result")
            throw ParticleException()
        }

        return result
    }

    @ApiOperation("Test Webhook")
    @CrossOrigin
    @PostMapping("/test-webhook")
    fun testWebHook(
        request: HttpServletRequest,
        @RequestBody postBody: String
    ) {
        throw ParticleException()
    }
}