package io.vault.jasper.model

import org.springframework.data.annotation.CreatedDate
import org.springframework.data.annotation.Id
import org.springframework.data.annotation.LastModifiedDate
import org.springframework.data.mongodb.core.index.Indexed
import org.springframework.data.mongodb.core.mapping.Document
import org.springframework.data.mongodb.core.mapping.Field
import org.springframework.data.mongodb.core.mapping.FieldType
import java.math.BigDecimal
import java.time.LocalDateTime

@Document("user_network_invite_code")
data class UserNetworkInviteCode(
    @Id
    val id: String? = null,
    @Indexed(background = true)
    val address: String, // 大小写敏感
    @Indexed(background = true)
    @Field("user_network_id")
    val userNetworkId: String,
    @Indexed(unique = true)
    @Field("invite_code")
    val inviteCode: String,
    @Indexed(background = true)
    var level: Int = 0,
    @Indexed(background = true)
    var grade: UserNetworkGrade = UserNetworkGrade.USER,
    @Field("protocol_fee_percentage", targetType = FieldType.DECIMAL128)
    var protocolFeePercentage: BigDecimal = BigDecimal.ZERO,
    var tag: String? = null,
    @CreatedDate
    val created: LocalDateTime = LocalDateTime.now(),
    @LastModifiedDate
    val updated: LocalDateTime = LocalDateTime.now(),
    @Field("bind_address")
    var bindAddress: String? = null,
    @Field("bind_user_network_id")
    var bindUserNetworkId: String? = null,
    @Field("bind_time")
    var bindTime: LocalDateTime? = null
)