package io.vault.jasper.repository

import io.vault.jasper.model.Activity
import io.vault.jasper.model.AirDropType
import io.vault.jasper.model.AirdropTransaction
import io.vault.jasper.model.Chain
import org.springframework.data.mongodb.repository.MongoRepository
import org.springframework.stereotype.Repository

@Repository
interface AirDropTransactionRepository : MongoRepository<AirdropTransaction, String> {

    fun findByUserIdAndType(
        userId: String,
        type: AirDropType
    ): List<AirdropTransaction>

    fun findByUserIdAndReferralUserIdAndType(
        userId: String,
        referralId: String,
        type: AirDropType
    ): List<AirdropTransaction>

    fun findByType(
        type: AirDropType
    ): List<AirdropTransaction>

    fun findByUserIdAndTypeAndHasClaimedFreeTrade(
        userId: String,
        type: AirDropType,
        hasClaimed: Boolean
    ): List<AirdropTransaction>

    fun countByReferralAddressIgnoreCaseAndType(
        referralAddress: String,
        type: AirDropType
    ): Long
}