package io.vault.jasper.service


import io.vault.jasper.enums.ChainType
import io.vault.jasper.model.OptionOrder
import io.vault.jasper.model.OptionStatus
import io.vault.jasper.model.User
import io.vault.jasper.model.UserPremiumSummary
import io.vault.jasper.repository.UserPremiumSummaryRepository
import io.vault.jasper.repository.UserRepository
import org.slf4j.LoggerFactory
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.data.mongodb.core.MongoTemplate
import org.springframework.data.mongodb.core.aggregation.Aggregation
import org.springframework.data.mongodb.core.aggregation.ConvertOperators
import org.springframework.data.mongodb.core.query.Criteria
import org.springframework.stereotype.Service
import java.math.BigDecimal

@Service
class UserPremiumSummaryService @Autowired constructor(
    private val userPremiumSummaryRepository: UserPremiumSummaryRepository,
    private val optionOrderService: OptionOrderService,
    private val mongoTemplate: MongoTemplate
){

    private val logger = LoggerFactory.getLogger(this::class.java)

    fun updateUserPremiumSummary(user: User) {
        val userPremiumSummary = userPremiumSummaryRepository.findByUserId(user.id!!) ?: userPremiumSummaryRepository.save(
            UserPremiumSummary(
                userId = user.id,
                evmAddress = user.address,
                registerTime = user.created,
                totalPremium = BigDecimal.ZERO
            )
        )

        var totalPremium = BigDecimal.ZERO
        var totalVolume = BigDecimal.ZERO
        ChainType.values().forEach { chainType ->
            val chainPremium = optionOrderService.getUserOptionPremiumTotal(
                listOf(user.address),
                chainType
            )

            val chainVolume = optionOrderService.getUserOptionVolumeTotal(
                listOf(user.address),
                chainType
            )

            logger.debug("User Premium Summary Service: ${user.address} chain $chainType premium $chainPremium volume $chainVolume")

            totalPremium = totalPremium.add(chainPremium)
            totalVolume = totalVolume.add(chainVolume)
        }

        userPremiumSummary.totalPremium = totalPremium
        userPremiumSummary.totalVolume = totalVolume
        userPremiumSummaryRepository.save(userPremiumSummary)
    }

    fun getUserPremiumSummaryTotal(
        userAddresses: List<String>,
        fieldName: String
    ): BigDecimal {

        val criteria = Criteria.where(UserPremiumSummary::evmAddress.name).`in`(userAddresses)

        val agg = Aggregation.newAggregation(
            Aggregation.match(criteria),
            Aggregation.group().sum(
                ConvertOperators.valueOf(
                    fieldName
                ).convertToDecimal()).`as`("total")
        )
        val groupResults = mongoTemplate.aggregate(agg, UserPremiumSummary::class.java, Map::class.java)
        val result = groupResults.mappedResults.firstOrNull()
        val total = (result?.get("total")?:"0").toString()

        return BigDecimal(total)
    }
}