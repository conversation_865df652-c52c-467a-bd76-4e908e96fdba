package io.vault.jasper.repository

import io.vault.jasper.model.UserPointDailySummary
import io.vault.jasper.model.UserReferralDailySummary
import org.springframework.data.mongodb.repository.MongoRepository
import org.springframework.stereotype.Repository
import java.math.BigDecimal

@Repository
interface UserPointDailySummaryRepository : MongoRepository<UserPointDailySummary, String> {

    fun findByDateString(
        dateString: String
    ): List<UserPointDailySummary>

    fun findByAddressAndDateString(
        address: String,
        date: String
    ): UserPointDailySummary?

    fun findByAddressAndDateStringGreaterThanEqual(
        address: String,
        date: String
    ): List<UserPointDailySummary>

    fun findByPremiumPointIsNot(
        premiumPoint: BigDecimal
    ): List<UserPointDailySummary>

    fun findByLoyaltyPoint(
        loyaltyPoint: BigDecimal
    ): List<UserPointDailySummary>

    fun findTopByAddressOrderByStreakDesc(
        address: String
    ): UserPointDailySummary?

    fun findByAddress(
        address: String
    ): List<UserPointDailySummary>

    fun findByDateStringAndCoin98StreakIsNull(
        dateString: String
    ): List<UserPointDailySummary>

    fun findByCoin98StreakGreaterThan(
        coin98Streak: Int
    ): List<UserPointDailySummary>

    fun findByFixPremiumIsNull(): List<UserPointDailySummary>
}