package io.vault.jasper.model.activity

import org.springframework.data.annotation.CreatedDate
import org.springframework.data.annotation.Id
import org.springframework.data.annotation.LastModifiedDate
import org.springframework.data.mongodb.core.index.Indexed
import org.springframework.data.mongodb.core.mapping.Document
import org.springframework.data.mongodb.core.mapping.Field
import org.springframework.data.mongodb.core.mapping.FieldType
import java.math.BigDecimal
import java.time.LocalDateTime

/**
 * CampaignReward model to define rewards for completing tasks in a campaign
 */
@Document("campaign_rewards")
data class CampaignReward(
    @Id
    val id: String? = null,

    @Field("campaign_id")
    @Indexed(background = true)
    val campaignId: String,

    @Field("reward_type")
    val rewardType: RewardType,

    @Field("reward_config")
    val rewardConfig: Map<String, Any>,

    @Field("total_reward_amount", targetType = FieldType.DECIMAL128)
    val totalRewardAmount: BigDecimal,

    @Field("distributed_amount", targetType = FieldType.DECIMAL128)
    var distributedAmount: BigDecimal = BigDecimal.ZERO,

    @CreatedDate
    val created: LocalDateTime = LocalDateTime.now(),

    @LastModifiedDate
    val updated: LocalDateTime = LocalDateTime.now(),

    @Field("i18n_info")
    var i18nInfo: MutableMap<String, I18nCampaignRewardInfo>? = null
)

/**
 * Enum representing different types of rewards
 */
enum class RewardType {
    ORDER_LOSS_COMPENSATION
}

/**
 * Constants for reward configuration parameters
 */
object RewardConfigParams {
    // ORDER_LOSS_COMPENSATION
    const val SMART_CONTRACT_ADDRESS = "smartContractAddress"
    const val GEN_REWARD_RECORD_FOR_ZERO_REWARD = "genRewardRecordForZeroReward" // 无论是否拿到实际奖励都算入奖励数
}

data class I18nCampaignRewardInfo(
    val title: String,
    val description: String
)
