package io.vault.jasper.repository


import io.vault.jasper.enums.ChainType
import io.vault.jasper.model.LPVault
import io.vault.jasper.model.LPVaultStatus
import org.springframework.data.mongodb.repository.MongoRepository
import org.springframework.stereotype.Repository

@Repository
interface LPVaultRepository : MongoRepository<LPVault, String> {
    fun findByLpEoaAddressIgnoreCase(lpEoaAddress: String): List<LPVault>
    fun findByLpEoaAddressIgnoreCaseAndStatus(
        lpEoaAddress: String,
        status: LPVaultStatus
    ): List<LPVault>
    fun findByLpEoaAddressIgnoreCaseAndStatusAndChain(
        lpEoaAddress: String,
        status: LPVaultStatus,
        chain: ChainType
    ): List<LPVault>

    fun findByStatus(status: LPVaultStatus): List<LPVault>
    fun findByLpEoaAddressIgnoreCaseAndLpVaultAddressIgnoreCase(
        lpEoaAddress: String,
        lpVaultAddress: String
    ): List<LPVault>

    fun findByLpEoaAddressIgnoreCaseAndLpVaultAddressIgnoreCaseAndStatus(
        lpEoaAddress: String,
        lpVaultAddress: String,
        status: LPVaultStatus
    ): List<LPVault>
}