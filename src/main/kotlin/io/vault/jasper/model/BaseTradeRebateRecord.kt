package io.vault.jasper.model

import io.vault.jasper.enums.OptionDirection
import io.vault.jasper.enums.Symbol
import org.springframework.data.annotation.CreatedDate
import org.springframework.data.annotation.Id
import org.springframework.data.annotation.LastModifiedDate
import org.springframework.data.mongodb.core.index.Indexed
import org.springframework.data.mongodb.core.mapping.Document
import org.springframework.data.mongodb.core.mapping.Field
import java.math.BigDecimal
import java.math.BigInteger
import java.time.LocalDateTime

@Document("base_trade_rebate_record")
data class BaseTradeRebateRecord(
    @Id
    val id: String? = null,
    @Field("option_order_id")
    @Indexed(unique = true, background = true)
    val optionOrderId: String,
    @Field("buyer_address")
    @Indexed(background = true)
    val buyerAddress: String,
    var direction: OptionDirection,
    @Field("retweet_base_time")
    var retweetBaseTime: Long = 0L,
    @Field("bid_amount")
    var bidAmount: BigDecimal = BigDecimal("0.01"),
    @Field("bid_asset")
    var bidAsset: Symbol = Symbol.CBBTC,
    @Field("strike_price")
    var strikePrice: BigDecimal = BigDecimal.ZERO,
    @Field("settlement_price")
    var settlementPrice: BigDecimal = BigDecimal.ZERO,
    @Field("premium_fee")
    var premiumFee: BigDecimal = BigDecimal.ZERO,
    @Field("premium_asset")
    var premiumAsset: Symbol = Symbol.USDT,
    @Field("premium_fee_in_btc")
    var premiumFeeInBtc: BigDecimal = BigDecimal.ZERO,
    @Field("premium_fee_in_usdt")
    var premiumFeeInUsdt: BigDecimal = BigDecimal.ZERO,
    var profit: BigDecimal = BigDecimal.ZERO,
    @Field("profit_asset")
    var profitAsset: Symbol = Symbol.USDT,
    @Field("profit_in_btc")
    var profitInBtc: BigDecimal = BigDecimal.ZERO,
    @Field("profit_in_usdt")
    var profitInUsdt: BigDecimal = BigDecimal.ZERO,
    @Field("net_profit")
    var netProfit: BigDecimal = BigDecimal.ZERO,
    @Field("rebate_amount")
    var rebateAmount: BigDecimal = BigDecimal.ZERO,
    @Field("discord_level")
    var discordLevel: Int = 0,
    @Field("discord_join_time")
    var discordJoinTime: LocalDateTime? = null,
    @Field("has_copy_address_to_discord")
    var hasCopyAddressToDiscord: Boolean = false,
    var status: BaseTradeRebateRecordStatus = BaseTradeRebateRecordStatus.EXECUTED,
    @Field("settle_tx_id")
    var settleTxId: String? = null,
    @CreatedDate
    val created: LocalDateTime = LocalDateTime.now(),
    @LastModifiedDate
    val updated: LocalDateTime = LocalDateTime.now(),
    var errorMsg: String? = null
) {}

enum class BaseTradeRebateRecordStatus {
     EXECUTED, CREATED, CLAIMED, PENDING, SETTLED, SETTLE_FAILED
}