package io.vault.jasper.dto

import EVMAddress
import io.swagger.annotations.ApiModel
import io.swagger.annotations.ApiModelProperty
import javax.validation.constraints.NotEmpty

@ApiModel("KOL申请DTO")
data class KolApplyDTO (

    @ApiModelProperty("Twitter/X链接", required = true)
    @field: NotEmpty
    val twitterHandle: String,

    @ApiModelProperty("Discord ID", required = true)
    @field: NotEmpty
    val discordId: String,

    @ApiModelProperty("Wallet Address")
    @field: NotEmpty
    @field:EVMAddress
    val walletAddress: String,

    @ApiModelProperty("如何了解到JV")
    @field: NotEmpty
    val whatDoYouKnowAboutJasperVault: String
)