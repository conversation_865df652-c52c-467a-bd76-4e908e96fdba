package io.vault.jasper.model

import io.vault.jasper.enums.ChainType
import org.springframework.data.annotation.Id
import org.springframework.data.mongodb.core.mapping.Document
import org.springframework.data.mongodb.core.mapping.Field
import java.math.BigDecimal
import java.time.LocalDateTime

@Document("echooo_campaign_parameter")
data class EchoooCampaignParameter(
    @Id
    val id: String? = null,
    @Field("start_date")
    var startDate: LocalDateTime = LocalDateTime.now(),
    @Field("end_date")
    var endDate: LocalDateTime = LocalDateTime.of(2025, 12, 30, 0, 0, 0),
    @Field("trade_rebate_chain")
    var tradeRebateChain: ChainType = ChainType.ARBITRUM,
    @Field("trade_rebate_contract_address")
    var tradeRebateContractAddress: String = "0x6E5aD859877aEb3ffC984A91D90bf7281C14A41F",
    @Field("first_trade_rebate_task_switch")
    var firstTradeRebateTaskSwitch: Boolean = true, // 推送First Trade Rebate任务的开关
    @Field("white_list_task_switch")
    var whiteListTaskSwitch: Boolean = false, // 推送First Trade Rebate任务的开关
    @Field("first_trade_rebate_count")
    var firstTradeRebateCount: Int = 10000,
    @Field("rebate_claim_time")
    var rebateClaimTime: Long = 900, // 15分钟
    @Field("first_trade_discord_level")
    var firstTradeDiscordLevel: Int = 1,
    @Field("first_trade_degen_quantity")
    var firstTradeDegenQuantity: BigDecimal = BigDecimal("0.2"),
    @Field("twitter_echooo_link")
    var twitterEchoooLink: String = "https://x.com/echooo_wallet",
    @Field("twitter_jasper_link")
    var twitterJasperLink: String = "https://x.com/jaspervault",
    @Field("retweet_link")
    var retweetLink: String = "https://x.com/jaspervault/status/1840701273026560148",
    @Field("increase_records_count")
    var increaseRecordsCount: Int = 10,
    @Field("increase_records_interval")
    var increaseRecordsInterval: Long = 3600,  // In seconds
    @Field("last_update_record_count_time")
    var lastUpdateRecordCountTime: Long = 0L,
)