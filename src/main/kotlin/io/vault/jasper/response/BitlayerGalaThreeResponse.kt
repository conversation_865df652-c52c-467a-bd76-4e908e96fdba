package io.vault.jasper.response

import io.swagger.annotations.ApiModel
import io.swagger.annotations.ApiModelProperty
import io.vault.jasper.enums.OptionDirection
import io.vault.jasper.enums.Symbol
import io.vault.jasper.model.OptionStatus
import org.web3j.abi.datatypes.Bool

@ApiModel
data class BitlayerGalaThreeResponse(

    @ApiModelProperty("订单列表", required = true)
    val list: List<BitlayerGalaThreeOrderResponse>,

    @ApiModelProperty("订单总数", required = true)
    val total: Int,
)

data class BitlayerGalaThreeOrderResponse(

    @ApiModelProperty("订单唯一标识", required = true)
    val uuid: String,

    @ApiModelProperty("订单hash", required = true)
    val tx_hash: String,

    @ApiModelProperty("任务完成时间", required = true)
    val finished_at: Long,
)
