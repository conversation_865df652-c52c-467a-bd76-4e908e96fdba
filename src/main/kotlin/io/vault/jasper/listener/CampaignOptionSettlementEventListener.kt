package io.vault.jasper.listener

import io.vault.jasper.event.OptionExecutedEvent
import io.vault.jasper.event.OptionSettlementSuccessEvent
import io.vault.jasper.repository.OptionOrderRepository
import io.vault.jasper.service.UserService
import io.vault.jasper.service.activity.CampaignIntegrationService
import org.slf4j.LoggerFactory
import org.springframework.context.event.EventListener
import org.springframework.data.repository.findByIdOrNull
import org.springframework.stereotype.Component

/**
 * Event listener for option settlement events to integrate with the campaign system
 */
@Component
class CampaignOptionSettlementEventListener(
    private val optionOrderRepository: OptionOrderRepository,
    private val campaignIntegrationService: CampaignIntegrationService,
    private val userService: UserService
) {
    private val logger = LoggerFactory.getLogger(this::class.java)
    
    /**
     * Handle option executed events
     * This is a backup in case the settlement success event is not fired
     */
    @EventListener
    fun handleOptionExecutedEvent(event: OptionExecutedEvent) {
        logger.info("Processing option executed event for order: ${event.orderId}")

        try {
            // Get the option order
            val optionOrder = optionOrderRepository.findByIdOrNull(event.orderId)
            
            if (optionOrder == null) {
                logger.warn("Option order not found for ID: ${event.orderId}")
                return
            }
            
            // Process the option order for activities and rewards
            campaignIntegrationService.processOptionOrder(optionOrder)
        } catch (e: Exception) {
            logger.error("Error processing option executed event for order: ${event.orderId}", e)
        }
    }
}
