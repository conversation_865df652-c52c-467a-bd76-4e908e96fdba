package io.vault.jasper.response

import io.swagger.annotations.ApiModel
import io.swagger.annotations.ApiModelProperty
import io.vault.jasper.enums.ChainType
import io.vault.jasper.model.KolStatus

@ApiModel
data class UserPointListResponse(

    @ApiModelProperty("address", required = true)
    val address: String,

    @ApiModelProperty("时间", required = true)
    val dateString: String? = null,

    @ApiModelProperty("用户交易积分", required = true)
    val point: String? = null,

    @ApiModelProperty("用户 Loyalty 积分", required = true)
    val loyaltyPoint: String? = null,

    @ApiModelProperty("用户权利金", required = true)
    val premiumAndFee: String? = null,

    @ApiModelProperty("创建时间", required = true)
    val created: Long? = null,

    @ApiModelProperty("持续交易时间", required = true)
    val streak: Int? = null
)
