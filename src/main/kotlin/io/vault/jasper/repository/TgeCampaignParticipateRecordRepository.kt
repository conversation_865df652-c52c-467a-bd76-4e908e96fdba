package io.vault.jasper.repository

import io.vault.jasper.enums.ChainType
import io.vault.jasper.model.BtrCampaignRecord
import io.vault.jasper.model.TgeCampaignParticipateRecord
import io.vault.jasper.model.TgeCampaignParticipateRecordStatus
import io.vault.jasper.model.TgeCampaignRecord
import org.springframework.data.mongodb.repository.MongoRepository
import org.springframework.stereotype.Repository

@Repository
interface TgeCampaignParticipateRecordRepository : MongoRepository<TgeCampaignParticipateRecord, String> {
    fun countByAddressIgnoreCase(address: String): Long
    fun countByAddressIgnoreCaseAndChain(
        address: String,
        chain: ChainType
    ): Long
    fun existsByOptionOrderId(optionOrderId: String): Boolean
    fun findByAddressIgnoreCase(address: String): List<TgeCampaignParticipateRecord>
    fun findByAddressIgnoreCaseAndStatusIn(
        address: String,
        status: List<TgeCampaignParticipateRecordStatus>
    ): List<TgeCampaignParticipateRecord>
    fun findByStatusIn(
        status: List<TgeCampaignParticipateRecordStatus>
    ): List<TgeCampaignParticipateRecord>

    fun countByBitlayerTradeOptionOrderIdIsNotNullAndStatusIn(
        status: List<TgeCampaignParticipateRecordStatus>
    ): Long
} 