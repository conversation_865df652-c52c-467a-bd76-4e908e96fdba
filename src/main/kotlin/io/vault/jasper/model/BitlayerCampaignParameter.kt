package io.vault.jasper.model

import io.vault.jasper.enums.ChainType
import org.springframework.data.annotation.Id
import org.springframework.data.mongodb.core.mapping.Document
import org.springframework.data.mongodb.core.mapping.Field
import java.math.BigDecimal
import java.time.LocalDateTime

@Document("bitlayer_campaign_parameter")
data class BitlayerCampaignParameter(
    @Id
    val id: String? = null,
    @Field("start_date")
    var startDate: LocalDateTime = LocalDateTime.now(),
    @Field("end_date")
    var endDate: LocalDateTime = LocalDateTime.of(2024, 9, 30, 11, 0, 0),
    @Field("trade_rebate_chain")
    var tradeRebateChain: ChainType = ChainType.BITLAYER,
    @Field("trade_rebate_contract_address")
    var tradeRebateContractAddress: String = "0x943105D4Ff1436c79d1D62d65Fc55816485A89dd",
    @Field("first_trade_rebate_task_switch")
    var firstTradeRebateTaskSwitch: Boolean = true, // 推送First Trade Rebate任务的开关
    @Field("first_trade_rebate_count")
    var firstTradeRebateCount: Int = 10000,
    @Field("rebate_claim_time")
    var rebateClaimTime: Long = 900, // 15分钟
    @Field("first_trade_discord_level")
    var firstTradeDiscordLevel: Int = 1,
    @Field("first_trade_degen_quantity")
    var firstTradeDegenQuantity: BigDecimal = BigDecimal("0.01"),
    @Field("campaign_4_start_date")
    var campaign4StartDate: LocalDateTime = LocalDateTime.of(2025, 1, 5, 0, 0, 0),
)