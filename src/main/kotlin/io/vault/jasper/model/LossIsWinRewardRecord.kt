package io.vault.jasper.model

import org.springframework.data.annotation.Id
import org.springframework.data.mongodb.core.mapping.Document
import org.springframework.data.mongodb.core.mapping.Field
import java.math.BigDecimal
import java.time.LocalDateTime

@Document("loss_is_win_reward_record")
data class LossIsWinRewardRecord(
    @Id
    val id: String? = null,

    @Field("address")
    val address: String,

    @Field("option_order_id")
    val optionOrderId: String,

    @Field("btr_amount")
    val btrAmount: BigDecimal,

    @Field("created_at")
    val createdAt: LocalDateTime = LocalDateTime.now()
)