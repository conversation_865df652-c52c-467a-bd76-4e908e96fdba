package io.vault.jasper.service.blockchain.loglistener

import io.vault.jasper.enums.ChainType
import io.vault.jasper.model.LpVaultRecord
import io.vault.jasper.model.PendingOptionOrder
import io.vault.jasper.repository.BlockchainRepository
import io.vault.jasper.repository.LpVaultRecordRepository
import io.vault.jasper.repository.PendingOptionOrderRepository
import io.vault.jasper.service.BlockchainConfigService
import io.vault.jasper.service.blockchain.EvmService
import io.vault.jasper.utils.DateTimeUtil
import org.slf4j.LoggerFactory
import org.web3j.abi.datatypes.Address
import org.web3j.protocol.core.DefaultBlockParameter
import org.web3j.protocol.core.DefaultBlockParameterName
import org.web3j.protocol.core.methods.request.EthFilter
import org.web3j.protocol.core.methods.response.Log
import org.web3j.utils.Numeric

class EvmLogListener(
    private val evmService: EvmService,
    blockchainRepository: BlockchainRepository,
    private val pendingOptionOrderRepository: PendingOptionOrderRepository,
    private val lpVaultRecordRepository: LpVaultRecordRepository,
    private val blockchainConfigService: BlockchainConfigService
) {
    private var logger = LoggerFactory.getLogger(this::class.java)

    private val chain: ChainType = evmService.chainType

    init {
        val blockchain =
            blockchainRepository.findFirstByChain(chain) ?: throw Exception("Blockchain $chain config not found")
        val fromBlock = if (blockchain.eventListenerBlockNumber == 0L) {
            DefaultBlockParameterName.LATEST
        } else {
            DefaultBlockParameter.valueOf(blockchain.eventListenerBlockNumber.toBigInteger())
        }
        val toBlock = DefaultBlockParameterName.LATEST
        val listeningAddressList = mutableListOf(evmService.addOrderLogAddress)
        if (evmService.lpVaultContract != null) {
            listeningAddressList.add(evmService.lpVaultContract!!)
        }
        val filter = EthFilter(fromBlock, toBlock, listeningAddressList)
        logger.info("开始监听${evmService.chainType}链上期权合约交易...")
        evmService.evmUtil.web3j.ethLogFlowable(filter).subscribe( { log ->
            logger.info("发现${chain}交易日志: ${log.transactionHash}\tAddress: ${log.address}")
            val processResult = when (log.address.lowercase()) {
                evmService.addOrderLogAddress.lowercase() -> optionOrderEvent(log)
                evmService.lpVaultContract?.lowercase() -> lpVaultEvent(log)
                else -> false
            }
            if (processResult) {
                // 有交易处理成功，更新最后监听的区块高度
                blockchainConfigService.updateEventListenerBlockNumber(chain, log.blockNumber.toLong())
            }
        }, { error ->
            logger.error("${evmService.chainType} 交易监听发生异常: ${error.message}", error)
        })
    }

    private fun optionOrderEvent(log: Log, times: Int = 3): Boolean {
        val fnList = listOf(evmService.callOptionFnHash, evmService.putOptionFnHash)
        val topicFirst = log.topics.firstOrNull() ?: return false
        if (!fnList.contains(topicFirst)) return false
        val block = try {
            evmService.evmUtil.web3j.ethGetBlockByHash(log.blockHash, false).send().block
        } catch (e: Exception) {
            logger.error(e.message, e)
            return if (times > 0) {
                optionOrderEvent(log, times - 1)
            } else {
                return false
            }
        }
        val blockTime = DateTimeUtil.convertTimestampToLocalDateTime(block.timestamp.toLong())
        val txid = log.transactionHash
        logger.info("发现期权合约交易: $txid")
        pendingOptionOrderRepository.save(
            PendingOptionOrder(
                chain = chain,
                txHash = txid,
                txBlockHeight = log.blockNumber.toLong(),
                blockTime = blockTime
            )
        )

        return true
    }

    private fun lpVaultEvent(log: Log): Boolean {
        val txid = log.transactionHash
        lpVaultRecordRepository.findFirstByChainAndTxHash(evmService.chainType, txid)?.let {
            return true
        }
        val topicFirst = log.topics.firstOrNull() ?: return false
        val method = when (topicFirst) {
            evmService.lpVaultDepositEventHash -> "deposit"
            evmService.lpVaultWithdrawEventHash -> "withdraw"
            else -> return false
        }
        val block = try {
            evmService.evmUtil.web3j.ethGetBlockByHash(log.blockHash, false).send().block
        } catch (e: Exception) {
            logger.error(e.message, e)
            return lpVaultEvent(log)
        }
        val blockTime = DateTimeUtil.convertTimestampToLocalDateTime(block.timestamp.toLong())
        val dataList = log.data.removePrefix("0x").chunked(64)
        if (dataList.size != 3) return false
        logger.info("发现LP Vault合约交易($method): $txid")
        val address = Address(dataList[0]).value
        val assetAmount = Numeric.toBigIntNoPrefix(dataList[1])
        val lpAmount = Numeric.toBigIntNoPrefix(dataList[2])

        val (from, to, vaultAddress) = when (topicFirst) {
            evmService.lpVaultDepositEventHash -> {
                val logs = try {
                    evmService.evmUtil.web3j.ethGetTransactionReceipt(txid).send().transactionReceipt.get().logs
                } catch (e: Exception) {
                    logger.error("Fail to get logs in TxHash=$txid")
                    emptyList<Log>()
                }
                val vAddress = logs.firstNotNullOfOrNull { l ->
                    if (l.topics.firstOrNull() == evmService.lpVaultIssueEventHash) {
                        l.data.substring(2).chunked(64).firstOrNull()?.let { Address(it).value }
                    } else {
                        null
                    }
                }
                Triple(address, null, vAddress)
            }
            else -> {
                Triple(null, address, null)
            }
        }

        lpVaultRecordRepository.save(
            LpVaultRecord(
                chain = chain,
                txHash = txid,
                txBlockHeight = log.blockNumber.toLong(),
                blockTime = blockTime,
                method = method,
                from = from,
                to = to,
                assetAmount = assetAmount,
                lpAmount = lpAmount,
                vault = vaultAddress
            )
        )

        return true
    }
}