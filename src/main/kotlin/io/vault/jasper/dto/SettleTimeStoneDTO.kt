package io.vault.jasper.dto

import EVMAddress
import io.swagger.annotations.ApiModel
import io.swagger.annotations.ApiModelProperty
import io.vault.jasper.enums.ChainType

@ApiModel("清算时光石请求")
data class SettleTimeStoneDTO(
    @ApiModelProperty("钱包地址", required = true)
    @field:EVMAddress
    val address: String,
    @ApiModelProperty("Chain", required = true)
    val chain: ChainType,
    @ApiModelProperty("Order ID", required = true)
    val orderId: String,
    @ApiModelProperty("清算的时间戳", required = true)
    val timestamp: Long,
    @ApiModelProperty("签名信息, 对请求参数组成的字符串进行签名，钱包地址(checksum)+chain+orderId+timestamp, 例如 address=0x1276fb1ccA72De39d876e8074fC781cf7201b3d4&chain=BITLAYER&orderId=12345&timestamp=123456789", required = true)
    val signature: String,
)
