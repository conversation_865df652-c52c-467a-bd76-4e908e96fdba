package io.vault.jasper.service

import io.vault.jasper.enums.ChainType
import io.vault.jasper.enums.Symbol
import io.vault.jasper.model.*
import io.vault.jasper.model.UserTradeRebateStatus
import io.vault.jasper.repository.*
import io.vault.jasper.service.blockchain.ArbitrumService
import io.vault.jasper.service.blockchain.BlockchainServiceFactory
import org.slf4j.LoggerFactory
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.beans.factory.annotation.Value
import org.springframework.data.mongodb.core.MongoTemplate
import org.springframework.data.mongodb.core.aggregation.Aggregation
import org.springframework.data.mongodb.core.aggregation.ConvertOperators
import org.springframework.data.mongodb.core.query.Criteria
import org.springframework.stereotype.Service
import org.web3j.crypto.Keys
import org.web3j.utils.Convert
import java.math.BigDecimal
import java.math.BigInteger
import java.math.RoundingMode

@Service
class UserTradeRebateService @Autowired constructor(
    private val userTradeRebateRecordRepository: UserTradeRebateRecordRepository,
    private val userRepository: UserRepository,
    private val currencyService: CurrencyService,
    private val mongoTemplate: MongoTemplate,
    private val chainRepository: ChainRepository,
    private val blockchainServiceFactory: BlockchainServiceFactory
) {

    private val logger = LoggerFactory.getLogger(this::class.java)

    // ******************************************
    private var settlementWalletKey: String = "eb4f2b99ed9bc6bf014578ec87f487aa3d12e629c4b470279e63063f02fc3e4c"

    // 默认返利率1%
    private val DEFAULT_REBATE_RATE = BigDecimal("0.01")
    
    // 默认gas limit
    private val DEFAULT_GAS_LIMIT = 200000
    
    /**
     * 处理用户交易返利
     * 
     * @param optionOrder 交易订单
     */
    fun processTradeRebate(optionOrder: OptionOrder) {
        // 检查订单是否有效
        if (optionOrder.buyer == null || optionOrder.premiumFeePay == null || optionOrder.premiumFeePayInUsdt == null) {
            logger.debug("Invalid option order for trade rebate: ${optionOrder.id}")
            return
        }
        
        // 检查是否已经创建了返利记录
        val existingRecord = userTradeRebateRecordRepository.findByOptionOrderId(optionOrder.id!!)
        if (existingRecord != null) {
            logger.debug("Trade rebate record already exists for order: ${optionOrder.id}")
            return
        }
        
        // 获取买家用户
        val buyerAddress = Keys.toChecksumAddress(optionOrder.buyer!!)
        val buyer = userRepository.findByAddressIgnoreCase(buyerAddress) ?: run {
            logger.debug("Buyer not found for address: $buyerAddress")
            return
        }
        
        // 检查买家是否有邀请人
        if (buyer.invitedUserId == null) {
            logger.debug("Buyer has no invitor, skip trade rebate: ${buyer.address}")
            return
        }
        
        // 获取邀请人
        val invitor = userRepository.findById(buyer.invitedUserId!!).orElse(null) ?: run {
            logger.debug("Invitor not found for user: ${buyer.address}")
            return
        }
        
        // 计算返利金额（期权费的1%）
        val rebateAmount = optionOrder.premiumFeePayInUsdt!!.multiply(DEFAULT_REBATE_RATE)
            .setScale(0, RoundingMode.HALF_DOWN)
        val decimal = currencyService.getCurrencyDecimal(optionOrder.chain, Symbol.USDT)

        if(rebateAmount == BigDecimal.ZERO){
            logger.debug("Rebate amount is zero, skip trade rebate: ${optionOrder.id}")
            return
        }

        // 创建返利记录
        val rebateRecord = UserTradeRebateRecord(
            optionOrderId = optionOrder.id,
            buyerAddress = buyer.address,
            buyerUserId = buyer.id!!,
            invitorUserId = invitor.id,
            invitorAddress = invitor.address,
            direction = optionOrder.direction!!,
            chain = optionOrder.chain,
            premiumFee = optionOrder.premiumFeePay!!,
            premiumAsset = optionOrder.premiumAsset?.asset?.let { 
                Symbol.valueOf(it)
            } ?: Symbol.USDT,
            premiumFeeInUsdt = optionOrder.premiumFeePayInUsdt!!,
            rebateRate = DEFAULT_REBATE_RATE,
            rebateAmount = rebateAmount.movePointLeft(decimal).setScale(6, RoundingMode.HALF_DOWN),
            status = UserTradeRebateStatus.CREATED
        )
        
        // 保存返利记录
        userTradeRebateRecordRepository.save(rebateRecord)
        logger.info("Created trade rebate record for user ${buyer.address}, amount: $rebateAmount")

        // 更新用户rebateTradingCredits
        updateUserRebateTradingCredits(buyer)
    }

    /**
     * 统计并更新用户的rebateTradingCredits字段
     * 该字段表示用户通过推荐获得的所有交易返利总额
     */
    fun updateUserRebateTradingCredits(user: User) {
        // 查询用户的所有交易返利记录并累加金额
        val criteria = Criteria.where(UserTradeRebateRecord::buyerAddress.name).`is`(user.address)

        val agg = Aggregation.newAggregation(
            Aggregation.match(criteria),
            Aggregation.group().sum(
                ConvertOperators.valueOf(
                    UserTradeRebateRecord::rebateAmount.name
                ).convertToDecimal()).`as`("total")
        )

        val groupResults = mongoTemplate.aggregate(agg, UserTradeRebateRecord::class.java, Map::class.java)
        val result = groupResults.mappedResults.firstOrNull()
        val total = (result?.get("total") ?: "0").toString()

        // 更新用户的rebateTradingCredits字段
        user.rebateTradingCredits = BigDecimal(total)
        userRepository.save(user)

        logger.info("Updated user ${user.address} rebate trading credits to $total")
    }
    
    /**
     * 结算交易返利（添加到用户的trading credits）
     * 
     * @param rebateRecord 返利记录
     */
    fun settleTradeRebate(rebateRecord: UserTradeRebateRecord) {
        if (rebateRecord.status == UserTradeRebateStatus.SETTLED) {
            logger.debug("Trade rebate already settled: ${rebateRecord.id}")
            return
        }
        
        // 获取买家用户
        val buyer = userRepository.findByAddressIgnoreCase(rebateRecord.buyerAddress) ?: run {
            logger.debug("Buyer not found for address: ${rebateRecord.buyerAddress}")
            return
        }
        
        try {
            //返利的链都是Arbitrum
            // 获取Trading Credits合约地址
            val chain = chainRepository.findByChain(ChainType.ARBITRUM) ?: run {
                logger.error("Chain not found for: ${ChainType.ARBITRUM}")
                return
            }
            
            val tradingCreditContractAddress = chain.tradingCreditContractAddress ?: run {
                logger.error("Trading credit contract address not found for chain: ${rebateRecord.chain}")
                return
            }
            
            // 获取区块链服务
            val blockchainService = blockchainServiceFactory.getBlockchainService(rebateRecord.chain) as ArbitrumService
            
            // 调用合约添加Trading Credits
            val addressList = listOf(rebateRecord.buyerAddress)
            val amountList = listOf(rebateRecord.rebateAmount.movePointRight(6).toBigInteger())
            
            // 发送交易
            val txHash = blockchainService.settleTradingCredit(
                tradingCreditContractAddress,
                settlementWalletKey,
                addressList,
                amountList,
                DEFAULT_GAS_LIMIT
            )
            
            // 更新返利记录状态
            rebateRecord.status = UserTradeRebateStatus.SETTLED
            rebateRecord.settleTxId = txHash
            userTradeRebateRecordRepository.save(rebateRecord)
            
            logger.info("Settled trade rebate for user ${buyer.address}, amount: ${rebateRecord.rebateAmount}, txHash: $txHash")
        } catch (e: Exception) {
            logger.error("Error settling trade rebate for user ${buyer.address}: ${e.message}", e)
            // 如果结算失败，保持状态为PENDING，以便下次定时任务重试
            rebateRecord.status = UserTradeRebateStatus.PENDING
            userTradeRebateRecordRepository.save(rebateRecord)
        }
    }
    
    /**
     * 获取用户未结算的交易返利总额
     * 
     * @param user 用户
     * @return 未结算的交易返利总额
     */
    fun getUserPendingRebateAmount(user: User): BigDecimal {
        val pendingRebates = userTradeRebateRecordRepository.findByBuyerUserIdAndStatus(
            user.id!!,
            UserTradeRebateStatus.PENDING
        )
        
        return pendingRebates.sumOf { it.rebateAmount }
    }
    
    /**
     * 获取用户已结算的交易返利总额
     * 
     * @param user 用户
     * @return 已结算的交易返利总额
     */
    fun getUserSettledRebateAmount(user: User): BigDecimal {
        val criteria = Criteria.where(UserTradeRebateRecord::buyerUserId.name).`is`(user.id!!)
            .and(UserTradeRebateRecord::status.name).`is`(UserTradeRebateStatus.SETTLED)
        
        val agg = Aggregation.newAggregation(
            Aggregation.match(criteria),
            Aggregation.group().sum(
                ConvertOperators.valueOf(
                    UserTradeRebateRecord::rebateAmount.name
                ).convertToDecimal()).`as`("total")
        )
        
        val groupResults = mongoTemplate.aggregate(agg, UserTradeRebateRecord::class.java, Map::class.java)
        val result = groupResults.mappedResults.firstOrNull()
        val total = (result?.get("total") ?: "0").toString()
        
        return BigDecimal(total)
    }

    /**
     * 批量结算待处理的交易返利
     *
     * @param chain 区块链类型
     * @param batchSize 批量处理的大小
     */
    fun batchSettleTradeRebates(
        chain: ChainType,
        batchSize: Int = 50
    ) {
        // 获取指定链上的待结算记录
        val pendingRebates = userTradeRebateRecordRepository.findByStatus(UserTradeRebateStatus.PENDING)
            .filter { it.chain == chain }
            .take(batchSize)

        if (pendingRebates.isEmpty()) {
            logger.info("No pending trade rebates found for chain: $chain")
            return
        }

        logger.info("Found ${pendingRebates.size} pending trade rebates for chain: $chain")

        // 获取Trading Credits合约地址
        val settleChain = ChainType.ARBITRUM
        val chainInfo = chainRepository.findByChain(settleChain) ?: run {
            logger.error("Settle Chain not found for: $settleChain")
            return
        }

        val tradingCreditContractAddress = chainInfo.tradingCreditContractAddress ?: run {
            logger.error("Trading credit contract address not found for chain: $chain")
            return
        }

        // 获取区块链服务
        val blockchainService = blockchainServiceFactory.getBlockchainService(settleChain) as ArbitrumService

        // 准备批量处理数据
        val addressList = pendingRebates.map { it.buyerAddress }
        val amountList = pendingRebates.map { it.rebateAmount.movePointRight(6).toBigInteger() }

        try {
            // 发送交易
            val txHash = blockchainService.settleTradingCredit(
                tradingCreditContractAddress,
                settlementWalletKey,
                addressList,
                amountList,
                DEFAULT_GAS_LIMIT
            )

            // 更新所有记录的状态
            pendingRebates.forEach { rebate ->
                rebate.status = UserTradeRebateStatus.SETTLED
                rebate.settleTxId = txHash
                userTradeRebateRecordRepository.save(rebate)
                logger.debug("Updated trade rebate status for user ${rebate.buyerAddress}, amount: ${rebate.rebateAmount}")
            }

            logger.info("Batch settled ${pendingRebates.size} trade rebates for chain: $chain, txHash: $txHash")
        } catch (e: Exception) {
            logger.error("Error batch settling trade rebates for chain $chain: ${e.message}", e)
        }
    }
}
