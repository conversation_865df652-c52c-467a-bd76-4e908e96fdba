package io.vault.jasper.model

import io.vault.jasper.enums.ChainType
import io.vault.jasper.enums.Symbol
import org.springframework.data.annotation.Id
import org.springframework.data.mongodb.core.index.CompoundIndex
import org.springframework.data.mongodb.core.index.Indexed
import org.springframework.data.mongodb.core.mapping.Document
import org.springframework.data.mongodb.core.mapping.Field
import org.springframework.data.mongodb.core.mapping.FieldType
import java.math.BigDecimal
import java.time.LocalDateTime

/**
 * 期权成交订单
 */
@Document()
@CompoundIndex(
    name = "chain_buyer_tx",
    def = "{'chain' : 1, 'buyer': 1, 'tx_hash': 1}",
    background = true
)
data class History(
    @Id
    val id: String? = null,

    val chain: ChainType,

    @Field("option_order_id")
    var optionOrderId: String,

    var buyer: String,

    var method: TransactionMethod,

    var asset: String,

    @Field(targetType = FieldType.DECIMAL128)
    val amount: BigDecimal,

    @Field("tx_hash")
    @Indexed(background = true)
    var txHash: String,

    @Indexed(background = true)
    var created: LocalDateTime,
)

enum class TransactionMethod {
    PAY, // 支付权利金
    RECEIVE, // 利润
}

enum class HistoryToken {
    CREDITS,
    FREE_ORDER,
}
