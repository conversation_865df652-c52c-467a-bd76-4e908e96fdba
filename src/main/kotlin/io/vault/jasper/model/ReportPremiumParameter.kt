package io.vault.jasper.model

import io.vault.jasper.enums.ChainType
import org.springframework.data.annotation.Id
import org.springframework.data.annotation.LastModifiedDate
import org.springframework.data.mongodb.core.index.Indexed
import org.springframework.data.mongodb.core.mapping.Document
import java.math.BigDecimal
import java.time.LocalDateTime

@Document
data class ReportPremiumParameter(
    @Id
    val id: String? = null,
    val countPerTransaction: Int = 20, // 每个链上交易提交多少个地址的权利金数据
    val gasLimit: Int = 1000000, // 推送权利金交易的 gasLimit
    val startDate: LocalDateTime = LocalDateTime.now(), // 统计的 Option 开始时间
    var chain: ChainType = ChainType.ARBITRUM, // 链
    val premiumContractAddress: String = "0xC512B704aAa896Cb318426AA35dA28449A70d735", // 权利金合约地址
    var safepayTotalCount: Int = 2000, // Safepal 奖励总数
)