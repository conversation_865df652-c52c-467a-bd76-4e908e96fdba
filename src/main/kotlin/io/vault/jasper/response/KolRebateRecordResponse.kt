package io.vault.jasper.response

import io.swagger.annotations.ApiModel
import io.swagger.annotations.ApiModelProperty
import io.vault.jasper.enums.ChainType
import io.vault.jasper.enums.OptionDirection
import io.vault.jasper.enums.Symbol
import io.vault.jasper.model.OrderStatus
import io.vault.jasper.model.OrderType
import io.vault.jasper.model.PremiumAsset
import io.vault.jasper.model.PremiumFeeInfo

@ApiModel
data class KolRebateRecordResponse(
    @ApiModelProperty("日期", required = true)
    val date: Long,
    @ApiModelProperty("每日活跃地址数", required = true)
    val dailyActive: Int,
    @ApiModelProperty("当天总返佣", required = true)
    val dailyTotalRebate: String

)
