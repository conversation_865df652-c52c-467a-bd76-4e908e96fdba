package io.vault.jasper.task

import io.vault.jasper.enums.OptionDirection
import io.vault.jasper.event.OptionSettlementEvent
import io.vault.jasper.model.OptionOrder
import io.vault.jasper.model.OptionStatus
import io.vault.jasper.repository.OptionOrderRepository
import io.vault.jasper.repository.OrderRepository
import io.vault.jasper.service.*
import io.vault.jasper.service.blockchain.BlockchainServiceFactory
import org.slf4j.LoggerFactory
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.beans.factory.annotation.Value
import org.springframework.context.ApplicationEventPublisher
import org.springframework.context.annotation.Profile
import org.springframework.data.mongodb.core.MongoTemplate
import org.springframework.data.mongodb.core.query.Criteria
import org.springframework.data.mongodb.core.query.Query
import org.springframework.data.repository.findByIdOrNull
import org.springframework.scheduling.annotation.Scheduled
import org.springframework.stereotype.Service
import org.web3j.crypto.Keys
import java.math.BigDecimal
import java.math.BigInteger
import java.time.LocalDateTime


@Service
class UpdateOptionsProfit @Autowired constructor(
    private val optionOrderRepository: OptionOrderRepository,
    private val blockchainServiceFactory: BlockchainServiceFactory,
    private val monitorService: MonitorService,
    private val orderRepository: OrderRepository,
    private val currencyService: CurrencyService,
    private val eventPublisher: ApplicationEventPublisher,
    private val optionOrderService: OptionOrderService,
    private val mongoTemplate: MongoTemplate,
    private val lossIsWinUserSummaryService: LossIsWinUserSummaryService,
) {

    private val logger = LoggerFactory.getLogger(this::class.java)

    @Value("\${schedule_task_on}")
    lateinit var scheduleOn: String

    private val startDate = LocalDateTime.of(2024, 12, 10, 0, 0, 0)

    @Scheduled(fixedDelay = 10 * 1000, initialDelay = 5 * 1000)
    fun run() {
        if(scheduleOn == "false"){
            return
        }
        try {
            updateProfit()
        } catch (e: Exception) {
            logger.error(e.message, e)
        }
        monitorService.taskMonitor(this::class.simpleName)
    }

    /**
     * 记录每笔已结算的期权合约中，买家的利润
     */
    private fun updateProfit() {
        optionOrderRepository.findByStatusAndBuyerProfitIsNull(OptionStatus.SETTLED).forEach { oo ->
            if (oo.settlementHash.isNullOrBlank()) {
                optionOrderService.updateFields(
                    mapOf(
                        OptionOrder::status.name to OptionStatus.SETTLE_FAILED,
                        OptionOrder::errorMsg.name to "Settlement hash is null or blank"
                    ),
                    oo.id!!
                )
                return@forEach
            }
            val service = blockchainServiceFactory.getBlockchainService(oo.chain) ?: run {
                logger.warn("${oo.chain} Service not found, Skipped")
                return@forEach
            }

            if(oo.expiryDate.isBefore(startDate)) {
                //logger.warn("Option(${oo.id}) expired before 2024-12-10, Skipped")
                return@forEach
            }

            val settlementInfo = service.getSettlementInfo(oo) ?: run {
                logger.warn("Settlement info not found, Skipped")
                return@forEach
            }

            val profit = settlementInfo.first

            //val profit = service.getProfit(oo)
            logger.info("UpdateOptionsProfitTask : ${oo.chain}\t${oo.settlementHash}\tbuyer profit = $profit")
            var newOptionOrder = optionOrderService.updateFields(
                mapOf(
                    OptionOrder::buyerProfit.name to profit,
                    OptionOrder::buyer.name to Keys.toChecksumAddress(oo.buyer!!)
                ),
                oo.id!!,
                true
            )

            if(newOptionOrder == null) {
                logger.error("UpdateOptionsProfitTask :Failed to update buyer profit for order: ${oo.id}")
                return@forEach
            }

            /**
             * 有利润，更新结算价格
             */
            val order = orderRepository.findByIdOrNull(newOptionOrder.orderId) ?: return@forEach
            if (order.bidAsset == null || order.bidAmount == null || order.bidAmount == BigInteger.ZERO) {
                logger.warn("UpdateOptionsProfitTask :Order(${order.id}) bid asset or bid amount is null or Zero, skipped")
                return@forEach
            }

            order.creator = Keys.toChecksumAddress(order.creator)
            orderRepository.save(order)

            // 利润为0，更新结算价格为行权价
            if(profit.compareTo(BigDecimal.ZERO) == 0) {
                logger.info("UpdateOptionsProfitTask :oo(${newOptionOrder.id}) profit is 0, update settlement price = strike price")
                optionOrderService.updateFields(
                    mapOf(
                        OptionOrder::marketPriceAtSettlement.name to order.strikePrice.toString()
                    ),
                    newOptionOrder.id!!
                )
            } else {

                val strikePrice = order.strikePrice.movePointLeft(18) // 行权价
                val bidAmount = order.bidAmount!! // 期权份数
                logger.info("UpdateOptionsProfitTask :oo(${newOptionOrder.id}) strikePrice=$strikePrice, bid amount=$bidAmount")

                val profitAsset = when (newOptionOrder.direction) {
                    OptionDirection.CALL -> newOptionOrder.underlyingAsset
                    else -> currencyService.getOptionQuoteAsset(
                        newOptionOrder.chain,
                        newOptionOrder.bidAsset
                    )
                }
                val profitDecimals = currencyService.getCurrencyDecimal(newOptionOrder.chain, profitAsset)
                val buyerProfit = newOptionOrder.buyerProfit!!.movePointLeft(profitDecimals) // 利润

                val settlementPrice = when (newOptionOrder.direction) {
                    OptionDirection.CALL -> {
                        strikePrice.multiply(bidAmount).divide((bidAmount - buyerProfit), 18, BigDecimal.ROUND_HALF_UP)
                    }

                    else -> strikePrice - buyerProfit.divide(bidAmount, 18, BigDecimal.ROUND_HALF_UP)
                }.movePointRight(18).toBigInteger()

                newOptionOrder = optionOrderService.updateFields(
                    mapOf(
                        OptionOrder::marketPriceAtSettlement.name to settlementPrice.toString()
                    ),
                    newOptionOrder.id!!,
                    true
                )

                if(newOptionOrder == null){
                    logger.error("UpdateOptionsProfitTask :Failed to update settlement price for order: ${oo.id}")
                    return@forEach
                }

                logger.info("UpdateOptionsProfitTask :oo(${newOptionOrder.id}) direction=${newOptionOrder.direction}, buyerProfit=$buyerProfit, settlement price=${newOptionOrder.marketPriceAtSettlement}")
            }

            if (newOptionOrder.lossInUsd == null) {
                newOptionOrder = optionOrderService.updateBuyerProfitInUsd(newOptionOrder!!) ?: run {
                    logger.error("UpdateOptionsProfitTask :Failed to update buyer profit in usd for order: ${oo.id}")
                    newOptionOrder!!
                } // 更新买家利润的USD值
                newOptionOrder = optionOrderService.updateLossInUsd(newOptionOrder!!) ?: run {
                    logger.error("UpdateOptionsProfitTask :Failed to update loss in usd for order: ${oo.id}")
                    newOptionOrder!!
                } // 更新LossInUsd的值
                // 发生亏损，可能可以参与活动
                if (newOptionOrder.lossInUsd != null && newOptionOrder.lossInUsd!! > BigDecimal.ZERO) {
                    lossIsWinUserSummaryService.updateLoss(
                        newOptionOrder.id!!,
                        newOptionOrder.buyer!!,
                        newOptionOrder.lossInUsd!!
                    )
                }
            }

            // 事件通知
            logger.info("UpdateOptionsProfitTask : 更新利润后事件: ${newOptionOrder.id}")
            eventPublisher.publishEvent(OptionSettlementEvent(this, newOptionOrder.id!!))
        }
    }
}