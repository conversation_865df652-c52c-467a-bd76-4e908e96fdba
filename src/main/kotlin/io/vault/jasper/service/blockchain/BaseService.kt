package io.vault.jasper.service.blockchain

import io.vault.jasper.blockchain.BaseBlockchainUtil
import io.vault.jasper.blockchain.BlockchainUtilFactory
import io.vault.jasper.blockchain.evm.arbitrum.BaseScanApi
import io.vault.jasper.repository.*
import io.vault.jasper.service.*
import io.vault.jasper.task.AsyncEvmScanner
import org.slf4j.LoggerFactory
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.beans.factory.annotation.Value
import org.springframework.context.ApplicationEventPublisher
import org.springframework.stereotype.Service
import org.web3j.abi.FunctionEncoder
import org.web3j.abi.TypeReference
import org.web3j.abi.datatypes.Address
import org.web3j.abi.datatypes.DynamicArray
import org.web3j.abi.datatypes.Function
import org.web3j.abi.datatypes.Type
import org.web3j.abi.datatypes.generated.Uint256
import org.web3j.crypto.Credentials
import org.web3j.crypto.RawTransaction
import org.web3j.crypto.TransactionEncoder
import org.web3j.utils.Numeric
import java.math.BigInteger

@Service
class BaseService @Autowired constructor(
    blockchainUtil: BaseBlockchainUtil,
    scanApi: BaseScanApi,
    optionOrderRepository: OptionOrderRepository,
    optionOrderInfoRepository: OptionOrderInfoRepository,
    orderRepository: OrderRepository,
    blockchainRepository: BlockchainRepository,
    asyncEvmScanner: AsyncEvmScanner,
    jasperVaultService: JasperVaultService,
    eventPublisher: ApplicationEventPublisher,
    lpVaultRepository: LPVaultRepository,
    contractEventRepository: ContractEventRepository,
    currencyService: CurrencyService,
    optionOrderService: OptionOrderService,
    blockchainUtilFactory: BlockchainUtilFactory,
    subgraphService: SubgraphService,
    historyService: HistoryService,
    miniBridgeSwapOrderRepository: MiniBridgeSwapOrderRepository,
    alertService: AlertService
) : EvmService(
    blockchainUtil,
    blockchainRepository,
    asyncEvmScanner,
    orderRepository,
    optionOrderRepository,
    optionOrderInfoRepository,
    jasperVaultService,
    eventPublisher,
    lpVaultRepository,
    contractEventRepository,
    currencyService,
    optionOrderService,
    blockchainUtilFactory,
    scanApi,
    subgraphService,
    historyService,
    miniBridgeSwapOrderRepository,
    alertService
) {

    private val logger = LoggerFactory.getLogger(this::class.java)

    @Value("\${blockchain.base.contract.vault_factory}")
    override lateinit var vaultFactoryContract: String

    @Value("\${blockchain.base.contract.option_service}")
    override lateinit var optionServiceContract: String

    @Value("\${blockchain.base.contract.diamond}")
    override lateinit var addOrderLogAddress: String

    @Value("\${blockchain.base.contract.issuance_module}")
    override lateinit var issuanceModuleContract: String

    @Value("\${blockchain.base.contract.lp_vault}")
    override lateinit var lpVaultContract: String

    /**
     * 结算空投首单永赚
     */
    fun settleAirdropebate(
        contractAddress: String,
        privateKey: String,
        addressList: List<String>,
        amountList: List<BigInteger>,
        gasLimit: Int,
        gasPrice: BigInteger? = null
    ): String {
        val inputs: List<Type<*>> = listOf(
            DynamicArray(Address::class.java, addressList.map { Address(it) }),
            DynamicArray(Uint256::class.java, amountList.map { Uint256(it) })
        )
        val outputs: MutableList<TypeReference<*>> = ArrayList()
        val method = "settle"
        val function = Function(method, inputs, outputs)
        val data = FunctionEncoder.encode(function)
        val credentials = Credentials.create(privateKey)
        val rawTransaction = RawTransaction.createTransaction(
            evmUtil.getNonce(credentials.address),
            gasPrice ?: evmUtil.getGasPrice(""),
            gasLimit.toBigInteger(),
            contractAddress,
            BigInteger.valueOf(0),
            data
        )
        val signMessage = TransactionEncoder.signMessage(rawTransaction, evmUtil.chainId, credentials)
        val toHexString = Numeric.toHexString(signMessage)
        val ethCall = evmUtil.web3j.ethSendRawTransaction(toHexString).sendAsync().get()
        if (ethCall.hasError()) {
            val errorMsg = ethCall.error.message
            // max fee per gas less than block base fee: address ******************************************, maxFeePerGas: 10000000 baseFee: 10041000
            if (errorMsg.startsWith("max fee per gas less than block base fee")) {
                val regex = Regex("baseFee:\\s*(\\d+)")
                regex.find(errorMsg)?.let {
                    val baseFee = it.groupValues[1].toBigInteger()
                    logger.debug("current baseFee: $baseFee")
                    val move = baseFee.toString(10).length - 2
                    val divisor = BigInteger.TEN.pow(move)
                    val newBaseFee = (baseFee.divide(divisor) + BigInteger.ONE).multiply(divisor)
                    logger.debug("new baseFee: $newBaseFee")
                    Thread.sleep(3000)

                    return settleAirdropebate(
                        contractAddress,
                        privateKey,
                        addressList,
                        amountList,
                        gasLimit,
                        newBaseFee
                    )
                }
            } else {
                logger.error("Error Message: ${ethCall.error.message}")
                throw Exception("Failed to send transaction")
            }
        }
        val txHash = ethCall.transactionHash
        logger.info("method: $method\t txid: $txHash")

        return txHash
    }

    fun settleAirdropNFTRecords(
        contractAddress: String,
        privateKey: String,
        address: String,
        gasLimit: Int,
        gasPrice: BigInteger? = null
    ): String {
        val inputs: List<Type<*>> = listOf(
            Address(address)
        )

        logger.info("Base service airdrop nft Settle Free Mint Records input: $inputs")

        val outputs: MutableList<TypeReference<*>> = ArrayList()
        val method = "adminMint"
        val function = Function(method, inputs, outputs)
        val data = FunctionEncoder.encode(function)
        val credentials = Credentials.create(privateKey)
        val rawTransaction = RawTransaction.createTransaction(
            evmUtil.getNonce(credentials.address),
            gasPrice ?: evmUtil.getGasPrice(""),
            gasLimit.toBigInteger(),
            contractAddress,
            BigInteger.valueOf(0),
            data
        )
        val signMessage = TransactionEncoder.signMessage(rawTransaction, evmUtil.chainId, credentials)
        val toHexString = Numeric.toHexString(signMessage)
        val ethCall = evmUtil.web3j.ethSendRawTransaction(toHexString).sendAsync().get()
        if (ethCall.hasError()) {
            val errorMsg = ethCall.error.message
            // max fee per gas less than block base fee: address ******************************************, maxFeePerGas: 10000000 baseFee: 10041000
            if (errorMsg.startsWith("max fee per gas less than block base fee")) {
                val regex = Regex("baseFee:\\s*(\\d+)")
                regex.find(errorMsg)?.let {
                    val baseFee = it.groupValues[1].toBigInteger()
                    logger.warn("current baseFee: $baseFee")
                    val move = baseFee.toString(10).length - 2
                    val divisor = BigInteger.TEN.pow(move)
                    val newBaseFee = (baseFee.divide(divisor) + BigInteger.ONE).multiply(divisor)
                    logger.info("new baseFee: $newBaseFee")
                    Thread.sleep(3000)

                    return settleAirdropNFTRecords(
                        contractAddress,
                        privateKey,
                        address,
                        gasLimit,
                        newBaseFee
                    )
                }
            } else {
                logger.error("Error Message: ${ethCall.error.message}")
                throw Exception("Failed to send transaction")
            }
        }
        val txHash = ethCall.transactionHash
        logger.info("Contract: ${contractAddress}\tmethod: $method\t txid: $txHash")

        return txHash
    }
}