package io.vault.jasper.model

import io.vault.jasper.enums.ChainType
import org.springframework.data.annotation.CreatedDate
import org.springframework.data.annotation.Id
import org.springframework.data.annotation.LastModifiedDate
import org.springframework.data.mongodb.core.index.Indexed
import org.springframework.data.mongodb.core.mapping.Document
import org.springframework.data.mongodb.core.mapping.Field
import java.math.BigDecimal
import java.time.LocalDateTime

@Document("bitlayer_campaign_4_result")
data class BitlayerCampaign4Result(
    @Id
    val id: String? = null,
    @Indexed(unique = true, background = true)
    var address: String,
    var result: Boolean = false,
    @CreatedDate
    val created: LocalDateTime = LocalDateTime.now(),
    @LastModifiedDate
    val updated: LocalDateTime = LocalDateTime.now(),
    @Field("tx_hash")
    var txHash: String? = null,
)