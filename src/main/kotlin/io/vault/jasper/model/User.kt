package io.vault.jasper.model

import org.springframework.data.annotation.CreatedDate
import org.springframework.data.annotation.Id
import org.springframework.data.annotation.LastModifiedDate
import org.springframework.data.mongodb.core.index.Indexed
import org.springframework.data.mongodb.core.mapping.Document
import org.springframework.data.mongodb.core.mapping.Field
import org.springframework.data.mongodb.core.mapping.FieldType
import java.math.BigDecimal
import java.time.LocalDateTime

@Document
data class User(
    @Id
    val id: String? = null,
    @Indexed(unique = true)
    val address: String, // 大小写敏感
    @Indexed(unique = true)
    val inviteCode: String,
    val avatar: String? = null,
    val nickname: String? = null,
    @Indexed(background = true)
    var invitedUserId: String? = null,
    /**
     * 是否拥有免权利金的权益
     */
    @Field("free_premium_benefit")
    var freePremiumBenefit: Boolean = false,
    @Field("zero_premium_benefits")
    var zeroPremiumBenefits: List<String>? = null,
    @CreatedDate
    val created: LocalDateTime = LocalDateTime.now(),
    @LastModifiedDate
    val updated: LocalDateTime = LocalDateTime.now(),
    @Field("twitter_oauth_token")
    @Indexed(background = true)
    var twitterOAuthToken: String? = null,
    @Field("twitter_account_id")
    @Indexed(background = true)
    var twitterAccountId: String? = null,
    @Field("twitter_account_name")
    var twitterAccountName: String? = null,
    @Field("twitter_account_screen_name")
    var twitterAccountScreenName: String? = null,
    @Field("twitter_task_finished")
    var twitterTaskFinished: Boolean = false,
    @Field("retweet_base_time")
    var retweetBaseTime: Long = 0L,
    @Field("discord_username")
    var discordUsername: String? = null,
    @Field("has_traded")
    var hasTraded: Boolean = false,
    @Field("claim_bitlayer_timestamp")
    var claimBitlayerTimestamp: Long = 0,
    /**
     * Discord
     */
    @Field("discord_info")
    var discordInfo: DiscordInfo? = null,

    @Field("discord_session")
    var discordSession: String? = null,
    @Field("use_kol_rebate")
    var useKolRebate: Boolean = true, // 邀请返佣是否使用KOL返佣体系

    /**
     * 交易积分
     */
    var jPoint: BigDecimal? = null,
    /**
     *  社交积分
     */
    var lPoint: BigDecimal? = null,
    /**
     * 空投积分
     */
    var sPoint: BigDecimal? = null,
    /**
     * 从推荐用户交易而获取的 sPoint
     */
    var referralSPoint: BigDecimal = BigDecimal.ZERO,
    /**
     * 有多少推荐用户
     */
    var referralCount: Int = 0,
    /**
     * rebate trading credits
     */
    @Field(targetType = FieldType.DECIMAL128)
    var rebateTradingCredits: BigDecimal = BigDecimal.ZERO
)

data class DiscordInfo(
    @Field("token_type")
    var tokenType: String,
    @Field("access_token")
    var accessToken: String,
    @Field("refresh_token")
    var refreshToken: String,
    @Field("grant_time")
    var grantTime: LocalDateTime,
    @Field("expires_in")
    var expiresIn: Long,
    @Field("in_guild")
    var inGuild: Boolean,
    @Field("user_id")
    val userId: String,
    var username: String,
    var avatar: String?
)