PORT=8111
DB_HOST=127.0.0.1
DB_PORT=27017
DB_DATABASE=jasper
DB_AUTHENTICATION_DATABASE=admin
DB_USERNAME=root
DB_PASSWORD=123456
DEVTOOLS_RESTART_ENABLED=true
REDIS_HOST=127.0.0.1
REDIS_PORT=6379
REDIS_PASSWORD=123456
# Redis连接池配置
REDIS_POOL_MAX_ACTIVE=20
REDIS_POOL_MAX_IDLE=10
REDIS_POOL_MIN_IDLE=5
REDIS_POOL_MAX_WAIT=-1ms
REDIS_POOL_EVICTION_INTERVAL=30s
LOGGING_LEVEL=info
SWAGGER_ENABLED=true
POLYGON_ENDPOINT=https://purple-chaotic-panorama.matic.quiknode.pro/8cb1f7e12302418d34af9de2230e67974930f58f/
POLYGON_CHAIN_ID=137
POLYGON_OPTIONS_CONTRACT=0xFF7708b167C7173fb25Bdf2C5df5f9924f6cC7eE
JASPER_VAULT_API_URL=https://apitest.jaspervault.io/api
KNIFE4J_ENABLED=true
# ARBITRUM_ENDPOINT=https://virtual.arbitrum.rpc.tenderly.co/198cb5e2-21b3-412b-8d25-8507aef9da6f
ARBITRUM_ENDPOINT=https://arb-mainnet.g.alchemy.com/v2/sbPwNr_CtJrYiaWenoy-UIkE5H4edS9X
ARBITRUM_CHAIN_ID=42161
# ARBITRUM_CONTRACT_OPTION_MODULE=******************************************
# ARBITRUM_CONTRACT_OPTION_SERVICE=******************************************
# ARBITRUM_CONTRACT_ENTRY_POINT=******************************************
ARBITRUM_CONTRACT_OPTION_MODULE=******************************************
ARBITRUM_CONTRACT_OPTION_SERVICE=******************************************
ARBITRUM_CONTRACT_ENTRY_POINT=******************************************
SETTLEMENT_WALLET=1ad44eaf5c984873d845384175c677838ff7f52168223a296677d31cb98459c1
# LOG_CONFIG_FILE=logback.xml
# LOGTAIL_SOURCE_TOKEN=KKMyPbHkVViK2rAVLFvWJr2f
API_V1_HOST=https://apiuat.jaspervault.io
MONITOR_SWITCH=false
SPRING_PROFILES_ACTIVE=dev
OPTION_PRICE_HOST=https://uat-jasperquotecenter.fly.dev
BASE_VAULT_FACTORY=******************************************
BASE_CONTRACT_ENTRY_POINT=******************************************
BASE_CONTRACT_OPTION_SERVICE=******************************************
BASE_DIAMOND=******************************************
BASE_ISSUANCE_MODULE=******************************************
