package io.vault.jasper.controller

import EVMAddress
import io.swagger.annotations.ApiImplicitParam
import io.swagger.annotations.ApiImplicitParams
import io.swagger.annotations.ApiOperation
import io.swagger.v3.oas.annotations.Operation
import io.vault.jasper.ApiResponse
import io.vault.jasper.annotation.ApiCache
import io.vault.jasper.annotation.InternalControllerAnnotation
import io.vault.jasper.controller.auth.TgeCampaignController.TgeCampaignInfo
import io.vault.jasper.dto.*
import io.vault.jasper.enums.ChainType
import io.vault.jasper.exception.BusinessException
import io.vault.jasper.exception.ResultEnum
import io.vault.jasper.model.*
import io.vault.jasper.repository.*
import io.vault.jasper.response.*
import io.vault.jasper.service.*
import io.vault.jasper.service.blockchain.BlockchainServiceFactory
import io.vault.jasper.service.blockchain.EvmService
import io.vault.jasper.utils.AuthUtil
import io.vault.jasper.utils.DateTimeUtil
import org.slf4j.LoggerFactory
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.data.domain.PageImpl
import org.springframework.data.domain.PageRequest
import org.springframework.data.domain.Sort
import org.springframework.data.mongodb.core.MongoTemplate
import org.springframework.data.mongodb.core.query.Criteria
import org.springframework.data.mongodb.core.query.Query
import org.springframework.data.repository.findByIdOrNull
import org.springframework.web.bind.annotation.*
import org.springframework.web.multipart.MultipartFile
import java.math.BigDecimal
import java.time.*
import javax.servlet.http.HttpServletRequest
import javax.validation.Valid


@InternalControllerAnnotation
@RestController
@RequestMapping("/internal")
// @Tag(name = "Internal Controller", description = "仅供内部服务调用，将受IP访问限制")
class InternalController @Autowired constructor(
    private val authUtil: AuthUtil,
    private val orderRepository: OrderRepository,
    private val mongoTemplate: MongoTemplate,
    private val optionOrderRepository: OptionOrderRepository,
    private val userPremiumSummaryService: UserPremiumSummaryService,
    private val userService: UserService,
    private val userRepository: UserRepository,
    private val systemService: SystemService,
    private val blockchainServiceFactory: BlockchainServiceFactory,
    private val kolRepository: KolRepository,
    private val kolRebateRecordDailySummaryRepository: KolRebateRecordDailySummaryRepository,
    private val blockchainService: BlockchainService,
    private val optionOrderInfoRepository: OptionOrderInfoRepository,
    private val optionOrderService: OptionOrderService,
    private val airdropService: AirDropService,
    private val tgeCampaignConfigRepository: TgeCampaignConfigRepository,
    private val tgeRetweetUserIdRepository: TgeRetweetUserIdRepository,
    private val userCampaignInfoRepository: UserCampaignInfoRepository,
    private val tgeCampaignRecordRepository: TgeCampaignRecordRepository,
    private val tgeCampaignParticipateRecordRepository: TgeCampaignParticipateRecordRepository,
) {

    private var logger = LoggerFactory.getLogger(this::class.java)

    @Operation(summary = "期权订单列表(分页)")
    @ApiImplicitParams(
        ApiImplicitParam(name = "orderType", value = "订单类型(逗号分隔)", dataType = "List", paramType = "query", required = false, allowMultiple = true),
        ApiImplicitParam(name = "buyers", value = "地址(逗号分隔)", dataType = "List", paramType = "query", required = false, allowMultiple = true),
        ApiImplicitParam(name = "statusIn", value = "订单状态(逗号分隔)", dataType = "List", paramType = "query", required = false, allowMultiple = true),
        ApiImplicitParam(name = "expiryDateBefore", value = "到期时间在本时间戳(秒)之前", dataType = "Long", paramType = "query", required = false),
        ApiImplicitParam(name = "page", value = "页码(起始/默认: 1)", dataType = "Int", paramType = "query", required = false),
        ApiImplicitParam(name = "sort", value = "排序(1(缺省): 顺序; others: 倒序)", dataType = "Int", paramType = "query", required = false),
    )
    @CrossOrigin
    @GetMapping("/orders/optionOrders")
    @ApiCache(
        key = "internal:orders:optionOrders",
        dynamicKey = ["orderType", "buyers", "statusIn", "expiryDateBefore", "page", "pageSize", "sort"],
        expire = 10
    )
    fun optionOrderList(
        @RequestParam orderType: List<OrderType>,
        @RequestParam(required = false) buyers: List<String>?,
        @RequestParam(required = false) statusIn: List<OptionStatus>?,
        @RequestParam(required = false) expiryDateBefore: Long?,
        @RequestParam(required = false) page: Int?,
        @RequestParam(required = false) pageSize: Int?,
        @RequestParam(required = false) sort: Int?,
        @RequestParam(required = false) sortField: String?,
    ): ApiResponse<PageResponse<OptionOrderResponse>> {
        val p = page ?: 1
        val ps = pageSize ?: 50
        val s = sort ?: 1
        val query = Query()
        val criteria = Criteria.where(OptionOrder::orderType.name).`in`(orderType).and(OptionOrder::jvault.name).`is`(true)
        // query.addCriteria(Criteria.where(OptionOrder::orderType.name).`in`(orderType))

        var sortName = sortField
        if(sortName == null){
            sortName = OptionOrder::created.name
        }

        if(!buyers.isNullOrEmpty()){
            // query.addCriteria(Criteria.where(OptionOrder::buyer.name).`in`(buyers))
            criteria.andOperator(Criteria.where(OptionOrder::buyer.name).`in`(buyers))
        }

        if (!statusIn.isNullOrEmpty()) {
            // query.addCriteria(Criteria.where(OptionOrder::status.name).`in`(statusIn))
            // criteria.andOperator(Criteria.where(OptionOrder::status.name).`in`(statusIn))
            criteria.and(OptionOrder::status.name).`in`(statusIn)
        }
        if (expiryDateBefore != null) {
            val expiryDate = DateTimeUtil.convertMilliTimestampToLocalDateTime(expiryDateBefore)
            // query.addCriteria(Criteria.where(OptionOrder::expiryDate.name).lt(expiryDate))
            criteria.andOperator(Criteria.where(OptionOrder::expiryDate.name).lt(expiryDate))
        }
        val sortDirection = when (s) {
            1 -> Sort.Direction.ASC
            else -> Sort.Direction.DESC
        }
        // query.addCriteria(Criteria.where(OptionOrder::jvault.name).`is`(true))
        // criteria.andOperator(Criteria.where(OptionOrder::jvault.name).`is`(true))

        // 月光宝盒活动中，清算订单不显示
        val orCriteria = Criteria().orOperator(
            Criteria.where(OptionOrder::usedMoonlightBox.name).isNull,
            Criteria.where(OptionOrder::liquidityType.name).ne(0)
        )
        // criteria.andOperator(orCriteria)
        query.addCriteria(criteria).addCriteria(orCriteria)

        val total = mongoTemplate.count(query, OptionOrder::class.java)
        val pageable = PageRequest.of(p - 1, ps, Sort.by(sortDirection, sortName))
        query.with(pageable)
        val resultSet = mongoTemplate.find(query, OptionOrder::class.java)
        val pageObj = PageImpl(resultSet, pageable, total)

        val result = resultSet.mapNotNull {
            it.premiumAsset ?: return@mapNotNull null
            val order = orderRepository.findByIdOrNull(it.orderId) ?: return@mapNotNull null
            OptionOrderResponse(
                id = it.id!!,
                chain = it.chain,
                orderType = it.orderType,
                buyerAddress = it.buyer!!,
                businessType = it.direction!!,
                underlyingAsset = it.underlyingAsset,
                underlyingAssetAddress = it.underlyingAssetAddress,
                totalAmount = it.amount.stripTrailingZeros().toPlainString(),
                strikePrice = order.strikePrice.stripTrailingZeros().toPlainString(),
                strikeAsset = PremiumAsset(asset = it.strikeAsset.name, address = it.strikeAssetAddress),
                premiumFees = it.premiumFeePay?.stripTrailingZeros()?.toPlainString() ?: "0",
                premiumFeesShouldPay = it.premiumFeeShouldPay?.stripTrailingZeros()?.toPlainString() ?: "0",
                premiumFeesAsset = it.premiumAsset!!,
                expiryDate = DateTimeUtil.convertLocalDateTimeToTimestamp(it.expiryDate),
                createDate = DateTimeUtil.convertLocalDateTimeToTimestamp(it.created),
                status = it.status,
                holder = it.buyerVault,
                strikeAmount = it.strikeAmount.stripTrailingZeros().toPlainString(),
                messageBeforeSign = order.contractContent?.get("message"),
                settlementHash = it.settlementHash,
                txHash = it.txHash,
                premiumFree = it.premiumFree ?: false,
                expiryInHour = it.expiryInHour,
                buyerProfit = it.buyerProfit?.stripTrailingZeros()?.toPlainString(),
                settlementPrice = it.marketPriceAtSettlement,
                usedMoonlightBox = it.usedMoonlightBox,
                bidAsset = it.bidAsset?.name,
                bidAmount = it.bidAmount?.stripTrailingZeros()?.toPlainString(),
                usedRealityStone = it.usedRealityStone ?: false,
                usedPowerStone = it.usedPowerStone ?: false,
                stoneActivityNftId = it.stoneActivityNftId,
                seller = it.seller,
                sellerVault = it.sellerVault,
                marketPriceAtSettlement = it.marketPriceAtSettlement,
                bidAssetPriceInUsdAtCreated = it.bidAssetPriceInUsdAtCreated?.stripTrailingZeros()?.toPlainString(),
            )
        }

        return ApiResponse.success(PageResponse(
            page = pageObj.number + 1,
            pageSize = pageObj.size,
            totalPage = pageObj.totalPages,
            total = pageObj.totalElements,
            hasNext = pageObj.hasNext(),
            data = result
        ))
    }

    @ApiOperation("个人邀请信息")
    @CrossOrigin
    @PostMapping("/referral/info")
    fun info(
        request: HttpServletRequest,
        @Valid @RequestBody dto: ReferralInfoDTO
    ): ApiResponse<ReferralInfoResponse> {
        var user: User? = null 
        
        if(dto.address != null) {
            user = userRepository.findByAddressIgnoreCase(dto.address)
        }

        if(dto.inviteCode != null) {
            user = userRepository.findByInviteCode(dto.inviteCode)
        }

        if(user == null){
            throw BusinessException(ResultEnum.USER_NOT_FOUND)
        }

        val kol = kolRepository.findFirstByWallet(user.address) ?: throw BusinessException(ResultEnum.KOL_NOT_FOUND)

        val referees = userService.getRefereesAddress(user)

        val totalPremium = userPremiumSummaryService.getUserPremiumSummaryTotal(
            referees,
            UserPremiumSummary::totalPremium.name
        )

        val parentUser = userService.findUserParent(user)
        val parentAddress = parentUser?.address
        val parentReferral = parentUser?.inviteCode

        val contractAddress = "******************************************"
        val evmService = blockchainServiceFactory.getBlockchainService(ChainType.ARBITRUM) as EvmService

        val totalRebate = blockchainService.getKolTotalRebate(
            evmService.evmUtil.web3j,
            contractAddress,
            kol.wallet
        )

        val unclaimRebate = blockchainService.getKolUnclaimRebate(
            evmService.evmUtil.web3j,
            contractAddress,
            kol.wallet
        )

        return ApiResponse.success(
            ReferralInfoResponse(
                referees.size,
                totalPremium.stripTrailingZeros().toPlainString(),
                user.inviteCode,
                parentAddress,
                parentReferral,
                totalRebate = totalRebate.stripTrailingZeros().toPlainString(),
                unclaimRebate = unclaimRebate.stripTrailingZeros().toPlainString(),
                userLevel = kol.level,
                address = user.address
            )
        )
    }

    @ApiOperation("Referral Directory")
    @CrossOrigin
    @PostMapping("/referral/premium_list")
    fun premiumList(
        request: HttpServletRequest,
        @Valid @RequestBody dto: ReferralPremiumDTO
    ): ApiResponse<PageResponse<ReferralPremiumInfoResponse>> {

        var user: User? = null

        if(dto.address != null) {
            user = userRepository.findByAddressIgnoreCase(dto.address)
        }

        if(dto.inviteCode != null) {
            user = userRepository.findByInviteCode(dto.inviteCode)
        }

        if(user == null){
            throw BusinessException(ResultEnum.USER_NOT_FOUND)
        }

        val referees = userService.getRefereesAddress(user)

        val p = dto.page
        val ps = dto.pageSize
        val pageable = PageRequest.of(p - 1, ps, Sort.by(Sort.Order.desc(UserPremiumSummary::registerTime.name)))

        val query = Query()
        query.addCriteria(Criteria.where(UserPremiumSummary::evmAddress.name).`in`(referees))

        if (dto.startTime != null && dto.endTime != null) {
            val start = DateTimeUtil.convertMilliTimestampToLocalDateTime(dto.startTime)
            val end = DateTimeUtil.convertMilliTimestampToLocalDateTime(dto.endTime)
            query.addCriteria(Criteria.where(UserPremiumSummary::registerTime.name).gte(start).lt(end))
        }

        val total = mongoTemplate.count(query, UserPremiumSummary::class.java)
        query.with(pageable)
        val resultSet = mongoTemplate.find(query, UserPremiumSummary::class.java)
        val optionsPage = PageImpl(resultSet, pageable, total)

        val systemParameter = systemService.getParameter()

        //val markupBase = systemParameter.premiumPriceRatePercentage.add(BigDecimal(100))
        //val markupRatio = systemParameter.premiumPriceRatePercentage.divide(markupBase, 18, BigDecimal.ROUND_HALF_UP)

        // 权利金费率算法再次调整。 2025-01-17
        val markupRatio = systemParameter.premiumPriceRatePercentage.movePointLeft(2)

        val optionsList = optionsPage.content.mapNotNull {

            val orderCount = optionOrderRepository.findByBuyerAndStatus(
                it.evmAddress,
                OptionStatus.SETTLED
            ).size

            ReferralPremiumInfoResponse(
                address = it.evmAddress,
                premiumAmount = it.totalPremium.stripTrailingZeros().toPlainString(),
                protocolFee = (it.totalPremium.multiply(markupRatio)).stripTrailingZeros().toPlainString(),
                createDate = DateTimeUtil.convertLocalDateTimeToTimestamp(it.registerTime),
                optionVolume = it.totalVolume.stripTrailingZeros().toPlainString(),
                optionOrderCount = orderCount.toString()
            )
        }

        return ApiResponse.success(
            PageResponse(
                total = optionsPage.totalElements,
                page = p,
                pageSize = ps,
                totalPage = optionsPage.totalPages,
                hasNext = optionsPage.hasNext(),
                data = optionsList
            )
        )
    }

    @ApiOperation("Rebate Records")
    @CrossOrigin
    @ApiCache(key = "internal:rebateRecord", dynamicKey = ["address", "lastDays", "inviteCode", "page", "pageSize"], expire = 60)
    @GetMapping("/referral/rebateRecord")
    fun rebateRecord(
        @RequestParam address: String? = null,
        @RequestParam(required = false) lastDays: Int? = null,
        @RequestParam(required = false) inviteCode: String? = null,
        @RequestParam page: Int,
        @RequestParam pageSize: Int
    ): ApiResponse<PageResponse<KolRebateRecordResponse>> {

        var kol: Kol? = null
        if(address != null){
            kol = kolRepository.findFirstByWallet(address)
        }

        if(inviteCode != null){
            val user = userRepository.findByInviteCode(inviteCode) ?: throw BusinessException(ResultEnum.USER_NOT_FOUND)
            kol = kolRepository.findFirstByWallet(user.address)
        }

        if(kol == null){
            return ApiResponse.success(PageResponse(
                page = page,
                pageSize = pageSize,
                totalPage = 0,
                total = 0,
                hasNext = false,
                data = emptyList()
            ))
        }

        val pageRequest = PageRequest.of(page - 1, pageSize, Sort.by(Sort.Order.desc(KolRebateRecordDailySummary::recordDate.name)))
        val pageObj = if (lastDays == null) {
            kolRebateRecordDailySummaryRepository.findByKolId(kol.id!!, pageRequest)
        } else {
            // 获取今天的LocalDate对象
            val today = LocalDate.now()
            // 往前减N日
            val startDate = today.minusDays(lastDays.toLong())
            kolRebateRecordDailySummaryRepository.findByKolIdAndRecordDateIsGreaterThanEqual(kol.id!!, startDate, pageRequest)
        }

        return ApiResponse.success(PageResponse(
            page = page,
            pageSize = pageSize,
            totalPage = pageObj.totalPages,
            total = pageObj.totalElements,
            hasNext = pageObj.hasNext(),
            data = pageObj.content.map {
                KolRebateRecordResponse(
                    date = it.recordDate.atStartOfDay(ZoneId.systemDefault()).toInstant().toEpochMilli(),
                    dailyTotalRebate = it.totalRebate.stripTrailingZeros().toPlainString(),
                    dailyActive = it.activeUsers
                )
            }
        ))
    }

    @CrossOrigin
    @ApiCache(key = "internal:orders:order_info", dynamicKey = ["chain", "onChainOrderId"], expire = 30)
    @GetMapping("/orders/order_info")
    fun optionOrderInfo(
        @RequestParam(required = true) chain: ChainType,
        @RequestParam(required = true) onChainOrderId: String,
    ): ApiResponse<OptionOrderResponse> {

        val it = optionOrderRepository.findByChainAndOnChainOrderId(chain, onChainOrderId)
            ?: throw BusinessException(ResultEnum.ORDER_NOT_FOUND)

        val order = orderRepository.findByIdOrNull(it.orderId) ?: throw BusinessException(ResultEnum.ORDER_NOT_FOUND)
        val response = OptionOrderResponse(
                id = it.id!!,
                chain = it.chain,
                orderType = it.orderType,
                buyerAddress = it.buyer!!,
                businessType = it.direction!!,
                underlyingAsset = it.underlyingAsset,
                underlyingAssetAddress = it.underlyingAssetAddress,
                totalAmount = it.amount.stripTrailingZeros().toPlainString(),
                strikePrice = order.strikePrice.stripTrailingZeros().toPlainString(),
                strikeAsset = PremiumAsset(asset = it.strikeAsset.name, address = it.strikeAssetAddress),
                premiumFees = it.premiumFeePay?.stripTrailingZeros()?.toPlainString() ?: "0",
                premiumFeesShouldPay = it.premiumFeeShouldPay?.stripTrailingZeros()?.toPlainString() ?: "0",
                premiumFeesAsset = it.premiumAsset!!,
                expiryDate = DateTimeUtil.convertLocalDateTimeToTimestamp(it.expiryDate),
                createDate = DateTimeUtil.convertLocalDateTimeToTimestamp(it.created),
                status = it.status,
                holder = it.buyerVault,
                strikeAmount = it.strikeAmount.stripTrailingZeros().toPlainString(),
                messageBeforeSign = order.contractContent?.get("message"),
                settlementHash = it.settlementHash,
                txHash = it.txHash,
                premiumFree = it.premiumFree ?: false,
                expiryInHour = it.expiryInHour,
                buyerProfit = it.buyerProfit?.stripTrailingZeros()?.toPlainString(),
                settlementPrice = it.marketPriceAtSettlement,
                usedMoonlightBox = it.usedMoonlightBox,
                bidAsset = it.bidAsset?.name,
                bidAmount = it.bidAmount?.stripTrailingZeros()?.toPlainString(),
                usedRealityStone = it.usedRealityStone ?: false,
                usedPowerStone = it.usedPowerStone ?: false,
                stoneActivityNftId = it.stoneActivityNftId,
                seller = it.seller,
                sellerVault = it.sellerVault,
                marketPriceAtSettlement = it.marketPriceAtSettlement,
                bidAssetPriceInUsdAtCreated = it.bidAssetPriceInUsdAtCreated?.stripTrailingZeros()?.toPlainString(),
            )
        return ApiResponse.success(response)
    }

    @ApiOperation("上报系统提交的订单")
    @CrossOrigin
    @PostMapping("/submit_order")
    fun submitOrder(
        request: HttpServletRequest,
        @RequestBody dto: SubmitSystemOrderDTO
    ): ApiResponse<Boolean> {

        authUtil.filterIPBlacklist(request)
        val channel = authUtil.authPartner(request)

        if(channel != UserChannel.BTC_FI_BACKEND){
            throw BusinessException(ResultEnum.INVALID_AUTHORIZATION_CODE)
        }

        val existingInfo = optionOrderInfoRepository.findByTxHash(dto.txHash)
        if(existingInfo == null){
            optionOrderInfoRepository.save(
                OptionOrderInfo(
                    txHash = dto.txHash,
                    channel = channel,
                    buyer = dto.address,
                    systemBuyer = dto.systemAddress,
                    buyerVault = dto.aaVaultAddress
                )
            )
        } else {
            existingInfo.channel = channel
            existingInfo.buyer = dto.address
            existingInfo.systemBuyer = dto.systemAddress
            existingInfo.buyerVault = dto.aaVaultAddress
            existingInfo.synced = null

            optionOrderInfoRepository.save(existingInfo)
        }

        val optionOrders = optionOrderRepository.findByTxHash(dto.txHash)
        for(oo in optionOrders){
            val updateFields = mutableMapOf<String, Any?>(
                OptionOrder::buyer.name to dto.address,
                OptionOrder::systemBuyer.name to dto.systemAddress,
                OptionOrder::channel.name to channel,
                OptionOrder::usedTimeStone.name to true,
                OptionOrder::stoneActivityNftId.name to "11",
                OptionOrder::buyerVault.name to oo.buyerVault
            )

            try {
                optionOrderService.updateFields(updateFields, oo.id!!)
            } catch (e: Exception){
                logger.error("Error in submitOrder ${dto.txHash}: ${e.message}")
            }
        }

        return ApiResponse.success(true)
    }

    @ApiOperation("Get TGE Campaign Information")
    @CrossOrigin
    @ApiCache(key = "tge:index", dynamicKey = ["address"], expire = 2)
    @GetMapping("/tge_campaign_info")
    fun getTgeCampaignInfo(
        request: HttpServletRequest,
        @RequestParam("address") @EVMAddress address: String
    ): ApiResponse<TgeCampaignInfo> {

        authUtil.filterIPBlacklist(request)
        //val user = authUtil.auth(request, address)
        val user = userRepository.findByAddressIgnoreCase(address) ?: throw BusinessException(ResultEnum.USER_NOT_FOUND)
        val userCampaignInfo = airdropService.getUserCampaignInfo(user)

        // 获取活动配置
        val config = tgeCampaignConfigRepository.findAll()[0]

        // 检查 Twitter 任务是否完成
        if(userCampaignInfo.retweetTgeTime == 0L &&
            config.isInCampaignPeriod(LocalDateTime.now()) &&
            user.twitterAccountId != null) {
            tgeRetweetUserIdRepository.findByRetweetUserId(user.twitterAccountId!!)?.let {
                userCampaignInfo.retweetTgeTime = System.currentTimeMillis()
                userCampaignInfoRepository.save(userCampaignInfo)
            }
        }

        // 检查Discord任务
        val discordLevel = airdropService.getDiscordLevel(user)
        val userRecordsCount = tgeCampaignRecordRepository.countByAddressIgnoreCase(user.address)

        // 检查BTR任务
        if(userCampaignInfo.tgeBitlayerTradeCount == 0 &&
            config.isInCampaignPeriod(LocalDateTime.now()) &&
            config.canDistributeBtr()){
            val count = tgeCampaignRecordRepository.countByAddressIgnoreCaseAndChain(
                user.address,
                ChainType.BITLAYER
            )

            userCampaignInfo.tgeBitlayerTradeCount = count.toInt()
            userCampaignInfoRepository.save(userCampaignInfo)
        }

        var userBtr = 0
        var canClaimTimeStone = false
        var hasClaimedTimeStone = false

        val campaignParticipateRecords = tgeCampaignParticipateRecordRepository.findByAddressIgnoreCase(
            user.address
        )

        if(campaignParticipateRecords.isNotEmpty()){
            val campaignParticipateRecord = campaignParticipateRecords[0]
            if(campaignParticipateRecord.status == TgeCampaignParticipateRecordStatus.CREATED){
                canClaimTimeStone = true
            }

            if(campaignParticipateRecord.status == TgeCampaignParticipateRecordStatus.SUCCESS ||
                campaignParticipateRecord.status == TgeCampaignParticipateRecordStatus.CLAIMED){
                hasClaimedTimeStone = true
            }

            if(campaignParticipateRecord.bitlayerTradeOptionOrderId != null){
                userBtr = config.btrAmountPerOrder
            }
        }

        val info = TgeCampaignInfo(
            totalParticipationCount = config.totalParticipation,
            isTwitterFollowed = (user.twitterAccountId != null),
            isTwitterRetweet = (userCampaignInfo.retweetTgeTime > 0L),
            isJoinDiscord = user.discordInfo?.inGuild ?: false,
            discordLevel = discordLevel,
            totalTradeCount = userRecordsCount.toInt(),
            retweetLink = config.retweetLink,
            totalBtr = config.totalBtr,
            totalDistributeBtr = config.totalDistributeBtr,
            getBtrCount = userBtr,
            canClaimTimeStone = canClaimTimeStone,
            hasClaimedTimeStone = hasClaimedTimeStone
        )

        return ApiResponse.success(info)
    }
}