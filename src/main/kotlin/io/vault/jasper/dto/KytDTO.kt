package io.vault.jasper.dto

import EVMAddress
import io.swagger.annotations.ApiModel
import io.swagger.annotations.ApiModelProperty
import io.vault.jasper.enums.ChainType
import io.vault.jasper.model.UserChannel

@ApiModel("KYT 信息")
data class KytDTO(
    @ApiModelProperty("链", required = true)
    val chain: ChainType,
    @ApiModelProperty("钱包地址", required = true)
    @field:EVMAddress
    val address: String,
    @ApiModelProperty("用户渠道", required = false)
    val channel: UserChannel?
)
