package io.vault.jasper.model


import org.springframework.data.annotation.Id
import org.springframework.data.mongodb.core.index.Indexed
import org.springframework.data.mongodb.core.mapping.Document
import org.springframework.data.mongodb.core.mapping.Field
import org.springframework.data.mongodb.core.mapping.FieldType
import java.math.BigDecimal
import java.time.LocalDateTime

@Document("user_point_daily_summary")
data class UserPointDailySummary(
    @Id
    val id: String? = null,
    @Indexed(background = true)
    @Field("date_string")
    val dateString: String, // 日期字符串
    @Indexed(background = true)
    val address: String, // 钱包地址
    @Field(name = "premium_point", targetType = FieldType.DECIMAL128)
    var premiumPoint: BigDecimal = BigDecimal.ZERO, // 权利金积分
    @Field(name = "loyalty_point", targetType = FieldType.DECIMAL128)
    var loyaltyPoint: BigDecimal = BigDecimal.ZERO, // 忠诚积分
    var streak: Int = 0, // 连续多少天有做交易
    @Field(name = "premium_and_fees", targetType = FieldType.DECIMAL128)
    var premiumFees: BigDecimal = BigDecimal.ZERO, // 支付的权利金和费用, USDT 为单位
    var counter: Int = 1, // 交易次数
    @Indexed(background = true)
    @Field(name = "user_id")
    var userId: String? = null, // 用户 ID
    @Field(name = "register_time")
    var registerTime: LocalDateTime? = null, // 钱包注册时间
    @Field(name = "coin98_streak")
    var coin98Streak: Int? = null, // Coin98 活动连续多少天有做交易
    @Field(name = "fix_premium")
    var fixPremium: Boolean? = null
)