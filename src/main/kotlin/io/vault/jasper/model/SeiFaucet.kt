package io.vault.jasper.model

import io.vault.jasper.enums.ChainType
import org.springframework.data.annotation.Id
import org.springframework.data.annotation.LastModifiedDate
import org.springframework.data.mongodb.core.mapping.Document
import org.springframework.data.mongodb.core.index.Indexed
import java.time.LocalDateTime
import java.util.*

@Document
data class SeiFaucet(
    @Id
    val id: String? = null,
    @Indexed(unique = true, background = true)
    val address: String,
    var lastUpdate: Date = Date()
)