[{"inputs": [], "stateMutability": "nonpayable", "type": "constructor"}, {"anonymous": false, "inputs": [{"indexed": false, "internalType": "address", "name": "previousAdmin", "type": "address"}, {"indexed": false, "internalType": "address", "name": "newAdmin", "type": "address"}], "name": "Admin<PERSON><PERSON>ed", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "beacon", "type": "address"}], "name": "BeaconUpgraded", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": false, "internalType": "uint8", "name": "version", "type": "uint8"}], "name": "Initialized", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": false, "internalType": "uint64", "name": "_orderID", "type": "uint64"}, {"components": [{"internalType": "address", "name": "holder", "type": "address"}, {"internalType": "address", "name": "writer", "type": "address"}, {"internalType": "address", "name": "recipient", "type": "address"}, {"internalType": "uint256", "name": "quantity", "type": "uint256"}, {"internalType": "uint256", "name": "settingsIndex", "type": "uint256"}, {"internalType": "uint256", "name": "productTypeIndex", "type": "uint256"}, {"internalType": "uint256", "name": "oracleIndex", "type": "uint256"}, {"internalType": "address", "name": "nftFreeOption", "type": "address"}, {"components": [{"internalType": "uint256", "name": "id", "type": "uint256"}, {"internalType": "uint256", "name": "chainId", "type": "uint256"}, {"internalType": "uint64", "name": "productType", "type": "uint64"}, {"internalType": "address", "name": "optionAsset", "type": "address"}, {"internalType": "uint256", "name": "strikePrice", "type": "uint256"}, {"internalType": "address", "name": "strikeAsset", "type": "address"}, {"internalType": "uint256", "name": "strikeAmount", "type": "uint256"}, {"internalType": "address", "name": "lockAsset", "type": "address"}, {"internalType": "uint256", "name": "lockAmount", "type": "uint256"}, {"internalType": "uint256", "name": "expireDate", "type": "uint256"}, {"internalType": "uint256", "name": "lockDate", "type": "uint256"}, {"internalType": "uint8", "name": "optionType", "type": "uint8"}, {"internalType": "address", "name": "premiumAsset", "type": "address"}, {"internalType": "uint256", "name": "premiumFee", "type": "uint256"}, {"internalType": "uint256", "name": "timestamp", "type": "uint256"}, {"internalType": "bytes[]", "name": "oracleSign", "type": "bytes[]"}], "internalType": "struct IOptionModuleV2.PremiumOracleSign", "name": "premiumSign", "type": "tuple"}, {"internalType": "uint8", "name": "optionSourceType", "type": "uint8"}, {"internalType": "bool", "name": "liquidationToEOA", "type": "bool"}, {"internalType": "uint256", "name": "offerID", "type": "uint256"}], "indexed": false, "internalType": "struct IOptionModuleV2.ManagedOrder", "name": "_info", "type": "tuple"}, {"indexed": false, "internalType": "uint256", "name": "_premiumAmount", "type": "uint256"}, {"indexed": false, "internalType": "uint256", "name": "_freePremiumAmount", "type": "uint256"}], "name": "OptionPremiun", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "implementation", "type": "address"}], "name": "Upgraded", "type": "event"}, {"inputs": [], "name": "diamond", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"components": [{"internalType": "address", "name": "holder", "type": "address"}, {"internalType": "address", "name": "writer", "type": "address"}, {"internalType": "address", "name": "recipient", "type": "address"}, {"internalType": "uint256", "name": "quantity", "type": "uint256"}, {"internalType": "uint256", "name": "settingsIndex", "type": "uint256"}, {"internalType": "uint256", "name": "productTypeIndex", "type": "uint256"}, {"internalType": "uint256", "name": "oracleIndex", "type": "uint256"}, {"internalType": "address", "name": "nftFreeOption", "type": "address"}, {"components": [{"internalType": "uint256", "name": "id", "type": "uint256"}, {"internalType": "uint256", "name": "chainId", "type": "uint256"}, {"internalType": "uint64", "name": "productType", "type": "uint64"}, {"internalType": "address", "name": "optionAsset", "type": "address"}, {"internalType": "uint256", "name": "strikePrice", "type": "uint256"}, {"internalType": "address", "name": "strikeAsset", "type": "address"}, {"internalType": "uint256", "name": "strikeAmount", "type": "uint256"}, {"internalType": "address", "name": "lockAsset", "type": "address"}, {"internalType": "uint256", "name": "lockAmount", "type": "uint256"}, {"internalType": "uint256", "name": "expireDate", "type": "uint256"}, {"internalType": "uint256", "name": "lockDate", "type": "uint256"}, {"internalType": "uint8", "name": "optionType", "type": "uint8"}, {"internalType": "address", "name": "premiumAsset", "type": "address"}, {"internalType": "uint256", "name": "premiumFee", "type": "uint256"}, {"internalType": "uint256", "name": "timestamp", "type": "uint256"}, {"internalType": "bytes[]", "name": "oracleSign", "type": "bytes[]"}], "internalType": "struct IOptionModuleV2.PremiumOracleSign", "name": "premiumSign", "type": "tuple"}, {"internalType": "uint8", "name": "optionSourceType", "type": "uint8"}, {"internalType": "bool", "name": "liquidationToEOA", "type": "bool"}, {"internalType": "uint256", "name": "offerID", "type": "uint256"}], "internalType": "struct IOptionModuleV2.ManagedOrder", "name": "_info", "type": "tuple"}, {"components": [{"internalType": "bool", "name": "isOpen", "type": "bool"}, {"internalType": "enum IOptionFacet.OrderType", "name": "orderType", "type": "uint8"}, {"internalType": "address", "name": "writer", "type": "address"}, {"internalType": "address", "name": "lockAsset", "type": "address"}, {"internalType": "address", "name": "underlyingAsset", "type": "address"}, {"internalType": "enum IOptionFacet.UnderlyingAssetType", "name": "lockAssetType", "type": "uint8"}, {"internalType": "uint256", "name": "underlyingNftID", "type": "uint256"}, {"internalType": "enum IOptionFacet.LiquidateMode", "name": "liquidateMode", "type": "uint8"}, {"internalType": "address", "name": "strikeAsset", "type": "address"}, {"internalType": "uint256", "name": "maximum", "type": "uint256"}, {"internalType": "enum IOptionFacetV2.PremiumOracleType", "name": "premiumOracleType", "type": "uint8"}, {"internalType": "address[]", "name": "premiumAssets", "type": "address[]"}, {"internalType": "uint64[]", "name": "productTypes", "type": "uint64[]"}, {"internalType": "uint256[]", "name": "premiumFloorAMMs", "type": "uint256[]"}, {"internalType": "uint256[]", "name": "premiumRates", "type": "uint256[]"}, {"internalType": "uint256", "name": "maxUnderlyingAssetAmount", "type": "uint256"}, {"internalType": "uint256", "name": "minUnderlyingAssetAmount", "type": "uint256"}, {"internalType": "uint256", "name": "minQuantity", "type": "uint256"}, {"internalType": "uint256", "name": "offerID", "type": "uint256"}], "internalType": "struct IOptionFacetV2.ManagedOptionsSettings", "name": "setting", "type": "tuple"}], "name": "getFeeToPayManagedOrder", "outputs": [{"internalType": "uint256", "name": "premiumFeeToPay", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"components": [{"internalType": "address", "name": "holder", "type": "address"}, {"internalType": "address", "name": "writer", "type": "address"}, {"internalType": "address", "name": "recipient", "type": "address"}, {"internalType": "uint256", "name": "quantity", "type": "uint256"}, {"internalType": "uint256", "name": "settingsIndex", "type": "uint256"}, {"internalType": "uint256", "name": "productTypeIndex", "type": "uint256"}, {"internalType": "uint256", "name": "oracleIndex", "type": "uint256"}, {"internalType": "address", "name": "nftFreeOption", "type": "address"}, {"components": [{"internalType": "uint256", "name": "id", "type": "uint256"}, {"internalType": "uint256", "name": "chainId", "type": "uint256"}, {"internalType": "uint64", "name": "productType", "type": "uint64"}, {"internalType": "address", "name": "optionAsset", "type": "address"}, {"internalType": "uint256", "name": "strikePrice", "type": "uint256"}, {"internalType": "address", "name": "strikeAsset", "type": "address"}, {"internalType": "uint256", "name": "strikeAmount", "type": "uint256"}, {"internalType": "address", "name": "lockAsset", "type": "address"}, {"internalType": "uint256", "name": "lockAmount", "type": "uint256"}, {"internalType": "uint256", "name": "expireDate", "type": "uint256"}, {"internalType": "uint256", "name": "lockDate", "type": "uint256"}, {"internalType": "uint8", "name": "optionType", "type": "uint8"}, {"internalType": "address", "name": "premiumAsset", "type": "address"}, {"internalType": "uint256", "name": "premiumFee", "type": "uint256"}, {"internalType": "uint256", "name": "timestamp", "type": "uint256"}, {"internalType": "bytes[]", "name": "oracleSign", "type": "bytes[]"}], "internalType": "struct IOptionModuleV2.PremiumOracleSign", "name": "premiumSign", "type": "tuple"}, {"internalType": "uint8", "name": "optionSourceType", "type": "uint8"}, {"internalType": "bool", "name": "liquidationToEOA", "type": "bool"}, {"internalType": "uint256", "name": "offerID", "type": "uint256"}], "internalType": "struct IOptionModuleV2.ManagedOrder", "name": "_info", "type": "tuple"}, {"components": [{"internalType": "bool", "name": "isOpen", "type": "bool"}, {"internalType": "enum IOptionFacet.OrderType", "name": "orderType", "type": "uint8"}, {"internalType": "address", "name": "writer", "type": "address"}, {"internalType": "address", "name": "lockAsset", "type": "address"}, {"internalType": "address", "name": "underlyingAsset", "type": "address"}, {"internalType": "enum IOptionFacet.UnderlyingAssetType", "name": "lockAssetType", "type": "uint8"}, {"internalType": "uint256", "name": "underlyingNftID", "type": "uint256"}, {"internalType": "enum IOptionFacet.LiquidateMode", "name": "liquidateMode", "type": "uint8"}, {"internalType": "address", "name": "strikeAsset", "type": "address"}, {"internalType": "uint256", "name": "maximum", "type": "uint256"}, {"internalType": "enum IOptionFacetV2.PremiumOracleType", "name": "premiumOracleType", "type": "uint8"}, {"internalType": "address[]", "name": "premiumAssets", "type": "address[]"}, {"internalType": "uint64[]", "name": "productTypes", "type": "uint64[]"}, {"internalType": "uint256[]", "name": "premiumFloorAMMs", "type": "uint256[]"}, {"internalType": "uint256[]", "name": "premiumRates", "type": "uint256[]"}, {"internalType": "uint256", "name": "maxUnderlyingAssetAmount", "type": "uint256"}, {"internalType": "uint256", "name": "minUnderlyingAssetAmount", "type": "uint256"}, {"internalType": "uint256", "name": "minQuantity", "type": "uint256"}, {"internalType": "uint256", "name": "offerID", "type": "uint256"}], "internalType": "struct IOptionFacetV2.ManagedOptionsSettings", "name": "setting", "type": "tuple"}, {"internalType": "address", "name": "eth", "type": "address"}, {"internalType": "address", "name": "weth", "type": "address"}, {"internalType": "uint256", "name": "premiumAssetPrice", "type": "uint256"}, {"internalType": "uint256", "name": "premiumDecimals", "type": "uint256"}], "name": "getPremiumFloorAmount", "outputs": [{"internalType": "uint256", "name": "premiumFloorAmount", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "_diamond", "type": "address"}, {"internalType": "contract IOptionModuleV2", "name": "_optionModuleV2", "type": "address"}], "name": "initialize", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "optionModuleV2", "outputs": [{"internalType": "contract IOptionModuleV2", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "optionModuleV2Handle", "outputs": [{"internalType": "contract IOptionModuleV2Handle", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "optionService", "outputs": [{"internalType": "contract IOptionService", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "priceOracleModule", "outputs": [{"internalType": "contract IPriceOracle", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "proxiableUUID", "outputs": [{"internalType": "bytes32", "name": "", "type": "bytes32"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "contract IOptionModuleV2", "name": "_optionModuleV2", "type": "address"}, {"internalType": "contract IOptionService", "name": "_optionService", "type": "address"}, {"internalType": "contract IOptionModuleV2Handle", "name": "_optionModuleV2Handle", "type": "address"}, {"internalType": "contract IPriceOracle", "name": "_priceOracleModule", "type": "address"}], "name": "setConfigAddr", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"components": [{"internalType": "address", "name": "holder", "type": "address"}, {"internalType": "address", "name": "writer", "type": "address"}, {"internalType": "address", "name": "recipient", "type": "address"}, {"internalType": "uint256", "name": "quantity", "type": "uint256"}, {"internalType": "uint256", "name": "settingsIndex", "type": "uint256"}, {"internalType": "uint256", "name": "productTypeIndex", "type": "uint256"}, {"internalType": "uint256", "name": "oracleIndex", "type": "uint256"}, {"internalType": "address", "name": "nftFreeOption", "type": "address"}, {"components": [{"internalType": "uint256", "name": "id", "type": "uint256"}, {"internalType": "uint256", "name": "chainId", "type": "uint256"}, {"internalType": "uint64", "name": "productType", "type": "uint64"}, {"internalType": "address", "name": "optionAsset", "type": "address"}, {"internalType": "uint256", "name": "strikePrice", "type": "uint256"}, {"internalType": "address", "name": "strikeAsset", "type": "address"}, {"internalType": "uint256", "name": "strikeAmount", "type": "uint256"}, {"internalType": "address", "name": "lockAsset", "type": "address"}, {"internalType": "uint256", "name": "lockAmount", "type": "uint256"}, {"internalType": "uint256", "name": "expireDate", "type": "uint256"}, {"internalType": "uint256", "name": "lockDate", "type": "uint256"}, {"internalType": "uint8", "name": "optionType", "type": "uint8"}, {"internalType": "address", "name": "premiumAsset", "type": "address"}, {"internalType": "uint256", "name": "premiumFee", "type": "uint256"}, {"internalType": "uint256", "name": "timestamp", "type": "uint256"}, {"internalType": "bytes[]", "name": "oracleSign", "type": "bytes[]"}], "internalType": "struct IOptionModuleV2.PremiumOracleSign", "name": "premiumSign", "type": "tuple"}, {"internalType": "uint8", "name": "optionSourceType", "type": "uint8"}, {"internalType": "bool", "name": "liquidationToEOA", "type": "bool"}, {"internalType": "uint256", "name": "offerID", "type": "uint256"}], "internalType": "struct IOptionModuleV2.ManagedOrder", "name": "_info", "type": "tuple"}], "name": "submitManagedOrderV4", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "newImplementation", "type": "address"}], "name": "upgradeTo", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "newImplementation", "type": "address"}, {"internalType": "bytes", "name": "data", "type": "bytes"}], "name": "upgradeToAndCall", "outputs": [], "stateMutability": "payable", "type": "function"}]