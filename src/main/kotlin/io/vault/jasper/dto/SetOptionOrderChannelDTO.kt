package io.vault.jasper.dto

import io.swagger.annotations.ApiModel
import io.swagger.annotations.ApiModelProperty
import io.vault.jasper.model.OptionOrderProduct
import io.vault.jasper.model.UserWallet

@ApiModel("设置期权订单渠道标识请求")
data class SetOptionOrderChannelDTO(
    @ApiModelProperty("链上交易hash", required = true)
    val txHash: String,
    @ApiModelProperty("请求时间", required = false)
    val timestamp: Long? = null,
    @ApiModelProperty("订单产品类型", required = false)
    val product: OptionOrderProduct? = null,
    @ApiModelProperty("订单由哪个钱包下单", required = false)
    val wallet: UserWallet? = null,
    @ApiModelProperty("从哪个渠道引流过来的订单", required = false)
    var from: String? = null,
    @ApiModelProperty("utm标签 - 流量来源", required = false, name = "utm_source")
    var utmSource: String? = null,
    @ApiModelProperty("utm标签 - 媒介", required = false, name = "utm_medium")
    var utmMedium: String? = null,
    @ApiModelProperty("utm标签 - 营销活动", required = false, name = "utm_campaign")
    var utmCampaign: String? = null,
    @ApiModelProperty("utm标签 - term", required = false, name = "utm_term")
    var utmTerm: String? = null,
    @ApiModelProperty("utm标签 - content", required = false, name = "utm_content")
    var utmContent: String? = null
)
