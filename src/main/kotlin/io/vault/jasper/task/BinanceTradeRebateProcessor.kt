package io.vault.jasper.task

import io.vault.jasper.model.*
import io.vault.jasper.repository.*
import io.vault.jasper.service.*
import io.vault.jasper.service.blockchain.BitlayerService
import org.slf4j.LoggerFactory
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.beans.factory.annotation.Value
import org.springframework.context.annotation.Profile
import org.springframework.data.repository.findByIdOrNull
import org.springframework.scheduling.annotation.Scheduled
import org.springframework.stereotype.Service
import java.math.BigDecimal
import java.math.BigInteger
import java.util.*

@Service
@Profile("prod", "test")
class BinanceTradeRebateProcessor @Autowired constructor(
    private val binanceCampaignService: BinanceCampaignService,
    private val binanceTradeRebateRecordRepository: BinanceTradeRebateRecordRepository,
    private val userRepository: UserRepository,
    private val bitlayerService: BitlayerService,
    private val airDropService: AirDropService,
    private val optionOrderRepository: OptionOrderRepository,
) {

    private val logger = LoggerFactory.getLogger(this::class.java)

    @Value("\${schedule_task_on}")
    lateinit var scheduleOn: String

    // ******************************************
    private val reportWalletKey = "eb4f2b99ed9bc6bf014578ec87f487aa3d12e629c4b470279e63063f02fc3e4c"

    // 每分钟执行一次
    //@Scheduled(fixedDelay = 10 * 1000, initialDelay = 30 * 1000)
    fun run() {

        if(scheduleOn == "false"){
            return
        }

        try {
            exec()
        } catch (e: Exception) {
            logger.error(e.message, e)
        }
    }

    private fun exec() {

        logger.info("Binance first trade processor start")

        val parameter = binanceCampaignService.getCampaignParameter()
        if(parameter.firstTradeRebateTaskSwitch == false){
            logger.info("Binance first trade rebate task switch is off")
            return
        }

        //Send rebate amount to user address
        handlePendingRecords()

        val allNftFilledRecords = binanceTradeRebateRecordRepository.findByStatus(
            BinanceTradeRebateRecordStatus.NFT_FILLED
        )

        logger.info("Binance first trade nft filled records size: ${allNftFilledRecords.size}")

        allNftFilledRecords.forEach{
            if(it.settleNftTxId == null){

                try {
                    val txHash = bitlayerService.settleBinanceWhiteList(
                        parameter.nftContractAddress,
                        reportWalletKey,
                        it.buyerAddress,
                        200000
                    )

                    it.settleNftTxId = txHash

                    if(it.settleMoonlightBoxTxId == null){
                        it.status = BinanceTradeRebateRecordStatus.CREATED
                    } else {
                        it.status = BinanceTradeRebateRecordStatus.SETTLED
                    }

                    binanceTradeRebateRecordRepository.save(it)
                    Thread.sleep(15 * 1000)

                } catch (e: Exception) {
                    logger.error("Binance first trade settleBinanceWhiteList failed", e)
                }
            }
        }

        val allRecords = binanceTradeRebateRecordRepository.findByStatus(
            BinanceTradeRebateRecordStatus.CLAIMED
        )

        logger.info("Binance first trade pending records size: ${allRecords.size}")

        allRecords.forEach {

            // 检查是否已经发放过月光宝盒
            if(it.settleMoonlightBoxTxId == null){

                try {
                    val txHash = bitlayerService.settleMoonlightBox(
                        parameter.moonlightBoxContractAddress,
                        reportWalletKey,
                        it.buyerAddress,
                        200000,
                        nftId = BigInteger.ONE
                    )

                    it.settleMoonlightBoxTxId = txHash
                    Thread.sleep(15 * 1000)

                } catch (e: Exception) {
                    logger.error("Binance first trade set moonlight box failed", e)
                }
            }

            if(it.settleNftTxId != null && it.settleMoonlightBoxTxId != null){
                it.status = BinanceTradeRebateRecordStatus.SETTLED
                binanceTradeRebateRecordRepository.save(it)
            }
        }

        checkSettleSuccess()
        fillRebateRecordInfo()

        logger.info("Binance first trade processor end")
    }

    private fun checkSettleSuccess() {

        val evmUtil = bitlayerService.evmUtil

        val allMoonlightRecords = binanceTradeRebateRecordRepository.findByStatusAndSettleMoonlightBoxTxIdIsNotNullAndSettledMoonlightBox(
            BinanceTradeRebateRecordStatus.SETTLED,
            false
        )

        for (record in allMoonlightRecords) {
            try {
                val txReceipt = evmUtil.web3j.ethGetTransactionReceipt(record.settleMoonlightBoxTxId)
                    .send().transactionReceipt.get()
                if (txReceipt.status == "0x1") {
                    record.settledMoonlightBox = true
                    binanceTradeRebateRecordRepository.save(record)
                } else {
                    logger.info("Binance first trade check moonlight failed, txReceipt: ${record.settleMoonlightBoxTxId}")

                    record.settledMoonlightBox = false
                    record.status = BinanceTradeRebateRecordStatus.CLAIMED
                    binanceTradeRebateRecordRepository.save(record)
                }
            } catch (e: Exception) {
                logger.error("Binance first trade check moonlight failed, optionOrderId: ${record.optionOrderId}", e)
            }
        }

        val allNftRecords = binanceTradeRebateRecordRepository.findByStatusAndSettleNftTxIdIsNotNullAndSettledNft(
            BinanceTradeRebateRecordStatus.SETTLED,
            false
        )

        for (record in allNftRecords) {
            try {
                val txReceipt = evmUtil.web3j.ethGetTransactionReceipt(record.settleMoonlightBoxTxId)
                    .send().transactionReceipt.get()
                if (txReceipt.status == "0x1") {
                    record.settledNft = true
                    binanceTradeRebateRecordRepository.save(record)
                } else {
                    logger.info("Binance first trade mint nft failed, txReceipt: ${record.settleMoonlightBoxTxId}")

                    record.settledNft = false
                    record.status = BinanceTradeRebateRecordStatus.CLAIMED
                    binanceTradeRebateRecordRepository.save(record)
                }
            } catch (e: Exception) {
                logger.error("Binance first trade check mint nft failed, optionOrderId: ${record.optionOrderId}", e)
            }
        }
    }

    private fun handlePendingRecords() {

        logger.info("Binance first trade handlePendingRecords start")
        val parameter = binanceCampaignService.getCampaignParameter()

        //Check Created Record discord level
        val allExecutedRecords = binanceTradeRebateRecordRepository.findByStatus(
            BinanceTradeRebateRecordStatus.EXECUTED
        )

        logger.info("Binance first trade handlePendingRecords executedRecords size: ${allExecutedRecords.size}")

        for (record in allExecutedRecords) {
            try {
                val user = userRepository.findByAddressIgnoreCase(record.buyerAddress)
                if(user == null){
                    continue
                }

                logger.info("Binance first trade createdRecord user: ${user.address}")

                val optionOrder = optionOrderRepository.findByIdOrNull(record.optionOrderId)
                if(optionOrder == null || optionOrder.channel == UserChannel.MINI_APP){
                    logger.info("Binance first trade is Mini App Record: ${user.address}, ${record.optionOrderId}, ${record.txHash}")
                    binanceTradeRebateRecordRepository.delete(record)
                    continue
                }

                val userCampaignInfo = airDropService.getUserCampaignInfo(user)
                if(record.retweetTime == 0L && userCampaignInfo.retweetBinanceTime > 0){
                    record.retweetTime = userCampaignInfo.retweetBinanceTime
                }

                if(record.discordLevel == 0) {
                    val discordLevel = binanceCampaignService.getDiscordLevel(user)
                    record.discordLevel = discordLevel
                }

                if(record.discordLevel >= parameter.firstTradeDiscordLevel && record.retweetTime > 0){
                    record.status = BinanceTradeRebateRecordStatus.NFT_FILLED
                }

                binanceTradeRebateRecordRepository.save(record)

            } catch (e: Exception) {
                logger.error("Binance first trade discord level failed, optionOrderId: ${record.optionOrderId}", e)
            }
        }

        logger.info("Binance first trade handlePendingRecords end")
    }

    private fun fillRebateRecordInfo(){

        val allExecutedRecords = binanceTradeRebateRecordRepository.findBySettlementPrice(
            BigDecimal.ZERO
        )

        for(record in allExecutedRecords){
            try {
                binanceCampaignService.checkBinanceTradeRebate(record)
            } catch (e: Exception) {
                logger.error("Binance first trade fill rebate record info failed, optionOrderId: ${record.optionOrderId}", e)
            }
        }
    }
}