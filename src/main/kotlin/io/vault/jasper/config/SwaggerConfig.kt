package io.vault.jasper.config

import io.vault.jasper.annotation.InternalControllerAnnotation
import org.springframework.beans.factory.annotation.Value
import org.springframework.context.annotation.Bean
import org.springframework.context.annotation.Configuration
import springfox.documentation.builders.PathSelectors
import springfox.documentation.builders.RequestHandlerSelectors
import springfox.documentation.builders.RequestParameterBuilder
import springfox.documentation.oas.annotations.EnableOpenApi
import springfox.documentation.service.ApiInfo
import springfox.documentation.service.Contact
import springfox.documentation.service.ParameterType
import springfox.documentation.service.RequestParameter
import springfox.documentation.spi.DocumentationType
import springfox.documentation.spring.web.plugins.Docket

@Configuration
@EnableOpenApi
open class SwaggerConfig {
    /**
     * 用于读取配置文件 application.properties 中 swagger 属性是否开启
     */
    @Value("\${swagger.enabled}")
    var swaggerEnabled: Boolean? = null

    // @Autowired
    // private lateinit var openApiExtensionResolver: OpenApiExtensionResolver

    @Bean
    open fun docket(): Docket {
        return Docket(DocumentationType.OAS_30)
            .apiInfo(apiInfo()) // 是否开启swagger
            .enable(swaggerEnabled!!)
            .select() // 过滤条件，扫描指定路径下的文件
            .apis(RequestHandlerSelectors.basePackage("io.vault.jasper.controller.auth"))
//            .paths(PathSelectors.any()) // 指定路径处理，PathSelectors.any()代表不过滤任何路径
            .build()
            .groupName("授权API")
            .globalRequestParameters(
                listOf(
                    createHeaderParameter("Authorization", "填写用户登录JWT")
                )
            )
        // .globalRequestParameters(globalOperation())
        // .extensions(openApiExtensionResolver.buildExtensions("Test Group"))
        // .extensions(openApiExtensionResolver.buildSettingExtensions())
    }

    @Bean
    open fun publicApi(): Docket {
        return Docket(DocumentationType.OAS_30)
            .apiInfo(apiInfo())
            .enable(swaggerEnabled!!)
            .select()
            .apis(RequestHandlerSelectors.basePackage("io.vault.jasper.controller.pub"))
            .paths(PathSelectors.any())
            .build()
            .groupName("公共API")
        
    }

    @Bean
    open fun internalApi(): Docket {
        return Docket(DocumentationType.OAS_30)
            .apiInfo(apiInfo())
            .enable(swaggerEnabled!!)
            .select()
            .apis(RequestHandlerSelectors.withClassAnnotation(InternalControllerAnnotation::class.java))
            .paths(PathSelectors.any())
            .build()
            .groupName("内部API")
    }

    private fun apiInfo(): ApiInfo {
        /*作者信息*/
        val contact = Contact("", "", "<EMAIL>")
        return ApiInfo(
            "Jasper 接口文档",
            "",
            "v1.0",
            "https://jaspervault.io",
            contact,
            "Apache 2.0",
            "http://www.apache.org/licenses/LICENSE-2.0",
            ArrayList()
        )
    }

    private fun createHeaderParameter(name: String, desc: String? = null, required: Boolean = false): RequestParameter {
        return RequestParameterBuilder()
            .name(name)
            .description(desc)
            .required(required)
            .`in`(ParameterType.HEADER)
            .build()
    }
}