package io.vault.jasper.model

import io.vault.jasper.enums.ChainType
import io.vault.jasper.enums.Symbol
import org.springframework.data.annotation.CreatedDate
import org.springframework.data.annotation.Id
import org.springframework.data.mongodb.core.index.CompoundIndex
import org.springframework.data.mongodb.core.index.Indexed
import org.springframework.data.mongodb.core.mapping.Document
import org.springframework.data.mongodb.core.mapping.Field
import org.springframework.data.mongodb.core.mapping.FieldType
import java.math.BigDecimal
import java.math.BigInteger
import java.time.LocalDateTime

@Document
@CompoundIndex(
    name = "chain_user_id_token",
    def = "{'chain' : 1, 'user_id': 1}",
    background = true
)
data class Funds(
    @Id
    val id: String? = null,

    val chain: ChainType,

    @Field("user_id")
    val userId: String? = null,

    val action: FundsAction,

    val token: Symbol,

    @Field(targetType = FieldType.DECIMAL128)
    val amount: BigDecimal,

    @Indexed(background = true)
    @Field("tx_hash")
    val txHash: String,

    @Field("tx_time")
    val txTime: Long,

    @Field("tx_block_height")
    val txBlockHeight: Long,

    @Field("receipt_status")
    val receiptStatus: Boolean,

    @CreatedDate
    val created: LocalDateTime = LocalDateTime.now()
)

enum class FundsAction {
    PAY,
    RECEIVE,
    DEPOSIT,
    WITHDRAW,
    CLAIM
}