package io.vault.jasper.response

import io.swagger.annotations.ApiModel
import io.swagger.annotations.ApiModelProperty
import io.vault.jasper.enums.ChainType
import io.vault.jasper.enums.Symbol
import io.vault.jasper.model.OrderType
import java.math.BigDecimal

@ApiModel("用户积分汇总")
data class UserPointsResponse(
    @ApiModelProperty("用户sPoint", required = false)
    val sPoint: String = "0",
    @ApiModelProperty("用户jPoint", required = false)
    val jPoint: String = "0",
    @ApiModelProperty("用户lPoint", required = false)
    val lPoint: String = "0",
)