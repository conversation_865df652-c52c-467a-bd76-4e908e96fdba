package io.vault.jasper.model

import org.springframework.data.annotation.CreatedDate
import org.springframework.data.annotation.Id
import org.springframework.data.annotation.LastModifiedDate
import org.springframework.data.mongodb.core.index.CompoundIndex
import org.springframework.data.mongodb.core.index.CompoundIndexes
import org.springframework.data.mongodb.core.index.Indexed
import org.springframework.data.mongodb.core.mapping.Document
import java.math.BigDecimal
import java.time.LocalDateTime

@Document
@CompoundIndexes(
    CompoundIndex(name = "kol_level_status", def = "{'level': 1, 'status': 1}", background = true)
)
data class Kol(
    @Id
    val id: String? = null,
    val wallet: String,
    var level: String,
    var referralCode: String? = null,
    var incentive: BigDecimal = BigDecimal.ZERO,
    var status: KolStatus = KolStatus.PENDING,
    @CreatedDate
    val created: LocalDateTime = LocalDateTime.now(),
    @LastModifiedDate
    val updated: LocalDateTime = LocalDateTime.now(),
    var autoSettle: Boolean = true
) {}

enum class KolStatus {
    PENDING,
    ACTIVE,
    INACTIVE,
    SUSPENDED,
    LOCKED,
    BANNED,
    ARCHIVED,
    DELETED
}
