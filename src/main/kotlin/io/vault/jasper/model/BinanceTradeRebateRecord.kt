package io.vault.jasper.model

import io.vault.jasper.enums.ChainType
import io.vault.jasper.enums.OptionDirection
import io.vault.jasper.enums.Symbol
import org.springframework.data.annotation.CreatedDate
import org.springframework.data.annotation.Id
import org.springframework.data.annotation.LastModifiedDate
import org.springframework.data.mongodb.core.index.Indexed
import org.springframework.data.mongodb.core.mapping.Document
import org.springframework.data.mongodb.core.mapping.Field
import java.math.BigDecimal
import java.math.BigInteger
import java.time.LocalDateTime

@Document("binance_trade_rebate_record")
data class BinanceTradeRebateRecord(
    @Id
    val id: String? = null,
    @Field("option_order_id")
    @Indexed(unique = true, background = true)
    val optionOrderId: String,
    @Field("tx_hash")
    @Indexed(background = true)
    val txHash: String,
    @Field("buyer_address")
    @Indexed(background = true)
    val buyerAddress: String,
    var direction: OptionDirection,
    val chain: ChainType = ChainType.BITLAYER,
    @Field("bid_amount")
    var bidAmount: BigDecimal = BigDecimal("0.01"),
    @Field("bid_asset")
    var bidAsset: Symbol = Symbol.BTC,
    @Field("expiry_in_hour")
    var expiryInHour: String? = "0.5",
    @Field("strike_price")
    var strikePrice: BigDecimal? = BigDecimal.ZERO,
    @Field("settlement_price")
    var settlementPrice: BigDecimal = BigDecimal.ZERO,
    @Field("premium_fee")
    var premiumFee: BigDecimal = BigDecimal.ZERO,
    @Field("premium_asset")
    var premiumAsset: Symbol = Symbol.USDT,
    @Field("premium_fee_in_usdt")
    var premiumFeeInUsdt: BigDecimal = BigDecimal.ZERO,
    var profit: BigDecimal = BigDecimal.ZERO,
    @Field("profit_asset")
    var profitAsset: Symbol = Symbol.USDT,
    @Field("profit_in_usdt")
    var profitInUsdt: BigDecimal = BigDecimal.ZERO,
    @Field("net_profit")
    var netProfit: BigDecimal = BigDecimal.ZERO,
    @Field("retweet_time")
    var retweetTime: Long = 0L,
    @Field("discord_level")
    var discordLevel: Int = 0,
    var status: BinanceTradeRebateRecordStatus = BinanceTradeRebateRecordStatus.EXECUTED,
    @Field("settle_nft_tx_id")
    var settleNftTxId: String? = null,
    @Field("settled_nft")
    var settledNft: Boolean = false,
    @Field("settle_moonlight_box_tx_id")
    var settleMoonlightBoxTxId: String? = null,
    @Field("settled_moonlight_box")
    var settledMoonlightBox: Boolean = false,
    @CreatedDate
    val created: LocalDateTime = LocalDateTime.now(),
    @LastModifiedDate
    val updated: LocalDateTime = LocalDateTime.now(),
) {}

enum class BinanceTradeRebateRecordStatus {
    EXECUTED, NFT_FILLED, CREATED, CLAIMED, SETTLED, SETTLE_FAILED
}