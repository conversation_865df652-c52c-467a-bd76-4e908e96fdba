package io.vault.jasper.model

import io.vault.jasper.enums.ChainType
import org.springframework.data.annotation.Id
import org.springframework.data.mongodb.core.mapping.Document
import org.springframework.data.mongodb.core.mapping.Field
import java.math.BigDecimal
import java.time.LocalDateTime

@Document("dlcbtc_campaign_parameter")
data class DlcbtcCampaignParameter(
    @Id
    val id: String? = null,
    @Field("start_date")
    var startDate: LocalDateTime = LocalDateTime.now(),
    @Field("end_date")
    var endDate: LocalDateTime = LocalDateTime.of(2025, 12, 30, 0, 0, 0),
    @Field("trade_rebate_chain")
    var tradeRebateChain: ChainType = ChainType.ARBITRUM,
    @Field("trade_rebate_contract_address")
    var tradeRebateContractAddress: String = "******************************************",
    @Field("first_trade_rebate_task_switch")
    var firstTradeRebateTaskSwitch: Boolean = true, // 推送First Trade Rebate任务的开关
    @Field("first_trade_rebate_count")
    var firstTradeRebateCount: Int = 10000,
    @Field("rebate_claim_time")
    var rebateClaimTime: Long = 900, // 15分钟
    @Field("first_trade_discord_level")
    var firstTradeDiscordLevel: Int = 1,
    @Field("first_trade_degen_quantity")
    var firstTradeDegenQuantity: BigDecimal = BigDecimal("0.01"),
    @Field("twitter_account_link")
    var twitterAccountLink: String = "https://x.com/dlcBTC",
    @Field("retweet_link")
    var retweetLink: String = "https://x.com/jaspervault/status/1840701273026560148",
)