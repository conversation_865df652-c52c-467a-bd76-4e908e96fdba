package io.vault.jasper.response

import io.swagger.annotations.ApiModel
import io.swagger.annotations.ApiModelProperty
import io.vault.jasper.enums.ChainType
import io.vault.jasper.enums.OptionDirection
import io.vault.jasper.enums.Symbol
import io.vault.jasper.model.KytStatus
import io.vault.jasper.model.OrderStatus
import io.vault.jasper.model.PremiumAsset
import io.vault.jasper.model.SettlementType

@ApiModel
data class OrderDetailResponse(
    val id: String,
    @ApiModelProperty("订单创建者")
    val creator: String,
    @ApiModelProperty("订单创建者Vault地址")
    val creatorVault: String?,
    @ApiModelProperty("链")
    val chain: ChainType,
    @ApiModelProperty("订单类型: CALL/PUT")
    val businessType: OptionDirection,
    val isCertified: Boolean,
    @ApiModelProperty("头像")
    val avatar: String,
    @ApiModelProperty("订单名称")
    val name: String,
    @ApiModelProperty("抵押资产")
    val underlyingAsset: Symbol,
    @ApiModelProperty("行权价格")
    val strikePrice: String,
    @ApiModelProperty("Deribit相应权利金")
    val deribitPrice: String,
    @ApiModelProperty("挂单数量")
    val amount: String,
    @ApiModelProperty("总数量")
    val totalAmount: String,
    @ApiModelProperty("权利金支付方式")
    val paywith: List<PremiumAsset>,
    @ApiModelProperty("Deribit当前权利金价格")
    val deribitPremiumPrice: String?,
    @ApiModelProperty("权利金")
    val premiumFee: String,
    @ApiModelProperty("接受的结算方式")
    val acceptList: List<SettlementType>,
    val kyt: KytStatus,
    @ApiModelProperty("创建时间")
    val createDate: Long,
    @ApiModelProperty("到期时间")
    val expiryDate: Long,
    @ApiModelProperty("订单状态")
    val status: OrderStatus,
    @ApiModelProperty("锁住的资产")
    val lockedAsset: String?,
    @ApiModelProperty("合约内容")
    val contractContent: Map<String, String>?,
    @ApiModelProperty("签名")
    val signData: String?
)
