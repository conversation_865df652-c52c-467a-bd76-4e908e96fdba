package io.vault.jasper.service.blockchain

import io.vault.jasper.blockchain.BitlayerBlockchainUtil
import io.vault.jasper.blockchain.BlockchainUtilFactory
import io.vault.jasper.blockchain.evm.arbitrum.BitlayerScanApi
import io.vault.jasper.repository.*
import io.vault.jasper.service.*
import io.vault.jasper.task.AsyncEvmScanner
import org.slf4j.LoggerFactory
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.beans.factory.annotation.Value
import org.springframework.context.ApplicationEventPublisher
import org.springframework.stereotype.Service
import org.web3j.abi.FunctionEncoder
import org.web3j.abi.TypeReference
import org.web3j.abi.datatypes.Address
import org.web3j.abi.datatypes.Bool
import org.web3j.abi.datatypes.DynamicArray
import org.web3j.abi.datatypes.Function
import org.web3j.abi.datatypes.Type
import org.web3j.abi.datatypes.generated.Uint256
import org.web3j.crypto.Credentials
import org.web3j.crypto.RawTransaction
import org.web3j.crypto.TransactionEncoder
import org.web3j.utils.Numeric
import java.math.BigDecimal
import java.math.BigInteger

@Service
class BitlayerService @Autowired constructor(
    blockchainUtil: BitlayerBlockchainUtil,
    scanApi: BitlayerScanApi,
    optionOrderRepository: OptionOrderRepository,
    optionOrderInfoRepository: OptionOrderInfoRepository,
    orderRepository: OrderRepository,
    blockchainRepository: BlockchainRepository,
    asyncEvmScanner: AsyncEvmScanner,
    private val jasperVaultService: JasperVaultService,
    eventPublisher: ApplicationEventPublisher,
    lpVaultRepository: LPVaultRepository,
    contractEventRepository: ContractEventRepository,
    currencyService: CurrencyService,
    optionOrderService: OptionOrderService,
    blockchainUtilFactory: BlockchainUtilFactory,
    subgraphService: SubgraphService,
    historyService: HistoryService,
    miniBridgeSwapOrderRepository: MiniBridgeSwapOrderRepository,
    alertService: AlertService
) : EvmService(
    blockchainUtil,
    blockchainRepository,
    asyncEvmScanner,
    orderRepository,
    optionOrderRepository,
    optionOrderInfoRepository,
    jasperVaultService,
    eventPublisher,
    lpVaultRepository,
    contractEventRepository,
    currencyService,
    optionOrderService,
    blockchainUtilFactory,
    scanApi,
    subgraphService,
    historyService,
    miniBridgeSwapOrderRepository,
    alertService
) {

    @Value("\${blockchain.bitlayer.contract.vault_factory}")
    override lateinit var vaultFactoryContract: String

    @Value("\${blockchain.bitlayer.contract.option_service}")
    override lateinit var optionServiceContract: String

    @Value("\${blockchain.bitlayer.contract.diamond}")
    override lateinit var addOrderLogAddress: String

    @Value("\${blockchain.bitlayer.contract.issuance_module}")
    override lateinit var issuanceModuleContract: String

    private val logger = LoggerFactory.getLogger(this::class.java)

    override fun getAndSetPrice(priceId: String, pythIdList: List<String>): BigDecimal {
        return jasperVaultService.getPythPrice(priceId) ?: throw Exception("Fail to get price by priceId=$priceId")
    }

//    /**
//     * 结算空投首单永赚
//     */
//    fun settleMoonlightBox(
//        contractAddress: String,
//        privateKey: String,
//        address: String,
//        gasLimit: Int,
//        gasPrice: BigInteger? = null,
//        nftId: BigInteger = BigInteger.ONE
//    ): String {
//        val inputs: List<Type<*>> = listOf(
//            Address(address),
//            Uint256(nftId),
//            Uint256(1)
//        )
//        val outputs: MutableList<TypeReference<*>> = ArrayList()
//        val method = "adminMint"
//        val function = Function(method, inputs, outputs)
//        val data = FunctionEncoder.encode(function)
//        val credentials = Credentials.create(privateKey)
//        val rawTransaction = RawTransaction.createTransaction(
//            evmUtil.getNonce(credentials.address),
//            gasPrice ?: evmUtil.getGasPrice(""),
//            gasLimit.toBigInteger(),
//            contractAddress,
//            BigInteger.valueOf(0),
//            data
//        )
//        val signMessage = TransactionEncoder.signMessage(rawTransaction, evmUtil.chainId, credentials)
//        val toHexString = Numeric.toHexString(signMessage)
//        val ethCall = evmUtil.web3j.ethSendRawTransaction(toHexString).sendAsync().get()
//        if (ethCall.hasError()) {
//            val errorMsg = ethCall.error.message
//            // max fee per gas less than block base fee: address ******************************************, maxFeePerGas: 10000000 baseFee: 10041000
//            if (errorMsg.startsWith("max fee per gas less than block base fee")) {
//                val regex = Regex("baseFee:\\s*(\\d+)")
//                regex.find(errorMsg)?.let {
//                    val baseFee = it.groupValues[1].toBigInteger()
//                    logger.debug("current baseFee: $baseFee")
//                    val move = baseFee.toString(10).length - 2
//                    val divisor = BigInteger.TEN.pow(move)
//                    val newBaseFee = (baseFee.divide(divisor) + BigInteger.ONE).multiply(divisor)
//                    logger.debug("new baseFee: $newBaseFee")
//                    Thread.sleep(3000)
//
//                    return settleMoonlightBox(
//                        contractAddress,
//                        privateKey,
//                        address,
//                        gasLimit,
//                        newBaseFee,
//                        nftId = nftId
//                    )
//                }
//            } else {
//                logger.error("Error Message: ${ethCall.error.message}")
//                throw Exception("Failed to send transaction")
//            }
//        }
//        val txHash = ethCall.transactionHash
//        logger.info("method: $method\t txid: $txHash")
//
//        return txHash
//    }

    fun isUserHasMoonlightBox(
            contractAddress: String,
            address: String,
            tokenId: BigInteger
    ): Boolean {

        val inputs: List<Type<*>> = listOf(
            Address(address),
            Uint256(tokenId)
        )
        val outputs: MutableList<TypeReference<*>> = ArrayList()
        val typeReference: TypeReference<Bool> = object : TypeReference<Bool>() {}
        outputs.add(typeReference)

        val method = "isValidNFT"
        val callResult = evmUtil.ethCall(method, contractAddress, inputs, outputs) ?: throw Exception("Failed to get moonlight box")
        return callResult.first().value as Boolean
    }

    /**
     * 结算空投首单永赚
     */
    fun settleBinanceWhiteList(
        contractAddress: String,
        privateKey: String,
        address: String,
        gasLimit: Int,
        gasPrice: BigInteger? = null
    ): String {
        val inputs: List<Type<*>> = listOf(
            Address(address),
            Uint256(1),
            Bool(true)
        )
        val outputs: MutableList<TypeReference<*>> = ArrayList()
        val method = "setMintAccount"
        val function = Function(method, inputs, outputs)
        val data = FunctionEncoder.encode(function)
        val credentials = Credentials.create(privateKey)
        val rawTransaction = RawTransaction.createTransaction(
            evmUtil.getNonce(credentials.address),
            gasPrice ?: evmUtil.getGasPrice(""),
            gasLimit.toBigInteger(),
            contractAddress,
            BigInteger.valueOf(0),
            data
        )
        val signMessage = TransactionEncoder.signMessage(rawTransaction, evmUtil.chainId, credentials)
        val toHexString = Numeric.toHexString(signMessage)
        val ethCall = evmUtil.web3j.ethSendRawTransaction(toHexString).sendAsync().get()
        val txHash = ethCall.transactionHash
        logger.info("method: $method\t txid: $txHash")

        return txHash
    }
}