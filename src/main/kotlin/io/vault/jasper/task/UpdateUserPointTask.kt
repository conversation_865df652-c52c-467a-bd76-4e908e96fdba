package io.vault.jasper.task

import io.vault.jasper.enums.Symbol
import io.vault.jasper.model.*
import io.vault.jasper.repository.*
import io.vault.jasper.service.*
import io.vault.jasper.utils.DateTimeUtil
import io.vault.jasper.utils.ProfileUtil
import org.slf4j.LoggerFactory
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.beans.factory.annotation.Value
import org.springframework.data.mongodb.core.MongoTemplate
import org.springframework.data.mongodb.core.query.Criteria
import org.springframework.data.mongodb.core.query.Query
import org.springframework.data.repository.findByIdOrNull
import org.springframework.scheduling.annotation.Scheduled
import org.springframework.stereotype.Service
import java.math.BigDecimal
import java.time.LocalDateTime

@Service
class UpdateUserPointTask @Autowired constructor(
    private val userPointService: UserPointService,
    private val optionOrderRepository: OptionOrderRepository,
    private val userPointRecordRepository: UserPointRecordRepository,
    private val currencyService: CurrencyService,
    private val mongoTemplate: MongoTemplate
) {

    private val logger = LoggerFactory.getLogger(this::class.java)

    private val fixCount = 1000

    @Value("\${schedule_task_on}")
    lateinit var scheduleOn: String

    // 每5分钟执行一次
    @Scheduled(fixedDelay = 2 * 60 * 1000, initialDelay = 60 * 1000)
    fun run() {
        if(scheduleOn == "false"){
            return
        }
        try {
            exec()
        } catch (e: Exception) {
            logger.error(e.message, e)
        }

        try{
            fixUserPointDailyPremiumInUsdt()
        } catch (e: Exception) {
            logger.error(e.message, e)
        }

        try{
            updateUserPoints()
        } catch (e: Exception) {
            logger.error(e.message, e)
        }
    }

    /**
     * 更新每条订单记录
     */
    private fun exec() {

        // 更新所有 status=Executed 的 Order 的 record
        val executedPointRecords = userPointRecordRepository.findByStatus(OptionStatus.EXECUTED)
        //logger.info("Update User Point task begin and executed point record count is ${executedPointRecords.size}.")
        for(record in executedPointRecords) {

            val optionOrderId = record.optionOrderId
            val optionOrder = optionOrderRepository.findByIdOrNull(optionOrderId)
            if(optionOrder == null) {
                //logger.info("Update User Point task Option Order not found, optionOrderId: $optionOrderId")
                continue
            }

            if(optionOrder.status == OptionStatus.SETTLE_FAILED){
                record.status = OptionStatus.SETTLE_FAILED
                userPointRecordRepository.save(record)
                continue
            }

            if(optionOrder.status != OptionStatus.SETTLED){
                //logger.warn("Update User Point task Option Order not settled, optionOrderId: $optionOrderId")
                continue
            }

            if(optionOrder.buyerProfit == null){
                logger.warn("Update User Point task Option Order buyerProfit is null, optionOrderId: $optionOrderId")
                continue
            }

            try {
                userPointService.updateUserPoint(optionOrder, record)
            }catch (e: Exception){
                //logger.info("Update User Point task update User Point Record Error, optionOrderId: $optionOrderId $e")
            }
        }

        // 更新新增的 OptionOrder record
        val lastPointRecord = userPointRecordRepository.findTopByOrderByCreatedDesc()
        //logger.info("Update User Point task last point record is ${lastPointRecord?.created}.")
        val lastPointRecordTime = lastPointRecord?.created?: LocalDateTime.now().minusYears(1)
        val newOptionOrders = optionOrderRepository.findByCreatedGreaterThan(lastPointRecordTime)

        //logger.info("Update User Point task begin and new option order record count is ${newOptionOrders.size}.")
        for (optionOrder in newOptionOrders) {
            try {
                userPointService.createUserPointRecord(optionOrder)
            } catch (e: Exception) {
                logger.info("Update User Point task create User Point Record Error, optionOrderId: ${optionOrder.id} $e")
            }
        }

        //logger.info("Update User Point Task End.")
    }

    private fun updateUserPoints() {

        val query = Query()
        query.addCriteria(Criteria.where(User::jPoint.name).isNull)

        // 设置返回的文档数量限制
        query.limit(100)

        // 使用 mongoTemplate 执行查询
        val results: List<User> = mongoTemplate.find(query, User::class.java)

        //logger.info("Update User Points for ${results.size} users")

        results.forEach { user ->
            try {
                userPointService.updateUserPointInfo(user)
            } catch (e: Exception) {
                logger.error("Failed to update user points ${user.address}", e)
            }
        }
    }

    private fun fixUserPointDailyPremiumInUsdt(){

        val allUserPointDailyRecords = userPointRecordRepository.findByPremiumInUsdtIsNull()
        var count = 0
        for(record in allUserPointDailyRecords){

            count ++
            if(count > fixCount){
                break
            }

            val optionOrder = optionOrderRepository.findByIdOrNull(record.optionOrderId)
            if(optionOrder == null){
                //logger.info("Fix User Point Daily Premium In Usdt, optionOrder not found, optionOrderId: ${record.optionOrderId}")
                continue
            }

            if(optionOrder.premiumFeePayInUsdt == null){
                //logger.info("Fix User Point Daily Premium In Usdt, optionOrder premiumFeePayInUsdt is null, optionOrderId: ${record.optionOrderId}")
                continue
            }

            val usdtDecimal = currencyService.getCurrencyDecimal(optionOrder.chain, Symbol.USDT)

            record.premiumInUsdt = optionOrder.premiumFeePayInUsdt!!.movePointLeft(usdtDecimal)
            userPointRecordRepository.save(record)
        }
    }
}