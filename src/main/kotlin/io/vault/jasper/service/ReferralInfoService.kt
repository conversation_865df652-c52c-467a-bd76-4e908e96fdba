package io.vault.jasper.service

import io.vault.jasper.model.*
import io.vault.jasper.repository.*
import io.vault.jasper.utils.DateTimeUtil
import org.slf4j.LoggerFactory
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.data.mongodb.core.MongoTemplate
import org.springframework.data.mongodb.core.aggregation.Aggregation
import org.springframework.data.mongodb.core.aggregation.ConvertOperators
import org.springframework.data.mongodb.core.query.Criteria
import org.springframework.stereotype.Service
import java.math.BigDecimal
import java.time.LocalDateTime

@Service
class ReferralInfoService @Autowired constructor(
    private val userReferralDailySummaryRepository: UserReferralDailySummaryRepository,
    private val userRepository: UserRepository,
    private val optionOrderService: OptionOrderService,
    private val userReferralTransactionRepository: UserReferralTransactionRepository,
    private val optionOrderRepository: OptionOrderRepository,
    private val mongoTemplate: MongoTemplate,
){

    private val logger = LoggerFactory.getLogger(this::class.java)

    fun updateReferralVolumeAtDate(
        user: User,
        dateTime: LocalDateTime? = null
    ) {

        val now = dateTime ?: LocalDateTime.now()

        val firstRecordDate = now.withHour(0).withMinute(0).withSecond(0)

        val dateInfo = DateTimeUtil.getDailySummaryTime(
            firstRecordDate
        )

        val startDate = dateInfo.second
        val endDate = dateInfo.third

        logger.info("Update User Referral Volume User: ${user.id}, from : $startDate to $endDate")

        val allReferees = userRepository.findByInvitedUserId(
            user.id!!
        )
        val dailyVolume = optionOrderService.getUserOptionVolumeTotal(
            allReferees.map { it.address },
            null,
            startDate,
            endDate
        )

        var referralDailySummary = userReferralDailySummaryRepository.findByAddressAndDateString(
            user.address,
            dateInfo.first
        )

        if(referralDailySummary == null){
            referralDailySummary = UserReferralDailySummary(
                address = user.address,
                dateString = dateInfo.first,
                userId = user.id,
            )
        }

        referralDailySummary.refereeVolume = dailyVolume
        userReferralDailySummaryRepository.save(referralDailySummary)
    }

    fun updateReferralCountAtDate(
        user: User,
        dateTime: LocalDateTime? = null
    ) {

        val now = dateTime ?: LocalDateTime.now()

        val firstRecordDate = now.withHour(0).withMinute(0).withSecond(0)

        val dateInfo = DateTimeUtil.getDailySummaryTime(
            firstRecordDate
        )

        val startDate = dateInfo.second
        val endDate = dateInfo.third

        logger.info("Update User Referral Volume User: ${user.id}, from : $startDate to $endDate")

        val dailyReferees = userRepository.findByInvitedUserIdAndCreatedBetween(
            user.id!!,
            startDate,
            endDate
        )

        var referralDailySummary = userReferralDailySummaryRepository.findByAddressAndDateString(
            user.address,
            dateInfo.first
        )

        if(referralDailySummary == null){
            referralDailySummary = UserReferralDailySummary(
                address = user.address,
                dateString = dateInfo.first,
                userId = user.id,
            )
        }

        referralDailySummary.referees = dailyReferees.size
        userReferralDailySummaryRepository.save(referralDailySummary)
    }

    /**
     * 检查被邀请用户是否是首次交易，如果是，给邀请人发送奖励
     *
     * @param refereeUser 被邀请用户
     * @param optionOrder 交易订单
     */
//    fun checkFirstTradeReward(
//        refereeUser: User,
//        optionOrder: OptionOrder
//    ) {
//        // 检查被邀请用户是否有邀请人
//        if (refereeUser.invitedUserId == null) {
//            logger.debug("User ${refereeUser.address} has no invitor, skip first trade reward")
//            return
//        }
//
//        // 获取邀请人
//        val invitorUser = userRepository.findById(refereeUser.invitedUserId!!).orElse(null) ?: run {
//            logger.debug("Invitor user ${refereeUser.invitedUserId} not found, skip first trade reward")
//            return
//        }
//
//        // 检查是否是首次交易
//        val userTransactions = optionOrderRepository.findByBuyerIgnoreCaseAndStatusIn(
//            refereeUser.address,
//            OptionStatus.executedStatusList()
//        ).sortedBy { it.id }
//
//        // 如果没有交易或者不是首次交易，跳过
//        if (userTransactions.isEmpty() || userTransactions.size > 1 || userTransactions[0].id != optionOrder.id) {
//            logger.debug("User ${refereeUser.address} is not first trade, skip reward")
//            return
//        }
//
//        // 检查是否已经发送过奖励
//        val existingTransactions = userReferralTransactionRepository.findByUserIdAndRefereeUserIdAndType(
//            invitorUser.id!!,
//            refereeUser.id!!,
//            UserReferralTransactionType.FIRST_TRADE_REWARD
//        )
//
//        if (existingTransactions.isNotEmpty()) {
//            logger.debug("First trade reward has already been sent to invitor ${invitorUser.address}, skip")
//            return
//        }
//
//        // 检查订单是否已经有关联的奖励记录
//        val existingOrderTransaction = userReferralTransactionRepository.findByOptionOrderId(optionOrder.id!!)
//        if (existingOrderTransaction != null) {
//            logger.debug("Option order ${optionOrder.id} already has a reward record, skip")
//            return
//        }
//
//        // 发送奖励
//        logger.info("Sending first trade reward to invitor ${invitorUser.address} for referee ${refereeUser.address}")
//        val transaction = userReferralTransactionRepository.save(
//            UserReferralTransaction(
//                userId = invitorUser.id,
//                address = invitorUser.address,
//                type = UserReferralTransactionType.FIRST_TRADE_REWARD,
//                amount = BigDecimal("1000"),
//                optionOrderId = optionOrder.id,
//                refereeUserId = refereeUser.id,
//                refereeAddress = refereeUser.address
//            )
//        )
//
//        // 更新用户sPoint
//        updateUserSPoint(invitorUser)
//
//        logger.info("Updated user ${invitorUser.address} rebate trading credits after creating referral transaction")
//    }

    /**
     * 获取用户的推荐交易奖励总积分
     */
    fun getUserReferralTotalPoint(
        user: User,
        type: UserReferralTransactionType
    ): BigDecimal {
        val criteria = Criteria.where(UserReferralTransaction::userId.name).`is`(user.id!!)
            .and(UserReferralTransaction::type.name).`is`(type)

        val agg = Aggregation.newAggregation(
            Aggregation.match(criteria),
            Aggregation.group().sum(
                ConvertOperators.valueOf(
                    UserReferralTransaction::amount.name
                ).convertToDecimal()).`as`("total")
        )

        val groupResults = mongoTemplate.aggregate(agg, UserReferralTransaction::class.java, Map::class.java)
        val result = groupResults.mappedResults.firstOrNull()
        val total = (result?.get("total") ?: "0").toString()

        return BigDecimal(total)
    }

    /**
     * 更新用户的sPoint
     */
//    fun updateUserSPoint(user: User) {
//        val totalPoint = getUserReferralTotalPoint(user, UserReferralTransactionType.FIRST_TRADE_REWARD)
//
//        user.referralSPoint = totalPoint
//        userRepository.save(user)
//    }

    /**
     * 更新用户的总推荐数
     */
    fun updateUserReferralCount(user: User) {
        // 查询被该用户邀请的用户数量
        val referralCount = userRepository.countByInvitedUserId(user.id!!)

        // 更新用户的referralCount字段
        user.referralCount = referralCount.toInt()
        userRepository.save(user)

        logger.info("Updated user ${user.address} referral count to $referralCount")
    }
}