package io.vault.jasper.dto

import EVMAddress
import io.swagger.annotations.ApiModel
import io.swagger.annotations.ApiModelProperty
import io.vault.jasper.enums.ChainType
import io.vault.jasper.enums.OptionDirection
import io.vault.jasper.enums.Symbol

@ApiModel("创建LP Vault请求")
data class CreateLPVaultDTO(
    @ApiModelProperty("钱包地址", required = true)
    @field:EVMAddress
    val address: String,
    @ApiModelProperty("链类型", required = true)
    val chain: ChainType,
    @ApiModelProperty("期权方向", required = true)
    val optionDirection: OptionDirection,
    @ApiModelProperty("期权币种", required = true)
    val optionSymbol: Symbol
)
