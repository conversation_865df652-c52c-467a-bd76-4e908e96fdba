package io.vault.jasper.repository

import io.vault.jasper.model.EchoooRetweetUserId
import io.vault.jasper.model.NftAirdropRetweetUserId
import org.springframework.data.mongodb.repository.MongoRepository
import org.springframework.stereotype.Repository

@Repository
interface EchoooRetweetUserIdRepository : MongoRepository<EchoooRetweetUserId, String> {
    fun findByRetweetUserId(retweetUserId: String): EchoooRetweetUserId?
}