package io.vault.jasper.repository


import io.vault.jasper.enums.ChainType
import io.vault.jasper.enums.OptionDirection
import io.vault.jasper.enums.Symbol
import io.vault.jasper.model.DegenLPVault
import io.vault.jasper.model.DegenLPVaultConfig
import org.springframework.data.mongodb.repository.MongoRepository
import org.springframework.stereotype.Repository

@Repository
interface DegenLPVaultConfigRepository : MongoRepository<DegenLPVaultConfig, String> {

    fun findByChain(chain: ChainType): List<DegenLPVaultConfig>
}