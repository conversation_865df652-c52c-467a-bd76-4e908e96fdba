package io.vault.jasper.model

import io.vault.jasper.enums.ChainType
import org.springframework.data.annotation.CreatedDate
import org.springframework.data.annotation.Id
import org.springframework.data.annotation.LastModifiedDate
import org.springframework.data.mongodb.core.index.Indexed
import org.springframework.data.mongodb.core.mapping.Document
import org.springframework.data.mongodb.core.mapping.Field
import java.time.LocalDateTime

@Document("mint_event")
data class MintEvent(
    @Id
    val id: String? = null,

    val chain: ChainType,

    var blockHeight: Long? = null,

    @Indexed(background = true, unique = true)
    @Field("tx_hash")
    val txHash: String,

    var txTimestamp: Long? = null,

    @Indexed(background = true)
    var userId: String? = null,

    @Field("user_id")
    var userId2: String? = null,

    @Field("receipt_status")
    var receiptStatus: Boolean? = null,

    @CreatedDate
    val created: LocalDateTime = LocalDateTime.now(),

    @LastModifiedDate
    val updated: LocalDateTime = LocalDateTime.now(),

    @Field("from_address")
    var fromAddress: String? = null,

    @Field("nft_code")
    var nftCode: String? = null,
)
