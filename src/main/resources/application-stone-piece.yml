# 石头碎片处理定时任务配置示例
# 注意：现在配置存储在 MongoDB 中，此文件仅作为参考
# 使用 /stone-piece-config API 来管理配置

# MongoDB 配置示例数据结构：
# {
#   "configName": "default",
#   "enabled": false,
#   "nftIds": ["1", "2", "3"],
#   "cronExpression": "0 0 * * * ?",
#   "batchSize": 100,
#   "enableDetailedLogging": true,
#   "maxRetryCount": 3,
#   "description": "Default stone piece process configuration",
#   "active": true
# }

# 定时任务现在每分钟检查一次数据库中的配置
# 可以通过以下 API 管理配置：
# GET /stone-piece-config/default - 获取默认配置
# POST /stone-piece-config - 创建配置
# PUT /stone-piece-config/{configName} - 更新配置
# PATCH /stone-piece-config/{configName}/toggle?enabled=true - 启用/禁用配置
# DELETE /stone-piece-config/{configName} - 删除配置
