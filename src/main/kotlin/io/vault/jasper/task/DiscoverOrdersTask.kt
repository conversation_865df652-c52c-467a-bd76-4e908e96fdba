package io.vault.jasper.task

import com.fasterxml.jackson.databind.JsonNode
import io.vault.jasper.enums.ChainType
import io.vault.jasper.enums.OptionDirection
import io.vault.jasper.model.*
import io.vault.jasper.repository.*
import io.vault.jasper.service.*
import io.vault.jasper.service.blockchain.BlockchainServiceFactory
import io.vault.jasper.service.blockchain.EvmService
import io.vault.jasper.utils.DateTimeUtil
import io.vault.jasper.utils.ProfileUtil
import org.slf4j.LoggerFactory
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.beans.factory.annotation.Value
import org.springframework.context.annotation.Profile
import org.springframework.scheduling.annotation.Scheduled
import org.springframework.stereotype.Service
import org.web3j.crypto.Keys
import java.time.LocalDateTime

@Service
@Profile("prod", "test")
class DiscoverOrdersTask @Autowired constructor(
    private val blockchainServiceFactory: BlockchainServiceFactory,
    private val blockchainConfigService: BlockchainConfigService,
    private val pendingOptionOrderRepository: PendingOptionOrderRepository,
    private val optionOrderRepository: OptionOrderRepository,
    private val subgraphService: SubgraphService,
    private val systemService: SystemService,
    private val larkNotifierService: LarkNotifierService
) {

    private val logger = LoggerFactory.getLogger(this::class.java)

    @Value("\${schedule_task_on}")
    lateinit var scheduleOn: String

    @Scheduled(fixedDelay = 3 * 1000, initialDelay = 10 * 1000)
    fun discoverExecutedOrders() {

        if(scheduleOn == "false"){
            return
        }

        try {
            // 获取当前时间戳（秒）
            val endTime = (System.currentTimeMillis() / 1000) - 30 // 30秒前
//            val supportChains = when (ProfileUtil.activeProfile) {
//                "prod" -> listOf(ChainType.ARBITRUM, ChainType.BASE, ChainType.BITLAYER, ChainType.BSC)
//                else -> ChainType.values().toList()
//            }
            val supportChains = listOf(ChainType.ARBITRUM, ChainType.BASE, ChainType.BITLAYER, ChainType.BSC)
            supportChains.forEach loopChain@{ chainType ->
                val service = blockchainServiceFactory.getBlockchainService(chainType) ?: return@loopChain
                if (service !is EvmService) return@loopChain
                var startTime = service.getLastCheckedTimeInSubgraph() ?: (endTime - 60 * 60)
                startTime = minOf(startTime, endTime)

                // 查询订单
                val pageSize = 100
                var skip = 0
                while (true) {
                    val callOrdersResponse = try {
                        service.getCallOrdersFromStartAndEnd(startTime, endTime, pageSize, skip)
                    } catch (e: Exception) {
                        logger.warn("[$chainType]查询Call订单时发生错误: ${e.message}")
                        return@loopChain
                    } ?: return@loopChain
                    callOrdersResponse.forEach { data ->
                        saveToPendingOrderTable(OptionDirection.CALL, data, service)
                    }
                    if (callOrdersResponse.size < pageSize) break
                    skip += pageSize
                }

                skip = 0
                while (true) {
                    val putOrdersResponse = try {
                        service.getPutOrdersFromStartAndEnd(startTime, endTime, pageSize, skip)
                    } catch (e: Exception) {
                        logger.warn("[$chainType]查询Put订单时发生错误: ${e.message}")
                        return@loopChain
                    } ?: return@loopChain
                    putOrdersResponse.forEach { data ->
                        saveToPendingOrderTable(OptionDirection.PUT, data, service)
                    }
                    if (putOrdersResponse.size < pageSize) break
                    skip += pageSize
                }

                // 更新最后检查subgraph的时间
                blockchainConfigService.updateField(
                    service.chainType,
                    Blockchain::lastCheckedTimeInSubgraph.name,
                    DateTimeUtil.convertTimestampToLocalDateTime(endTime)
                )
            }
        } catch (e: Exception) {
            logger.error("发现已执行订单时报错: ${e.message}", e)
        }
    }

//    @Scheduled(fixedDelay = 60 * 1000, initialDelay = 30 * 1000)
//    fun discoverSettledOrders() {
//        try {
//            optionOrderRepository.findByStatusAndExpiryDateGreaterThan(
//                OptionStatus.EXECUTED,
//                LocalDateTime.now()
//            ).forEach { oo ->
//                val orderId = oo.onChainOrderId ?: return@forEach
//                subgraphService.getSettlementFromOrderId(oo.chain, orderId)?.let { settlementHash ->
//                    val evmService = blockchainServiceFactory.getBlockchainService(oo.chain) ?: return@forEach
//                    if (evmService !is EvmService) return@forEach
//                    evmService.saveOptionSettleFromTxHash(settlementHash)
//                }
//            }
//        } catch (e: Exception) {
//            logger.error("发现已结算订单时报错: ${e.message}", e)
//        }
//    }

    private fun saveToPendingOrderTable(optionDirection: OptionDirection, data: JsonNode, service: EvmService) {
        val txHash = data["transactionHash"].asText()
        val orderKey = when (optionDirection) {
            OptionDirection.CALL -> "callOrder"
            OptionDirection.PUT -> "putOrder"
        }
        val orderData = data[orderKey]
        val optionOrderStruct = EvmService.OptionOrderStruct(
            orderId = data["orderId"].asText().toBigInteger(),
            optionType = optionDirection,
            holderVault = Keys.toChecksumAddress(orderData["holder"].asText()),
            liquidateMode = orderData["liquidateMode"].asText().toBigInteger(),
            writerVault = Keys.toChecksumAddress(orderData["writer"].asText()),
            underlyingAssetType = null,
            underlyingAssetAddress = Keys.toChecksumAddress(orderData["underlyingAsset"].asText()),
            underlyingAmount = orderData["lockAmount"].asText().toBigInteger(),
            strikeAssetAddress = Keys.toChecksumAddress(orderData["strikeAsset"].asText()),
            strikeAmount = orderData["strikeAmount"].asText().toBigInteger(),
            expirationDateTimestamp = orderData["expirationDate"].asText().toBigInteger(),
            lockDateTimestamp = orderData["lockDate"].asText().toBigInteger(),
            holderEoaWallet = Keys.toChecksumAddress(data["holderWallet"].asText()),
            writerEoaWallet = Keys.toChecksumAddress(data["writerWallet"].asText()),
            quantity = orderData["quantity"].asText().toBigInteger(),
            recipient = Keys.toChecksumAddress(orderData["recipient"].asText()),
            lockAssetAddress = Keys.toChecksumAddress(orderData["lockAsset"].asText()),
        )
        var txBlockHeight: Long? = null
        var blockTime: LocalDateTime? = null
        service.evmUtil.web3j.ethGetTransactionByHash(txHash).send().transaction.ifPresent { tx ->
            txBlockHeight = tx.blockNumber.toLong()
            val block = service.evmUtil.web3j.ethGetBlockByHash(tx.blockHash, false).send().block
            blockTime = DateTimeUtil.convertTimestampToLocalDateTime(block.timestamp.toLong())
        }
        pendingOptionOrderRepository.existsByChainAndTxHashAndOnChainOrderId(
            service.chainType,
            txHash,
            optionOrderStruct.orderId.toString()
        ).let { if (it) return }
        val pendingOrder = PendingOptionOrder(
            chain = service.chainType,
            txHash = txHash,
            txBlockHeight = txBlockHeight,
            blockTime = blockTime,
            optionOrderStruct = optionOrderStruct,
            onChainOrderId = optionOrderStruct.orderId.toString()
        )
        try {
            pendingOptionOrderRepository.save(pendingOrder)
        } catch (e: Exception) {
            logger.warn("Failed to save pending option order = ${service.chainType} - $txHash")
        }
    }

    /**
     * 检查Arb链订单最后一次收录时间，超过时间t后需要发送预警
     */
    @Profile("prod", "test")
    @Scheduled(fixedDelay = 10 * 60 * 1000, initialDelay = 10 * 1000)
    fun checkArbitrumOrders() {
        if(scheduleOn == "false"){
            return
        }
        optionOrderRepository.findFirstByChainOrderByCreatedDesc(ChainType.ARBITRUM)?.let { lastOrder ->
            val lastOrderTime = lastOrder.created
            val now = LocalDateTime.now()
            val warningMinutes = systemService.getParameter().lastOrderTimeWarningMinutes
            // 时间相差n分钟就发预警
            if (now.minusMinutes(warningMinutes.toLong()).isAfter(lastOrderTime)) {
                larkNotifierService.sendLark(
                    "两小时内没有捕获到Arbitrum链新订单",
                    "两小时内没有捕获到Arbitrum链新订单，请检查扫描订单服务，subgraph服务是否正常"
                )
            }
        }
    }
}