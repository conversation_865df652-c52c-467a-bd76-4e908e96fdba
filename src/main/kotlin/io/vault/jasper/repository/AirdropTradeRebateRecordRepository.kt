package io.vault.jasper.repository


import io.vault.jasper.model.AirdropFreeTradeRecordStatus
import io.vault.jasper.model.AirdropTradeRebateRecord
import io.vault.jasper.model.AirdropTradeRebateRecordStatus
import io.vault.jasper.model.Chain
import org.springframework.data.mongodb.repository.MongoRepository
import org.springframework.stereotype.Repository

@Repository
interface AirdropTradeRebateRecordRepository : MongoRepository<AirdropTradeRebateRecord, String> {
    fun findByOptionOrderId(optionOrderId: String): AirdropTradeRebateRecord?
    fun findTopByOrderByOptionOrderIdDesc(): AirdropTradeRebateRecord?
    fun findByStatus(
        status: AirdropTradeRebateRecordStatus
    ): List<AirdropTradeRebateRecord>

    fun findByBuyerAddress(
        buyerAddress: String
    ): List<AirdropTradeRebateRecord>

    fun findByBuyerAddressIgnoreCase(
        buyerAddress: String
    ): List<AirdropTradeRebateRecord>

    fun findByStatusAndSettleTxIdIsNotNull(
        status: AirdropTradeRebateRecordStatus
    ): List<AirdropTradeRebateRecord>

    fun findByStatusInAndSettleTxIdIsNotNull(
        statuses: List<AirdropTradeRebateRecordStatus>,
    ): List<AirdropTradeRebateRecord>
}