package io.vault.jasper.service

import io.vault.jasper.config.StonePieceProcessParameter
import io.vault.jasper.repository.StonePieceProcessParameterRepository
import org.slf4j.LoggerFactory
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.stereotype.Service
import java.time.LocalDateTime

/**
 * 石头碎片处理参数配置管理服务
 */
@Service
class StonePieceProcessParameterService @Autowired constructor(
    private val stonePieceProcessParameterRepository: StonePieceProcessParameterRepository
) {

    private val logger = LoggerFactory.getLogger(this::class.java)

    companion object {
        const val DEFAULT_CONFIG_NAME = "default"
    }

    /**
     * 获取默认配置
     */
    fun getDefaultConfig(): StonePieceProcessParameter {
        return getConfigByName(DEFAULT_CONFIG_NAME) ?: createDefaultConfig()
    }

    /**
     * 根据配置名称获取配置
     */
    fun getConfigByName(configName: String): StonePieceProcessParameter? {
        return stonePieceProcessParameterRepository.findByConfigNameAndActiveTrue(configName)
    }

    /**
     * 获取所有启用的配置
     */
    fun getEnabledConfigs(): List<StonePieceProcessParameter> {
        return stonePieceProcessParameterRepository.findByEnabledTrueAndActiveTrue()
    }

    /**
     * 获取所有活跃的配置
     */
    fun getAllActiveConfigs(): List<StonePieceProcessParameter> {
        return stonePieceProcessParameterRepository.findByActiveTrue()
    }

    /**
     * 创建或更新配置
     */
    fun saveConfig(config: StonePieceProcessParameter): StonePieceProcessParameter {
        logger.info("Saving stone piece process config: ${config.configName}")
        return stonePieceProcessParameterRepository.save(config)
    }

    /**
     * 更新配置
     */
    fun updateConfig(
        configName: String,
        enabled: Boolean? = null,
        nftIds: List<String>? = null,
        cronExpression: String? = null,
        batchSize: Int? = null,
        enableDetailedLogging: Boolean? = null,
        maxRetryCount: Int? = null,
        description: String? = null
    ): StonePieceProcessParameter? {
        val existingConfig = getConfigByName(configName)
        if (existingConfig == null) {
            logger.warn("Config not found: $configName")
            return null
        }

        val updatedConfig = existingConfig.copy(
            enabled = enabled ?: existingConfig.enabled,
            nftIds = nftIds ?: existingConfig.nftIds,
            cronExpression = cronExpression ?: existingConfig.cronExpression,
            batchSize = batchSize ?: existingConfig.batchSize,
            enableDetailedLogging = enableDetailedLogging ?: existingConfig.enableDetailedLogging,
            maxRetryCount = maxRetryCount ?: existingConfig.maxRetryCount,
            description = description ?: existingConfig.description,
            updated = LocalDateTime.now()
        )

        logger.info("Updating stone piece process config: $configName")
        return stonePieceProcessParameterRepository.save(updatedConfig)
    }

    /**
     * 启用/禁用配置
     */
    fun toggleConfig(configName: String, enabled: Boolean): StonePieceProcessParameter? {
        return updateConfig(configName, enabled = enabled)
    }

    /**
     * 删除配置（软删除，设置为非活跃）
     */
    fun deleteConfig(configName: String): Boolean {
        val config = getConfigByName(configName)
        if (config == null) {
            logger.warn("Config not found for deletion: $configName")
            return false
        }

        val deactivatedConfig = config.copy(
            active = false,
            updated = LocalDateTime.now()
        )
        stonePieceProcessParameterRepository.save(deactivatedConfig)
        logger.info("Deactivated stone piece process config: $configName")
        return true
    }

    /**
     * 创建默认配置
     */
    private fun createDefaultConfig(): StonePieceProcessParameter {
        logger.info("Creating default stone piece process config")
        val defaultConfig = StonePieceProcessParameter(
            configName = DEFAULT_CONFIG_NAME,
            enabled = false,
            nftIds = emptyList(),
            cronExpression = "0 0 * * * ?",
            batchSize = 100,
            enableDetailedLogging = true,
            maxRetryCount = 3,
            description = "Default stone piece process configuration",
            active = true
        )
        return stonePieceProcessParameterRepository.save(defaultConfig)
    }

    /**
     * 初始化配置（如果不存在默认配置则创建）
     */
    fun initializeConfig() {
        val defaultConfig = getConfigByName(DEFAULT_CONFIG_NAME)
        if (defaultConfig == null) {
            createDefaultConfig()
            logger.info("Initialized default stone piece process config")
        } else {
            logger.info("Default stone piece process config already exists")
        }
    }

    /**
     * 检查是否有任何启用的配置
     */
    fun hasEnabledConfig(): Boolean {
        return getEnabledConfigs().isNotEmpty()
    }
}
