package io.vault.jasper.model

import org.springframework.data.annotation.Id
import org.springframework.data.annotation.LastModifiedDate
import org.springframework.data.mongodb.core.index.Indexed
import org.springframework.data.mongodb.core.mapping.Document
import org.springframework.data.mongodb.core.mapping.Field
import java.time.LocalDateTime

@Document
data class KYT(
    @Id
    val id: String? = null,
    @Indexed(unique = true, background = true)
    val address: String,
    var score: Int,
    var channel: UserChannel? = null,
    @LastModifiedDate
    var updated: LocalDateTime = LocalDateTime.now(),
    @Field("ip_address")
    var ipAddress: String? = null,
    var from: String? = null,
    @Field("utm_info")
    var utmInfo: UTMInfo? = null
)

enum class UserChannel{
    JASPER_VAULT,
    JP,
    ECHOOO,
    JASPER_QUANT,
    MINI_APP,
    MINI_APP_BACKEND,
    MARKET_PLACE,
    BTC_FI,
    BTC_FI_BACKEND
}

enum class UserWallet{
    BINANCE, META_MASK, TRUST_WALLET, COINBASE, JASPER_VAULT, OKX, BITGET, TOKEN_POCKET
}