package io.vault.jasper.response

import io.swagger.annotations.ApiModel
import io.swagger.annotations.ApiModelProperty
import java.math.BigDecimal

@ApiModel("用户网络邀请码信息")
data class UserNetworkInviteCodeResponse(
    @ApiModelProperty("邀请码", required = true)
    val inviteCode: String,
    @ApiModelProperty("返佣比例", required = true)
    val percentage: String,
    @ApiModelProperty("已绑定地址", required = true)
    val address: String?,
    @ApiModelProperty("别名", required = true)
    val alias: String?,
    @ApiModelProperty("绑定时间", required = true)
    val bindTime: Long?
)