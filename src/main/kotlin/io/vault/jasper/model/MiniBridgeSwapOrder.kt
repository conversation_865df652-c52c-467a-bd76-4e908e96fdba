package io.vault.jasper.model

import io.vault.jasper.enums.ChainType
import io.vault.jasper.enums.Symbol
import org.springframework.data.annotation.CreatedDate
import org.springframework.data.annotation.Id
import org.springframework.data.annotation.LastModifiedDate
import org.springframework.data.mongodb.core.index.Indexed
import org.springframework.data.mongodb.core.mapping.Document
import org.springframework.data.mongodb.core.mapping.Field
import org.springframework.data.mongodb.core.mapping.FieldType
import java.math.BigDecimal
import java.time.LocalDateTime

@Document("mini_bridge_swap_order")
data class MiniBridgeSwapOrder(
    @Id
    val id: String? = null,

    @Field("from_chain")
    @Indexed(background = true)
    val fromChain: ChainType,

    @Field("to_chain")
    @Indexed(background = true)
    val toChain: ChainType,

    @Field("from_address")
    @Indexed(background = true)
    var fromAddress: String,

    @Field("to_address")
    @Indexed(background = true)
    var toAddress: String,

    @Field("deposit_asset")
    @Indexed(background = true)
    var fromAsset: Symbol,

    @Field("withdraw_asset")
    @Indexed(background = true)
    var toAsset: Symbol,

    @Field("deposit_amount", targetType = FieldType.DECIMAL128)
    var depositAmount: BigDecimal,

    @Field("withdraw_amount", targetType = FieldType.DECIMAL128)
    var withdrawAmount: BigDecimal,

    @Field("price_id")
    @Indexed(background = true)
    var priceId: String,

    @Field("swap_fee", targetType = FieldType.DECIMAL128)
    @Indexed(background = true)
    var swapFee: BigDecimal? = null,

    @Field("actual_withdraw_amount", targetType = FieldType.DECIMAL128)
    var actualWithdrawAmount: BigDecimal? = null,

    @Indexed(background = true)
    @Field("deposit_tx_hash")
    var depositTxHash: String? = null,

    @Indexed(background = true)
    @Field("withdraw_fordefi_id")
    var withdrawFordefiId: String? = null,

    @Indexed(background = true)
    @Field("withdraw_tx_hash")
    var withdrawTxHash: String? = null,

    @Field("withdraw_settled")
    var withdrawSettled: Boolean = false,

    @CreatedDate
    val created: LocalDateTime = LocalDateTime.now(),

    @LastModifiedDate
    val updated: LocalDateTime = LocalDateTime.now(),

    @Indexed(background = true)
    var status: MiniBridgeSwapOrderStatus = MiniBridgeSwapOrderStatus.PENDING_DEPOSIT,

    @Field("error_message")
    var errorMessage: String? = null
)

enum class MiniBridgeSwapOrderStatus {
    PENDING_DEPOSIT,
    DEPOSIT_TIMEOUT,
    DEPOSIT_FAILED,
    PENDING_WITHDRAW,
    WITHDRAW_PROCESSING,
    WITHDRAW_FAILED,
    SUCCESS
}
