package io.vault.jasper.model

import io.swagger.annotations.ApiModel
import io.swagger.annotations.ApiModelProperty
import io.vault.jasper.enums.ChainType
import io.vault.jasper.enums.OptionDirection
import io.vault.jasper.enums.Symbol
import org.springframework.data.annotation.CreatedDate
import org.springframework.data.annotation.Id
import org.springframework.data.annotation.LastModifiedDate
import org.springframework.data.mongodb.core.index.Indexed
import org.springframework.data.mongodb.core.mapping.Document
import org.springframework.data.mongodb.core.mapping.Field
import org.springframework.data.mongodb.core.mapping.FieldType
import java.math.BigDecimal
import java.time.LocalDateTime

@Document
data class Order(
    @Id
    val id: String? = null,
    // 挂单创建者(地址)
    @Indexed(background = true)
    var creator: String,
    @Field("creator_role")
    val creatorRole: CreatorRole,
    @Indexed(background = true)
    val chain: ChainType,
    @Field("order_type")
    @Indexed(background = true)
    val orderType: OrderType,
    @Field("options_type")
    @Indexed(background = true)
    var optionsType: OptionDirection?,
    // 订单名称
    val name: String? = null,
    @Field("expiry_in_hour")
    var expiryInHour: String? = null,
    @Field("expiry_date")
    var expiryDate: LocalDateTime? = null,
    // 标的/抵押资产
    @Indexed
    @Field("underlying_asset")
    val underlyingAsset: Symbol,
    // 如果抵押资产是合约代币，则需要填写代币合约地址
    @Field("underlying_asset_address")
    val underlyingAssetAddress: String? = null,
    // 标的/抵押资产数量
    @Field("total_amount", targetType = FieldType.DECIMAL128)
    val totalAmount: BigDecimal,
    @Field("strike_asset")
    val strikeAsset: Symbol,
    // 行权价格(美元)
    @Field("strike_price", targetType = FieldType.DECIMAL128)
    val strikePrice: BigDecimal,
    // 可用行权资产列表
    @Field("available_premium_assets")
    var availablePremiumAssets: List<PremiumAsset>,
    // 权利金
    @Field("premium_fee", targetType = FieldType.DECIMAL128)
    var premiumFee: BigDecimal,
    // 结算方式
    @Field("settlement_types")
    val settlementTypes: List<SettlementType>,
    @Field("contract_content")
    val contractContent: Map<String, String>? = null,
    @Field("sign_data")
    val signData: String? = null,

    // 挂单创建者的vault地址
    @Field("creator_vault")
    @Indexed(background = true)
    val creatorVault: String? = null,
    // PUT: 锁住资产
    @Field("locked_asset")
    val lockedAsset: String? = null,
    // Deribit当前权利金价格
    @Field("deribit_premium_price", targetType = FieldType.DECIMAL128)
    val deribitPremiumPrice: BigDecimal? = null,
    @Indexed
    var status: OrderStatus = OrderStatus.CREATED,
    @Field("is_certified")
    var isCertified: Boolean = false,
    @Field("kyt_status")
    var kytStatus: KytStatus = KytStatus.UNDER_REVIEW,

    // 剩余标的/抵押资产数量
    @Field(targetType = FieldType.DECIMAL128)
    var amount: BigDecimal,

    @CreatedDate
    var created: LocalDateTime = LocalDateTime.now(),
    @LastModifiedDate
    val updated: LocalDateTime = LocalDateTime.now(),
    // 同步的原始数据
    val raw: String? = null,
    @Field("error_msg")
    var errorMsg: String? = null,
    @Field("bid_asset")
    @Indexed(background = true)
    var bidAsset: Symbol? = null,
    @Field("bid_amount")
    var bidAmount: BigDecimal? = null,
    @Field(targetType = FieldType.DECIMAL128)
    var volume: BigDecimal = BigDecimal.ZERO, //本订单的交易量。计算公式为 strike_price x bit_amount
    @Field("quote_asset")
    var quoteAsset: Symbol? = null,
    @Field("quote_asset_address")
    var quoteAssetAddress: String? = null,
)

enum class OrderStatus {
    CREATED,
    PENDING,
    PENDING_SETTLEMENT,
    COMPLETED,
    CANCELLED,
    FAILED;

    companion object {
        fun convert(s: String?) = when (s) {
            "pending" -> PENDING
            else -> COMPLETED
        }

        fun terminateStatuses(): List<OrderStatus> = listOf(COMPLETED, CANCELLED, FAILED)
    }
}

@ApiModel
data class PremiumAsset(
    @ApiModelProperty("资产类型", required = true)
    val asset: String,
    @ApiModelProperty("资产合约地址(可为空)")
    val address: String? = null
)

enum class SettlementType {
    CASH_SETTLEMENT,
    ASSET_DELIVERY
}

enum class KytStatus {
    UNDER_REVIEW,
    PASSED,
    REJECTED
}

enum class CreatorRole {
    SELLER, BUYER, SELLER_VAULT, BUYER_VAULT
}

enum class OrderType {
    DEGEN, SWAP
}