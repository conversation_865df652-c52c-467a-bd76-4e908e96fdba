package io.vault.jasper.task

import com.fasterxml.jackson.databind.ObjectMapper
import io.vault.jasper.model.*
import io.vault.jasper.repository.*
import io.vault.jasper.service.MailManager
import io.vault.jasper.service.SystemService
import io.vault.jasper.service.kol.KolLevelService
import org.slf4j.LoggerFactory
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.beans.factory.annotation.Value
import org.springframework.data.domain.Sort
import org.springframework.data.mongodb.core.MongoTemplate
import org.springframework.data.mongodb.core.aggregation.Aggregation
import org.springframework.data.mongodb.core.aggregation.AggregationResults
import org.springframework.data.mongodb.core.aggregation.MatchOperation
import org.springframework.data.mongodb.core.query.Criteria
import org.springframework.scheduling.annotation.Scheduled
import org.springframework.stereotype.Service
import java.math.BigDecimal
import java.time.LocalDate
import java.time.LocalDateTime
import java.time.ZoneId
import java.time.ZonedDateTime
import java.util.regex.Pattern

@Service
class KolLevelProcessor @Autowired constructor(
    private val kolApplyFormRepository: KolApplyFormRepository,
    private val mailManager: MailManager,
    private val kolRepository: KolRepository,
    private val kolLevelRepository: KolLevelRepository,
    private val kolLevelService: KolLevelService,
    private val userPremiumSummaryRepository: UserPremiumSummaryRepository,
    private val userRepository: UserRepository,
    private val optionOrderRepository: OptionOrderRepository,
    private val kolRebateRecordDailySummaryRepository: KolRebateRecordDailySummaryRepository,
    private val systemService: SystemService,
    private val mongoTemplate: MongoTemplate
) {

    private val logger = LoggerFactory.getLogger(this::class.java)

    private val objectMapper = ObjectMapper()

    @Value("\${schedule_task_on}")
    lateinit var scheduleOn: String

    // @Scheduled(fixedDelay = 24 * 60 * 60 * 1000, initialDelay = 10 * 1000)
    //@Scheduled(cron = "0 0 6 * * *", zone = "Asia/Singapore")
    fun run() {
        if(scheduleOn == "false"){
            return
        }
        try {
            exec()
        } catch (e: Exception) {
            logger.error(e.message, e)
        }
    }

    private fun exec() {
        val kolLevelList = kolLevelRepository.findAll(Sort.by(Sort.Order.asc(KolLevel::order.name)))

        val kolList = kolRepository.findByStatus(KolStatus.ACTIVE)
        val minInvitedUserCount = run {
            val level1 = kolLevelList.firstOrNull { it.name == "1" } ?: return@run null
            val conditions = try {
                objectMapper.readTree(level1.conditions ?: "")
            } catch (e: Exception) {
                logger.error("解析Level_1 conditions json字符串失败: ${level1.conditions}", e)
                return@run null
            }
            conditions["min_users"].asInt()
        } ?: throw Exception("未找到Level_1的最小用户数配置")

        val userCount = kolList.count()
        logger.info("共 $userCount 个用户需要计算KOL等级")
        kolList.forEach { kol ->

            // 看看该用户是否是自动结算的
            if(!kol.autoSettle){
                logger.info("用户 ${kol.wallet} 不是自动结算的")
                return@forEach
            }

            val user = userRepository.findByAddressIgnoreCase(kol.wallet) ?: return@forEach
            val invitedUsers = userRepository.findByInvitedUserId(user.id!!)
            if (invitedUsers.size < minInvitedUserCount) {
                logger.info("用户 ${user.address} 邀请的用户数不足 $minInvitedUserCount 个")
                if (kol.level != "0") {
                    logger.info("用户 ${user.address} 等级变化: Lv${kol.level} -> Lv0")
                    kol.level = "0"
                    kol.incentive = BigDecimal.ZERO
                    kolRepository.save(kol)
                }
                return@forEach
            }
            // v1(invitedUsers, kol, kolLevelList)
            v2(user, invitedUsers, kol, kolLevelList)
        }
        logger.info("KOL等级计算完成")
    }

    private fun v1(invitedUsers: List<User>, kol: Kol, kolLevelList: List<KolLevel>) {
        // 累积支付权利金
        val totalPremiums = invitedUsers.mapNotNull {
            userPremiumSummaryRepository.findByUserId(it.id!!)?.totalPremium
        }.fold(BigDecimal.ZERO) { acc, i -> acc + i } // 受邀地址累积支付权利金

        logger.info("KOL Level Processor Referral total premium found: $totalPremiums")
        // 日均活跃地址数
        val now = LocalDate.now()
        val monthAgo = now.minusDays(30)
        val dailySummaryList = kolRebateRecordDailySummaryRepository.findByKolIdAndRecordDateIsGreaterThanEqual(
            kol.id!!,
            monthAgo
        )
        val averageActiveAddressCount = dailySummaryList.map { it.activeUsers }.average()
        logger.info("KOL Level Processor Last 30 days Active addresses found: $averageActiveAddressCount")

        kolLevelList.forEach { l ->
            val level = kolLevelService.getKolLevel(l.name)
            if (level.matchConditions(
                    Pair("totalPremium", totalPremiums),
                    Pair("activeAddresses", averageActiveAddressCount.toLong())
                )
            ) {
                kol.level = l.name
                kolRepository.save(kol)
                logger.info("KOL ${kol.wallet} level updated to Lv${l.name}")
            }
        }
    }

    private fun v2(user: User, invitedUsers: List<User>, kol: Kol, kolLevelList: List<KolLevel>) {
        val kolRatingDays = systemService.getParameter().kolRatingDays
        val (start, end) = getDateTimeBetween(kolRatingDays)
        val addresses = invitedUsers.map { it.address }.map { Pattern.compile("^$it$", Pattern.CASE_INSENSITIVE) }
        
        // 修改查询条件顺序
        val match = Aggregation.match(
            Criteria.where(OptionOrder::buyer.name).`in`(addresses)
                .and(OptionOrder::updated.name).gte(start).lt(end)
                .and(OptionOrder::status.name).`is`(OptionStatus.SETTLED)
                .and(OptionOrder::premiumFree.name).ne(true)
        )

        val groupFields: String = OptionOrder::buyer.name

        val aggregation: Aggregation = Aggregation.newAggregation(
            match,
            Aggregation.group(groupFields).count().`as`("count")
        )

        val results: AggregationResults<Map<*, *>> =
            mongoTemplate.aggregate(aggregation, OptionOrder::class.java, Map::class.java)

        val mappedActiveUserCount = results.mappedResults.size
        logger.info("用户 ${user.address} 受邀用户 $kolRatingDays 天内有 $mappedActiveUserCount 个参与了交易")
        kolLevelList.firstOrNull {
            val levelImpl = kolLevelService.getKolLevel(it.name)
            levelImpl.matchInvitedUsers(mappedActiveUserCount)
        }?.let { newLv ->
            if (kol.level != newLv.name) {
                val oldLv = kol.level
                kol.level = newLv.name
                val oldIncentive = kol.incentive
                kol.incentive = newLv.incentiveRate
                kolRepository.save(kol)
                logger.info("用户 ${user.address} 等级变化: Lv$oldLv -> Lv${newLv.name}\t" +
                        "返佣比例: $oldIncentive -> ${newLv.incentiveRate}")
            }
        }
    }

    private fun getDateTimeBetween(days: Int): Pair<LocalDateTime, LocalDateTime> {
        val targetZoneId = ZoneId.of("Asia/Singapore")
        val now = ZonedDateTime.now().withZoneSameInstant(targetZoneId)
        val today = now.withHour(0).withMinute(0).withSecond(0).withNano(0)
        val startDateTime = today.minusDays(days.toLong()).withZoneSameInstant(ZoneId.systemDefault()).toLocalDateTime()
        val endDateTime = today.withZoneSameInstant(ZoneId.systemDefault()).toLocalDateTime()

        return Pair(startDateTime, endDateTime)
    }
}