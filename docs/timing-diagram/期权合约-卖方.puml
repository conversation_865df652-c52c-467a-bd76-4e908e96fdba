@startuml
participant SellerUser as S
participant BuyerUser as B
participant Server as Srv
participant Deribit as D
participant OptionContract as OC


S -> Srv: 发起挂单交易\n提交授权签名和订单参数
activate Srv
Srv -> S: 挂单成功
deactivate Srv

loop 价格更新
    Srv -> D: 从Deribit供应商更新价格
    activate Srv
    activate D
    Srv <- D: 更新成功
    deactivate Srv
    deactivate D
end

B -> Srv: 发起订单交易\n提交授权签名
activate B
activate Srv
Srv -> OC: 发送买卖双方的授权签名
activate OC
OC --> Srv: 合约成功创建
Srv -> B: 订单交易成功
deactivate Srv
deactivate B
deactivate OC

Srv -> OC: 到期发起结算操作
activate Srv
activate OC
OC -> Srv: 结算完成
deactivate Srv
OC --> S: 到账
OC --> B: 到账
deactivate OC
@enduml
