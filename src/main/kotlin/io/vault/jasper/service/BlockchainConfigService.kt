package io.vault.jasper.service

import io.vault.jasper.enums.ChainType
import io.vault.jasper.model.Blockchain
import io.vault.jasper.repository.BlockchainRepository
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.data.mongodb.core.MongoTemplate
import org.springframework.data.mongodb.core.query.Criteria
import org.springframework.data.mongodb.core.query.Query
import org.springframework.data.mongodb.core.query.Update
import org.springframework.stereotype.Service
import java.time.LocalDateTime

@Service
class BlockchainConfigService @Autowired constructor(
    private val blockchainRepository: BlockchainRepository,
    private val mongoTemplate: MongoTemplate
) {

    fun getByChain(chain: ChainType): Blockchain? = blockchainRepository.findFirstByChain(chain)

    fun updateBlockNumber(chain: ChainType, blockNumber: Long): Long =
        updateField(chain, Blockchain::blockNumber.name, blockNumber)

    fun updateEventListenerBlockNumber(chain: ChainType, blockNumber: Long): Long =
        updateField(chain, Blockchain::eventListenerBlockNumber.name, blockNumber)

    fun updateField(chain: ChainType, fieldName: String, value: Any): Long =
        updateFields(chain, mapOf(fieldName to value))

    fun updateFields(chain: ChainType, data: Map<String, Any>): Long {
        val update = Update()
        data.forEach { (fieldName, value) -> update.set(fieldName, value) }
        if (data.filter { it.key == Blockchain::updated.name }.isEmpty()) {
            update.set(Blockchain::updated.name, LocalDateTime.now())
        }
        val query = Query()
        query.addCriteria(Criteria.where(Blockchain::chain.name).`is`(chain))
        val updateResult = mongoTemplate.updateMulti(query, update, Blockchain::class.java)

        return updateResult.modifiedCount
    }
}