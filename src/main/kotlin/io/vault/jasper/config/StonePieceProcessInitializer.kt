package io.vault.jasper.config

import io.vault.jasper.service.StonePieceProcessParameterService
import org.slf4j.LoggerFactory
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.ApplicationArguments
import org.springframework.boot.ApplicationRunner
import org.springframework.stereotype.Component

/**
 * 石头碎片处理配置初始化器
 * 应用启动时自动初始化默认配置
 */
@Component
class StonePieceProcessInitializer @Autowired constructor(
    private val stonePieceProcessParameterService: StonePieceProcessParameterService
) : ApplicationRunner {

    private val logger = LoggerFactory.getLogger(this::class.java)

    override fun run(args: ApplicationArguments?) {
        try {
            logger.info("Initializing stone piece process configuration...")
            stonePieceProcessParameterService.initializeConfig()
            logger.info("Stone piece process configuration initialized successfully")
        } catch (e: Exception) {
            logger.error("Failed to initialize stone piece process configuration", e)
        }
    }
}
