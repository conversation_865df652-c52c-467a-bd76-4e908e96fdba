package io.vault.jasper.task

import io.vault.jasper.model.*
import io.vault.jasper.repository.*
import io.vault.jasper.service.*
import io.vault.jasper.service.blockchain.ArbitrumService
import org.slf4j.LoggerFactory
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.beans.factory.annotation.Value
import org.springframework.context.annotation.Profile
import org.springframework.scheduling.annotation.Scheduled
import org.springframework.stereotype.Service
import java.math.BigDecimal

@Service
@Profile("prod", "test")
class AirdropUpdateUserCampaignInfoProcessor @Autowired constructor(
    private val airDropService: AirDropService,
    private val userCampaignInfoRepository: UserCampaignInfoRepository,
    private val userRepository: UserRepository,
    private val airdropSummaryRepository: AirdropSummaryRepository
) {

    private val logger = LoggerFactory.getLogger(this::class.java)

    @Value("\${schedule_task_on}")
    lateinit var scheduleOn: String

    // 每15分钟执行一次
    @Scheduled(fixedDelay = 2 * 60 * 1000, initialDelay = 30 * 1000)
    fun run() {

        if(scheduleOn == "false"){
            return
        }

        try {
            exec()
        } catch (e: Exception) {
            logger.error(e.message, e)
        }
    }

    private fun exec() {

        //logger.info("Airdrop Update UserCampaignInfo Processor start")

        val airdropSummary = airDropService.getAirdropSummary()
        airdropSummary.socialTaskTotalPoint = airDropService.getTotalSPoint(AirDropType.SOCIAL_TASK)
        //airdropSummary.inviteTaskTotalPoint = airDropService.getTotalSPoint(AirDropType.INVITATION_TASK)
        airdropSummary.inviteTaskTotalPoint = BigDecimal("20000000")
        airdropSummary.tradeTaskTotalPoint = airDropService.getTotalSPoint(AirDropType.TRADING_TASK)
        airdropSummaryRepository.save(airdropSummary)

        val parameter = airDropService.getAirdropParameter()
        if(parameter.userCampaignTaskSwitch == false){
            logger.info("Airdrop Update UserCampaignInfo task switch is off")
            return
        }

//        val allAirdropTransactions = airDropService.getAllAirdropTransactions()
//        val handledList = mutableListOf<String>()
//        var count = 0
//
//        for(airdropTransaction in allAirdropTransactions){
//
//            count++
//            val address = airdropTransaction.address
//            if(handledList.contains(address)){
//                continue
//            }
//
//            logger.info("Airdrop Update UserCampaignInfo processing count: $count / ${allAirdropTransactions.size}")
//
//            val user = userRepository.findByAddressIgnoreCase(address) ?: continue
//
//            airDropService.updateUserCampaignReferralCount(
//                user
//            )
//            airDropService.updateUserCampaignInfoTaskCountWithType(
//                user,
//                AirDropType.INVITATION_TASK
//            )
//            airDropService.updateUserCampaignInfoTaskCountWithType(
//                user,
//                AirDropType.TRADING_TASK
//            )
//            airDropService.updateUserCampaignInfoTaskPointWithType(
//                user,
//                AirDropType.SOCIAL_TASK
//            )
//            airDropService.updateUserCampaignInfoTaskPointWithType(
//                user,
//                AirDropType.INVITATION_TASK
//            )
//            airDropService.updateUserCampaignInfoTaskPointWithType(
//                user,
//                AirDropType.TRADING_TASK
//            )
//
//            handledList.add(address)
//        }

        val allNullTotalPointInfo = userCampaignInfoRepository.findByTotalPointIsNull()
        //logger.info("Airdrop Update UserCampaignInfo Null total point info size: ${allNullTotalPointInfo.size}")
        var count = 0

        for(userCampaignInfo in allNullTotalPointInfo){
            count++
            //logger.info("Airdrop Update UserCampaignInfo processing count: $count / ${allNullTotalPointInfo.size}")

            val user = userRepository.findByAddressIgnoreCase(userCampaignInfo.address) ?: continue
            airDropService.updateUserCampaignInfo(user)
        }

        //logger.info("Airdrop Update UserCampaignInfo Processor start end")
    }
}