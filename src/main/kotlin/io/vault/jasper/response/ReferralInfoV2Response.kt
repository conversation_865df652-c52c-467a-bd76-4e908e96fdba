package io.vault.jasper.response

import io.swagger.annotations.ApiModel
import io.swagger.annotations.ApiModelProperty
import io.vault.jasper.enums.ChainType
import io.vault.jasper.enums.Symbol
import io.vault.jasper.model.OrderType
import java.math.BigDecimal

@ApiModel("邀请信息")
data class ReferralInfoV2Response(
    @ApiModelProperty("邀请总数", required = true)
    val refereeCount: Int,
    @ApiModelProperty("邀请码", required = true)
    val inviteCode: String,
    @ApiModelProperty("父级钱包地址", required = false)
    val parentAddress: String? = null,
    @ApiModelProperty("父级邀请码", required = false)
    val parentReferral: String? = null,
    @ApiModelProperty("获取的SPoint总数", required = false)
    val totalSPoints: String? = null,
    @ApiModelProperty("Rebate 总数", required = false)
    val totalRebate: String? = null,
    @ApiModelProperty("用户地址", required = false)
    val address: String? = null,
    @ApiModelProperty("返佣比例", required = false)
    val percentage: String? = null
)