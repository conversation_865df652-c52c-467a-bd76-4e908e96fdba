package io.vault.jasper.config

import org.springframework.boot.context.properties.ConfigurationProperties
import org.springframework.stereotype.Component
import java.math.BigInteger

/**
 * 石头碎片处理定时任务参数配置
 */
@Component
@ConfigurationProperties(prefix = "stone-piece-process")
data class StonePieceProcessParameter(
    /**
     * 任务开关，默认关闭
     */
    var enabled: Boolean = false,
    
    /**
     * 需要处理的NFT ID列表
     */
    var nftIds: List<String> = emptyList(),
    
    /**
     * 定时任务执行间隔（cron表达式），默认每小时执行一次
     */
    var cronExpression: String = "0 0 * * * ?",
    
    /**
     * 批处理大小，默认100
     */
    var batchSize: Int = 100,
    
    /**
     * 是否启用详细日志
     */
    var enableDetailedLogging: Boolean = true,
    
    /**
     * 最大重试次数
     */
    var maxRetryCount: Int = 3
) {
    
    /**
     * 获取BigInteger格式的NFT ID列表
     */
    fun getNftIdsBigInteger(): List<BigInteger> {
        return nftIds.map { BigInteger(it) }
    }
    
    /**
     * 检查指定的NFT ID是否在处理列表中
     */
    fun shouldProcessNftId(nftId: String): Boolean {
        return nftIds.contains(nftId)
    }
    
    /**
     * 检查指定的NFT ID是否在处理列表中
     */
    fun shouldProcessNftId(nftId: BigInteger): Boolean {
        return nftIds.contains(nftId.toString())
    }
}
