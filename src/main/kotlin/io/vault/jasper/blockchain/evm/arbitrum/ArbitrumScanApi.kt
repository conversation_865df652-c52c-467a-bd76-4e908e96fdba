package io.vault.jasper.blockchain.evm.arbitrum

import io.vault.jasper.blockchain.EvmScanApi
import org.springframework.beans.factory.annotation.Value
import org.springframework.stereotype.Service

@Service
class ArbitrumScanApi : EvmScanApi() {

    @Value("\${blockchain.arbitrum.arbiscan.base_url}")
    override lateinit var baseUrl: String

    @Value("\${blockchain.arbitrum.arbiscan.api_tokens}")
    override lateinit var apiTokens: List<String>

}