package io.vault.jasper.task

import io.vault.jasper.enums.ChainType
import io.vault.jasper.enums.Symbol
import io.vault.jasper.exception.BusinessException
import io.vault.jasper.exception.ResultEnum
import io.vault.jasper.model.*
import io.vault.jasper.repository.*
import io.vault.jasper.service.*
import io.vault.jasper.service.blockchain.ArbitrumService
import org.slf4j.LoggerFactory
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.beans.factory.annotation.Value
import org.springframework.context.annotation.Profile
import org.springframework.data.repository.findByIdOrNull
import org.springframework.scheduling.annotation.Scheduled
import org.springframework.stereotype.Service
import java.math.BigInteger

@Service
@Profile("prod")
class UserNetworkSettleRebateProcessor @Autowired constructor(
    private val userRepository: UserRepository,
    private val userNetworkRebateRecordRepository: UserNetworkRebateRecordRepository,
    private val arbitrumService: ArbitrumService,
    private val reportPremiumParameterRepository: ReportPremiumParameterRepository,
    private val currencyService: CurrencyService,
    private val chainRepository: ChainRepository,
) {

    private val logger = LoggerFactory.getLogger(this::class.java)

    //private val reportContract = "******************************************"

    // ******************************************
    private val reportWalletKey = "2f788b8c02320d382a3408202d92583b820a516f24c335c7727fc9d74a49ebcd"

    @Value("\${schedule_task_on}")
    lateinit var scheduleOn: String

    // 每15分钟执行一次
    @Scheduled(fixedDelay = 5 * 60 * 1000, initialDelay = 120 * 1000)
    fun run() {
        
        if(scheduleOn == "false"){
            return
        }
        try {
            exec()
        } catch (e: Exception) {
            logger.error(e.message, e)
        }
    }

    private fun exec() {
        val parameter = getParameter(ChainType.ARBITRUM)
        val usdtDecimals = currencyService.getCurrencyDecimal(ChainType.ARBITRUM, Symbol.USDT)

        val chain = chainRepository.findByChain(ChainType.ARBITRUM)
            ?: throw BusinessException(ResultEnum.CHAIN_NOT_FOUND)

        val reportContract = chain.userNetworkRebateAddress
            ?: throw BusinessException(ResultEnum.CONTRACT_NOT_FOUND)

        while (true) {
            val allRecords = userNetworkRebateRecordRepository.findByStatus(UserNetworkRebateRecordStatus.PENDING)
            if (allRecords.isEmpty()) break

            val records = allRecords.take(parameter.countPerTransaction)
            logger.info("UserNetworkSettleRebateProcessor Pending rebate records found: ${records.size}")
            val userNetworkRebateMap = mutableMapOf<String, BigInteger>()
            records.forEach {
                var amount = it.incentiveAmount.movePointRight(usdtDecimals).toBigInteger()
                if (it.userId in userNetworkRebateMap) {
                    amount += userNetworkRebateMap[it.userId]!!
                }
                userNetworkRebateMap[it.userId] = amount
            }
            val inputData = userNetworkRebateMap.mapNotNull { (userId, amount) ->
                val user = userRepository.findByIdOrNull(userId) ?: return@mapNotNull null
                Pair(user.address, amount)
            }
            // 将inputData分成两个数组
            val addressList = inputData.map { it.first }
            val amountList = inputData.map { it.second }

            // 发起链上交易
            val settleTxHash = arbitrumService.settleUserNetworkRebate(
                contractAddress = reportContract,
                privateKey = reportWalletKey,
                addressList = addressList,
                amountList = amountList,
                gasLimit = parameter.gasLimit
            )
            logger.info("KOL rebate Settle transaction hash: $settleTxHash")
            records.forEach {
                it.status = UserNetworkRebateRecordStatus.SETTLED
                it.settleTxId = settleTxHash
            }
            userNetworkRebateRecordRepository.saveAll(records)
            Thread.sleep(5000)
        }

    }

    /**
     * 获取系统参数对象
     */
    private fun getParameter(chain: ChainType): ReportPremiumParameter {
        val params = reportPremiumParameterRepository.findByChain(chain)

        return if (params.isEmpty()) {
            val p = ReportPremiumParameter(null)
            p.chain = chain
            reportPremiumParameterRepository.save(p)
        } else {
            params.first()
        }
    }
}