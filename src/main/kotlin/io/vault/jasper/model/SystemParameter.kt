package io.vault.jasper.model

import org.springframework.data.annotation.Id
import org.springframework.data.mongodb.core.mapping.Document
import java.math.BigDecimal
import java.time.LocalDateTime

@Document
data class SystemParameter(
    @Id
    val id: String? = null,
    val premiumPriceTimeout: Long = 5, // 期权价格超时时间，单位秒
    val userAccessTokenTimeout: Long = 720, // 用户访问令牌超时时间，单位小时
    val premiumPriceRatePercentage: BigDecimal = BigDecimal("23.08"), // 期权价格费率(百分比)
    val systemStatus: String = "NORMAL", // 系统状态，NORMAL: 正常，MAINTAIN: 维护中
    var kolApplyReceivingMails: List<String> = listOf(),
    val premiumPriceRateInContract: BigDecimal = BigDecimal("23.08"), // 智能合约中设置的期权价格费率(百分比)
    val orderExpiryTime: Int = 60, // 订单过期时间，单位秒
    // MintEventTask任务的开关
    var mintEventTaskSwitch: Boolean = false,
    /**
     * KOL评级计算天数
     * 2024-08-29
     */
    var kolRatingDays: Int = 90,

    /**
     * 订单的交易量的预警阈值
     */
    var orderVolumeWarningThreshold: BigDecimal = BigDecimal("1000000"),

    /**
     * 最后一笔订单生成时间预警（分钟）
     */
    var lastOrderTimeWarningMinutes: Int = 120,

    /**
     * 月光宝盒活动：允许N分钟前使用本功能
     */
    var moonlightBoxActivityAllowMinutes: Int = 10,
    /**
     * 结算UserNetwork任务开关
     */
    var settleUserNetworkTaskSwitch: Boolean = false,
    /**
     * KYT任务开关
     */
    var kytTaskSwitch: Boolean = false,

    /**
     * KYT检查时间间隔(分钟)
     */
    var kytCheckIntervalInMinutes: Int = 60,

    /**
     * Limit order settlement time out in minutes
     */
    var limitOrderSettlementTimeOutInMinutes: Int = 60,

    /**
     * premium fee update task option order id
     */
    var premiumFeeUpdateTaskOrderCreated: LocalDateTime? = null,

    /**
     * 买家收益率预警阈值(百分比)
     */
    var maxBuyerProfitRatio: BigDecimal = BigDecimal("500"),

    /**
     * 同一区块中允许的最大订单成交数量
     */
    var maxOrdersPerBlock: Int = 20,

    /**
     * 同一交易中允许的最大订单数量
     */
    var maxOrdersPerTransaction: Int = 5,
)