package io.vault.jasper.service

import io.vault.jasper.enums.ChainType
import io.vault.jasper.enums.Symbol
import io.vault.jasper.exception.BusinessException
import io.vault.jasper.exception.ResultEnum
import io.vault.jasper.model.Currency
import io.vault.jasper.repository.CurrencyRepository
import org.slf4j.LoggerFactory
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.stereotype.Service

@Service
class CurrencyService @Autowired constructor(
    private val currencyRepository: CurrencyRepository
){

    private val logger = LoggerFactory.getLogger(this::class.java)

    fun initCurrency(
        chain: ChainType,
        symbol: Symbol,
        contractAddress: String,
        decimal: Int,
        pythId: String?, // Pyth 报价 ID
        aproId: String? = null // APRO 报价 ID
    ){
        val currency = currencyRepository.findFirstBySymbol(symbol.name)
        if (currency == null) {
            currencyRepository.save(
                Currency(
                    symbol = symbol.name,
                    addresses = mutableMapOf(chain to contractAddress),
                    decimals = mutableMapOf(chain to decimal),
                    pythId = pythId,
                    chains = mutableListOf(chain),
                    aproId = aproId
                )
            )
        } else {

            if(!currency.chains.contains(chain)){
                currency.chains.add(chain)
            }

            if (currency.decimals == null) {
                currency.decimals = mutableMapOf()
                currency.decimals!![chain] = decimal
            } else if (chain !in currency.decimals!!) {
                currency.decimals!![chain] = decimal
            }

            if(currency.addresses == null){
                currency.addresses = mutableMapOf()
                currency.addresses!![chain] = contractAddress
            } else if (chain !in currency.addresses!!) {
                currency.addresses!![chain] = contractAddress
            }

            currencyRepository.save(currency)
        }
    }

    fun getCurrencyDecimal(chain: ChainType, symbol: Symbol): Int {
        val currencyDecimal = currencyRepository.findFirstBySymbol(symbol.name)?.decimals
        if (currencyDecimal == null) {
            logger.error("$symbol currency decimal not found")
            throw BusinessException(ResultEnum.DECIMAL_NOT_FOUND)
        }
        val decimal = currencyDecimal[chain] ?: 18
        return decimal
    }

    fun getCurrencyContract(chain: ChainType, symbol: Symbol): String{
        val currencyContract = currencyRepository.findFirstBySymbol(symbol.name)?.addresses
        if (currencyContract == null) {
            logger.error("$symbol currency contract not found")
            throw BusinessException(ResultEnum.CONTRACT_NOT_FOUND)
        }
        val contract = currencyContract[chain] ?: ""
        return contract
    }

    fun getPriceId(symbol: Symbol): String {
        val pythId = currencyRepository.findFirstBySymbol(symbol.name)?.pythId
        if (pythId == null) {
            logger.error("$symbol currency pythId not found")
            throw BusinessException(ResultEnum.UNSUPPORTED_BID_ASSET)
        }
        return pythId
    }

    fun getAproId(symbol: Symbol): String {
        val aproId = currencyRepository.findFirstBySymbol(symbol.name)?.aproId
        if (aproId == null) {
            logger.error("$symbol currency aproid not found")
            throw BusinessException(ResultEnum.UNSUPPORTED_BID_ASSET)
        }
        return aproId
    }

    fun getByPythId(pythId: String): Currency? {
        return currencyRepository.findFirstByPythId(pythId)
    }

    fun getAddressByChainAndSymbol(symbol: Symbol, chain: ChainType): String? {
        val currency = currencyRepository.findFirstBySymbol(symbol.name)

        return currency?.addresses?.get(chain)
    }

    fun fetchAll(): List<Currency> = currencyRepository.findAll()

    fun getCurrencyByAddressAndChain(address: String, chain: ChainType): Currency? {
        val currencies = fetchAll().filter { c ->
            // c.chains.contains(chain) && (c.addresses?.values?.any { a -> a.compareTo(address, true) == 0 } ?: false)
            if (!c.chains.contains(chain)) {
                false
            } else {
                val addressInChain = c.addresses?.get(chain) ?: return@filter false
                address.compareTo(addressInChain, true) == 0
            }
        }

        return currencies.firstOrNull()
    }

    fun getOptionQuoteAsset(
        chain: ChainType,
        symbol: Symbol?
    ): Symbol {

        if (chain == ChainType.BASE) {
            return Symbol.USDC
        }
        return Symbol.USDT
    }
}