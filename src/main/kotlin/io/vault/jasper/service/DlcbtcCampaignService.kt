package io.vault.jasper.service

import io.vault.jasper.enums.ChainType
import io.vault.jasper.enums.OptionDirection
import io.vault.jasper.enums.Symbol
import io.vault.jasper.model.*
import io.vault.jasper.repository.*
import org.slf4j.LoggerFactory
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.data.repository.findByIdOrNull
import org.springframework.stereotype.Service
import java.math.BigDecimal
import java.time.LocalDateTime
import java.util.*

@Service
class DlcbtcCampaignService @Autowired constructor(
    private val dlcbtcTradeRebateRecordRepository: DlcbtcTradeRebateRecordRepository,
    private val dlcbtcCampaignParameterRepository: DlcbtcCampaignParameterRepository,
    private val optionOrderRepository: OptionOrderRepository,
    private val userRepository: UserRepository,
    private val orderRepository: OrderRepository,
    private val discordLevelRepository: DiscordLevelRepository,
    private val currencyService: CurrencyService,
    private val airDropService: AirDropService,
    private val userPointRecordRepository: UserPointRecordRepository,
    private val userPointService: UserPointService
){

    private val logger = LoggerFactory.getLogger(this::class.java)

    /**
     * 获取空投参数对象
     */
    fun getCampaignParameter(): DlcbtcCampaignParameter {
        val params = dlcbtcCampaignParameterRepository.findAll()

        return if (params.isEmpty()) {
            val p = DlcbtcCampaignParameter(null)
            dlcbtcCampaignParameterRepository.save(p)
        } else {
            params.first()
        }
    }

    /**
     * 判断是否活动内首单
     */
    fun isFirstOrderInCampaign(
        optionOrder: OptionOrder
    ): Boolean{

        if(optionOrder.status != OptionStatus.SETTLED &&
            optionOrder.status != OptionStatus.EXECUTED &&
            optionOrder.status != OptionStatus.SETTLE_FAILED){
            return false
        }

        val parameter = getCampaignParameter()

        // 符合 2H，0.01 dlcBTC 使用 BTC 或者 USDT 支付期权费 规格的订单
        if(optionOrder.bidAsset == null || optionOrder.bidAsset != Symbol.DLCBTC){
            logger.info("DlcbtcCampaignService Service Not BTC ${optionOrder.buyer} ${optionOrder.id}")
            return false
        }

        if(optionOrder.expiryInHour == null || optionOrder.expiryInHour != "2"){
            logger.info("DlcbtcCampaignService Service Not 2 hours ${optionOrder.buyer} ${optionOrder.id}")
            return false
        }

        if(optionOrder.bidAmount == null || optionOrder.bidAmount!!.compareTo(parameter.firstTradeDegenQuantity) != 0){
            logger.info("DlcbtcCampaignService Service Not 0.01 BTC ${optionOrder.buyer} ${optionOrder.id}")
            return false
        }

        // 活动开始后的第一单DLCBTC
        val firstOptionOrder = optionOrderRepository.findFirstByChainAndBidAssetAndBuyerIgnoreCaseAndCreatedGreaterThan(
            ChainType.ARBITRUM,
            Symbol.DLCBTC,
            optionOrder.buyer!!,
            parameter.startDate
        )

        if(firstOptionOrder == null){
            return false
        }

        if(firstOptionOrder.id!! != optionOrder.id!!){
            return false
        }

        val user = userRepository.findByAddressIgnoreCase(optionOrder.buyer!!)
        if(user == null){
            return false
        }

        val airdropParameter = airDropService.getAirdropParameter()

        if(user.created.isBefore(airdropParameter.startDate)){
            logger.info("DlcbtcCampaignService Service Not new wallet")
            return false
        }

        return true
    }

    fun createTradeRebateRecord(
        optionOrder: OptionOrder
    ){

        val parameter = getCampaignParameter()
        val rebateCount = getFirstTradeRebateCount()
        if( rebateCount >= parameter.firstTradeRebateCount){
            logger.info("DlcbtcCampaignService first trade rebate task reach max count")
            return
        }

        val now = LocalDateTime.now()
        if(now.isAfter(parameter.endDate)){
            logger.info("DlcbtcCampaignService first trade rebate task $now reach end date ${parameter.endDate}")
            return
        }

        // 是否已经创建了订单
        val existingRecord = dlcbtcTradeRebateRecordRepository.findByOptionOrderId(
            optionOrder.id!!
        )

        if(existingRecord != null){
            logger.info("DlcbtcCampaignService Service Already add rebate record ${optionOrder.buyer} ${optionOrder.id}")
            return
        }

        // 是否有同样地址的订单
        val existingAddressRecords = dlcbtcTradeRebateRecordRepository.findByBuyerAddressIgnoreCase(
            optionOrder.buyer!!
        )

        if(existingAddressRecords.isNotEmpty()){
            logger.info("DlcbtcCampaignService Service Already same address rebate record ${optionOrder.buyer} ${optionOrder.id}")
            return
        }

        val record = DlcbtcTradeRebateRecord(
            optionOrderId = optionOrder.id,
            buyerAddress = optionOrder.buyer!!,
            direction = optionOrder.direction!!,
            bidAmount = optionOrder.bidAmount!!
        )

        dlcbtcTradeRebateRecordRepository.save(record)
    }

    fun checkDlcbtcTradeRebate(
        record: DlcbtcTradeRebateRecord
    ){

        val optionOrder = optionOrderRepository.findByIdOrNull(record.optionOrderId)
        if(optionOrder == null){
            logger.info("DlcbtcCampaignService Service can not find order record ${record.buyerAddress} ${record.optionOrderId}")
            return
        }

        if(optionOrder.status == OptionStatus.SETTLE_FAILED){
            logger.info("DlcbtcCampaignService Service order settle failed ${optionOrder.buyer} ${optionOrder.id}")
            record.status = DlcbtcTradeRebateRecordStatus.SETTLE_FAILED
            dlcbtcTradeRebateRecordRepository.save(record)
            return
        }

        if(optionOrder.status != OptionStatus.SETTLED){
            logger.info("DlcbtcCampaignService Service Not Settled order ${optionOrder.buyer} ${optionOrder.id}")
            return
        }

        //是否是新用户
        val user = userRepository.findByAddressIgnoreCase(optionOrder.buyer!!)
        if(user == null){
            logger.debug("DlcbtcCampaignService Service not new user ${optionOrder.buyer} ${optionOrder.id}")
            return
        }

        val airdropParameter = airDropService.getAirdropParameter()
        if(user.created.isBefore(airdropParameter.startDate)){
            logger.debug("DlcbtcCampaignService Service not new user ${optionOrder.buyer} ${optionOrder.id}")
            return
        }

        val order = orderRepository.findByIdOrNull(optionOrder.orderId)
        if(order == null){
            logger.debug("DlcbtcCampaignService Service can not find order record ${optionOrder.buyer} ${optionOrder.id}")
            return
        }

        record.twitterAccountId = user.twitterAccountId
        record.twitterTaskFinished = user.twitterTaskFinished
        record.hasJoinDiscord = user.discordInfo?.inGuild ?: false

        record.strikePrice = order.strikePrice.movePointLeft(18)
        record.settlementPrice = BigDecimal(optionOrder.marketPriceAtSettlement!!).movePointLeft(18)

        val usdcDecimal = currencyService.getCurrencyDecimal(
            ChainType.ARBITRUM, Symbol.USDT
        )
        val btcDecimal = currencyService.getCurrencyDecimal(
            ChainType.ARBITRUM, Symbol.DLCBTC
        )

        record.premiumFee = optionOrder.premiumFeePay!!
        record.premiumAsset = Symbol.valueOf(optionOrder.premiumAsset!!.asset)

        if(record.premiumAsset == Symbol.DLCBTC){
            record.premiumFeeInBtc = optionOrder.premiumFeePay!!
        } else {
            val readablePremiumFee = optionOrder.premiumFeePay!!.movePointLeft(usdcDecimal)
            val readableFeeInBtc = readablePremiumFee.divide(record.strikePrice, btcDecimal, BigDecimal.ROUND_HALF_UP)
            record.premiumFeeInBtc = readableFeeInBtc.movePointRight(btcDecimal).setScale(0, BigDecimal.ROUND_HALF_UP)
        }

        record.premiumFeeInUsdt = optionOrder.premiumFeePayInUsdt!!

        record.profit = optionOrder.buyerProfit!!

        if(record.direction == OptionDirection.CALL){
            record.profitAsset = Symbol.DLCBTC
            record.profitInBtc = optionOrder.buyerProfit!!
            val readableBtcProfit = record.profitInBtc.movePointLeft(btcDecimal)
            val readableUsdcProfit = readableBtcProfit.multiply(record.settlementPrice).setScale(usdcDecimal, BigDecimal.ROUND_HALF_UP)
            record.profitInUsdt = readableUsdcProfit.movePointRight(usdcDecimal).setScale(0, BigDecimal.ROUND_HALF_UP)
        } else {
            record.profitAsset = Symbol.USDT
            record.profitInUsdt = optionOrder.buyerProfit!!
            val readableUsdcProfit = record.profitInUsdt.movePointLeft(usdcDecimal)
            val readableBtcProfit = readableUsdcProfit.divide(record.settlementPrice, btcDecimal, BigDecimal.ROUND_HALF_UP)
            record.profitInBtc = readableBtcProfit.movePointRight(btcDecimal).setScale(0, BigDecimal.ROUND_HALF_UP)
        }

        val discordLevel = getDiscordLevel(user)
        record.discordLevel = discordLevel

        record.netProfit = record.profitInUsdt.subtract(record.premiumFeeInUsdt).setScale(0, BigDecimal.ROUND_HALF_UP)
        if(record.netProfit.compareTo(BigDecimal.ZERO) == -1){
            record.rebateAmount = record.netProfit.multiply(BigDecimal("-1"))
            record.status = DlcbtcTradeRebateRecordStatus.CREATED

            //获得双倍 杠杆 jpoint
            val userPointRecord = userPointRecordRepository.findByOptionOrderId(
                optionOrder.id!!
            )
            
            if(userPointRecord != null){
                userPointRecord.premiumPoint = userPointRecord.premiumPoint.multiply(BigDecimal("2"))
                userPointRecord.totalPoint = userPointRecord.netProfitPoint.add(userPointRecord.premiumPoint)
                userPointRecordRepository.save(userPointRecord)

                userPointService.updateUserPointDailySummary(userPointRecord)
            }

        } else {
            record.status = DlcbtcTradeRebateRecordStatus.SETTLED
            record.settled = true
        }

        dlcbtcTradeRebateRecordRepository.save(record)
    }

    fun getDiscordLevel(
        user: User
    ): Int {

        val discordId = user.discordInfo?.userId ?: return 0
        val level = discordLevelRepository.findByDiscordId(discordId)
        if(level == null){
            return 0
        }

        return level.level
    }

    fun getFirstTradeRebateCount(): Int {
        return dlcbtcTradeRebateRecordRepository.findAll().size
    }

    fun getClaimRebateTotalCount(): Int {
        return dlcbtcTradeRebateRecordRepository.findAll().size
    }

    fun getFirstDlcbtcTradesCount(
        bidAmount: BigDecimal
    ): Int {
        return optionOrderRepository.findByChainAndStatusInAndBidAssetAndBidAmountEqualsAndExpiryInHour(
            ChainType.ARBITRUM,
            listOf(OptionStatus.SETTLED, OptionStatus.EXECUTED, OptionStatus.SETTLE_FAILED),
            Symbol.DLCBTC,
            bidAmount,
            "2"
        ).size
    }

    fun getUnclaimedRebateRecord(
        user: User
    ): List<DlcbtcTradeRebateRecord> {
        return dlcbtcTradeRebateRecordRepository.findByBuyerAddressIgnoreCaseAndStatus(
            user.address,
            DlcbtcTradeRebateRecordStatus.CREATED
        )
    }
}