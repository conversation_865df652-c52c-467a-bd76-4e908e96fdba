[{"inputs": [], "stateMutability": "nonpayable", "type": "constructor"}, {"anonymous": false, "inputs": [{"indexed": false, "internalType": "address", "name": "previousAdmin", "type": "address"}, {"indexed": false, "internalType": "address", "name": "newAdmin", "type": "address"}], "name": "Admin<PERSON><PERSON>ed", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "beacon", "type": "address"}], "name": "BeaconUpgraded", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": false, "internalType": "uint8", "name": "version", "type": "uint8"}], "name": "Initialized", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": false, "internalType": "enum IOptionFacet.OrderType", "name": "_orderType", "type": "uint8"}, {"indexed": false, "internalType": "uint64", "name": "_orderID", "type": "uint64"}, {"indexed": false, "internalType": "enum IOptionService.LiquidateType", "name": "_type", "type": "uint8"}, {"indexed": false, "internalType": "uint64", "name": "_index", "type": "uint64"}, {"components": [{"internalType": "uint256", "name": "amount", "type": "uint256"}, {"internalType": "uint256", "name": "strikeAssetPrice", "type": "uint256"}, {"internalType": "uint256", "name": "lockAssetPrice", "type": "uint256"}], "indexed": false, "internalType": "struct IOptionService.LiquidateResult", "name": "_result", "type": "tuple"}], "name": "LiquidateOption", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "implementation", "type": "address"}], "name": "Upgraded", "type": "event"}, {"inputs": [{"components": [{"internalType": "address", "name": "holder", "type": "address"}, {"internalType": "enum IOptionFacet.LiquidateMode", "name": "liquidateMode", "type": "uint8"}, {"internalType": "address", "name": "writer", "type": "address"}, {"internalType": "enum IOptionFacet.UnderlyingAssetType", "name": "lockAssetType", "type": "uint8"}, {"internalType": "address", "name": "recipient", "type": "address"}, {"internalType": "address", "name": "lockAsset", "type": "address"}, {"internalType": "address", "name": "underlyingAsset", "type": "address"}, {"internalType": "address", "name": "strikeAsset", "type": "address"}, {"internalType": "uint256", "name": "lockAmount", "type": "uint256"}, {"internalType": "uint256", "name": "strikeAmount", "type": "uint256"}, {"internalType": "uint256", "name": "expirationDate", "type": "uint256"}, {"internalType": "uint256", "name": "lockDate", "type": "uint256"}, {"internalType": "uint256", "name": "underlyingNftID", "type": "uint256"}, {"internalType": "uint256", "name": "quantity", "type": "uint256"}], "internalType": "struct IOptionFacet.CallOrder", "name": "_callOrder", "type": "tuple"}], "name": "createCallOrder", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"components": [{"internalType": "address", "name": "holder", "type": "address"}, {"internalType": "enum IOptionFacet.LiquidateMode", "name": "liquidateMode", "type": "uint8"}, {"internalType": "address", "name": "writer", "type": "address"}, {"internalType": "enum IOptionFacet.UnderlyingAssetType", "name": "lockAssetType", "type": "uint8"}, {"internalType": "address", "name": "recipient", "type": "address"}, {"internalType": "address", "name": "lockAsset", "type": "address"}, {"internalType": "address", "name": "underlyingAsset", "type": "address"}, {"internalType": "address", "name": "strikeAsset", "type": "address"}, {"internalType": "uint256", "name": "lockAmount", "type": "uint256"}, {"internalType": "uint256", "name": "strikeAmount", "type": "uint256"}, {"internalType": "uint256", "name": "expirationDate", "type": "uint256"}, {"internalType": "uint256", "name": "lockDate", "type": "uint256"}, {"internalType": "uint256", "name": "underlyingNftID", "type": "uint256"}, {"internalType": "uint256", "name": "quantity", "type": "uint256"}], "internalType": "struct IOptionFacet.PutOrder", "name": "_putOrder", "type": "tuple"}], "name": "createPutOrder", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "diamond", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "quantity", "type": "uint256"}, {"internalType": "uint256", "name": "strikeAmount", "type": "uint256"}], "name": "getParts", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "pure", "type": "function"}, {"inputs": [{"internalType": "address", "name": "_diamond", "type": "address"}], "name": "initialize", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"components": [{"internalType": "enum IOptionFacet.OrderType", "name": "_orderType", "type": "uint8"}, {"internalType": "uint64", "name": "_orderID", "type": "uint64"}, {"internalType": "enum IOptionService.LiquidateType", "name": "_type", "type": "uint8"}, {"internalType": "uint64", "name": "_index", "type": "uint64"}, {"internalType": "bytes[]", "name": "lockAssetPricData", "type": "bytes[]"}, {"internalType": "bytes[]", "name": "strikeAssetPricData", "type": "bytes[]"}], "internalType": "struct IOptionService.LiquidateParams", "name": "_params", "type": "tuple"}], "name": "liquidateOption", "outputs": [], "stateMutability": "payable", "type": "function"}, {"inputs": [], "name": "liquidateSerivce", "outputs": [{"internalType": "contract IOptionLiquidateService", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "priceOracle", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "proxiableUUID", "outputs": [{"internalType": "bytes32", "name": "", "type": "bytes32"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "contract IOptionLiquidateService", "name": "_addr", "type": "address"}], "name": "setLiquidateSerivce", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "newImplementation", "type": "address"}], "name": "upgradeTo", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "newImplementation", "type": "address"}, {"internalType": "bytes", "name": "data", "type": "bytes"}], "name": "upgradeToAndCall", "outputs": [], "stateMutability": "payable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "", "type": "address"}], "name": "whiteList", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "view", "type": "function"}]