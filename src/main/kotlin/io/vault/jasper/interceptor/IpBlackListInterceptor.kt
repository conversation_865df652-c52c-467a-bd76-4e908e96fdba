package io.vault.jasper.interceptor

import io.vault.jasper.exception.BusinessException
import io.vault.jasper.exception.ResultEnum
import io.vault.jasper.service.Blacklist
import org.slf4j.LoggerFactory
import org.springframework.web.servlet.HandlerInterceptor
import javax.servlet.http.HttpServletRequest
import javax.servlet.http.HttpServletResponse


class IpBlackListInterceptor : HandlerInterceptor {

    private var logger = LoggerFactory.getLogger(this::class.java)

    @Throws(Exception::class)
    override fun preHandle(request: HttpServletRequest, response: HttpServletResponse, handler: Any): Bo<PERSON>an {
        val ipAddress = getIpAddr(request)
        //logger.info("IP Interceptor request path ${request.requestURI} IP: $ipAddress")
        if (ipAddress == null || Blacklist.contains(ipAddress)) {
            logger.info("IP Interceptor request path ${request.requestURI} IP: $ipAddress is in blacklist")
            // IP地址在黑名单中，拒绝请求
            //response.status = HttpServletResponse.SC_FORBIDDEN
            //response.setHeader("Access-Control-Allow-Origin", "*")
            //response.setHeader("Access-Control-Allow-Credentials", "true")
            throw BusinessException(ResultEnum.SERVICE_NOT_AVAILABLE)
            //return false
        }
        //logger.info("IP Interceptor request path ${request.requestURI} IP: $ipAddress is not in blacklist")
        return true
    }

    /**
     * 获取IP地址
     */
    fun getIpAddr(request: HttpServletRequest): String? {
        // nginx代理获取的真实用户ip
        var ip = request.getHeader("X-Real-IP")
        if (ip.isNullOrBlank() || "unknown".equals(ip, ignoreCase = true)) {
            ip = request.getHeader("X-Forwarded-For")
        }
        if (ip.isNullOrBlank() || "unknown".equals(ip, ignoreCase = true)) {
            ip = request.getHeader("Proxy-Client-IP")
        }
        if (ip.isNullOrBlank() || "unknown".equals(ip, ignoreCase = true)) {
            ip = request.getHeader("WL-Proxy-Client-IP")
        }
        if (ip.isNullOrBlank() || "unknown".equals(ip, ignoreCase = true)) {
            ip = request.remoteAddr
        }
        /*
          对于通过多个代理的情况， 第一个IP为客户端真实IP,多个IP按照','分割 "***.***.***.***".length() =
          15
         */if (ip != null && ip.length > 15) {
            if (ip.indexOf(",") > 0) {
                ip = ip.substring(0, ip.indexOf(","))
            }
        }
        return ip
    }
}
