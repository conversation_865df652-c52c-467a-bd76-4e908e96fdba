package io.vault.jasper.repository


import io.vault.jasper.model.*
import org.springframework.data.mongodb.repository.MongoRepository
import org.springframework.stereotype.Repository

@Repository
interface SystemOptionOrderRebateRecordRepository : MongoRepository<SystemOptionOrderRebateRecord, String> {
    fun findByOptionOrderId(optionOrderId: String): SystemOptionOrderRebateRecord?
    fun findTopByOrderByOptionOrderIdDesc(): SystemOptionOrderRebateRecord?
    fun findByStatus(
        status: SystemOptionOrderRebateRecordStatus
    ): List<SystemOptionOrderRebateRecord>

    fun findByBuyerAddress(
        buyerAddress: String
    ): List<SystemOptionOrderRebateRecord>

    fun findByBuyerAddressIgnoreCase(
        buyerAddress: String
    ): List<SystemOptionOrderRebateRecord>

    fun findByBuyerAddressIgnoreCaseAndStatus(
        buyerAddress: String,
        status: SystemOptionOrderRebateRecordStatus
    ): List<SystemOptionOrderRebateRecord>

    fun findByStatusAndSettleTxIdIsNotNull(
        status: SystemOptionOrderRebateRecordStatus
    ): List<SystemOptionOrderRebateRecord>

    fun findByStatusInAndSettleTxIdIsNotNull(
        statuses: List<SystemOptionOrderRebateRecordStatus>,
    ): List<SystemOptionOrderRebateRecord>

    fun findByStatusAndSettleTxIdIsNotNullAndSettled(
        status: SystemOptionOrderRebateRecordStatus,
        settled: Boolean
    ): List<SystemOptionOrderRebateRecord>
}