package io.vault.jasper.response

import io.swagger.annotations.ApiModel
import io.swagger.annotations.ApiModelProperty
import io.vault.jasper.enums.OptionDirection
import io.vault.jasper.enums.Symbol
import javax.validation.constraints.NotEmpty
import javax.validation.constraints.NotNull

@ApiModel
data class ActivityListResponse(

    @ApiModelProperty("活动ID", required = true)
    @field:NotEmpty
    val id: String,

    @ApiModelProperty("活动名称", required = true)
    @field:NotEmpty
    val name: String,

    @ApiModelProperty("开始时间（毫秒）", required = true)
    @field:NotNull
    val startTime: Long,

    @ApiModelProperty("结束时间（毫秒）", required = false)
    val endTime: Long?

)