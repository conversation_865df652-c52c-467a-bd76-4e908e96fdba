package io.vault.jasper.model.activity

import io.vault.jasper.enums.ChainType
import org.springframework.data.annotation.CreatedDate
import org.springframework.data.annotation.Id
import org.springframework.data.annotation.LastModifiedDate
import org.springframework.data.mongodb.core.index.CompoundIndex
import org.springframework.data.mongodb.core.index.Indexed
import org.springframework.data.mongodb.core.mapping.Document
import org.springframework.data.mongodb.core.mapping.Field
import org.springframework.data.mongodb.core.mapping.FieldType
import java.math.BigDecimal
import java.time.LocalDateTime

/**
 * CampaignRewardRecord model to track distributed rewards
 */
@Document("campaign_reward_records")
@CompoundIndex(name = "user_campaign_reward_idx", def = "{'user_id': 1, 'campaign_id': 1, 'option_order_id': 1}", unique = true)
data class CampaignRewardRecord(
    @Id
    val id: String? = null,

    @Field("user_id")
    @Indexed(background = true)
    val userId: String,

    @Indexed(background = true)
    val address: String,

    @Field("campaign_id")
    @Indexed(background = true)
    val campaignId: String,

    @Field("reward_type")
    val rewardType: RewardType,

    @Field("option_order_id")
    @Indexed(background = true)
    val optionOrderId: String? = null,

    @Field("chain")
    val chain: ChainType? = null,

    @Field("amount", targetType = FieldType.DECIMAL128)
    val amount: BigDecimal,

    @Field("status")
    var status: RewardRecordStatus = RewardRecordStatus.PENDING,

    @Field("transaction_hash")
    var transactionHash: String? = null,

    @Field("error_message")
    var errorMessage: String? = null,

    @Field("scheduled_time")
    var scheduledTime: LocalDateTime? = null,

    @CreatedDate
    val created: LocalDateTime = LocalDateTime.now(),

    @LastModifiedDate
    val updated: LocalDateTime = LocalDateTime.now()
)

/**
 * Enum representing different statuses of reward records
 */
enum class RewardRecordStatus {
    PENDING,
    PROCESSING,
    COMPLETED,
    FAILED
}
