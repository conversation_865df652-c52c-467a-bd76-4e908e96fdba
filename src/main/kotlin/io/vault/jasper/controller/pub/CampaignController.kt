package io.vault.jasper.controller.pub

import com.fasterxml.jackson.databind.ObjectMapper
import io.swagger.annotations.ApiOperation
import io.swagger.annotations.ApiParam
import io.vault.jasper.ApiResponse
import io.vault.jasper.annotation.ApiCache
import io.vault.jasper.enums.ChainType
import io.vault.jasper.enums.OptionDirection
import io.vault.jasper.enums.Symbol
import io.vault.jasper.model.OptionOrder
import io.vault.jasper.model.OptionStatus
import io.vault.jasper.model.UserChannel
import io.vault.jasper.repository.*
import io.vault.jasper.response.CampaignTradingListResponse
import io.vault.jasper.service.*
import org.slf4j.LoggerFactory
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.data.domain.PageRequest
import org.springframework.data.domain.Sort
import org.springframework.data.mongodb.core.MongoTemplate
import org.springframework.data.mongodb.core.query.Criteria
import org.springframework.data.mongodb.core.query.Query
import org.springframework.data.repository.findByIdOrNull
import org.springframework.web.bind.annotation.*
import org.springframework.web.client.RestTemplate
import java.math.BigDecimal
import java.math.RoundingMode
import java.time.LocalDateTime
import java.time.ZoneId
import java.time.ZonedDateTime
import java.time.format.DateTimeFormatter
import java.util.*


@RestController
@RequestMapping("/campaign")
class CampaignController @Autowired constructor(
    private val mongoTemplate: MongoTemplate,
) {

    private var logger = LoggerFactory.getLogger(this::class.java)

    @ApiOperation("Trading活动列表")
    @CrossOrigin
    @ApiCache(key = "campaign:trading:list", dynamicKey = ["start", "end"], expire = 30)
    @GetMapping("/trading/list")
    fun tradingList(
        @ApiParam("起始时间，格式 2024-08-27 20:00:00") @RequestParam(required = false) start: String?,
        @ApiParam("起始时间，格式 2024-08-28 20:00:00") @RequestParam(required = false) end: String?,
    ): ApiResponse<List<CampaignTradingListResponse>> {

        val zoneId = ZoneId.of("Asia/Singapore")
        val dateTimeFmt = "yyyy-MM-dd HH:mm:ss"
        val formatter = DateTimeFormatter.ofPattern(dateTimeFmt)
        val (startLocal, endLocal) = if (start != null && end != null) {
            val startAtZone = LocalDateTime.parse(start, formatter).atZone(zoneId)
            val endAtZone = LocalDateTime.parse(end, formatter).atZone(zoneId)
            val startLocal = startAtZone.withZoneSameInstant(ZoneId.systemDefault()).toLocalDateTime()
            val endLocal = endAtZone.withZoneSameInstant(ZoneId.systemDefault()).toLocalDateTime()
            Pair(startLocal, endLocal)
        } else {
            val now = ZonedDateTime.now().withZoneSameInstant(zoneId)
            val midnight = now.withHour(0).withMinute(0).withSecond(0).withNano(0)
            var startAtZone = midnight.minusHours(4)
            var endAtZone = midnight.plusHours(20)
            if (now > endAtZone) {
                startAtZone = endAtZone
            }
            endAtZone = now
            val startLocal = startAtZone.withZoneSameInstant(ZoneId.systemDefault()).toLocalDateTime()
            val endLocal = endAtZone.withZoneSameInstant(ZoneId.systemDefault()).toLocalDateTime()
            Pair(startLocal, endLocal)
        }

        //logger.info("Trading查询时间段: $startLocal - $endLocal\tSystem default zone: ${ZoneId.systemDefault()}")
        val baseCriteria = Criteria.where(OptionOrder::status.name).`is`(OptionStatus.SETTLED)
            .and(OptionOrder::expiryDate.name).gte(startLocal).lt(endLocal)

        val pageable = PageRequest.of(0, 10, Sort.by(Sort.Order.desc(OptionOrder::roi.name)))
        val query2 = Query().addCriteria(baseCriteria).with(pageable)
        val result = mongoTemplate.find(query2, OptionOrder::class.java)

        return ApiResponse.success(result.mapNotNull {
            CampaignTradingListResponse(
                optionOrderId = it.id!!,
                buyerAddress = it.buyer ?: return@mapNotNull null,
                roi = (it.roi ?: BigDecimal.ZERO).stripTrailingZeros().toPlainString(),
                chain = it.chain,
                txHash = it.txHash!!,
                settlementHash = it.settlementHash!!
            )
        })
    }

    @ApiOperation("Mini App 交易竞赛")
    @CrossOrigin
    @ApiCache(key = "campaign:miniapp:trading:list", expire = 60)
    @GetMapping("/trading/mini/list")
    fun tradingMiniList(): ApiResponse<List<CampaignTradingListResponse>> {

        val endLocal = LocalDateTime.now()
        val startLocal = endLocal.minusDays(1)

        val baseCriteria = Criteria.where(OptionOrder::status.name).`is`(OptionStatus.SETTLED)
            .and(OptionOrder::expiryDate.name).gte(startLocal).lt(endLocal)
            .and(OptionOrder::chain.name).`is`(ChainType.BITLAYER)
            //.and(OptionOrder::channel.name).`is`(UserChannel.MINI_APP)

        val pageable = PageRequest.of(0, 10, Sort.by(Sort.Order.desc(OptionOrder::roi.name)))
        val query2 = Query().addCriteria(baseCriteria).with(pageable)
        val result = mongoTemplate.find(query2, OptionOrder::class.java)

        //logger.info("Mini App Trading查询时间段: $startLocal - $endLocal\t result size = ${result.size}")

        return ApiResponse.success(result.mapNotNull {
            CampaignTradingListResponse(
                optionOrderId = it.id!!,
                buyerAddress = it.buyer ?: return@mapNotNull null,
                roi = (it.roi ?: BigDecimal.ZERO).stripTrailingZeros().toPlainString(),
                chain = it.chain,
                txHash = it.txHash!!,
                settlementHash = it.settlementHash!!
            )
        })
    }
}