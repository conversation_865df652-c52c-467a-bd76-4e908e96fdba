package io.vault.jasper.model

import org.springframework.data.annotation.CreatedDate
import org.springframework.data.annotation.Id
import org.springframework.data.annotation.LastModifiedDate
import org.springframework.data.mongodb.core.index.Indexed
import org.springframework.data.mongodb.core.mapping.Document
import org.springframework.data.mongodb.core.mapping.Field
import java.math.BigDecimal
import java.time.LocalDateTime

enum class SafepalTaskStatus {
    PENDING,
    SETTLED
}

@Document("safepal_task_record")
data class SafepalTaskRecord(
    @Id
    val id: String? = null,
    @Indexed(unique = true, background = true)
    val address: String,
    @Field("finish_task")
    @Indexed(background = true)
    var finishTask: Boolean = false,
    @CreatedDate
    val created: LocalDateTime = LocalDateTime.now(),
    @LastModifiedDate
    val updated: LocalDateTime = LocalDateTime.now(),
    @Indexed(background = true)
    var status: SafepalTaskStatus = SafepalTaskStatus.PENDING,
    @Field("tx_hash")
    var txHash: String? = null,
    @Field("credit_amount")
    var creditAmount: BigDecimal = BigDecimal("10") // 10 USDT
)