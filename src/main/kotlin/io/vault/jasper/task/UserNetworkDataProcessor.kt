package io.vault.jasper.task

import io.vault.jasper.enums.ChainType
import io.vault.jasper.enums.Symbol
import io.vault.jasper.model.*
import io.vault.jasper.repository.*
import io.vault.jasper.service.*
import io.vault.jasper.service.blockchain.ArbitrumService
import org.slf4j.LoggerFactory
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.beans.factory.annotation.Value
import org.springframework.context.annotation.Profile
import org.springframework.data.repository.findByIdOrNull
import org.springframework.scheduling.annotation.Scheduled
import org.springframework.stereotype.Service
import java.math.BigInteger

@Service
@Profile("prod", "test", "dev")
class UserNetworkDataProcessor @Autowired constructor(
    private val userNetworkRebateRecordRepository: UserNetworkRebateRecordRepository,
    private val userNetworkRepository: UserNetworkRepository,
    private val userNetworkService: UserNetworkService,
    private val systemService: SystemService
) {

    private val logger = LoggerFactory.getLogger(this::class.java)

    @Value("\${schedule_task_on}")
    lateinit var scheduleOn: String

    // 每天早上6点整点执行一次
    //@Scheduled(fixedDelay = 5 * 60 * 1000, initialDelay = 60 * 1000)
    @Scheduled(cron = "0 0 6 * * *", zone = "Asia/Singapore")
    fun run() {
        
        if(scheduleOn == "false"){
            return
        }
        try {
            exec()
        } catch (e: Exception) {
            logger.error(e.message, e)
        }
    }

    private fun exec() {

        val systemParameter = systemService.getParameter()
        if(systemParameter.settleUserNetworkTaskSwitch == false){
            logger.info("UserNetworkDataProcessor SettleUserNetworkTaskSwitch is off")
            return
        }

        logger.info("UserNetworkDataProcessor SettleUserNetworkTaskSwitch is on")

        try {
            initUserNetworkSummary()
        } catch (e: Exception) {
            logger.error(e.message, e)
        }

        logger.info("UserNetworkDataProcessor SettleUserNetworkTaskSwitch is finished")
    }

    private fun initUserNetworkSummary(){

//        val rootNetwork = userNetworkRepository.findByAddressIgnoreCase("0x7c164c8b5Dc8E61B0cBCD8214063865Bd3df1366")
//        if(rootNetwork == null){
//            return
//        }
        userNetworkService.calculateUserNetworkMemberCountAndPremium()
    }
}