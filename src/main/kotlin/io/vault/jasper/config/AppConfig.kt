package io.vault.jasper.config

import org.springframework.context.annotation.Configuration
import org.springframework.scheduling.annotation.AsyncConfigurer
import org.springframework.scheduling.annotation.EnableAsync
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor
import java.util.concurrent.Executor
import org.springframework.context.annotation.EnableAspectJAutoProxy

@Configuration
@EnableAsync
@EnableAspectJAutoProxy
open class AppConfig : AsyncConfigurer {
    override fun getAsyncExecutor(): Executor? {
        val executor = ThreadPoolTaskExecutor()
        executor.corePoolSize = 5
        executor.maxPoolSize = 10
        executor.queueCapacity = 1000
        executor.threadNamePrefix = "JV-Thread-"
        executor.initialize()

        return executor
    }
}