package io.vault.jasper.service

import com.fasterxml.jackson.core.type.TypeReference
import com.fasterxml.jackson.databind.ObjectMapper
import io.vault.jasper.enums.Symbol
import io.vault.jasper.model.*
import io.vault.jasper.repository.UserNetworkPushDataRepository
import io.vault.jasper.repository.UserNetworkPushDataTaskParameterRepository
import io.vault.jasper.repository.UserNetworkRepository
import io.vault.jasper.response.ExecutedOrderResponse
import io.vault.jasper.utils.DateTimeUtil
import org.slf4j.LoggerFactory
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.data.mongodb.core.MongoTemplate
import org.springframework.data.repository.findByIdOrNull
import org.springframework.http.HttpEntity
import org.springframework.http.HttpHeaders
import org.springframework.http.MediaType
import org.springframework.http.client.SimpleClientHttpRequestFactory
import org.springframework.stereotype.Service
import org.springframework.web.client.RestTemplate
import java.time.LocalDateTime

@Service
class UserNetworkPushDataService @Autowired constructor(
    private val userNetworkRepository: UserNetworkRepository,
    private val userNetworkPushDataRepository: UserNetworkPushDataRepository,
    private val userNetworkPushDataTaskParameterRepository: UserNetworkPushDataTaskParameterRepository
){

    private val logger = LoggerFactory.getLogger(this::class.java)

    private val objectMapper = ObjectMapper()

    private val restTemplate: RestTemplate by lazy {
        val rt = RestTemplate()

        val requestFactory = SimpleClientHttpRequestFactory()
        // Set the 30s timeout in milliseconds
        requestFactory.setConnectTimeout(30000)
        requestFactory.setReadTimeout(30000)

        rt.requestFactory = requestFactory
        rt
    }

    fun getUserIdentity(userNetwork: UserNetwork): Int {
        return when (userNetwork.grade) {
            UserNetworkGrade.USER -> 1
            UserNetworkGrade.GRADE2 -> 2
            UserNetworkGrade.GRADE1 -> 3
            UserNetworkGrade.WUJIE -> 4
            else -> 0
        }
    }

    fun getUserInfoParameter(userNetwork: UserNetwork): Map<String, Any>? {

        var invitorAddress = ""
        if(userNetwork.grade == UserNetworkGrade.USER ||
            userNetwork.grade == UserNetworkGrade.GRADE2 ||
            userNetwork.grade == UserNetworkGrade.GRADE1){
            val invitorUserNetworkId = userNetwork.invitedNetworkId

            val invitorUserNetwork = userNetworkRepository.findByIdOrNull(invitorUserNetworkId)
            if(invitorUserNetwork != null){
                invitorAddress = invitorUserNetwork.address
            }
        }

        return mutableMapOf(
            "wallet_address" to userNetwork.address,
            "invitation_code" to userNetwork.inviteCode,
            "register_timestamp" to DateTimeUtil.convertLocalDateTimeToTimestamp(userNetwork.created) / 1000,
            "inviter_wallet_address" to invitorAddress,
            "current_ratio" to userNetwork.protocolFeePercentage.stripTrailingZeros().toPlainString(),
            "user_identity" to getUserIdentity(userNetwork)
        )
    }

    fun getUserNetworkChangeRateLogParameter(userNetworkChangeRateLog: UserNetworkChangeRateLog): Map<String, Any>? {

        return mutableMapOf(
            "wallet_address" to userNetworkChangeRateLog.address,
            "adjust_timestamp" to DateTimeUtil.convertLocalDateTimeToTimestamp(userNetworkChangeRateLog.created) / 1000,
            "before_ratio" to userNetworkChangeRateLog.beforeRate.stripTrailingZeros().toPlainString(),
            "after_ratio" to userNetworkChangeRateLog.afterRate.stripTrailingZeros().toPlainString(),
        )
    }

    fun getTransactionRecordParameter(
        optionOrder: OptionOrder
    ): Map<String, Any?> {
        return mutableMapOf(
            "wallet_address" to optionOrder.buyer!!,
            "id" to optionOrder.id!!,
            "order_id" to optionOrder.orderId,
            "order_type" to optionOrder.orderType.toString(),
            "chain" to optionOrder.chain.toString(),
            "on_chain_order_id" to optionOrder.onChainOrderId!!,
            "buyer_address" to optionOrder.buyer,
            "buyer_vault" to optionOrder.buyerVault,
            "seller_address" to optionOrder.seller,
            "business_type" to optionOrder.direction!!.toString(),
            "bid_asset" to optionOrder.bidAsset,
            "bid_amount" to optionOrder.bidAmount?.stripTrailingZeros()?.toPlainString(),
            "underlying_asset" to optionOrder.underlyingAsset,
            "underlying_asset_address" to optionOrder.underlyingAssetAddress,
            "underlying_asset_amount" to optionOrder.amount.stripTrailingZeros().toPlainString(),
            "strike_asset" to optionOrder.strikeAsset,
            "strike_amount" to optionOrder.strikeAmount.stripTrailingZeros().toPlainString(),
            "strike_price" to optionOrder.strikePrice!!.stripTrailingZeros().toPlainString(),
            "actual_strike_amount" to optionOrder.actualStrikeAmount?.stripTrailingZeros()?.toPlainString(),
            "premium_fees_asset" to optionOrder.premiumAsset?.asset,
            "premium_fee" to optionOrder.premiumFeePay?.stripTrailingZeros()?.toPlainString(),
            "premium_fee_in_usdt" to optionOrder.premiumFeePayInUsdt?.stripTrailingZeros()?.toPlainString(),
            "premium_fee_should_pay" to optionOrder.premiumFeeShouldPay?.stripTrailingZeros()?.toPlainString(),
            "premium_fee_should_pay_in_usdt" to optionOrder.premiumFeeShouldPayInUsdt?.stripTrailingZeros()?.toPlainString(),
            "create_date" to DateTimeUtil.convertLocalDateTimeToTimestamp(optionOrder.created) / 1000,
            "expiry_date" to DateTimeUtil.convertLocalDateTimeToTimestamp(optionOrder.expiryDate) / 1000,
            "status" to optionOrder.status,
            "degen" to optionOrder.jvault,
            "market_price_at_settlement" to optionOrder.marketPriceAtSettlement,
            "settlement_hash" to optionOrder.settlementHash,
            "buyer_profit" to optionOrder.buyerProfit?.stripTrailingZeros()?.toPlainString(),
            "expiry_in_hour" to optionOrder.expiryInHour,
            "used_moonlight_box" to optionOrder.usedMoonlightBox,
            "used_reality_stone" to optionOrder.usedRealityStone,
            "used_power_stone" to optionOrder.usedPowerStone,
            "used_space_stone" to optionOrder.usedSpaceStone,
            "stone_activity_nft_id" to optionOrder.stoneActivityNftId,
            "tx_hash" to optionOrder.txHash
        )
    }

    fun getUserNetworkRebateRecordParameter(
        userNetworkRebateRecord: UserNetworkRebateRecord
    ): Map<String, Any>? {

        return mutableMapOf(
            "wallet_address" to userNetworkRebateRecord.userAddress,
            "transaction_id" to userNetworkRebateRecord.optionOrderId,
            "commission_amount" to userNetworkRebateRecord.incentiveAmount.stripTrailingZeros().toPlainString(),
            "commission_ratio" to userNetworkRebateRecord.actualIncentiveRate!!.stripTrailingZeros().toPlainString(),
            "record_timestamp" to DateTimeUtil.convertLocalDateTimeToTimestamp(userNetworkRebateRecord.created) / 1000,
        )
    }

    fun handleUserNetworkData(
        userNetwork: UserNetwork,
    ){
        val parameter = getUserInfoParameter(userNetwork)
        if(parameter != null){

            val userNetworkPushData = UserNetworkPushData(
                path = UserNetworkPushDataPath.USER_INFO,
                param = parameter
            )
            userNetworkPushDataRepository.save(userNetworkPushData)
        }
    }

    fun handleCommissionRateLog(
        commissionRateLog: UserNetworkChangeRateLog
    ){
        val parameter = getUserNetworkChangeRateLogParameter(
            commissionRateLog
        )

        if(parameter != null) {
            val userNetworkPushData = UserNetworkPushData(
                path = UserNetworkPushDataPath.USER_RATE_CHANGE_LOG,
                param = parameter
            )
            userNetworkPushDataRepository.save(userNetworkPushData)
        }
    }

    fun handleTransactionRecord(
        optionOrder: OptionOrder
    ){
        val parameter = getTransactionRecordParameter(
            optionOrder
        )

        if(parameter != null) {
            val userNetworkPushData = UserNetworkPushData(
                path = UserNetworkPushDataPath.USER_NETWORK_TRANSACTION,
                param = parameter
            )
            userNetworkPushDataRepository.save(userNetworkPushData)
        }
    }

    fun handleRebateRecord(
        userNetworkRebateRecord: UserNetworkRebateRecord
    ){
        val parameter = getUserNetworkRebateRecordParameter(
            userNetworkRebateRecord
        )

        if(parameter != null) {
            val userNetworkPushData = UserNetworkPushData(
                path = UserNetworkPushDataPath.USER_NETWORK_REBATE_RECORD,
                param = parameter
            )
            userNetworkPushDataRepository.save(userNetworkPushData)
        }
    }

    fun handlePointRecord(
        userPointRecord: UserPointRecord
    ){
        val parameter = mutableMapOf(
            "wallet_address" to userPointRecord.address,
            "transaction_id" to userPointRecord.optionOrderId,
            "transaction_point" to userPointRecord.premiumPoint.stripTrailingZeros().toPlainString(),
            "loyalty_point" to userPointRecord.loyaltyPoint.stripTrailingZeros().toPlainString(),
            "record_timestamp" to DateTimeUtil.convertLocalDateTimeToTimestamp(userPointRecord.created) / 1000,
        )

        val userNetworkPushData = UserNetworkPushData(
            path = UserNetworkPushDataPath.USER_NETWORK_POINT_RECORD,
            param = parameter
        )
        userNetworkPushDataRepository.save(userNetworkPushData)
    }

    fun getTaskParameter() : UserNetworkPushDataTaskParameter{

        val params = userNetworkPushDataTaskParameterRepository.findAll()

        return if (params.isEmpty()) {
            val p = UserNetworkPushDataTaskParameter(null)
            userNetworkPushDataTaskParameterRepository.save(p)
        } else {
            params.first()
        }
    }

    fun sendJasperQuantData(
        userNetworkPushDatas: List<UserNetworkPushData>,
        url: String
    ){

        val now = LocalDateTime.now()

        val dataList = userNetworkPushDatas.map {
            it.param
        }

        val params = mapOf(
            "batch_timestamp" to DateTimeUtil.convertLocalDateTimeToTimestamp(now) / 1000,
            "data" to dataList
        )

        val body = objectMapper.writeValueAsString(params)
        logger.info("Send Jasper Quant Batch Request: $url, $body")
        val requestTime = LocalDateTime.now()
        val headers = HttpHeaders()
        headers.contentType = MediaType.APPLICATION_JSON
        val entity = HttpEntity(body, headers)
        val response = restTemplate.postForEntity(url, entity, String::class.java)

        logger.info("Send Jasper Quant Batch Request Response: ${response.body}")

        val responseTime = LocalDateTime.now()

        userNetworkPushDatas.forEach {
            it.requestTime = requestTime
            it.responseTime = responseTime
            it.response = response.body
            it.pushed = true
            userNetworkPushDataRepository.save(it)
        }

        when (response.statusCodeValue) {
            200 -> {
                Thread.sleep(1000)
                return
            }
            else -> {
                logger.error("Send Jasper Quant Batch Request failed: ${response.body}")
                throw Exception("Send Jasper Quant Batch Request failed: (${response.statusCodeValue})${response.body}")
            }
        }
    }

}