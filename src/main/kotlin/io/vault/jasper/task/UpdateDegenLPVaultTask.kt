package io.vault.jasper.task

import io.vault.jasper.enums.ChainType
import io.vault.jasper.enums.OptionDirection
import io.vault.jasper.enums.Symbol
import io.vault.jasper.model.LPVaultStatus
import io.vault.jasper.repository.*
import io.vault.jasper.service.CurrencyService
import io.vault.jasper.service.DegenLPVaultService
import io.vault.jasper.service.OptionOrderService
import io.vault.jasper.service.blockchain.BlockchainServiceFactory
import io.vault.jasper.service.blockchain.EvmService
import org.slf4j.LoggerFactory
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.beans.factory.annotation.Value
import org.springframework.scheduling.annotation.Scheduled
import org.springframework.stereotype.Service
import java.util.*

@Service
class UpdateDegenLPVaultTask @Autowired constructor(
    private val degenLPVaultService: DegenLPVaultService,
    private val degenConfigRepository: DegenConfigRepository,
    private val lpVaultRepository: LPVaultRepository,
    private val optionOrderService: OptionOrderService,
    private val currencyService: CurrencyService,
    private val blockchainServiceFactory: BlockchainServiceFactory
) {

    private val logger = LoggerFactory.getLogger(this::class.java)

    private var lastUpdateVaultTime = 0L

    private val updateConfigTime = 60 * 1000

    @Value("\${schedule_task_on}")
    lateinit var scheduleOn: String

    // 每分钟执行一次
    @Scheduled(fixedDelay = 30 * 1000, initialDelay = 1 * 1000)
    fun run() {
        if(scheduleOn == "false"){
            return
        }
        try {
            exec()
        } catch (e: Exception) {
            logger.error(e.message, e)
        }
    }

    /**
     * 更新每条订单记录
     */
    private fun exec() {

        //logger.info("Update Degen LPVault Task Begin.")
        val degenConfigs = degenConfigRepository.findAll()
        val now = Date().time

        for(degenConfig in degenConfigs) {

            try {
                degenLPVaultService.updateFromDegenLPVaultConfig()
            }catch (e: Exception) {
                logger.error(e.message, e)
            }
        }

        if (now - lastUpdateVaultTime > updateConfigTime) {
            lastUpdateVaultTime = now
        }

        //logger.info("Update Degen LPVault Task End.")
    }

    /**
     * 更新每条订单记录
     */
    @Scheduled(fixedDelay = 5 * 60 * 1000, initialDelay = 90 * 1000)
    fun updateLPVaultData() {

        if(scheduleOn == "false"){
            return
        }

        logger.info("Update LP Vault Data Begin.")

        val lpVaults = lpVaultRepository.findByStatus(LPVaultStatus.DONE)

        for(lpVault in lpVaults) {
            logger.info("Begin Update LPVault Data ${lpVault.chain}, lpVaultAddress=${lpVault.lpVaultAddress}")
            try {

                val vaultAddress = lpVault.lpVaultAddress!!
                val optionDirection = lpVault.optionType
                val optionSymbol = lpVault.optionSymbol
                val chain = lpVault.chain
                val evmService = blockchainServiceFactory.getBlockchainService(chain) as EvmService

                var lockSymbol = optionSymbol
                var quoteSymbol = Symbol.USDT
                if(optionDirection == OptionDirection.PUT){
                    lockSymbol = "USDT"
                    if(chain == ChainType.BASE){
                        lockSymbol = "USDC"
                        quoteSymbol = Symbol.USDC
                    }
                }

                val usdtContract = currencyService.getCurrencyContract(
                    chain,
                    quoteSymbol
                )

                val openInterest = optionOrderService.getSellerOpenInterest(
                    chain,
                    vaultAddress,
                    Symbol.valueOf(lockSymbol)
                )

                val premiumEarn = optionOrderService.getSellerPremiumEarn(
                    chain,
                    vaultAddress
                )

                val tradingVolume = optionOrderService.getSellerOptionVolumeTotal(
                    chain,
                    vaultAddress
                )

                val lockContract = currencyService.getCurrencyContract(
                    chain,
                    Symbol.valueOf(lockSymbol)
                )

                val lockSymbolBalance = evmService.evmUtil.getBalance(
                    vaultAddress,
                    lockContract
                )

                val usdtBalance = evmService.evmUtil.getBalance(
                    vaultAddress,
                    usdtContract
                )

                lpVault.oi = openInterest
                lpVault.premiumEarn = premiumEarn
                lpVault.tradingVolume = tradingVolume
                lpVault.lockSymbolBalance = lockSymbolBalance
                lpVault.usdtBalance = usdtBalance
                lpVaultRepository.save(lpVault)

            }catch (e: Exception) {
                logger.error(e.message, e)
            }

            logger.info("End Update LPVault Data ${lpVault.chain}, lpVaultAddress=${lpVault.lpVaultAddress}")
        }

        logger.info("Update LP Vault Data End")
    }
}