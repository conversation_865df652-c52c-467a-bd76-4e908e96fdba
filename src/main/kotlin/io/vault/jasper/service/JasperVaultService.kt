package io.vault.jasper.service

import com.fasterxml.jackson.core.JsonProcessingException
import com.fasterxml.jackson.databind.JsonNode
import com.fasterxml.jackson.databind.ObjectMapper
import io.vault.jasper.enums.ChainType
import io.vault.jasper.enums.OptionDirection
import io.vault.jasper.enums.Symbol
import io.vault.jasper.utils.DateTimeUtil
import io.vault.jasper.utils.ProfileUtil
import io.vault.jasper.utils.SecretUtil
import org.slf4j.LoggerFactory
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.beans.factory.annotation.Value
import org.springframework.http.HttpEntity
import org.springframework.http.HttpHeaders
import org.springframework.http.HttpMethod
import org.springframework.http.MediaType
import org.springframework.http.client.SimpleClientHttpRequestFactory
import org.springframework.stereotype.Service
import org.springframework.web.client.HttpClientErrorException
import org.springframework.web.client.RestTemplate
import org.springframework.web.client.getForEntity
import java.math.BigDecimal
import java.math.BigInteger
import java.time.LocalDateTime
import java.util.*
import org.bouncycastle.jce.provider.BouncyCastleProvider
import org.bouncycastle.jce.ECNamedCurveTable
import org.bouncycastle.jce.spec.ECParameterSpec
import org.bouncycastle.jce.interfaces.ECPrivateKey
import org.bouncycastle.jce.interfaces.ECPublicKey
import org.web3j.crypto.*
import org.web3j.utils.Numeric
import java.security.*
import java.security.spec.ECGenParameterSpec
import kotlin.Pair


@Service
class JasperVaultService @Autowired constructor(
    private val systemService: SystemService,
    private val currencyService: CurrencyService
){

    private val logger = LoggerFactory.getLogger(this::class.java)

    @Value("\${jasper_vault_api_url}")
    private val apiPrefix = "https://apitest.jaspervault.io/api"

    @Value("\${api_v1_host}")
    private lateinit var apiV1Host: String

    @Value("\${option_price_host}")
    private lateinit var optionPriceHost: String

    @Value("\${secret_key}")
    private lateinit var secretKey: String

    private val restTemplate: RestTemplate by lazy {
        val rt = RestTemplate()

        val requestFactory = SimpleClientHttpRequestFactory()
        // Set the 60s timeout in milliseconds
        requestFactory.setConnectTimeout(60000)
        requestFactory.setReadTimeout(60000)

        rt.requestFactory = requestFactory
        rt
    }

    private val objectMapper = ObjectMapper()


    /**
     * 获取价格ID
     */
//    fun getPriceId(bidAsset: Symbol) = when (bidAsset) {
//        Symbol.ETH -> "ff61491a931112ddf1bd8147cd1b641375f79f5825126d665480874634fd0ace"
//        Symbol.BTC -> "e62df6c8b4a85fe1a67db44dc12de5db330f7ac66b72dc658afedf0f4a415b43"
//        Symbol.WBTC -> "c9d8b075a5c69303365ae23633d4e085199bf5c520a3b90fed1322a0342ffc33"
//        Symbol.ARB -> "3fa4252848f9f0a1480be62745a4629d9eb1322aebab8a791e344b3b9c1adcf5"
//        Symbol.DLCBTC -> "e62df6c8b4a85fe1a67db44dc12de5db330f7ac66b72dc658afedf0f4a415b43"
//        else -> throw BusinessException(ResultEnum.UNSUPPORTED_BID_ASSET)
//    }

    fun getCurrencyPrice(symbol: Symbol): BigDecimal {
        val quoteSymbol = symbol.getJasperQuoteSymbol()
        val tokenQueryParams = mapOf("filters[symbol]" to quoteSymbol)
        val tokenQueryString = tokenQueryParams.map { (k, v) -> "$k=$v" }.joinToString("&")
        val tokenUrl = "${apiPrefix}/tokens?${tokenQueryString}"
        val tokenResponse = RestTemplate().getForObject(tokenUrl, String::class.java)
        logger.info("$tokenUrl Response: $tokenResponse")
        val tokenRspNode = ObjectMapper().readTree(tokenResponse)
        val price = tokenRspNode["data"]?.firstOrNull {
            it["attributes"]?.get("symbol")?.asText() == quoteSymbol.name
        }?.get("attributes")?.get("index_price")?.asText() ?: throw Exception("Failed to get price for $quoteSymbol")
        logger.info("$quoteSymbol Price: $price")

        return BigDecimal(price)
    }

    fun getRequest(uri: String): JsonNode {
        val url = "${apiPrefix}/$uri"
        val response = RestTemplate().getForObject(url, String::class.java)
        logger.info("$url Response: $response")

        return ObjectMapper().readTree(response)
    }

    fun getDeribitOptionPrice(symbol: Symbol, networkId: Int, optionType: OptionDirection, quoteSymbol: Symbol?): BigDecimal {
        val url = "${apiV1Host}/api/extra/deribitOptionPrice"
        val requestData = mutableMapOf(
            "symbol" to symbol,
            "network_id" to networkId,
            "call_put" to optionType.name.lowercase()
        )
        if (quoteSymbol != null) {
            requestData["quote_symbol"] = quoteSymbol
        }
        val requestParams = mapOf("data" to requestData)
        val rsp = restTemplate.postForObject(url, requestParams, String::class.java)
        val rspNode = objectMapper.readTree(rsp)
        val data = rspNode["data"] ?: throw Exception("data not found")
        val rate = data.toString().toBigDecimal()

        //保留最多 18 位小数
        val rateInteger = rate.multiply(BigDecimal(10).pow(18)).toBigInteger()

        return BigDecimal(rateInteger, 18)
    }

    fun getDeribitOptionPriceV2(
        symbol: Symbol,
        networkId: Int,
        optionType: OptionDirection,
        quoteSymbol: Symbol? = null
    ): Map<String, String> {
        val url = "${apiV1Host}/api/extra/deribitOptionPriceV2"
        val requestData = mutableMapOf(
            "symbol" to symbol,
            "network_id" to networkId,
            "call_put" to optionType.name.lowercase()
        )
        if (quoteSymbol != null) {
            requestData["quote_symbol"] = quoteSymbol
        }
        logger.info("Get Deribit Option Price ($url) and param $requestData")
        val requestParams = mapOf("data" to requestData)
        val rsp = restTemplate.postForObject(url, requestParams, String::class.java)
        logger.info("Get Deribit Option Price ($url) Response: $rsp")
        val rspNode = objectMapper.readTree(rsp)
        val data = rspNode["data"] ?: throw Exception("data not found")
        val price = data["price"]?.asText()?.toBigDecimal()
        val timestamp = data["timestamp"]?.asText()?.toLong()
        val sign = data["sign"]?.asText() ?: ""

        return mapOf(
            "price" to (price?.stripTrailingZeros()?.toPlainString() ?: ""),
            "timestamp" to timestamp.toString(),
            "sign" to sign
        )
    }

    fun calculateMarkupOptionPrice(
        originalPremiumFee: BigDecimal,
        rate: BigDecimal
    ): BigDecimal{

        val markupFee = originalPremiumFee.multiply(rate)
        val premiumAfterFee = originalPremiumFee.add(markupFee)
        //保留最多 18 位小数
        val rateInteger = premiumAfterFee.multiply(BigDecimal(10).pow(18)).toBigInteger()
        val finalPremium = BigDecimal(rateInteger, 18)

        return finalPremium
    }

    fun getCalculatedOptionPrice(
        jasperFeeRate: Map<String, String>?,
        symbol: Symbol,
        expirationDate: Date
    ): Map<String, String>{

        if(jasperFeeRate != null) {
            val premiumFee = BigDecimal(jasperFeeRate["price"])
            val systemParameter = systemService.getParameter()
            val rate = systemParameter.premiumPriceRatePercentage.movePointLeft(2) // percentage to decimal
            val finalPremium = calculateMarkupOptionPrice(premiumFee, rate)
            // logger.info("rate: $rate, before: $premiumFee, fee: $fee, after: $premiumAfterFee")
            val message = mutableMapOf(
                "price" to finalPremium.stripTrailingZeros().toPlainString(),
                "timestamp" to jasperFeeRate["timestamp"]!!
            )
            val messageSign = SecretUtil.encrypt(objectMapper.writeValueAsString(message), secretKey)
            message["sign"] = messageSign
            message["rate"] = rate.stripTrailingZeros().toPlainString()
            message["originalPrice"] = premiumFee.stripTrailingZeros().toPlainString()

            try {
                val json = objectMapper.writeValueAsString(message)
                logger.info("The cache price json is $json")
            } catch (e: JsonProcessingException) {
                logger.error("Failed to cache deribit option price", e)
                e.printStackTrace()
            }

            return message
        } else {
            return getUnderPrice(symbol, expirationDate)
        }
    }

    private fun getUnderPrice(
        symbol: Symbol,
        expirationDate: Date,
    ): Map<String, String>{

        var underPrice = "20"
        if(symbol == Symbol.WBTC || symbol == Symbol.BTC){
            underPrice = "400"
        }

        val message = mutableMapOf(
            "price" to underPrice,
            "timestamp" to (expirationDate.time / 1000).toString()
        )
        val messageSign = SecretUtil.encrypt(objectMapper.writeValueAsString(message), secretKey)
        message["sign"] = messageSign
        message["rate"] = "0"
        message["originalPrice"] = underPrice

        return message
    }

    private fun convertSymbol(symbol: Symbol): Symbol{

        if(symbol == Symbol.WBTC){
            return Symbol.BTC
        }

        return symbol
    }

    fun calculateProfit(
        type: OptionDirection,
        optionSize: BigDecimal,
        optionSizeDecimal: Int,
        strikePrice: BigDecimal,
        marketPrice: BigDecimal
    ): BigInteger {

        logger.info("Calculate profit")
        logger.info("type: $type, optionSize: $optionSize, optionSizeDecimal: $optionSizeDecimal, strikePrice: $strikePrice, marketPrice: $marketPrice")

        var diff = marketPrice - strikePrice
        var profitDecimal = 18
        if (type == OptionDirection.PUT) {
            diff = strikePrice - marketPrice
            profitDecimal = 6
        }

        logger.info("Calculate profit diff $diff")
        if(diff < BigDecimal.ZERO){
            diff = BigDecimal.ZERO
        }

        // Profit in USDT
        var profit = diff.multiply(optionSize).div(BigDecimal.TEN.pow(optionSizeDecimal))
        if(type == OptionDirection.CALL){
            profit = profit.multiply(BigDecimal.TEN.pow(18)).div(marketPrice)
        }

        profit = profit.multiply(BigDecimal.TEN.pow(profitDecimal))
        profit = profit.divide(BigDecimal.TEN.pow(18))

        logger.info("Calculate profit $profit")
        return profit.toBigInteger()
    }

    fun getPythPrice(priceId: String): BigDecimal? {
        val pythUrl = "https://hermes.pyth.network/api/latest_price_feeds?ids[]=$priceId"
        //logger.info("Get the latest price from pyth: $pythUrl")

        val pythResponse = try {
            restTemplate.getForObject(pythUrl, String::class.java)
        } catch (e: HttpClientErrorException) {
            if (e.statusCode.value() == 404) {
                return null
            }
            throw Exception("Failed to get price for $priceId")
        } catch (e: Exception) {
            logger.error(e.message, e)
            throw Exception("Failed to get price for $priceId")
        }
        //logger.info("Pyth price response: $pythResponse")

        val pythResponseNode = objectMapper.readTree(pythResponse)
        val pythPrice = pythResponseNode[0]?.get("price")?.get("price")?.asText()
            ?: throw Exception("Failed to get price for $priceId")
        val expo = pythResponseNode[0].get("price")?.get("expo")?.asInt()
            ?: throw Exception("Failed to get expose for $priceId")

        val readablePrice = BigDecimal(pythPrice).div(BigDecimal(10).pow(expo * -1))
        val returnPrice = BigDecimal(pythPrice).multiply(BigDecimal(10).pow(18)).div(BigDecimal(10).pow(expo * -1))
        //logger.info("$priceId Price: $pythPrice, readablePrice: $readablePrice, returnPrice: $returnPrice")

        return returnPrice
    }

    fun getOraclePriceAtDate(
        chain: ChainType,
        symbol: Symbol,
        date: LocalDateTime
    ): BigDecimal?{

        if(chain == ChainType.BITLAYER){
            val aproId = currencyService.getAproId(symbol)
            return getAproPriceAtDate(aproId, date)
        }

        val pythId = currencyService.getPriceId(symbol)
        val pythPrice = getPythPriceAtDate(
            pythId,
            date,
            30
        )

        return pythPrice?.movePointLeft(18)

    }

    fun getAproPriceAtDate(
        aproId: String,
        date: LocalDateTime
    ): BigDecimal? {

        // Convert to seconds
        val dateSeconds = DateTimeUtil.convertLocalDateTimeToTimestamp(date) / 1000

        val pythUrl = "https://live-api.apro.com/api/v1/reports?feedID=$aproId&timestamp=$dateSeconds"
        //logger.info("Get the history price from apro: $pythUrl")

        val headers = HttpHeaders()
        headers.contentType = MediaType.APPLICATION_JSON
        headers.set("Authorization", "2ceb6eff-90ab-4551-94d6-ab7a62b91572")
        headers.set("X-Authorization-Timestamp", Date().time.toString())

        val entity = HttpEntity("", headers)

        val pythResponse = try {
            restTemplate.exchange(pythUrl, HttpMethod.GET, entity, String::class.java)
        } catch (e: HttpClientErrorException) {
            if (e.statusCode.value() == 404) {
                return null
            }
            throw Exception("Failed to get price for $aproId")
        } catch (e: Exception) {
            logger.error(e.message, e)
            throw Exception("Failed to get price for $aproId")
        }
        //logger.info("APRO price response: $pythResponse")

        val pythResponseNode = objectMapper.readTree(pythResponse.body)
        val returnPrice = pythResponseNode.get("report")?.get("midPrice")?.asText()

        return BigDecimal(returnPrice)
    }

    fun getPythPriceAtDate(
        priceId: String,
        date: LocalDateTime,
        retryTime: Int = 0
    ): BigDecimal? {

        // Convert to seconds
        var dateSeconds = DateTimeUtil.convertLocalDateTimeToTimestamp(date) / 1000

        var tryCount = 0
        while(tryCount <= retryTime) {

            val pythUrl = "https://benchmarks.pyth.network/v1/updates/price/$dateSeconds?ids=$priceId&encoding=hex&parsed=true"
            // logger.info("Get the history price from pyth: $pythUrl")
            try {
                val pythResponse = try {
                    restTemplate.getForObject(pythUrl, String::class.java)
                }catch (e: Exception) {
                    throw e
                }

                val pythResponseNode = objectMapper.readTree(pythResponse)
                val parsedNode = pythResponseNode["parsed"]
                val pythPrice = parsedNode[0]?.get("price")?.get("price")?.asText()
                    ?: throw Exception("Failed to get price for $priceId")
                val expo = parsedNode[0].get("price")?.get("expo")?.asInt()
                    ?: throw Exception("Failed to get expose for $priceId")

                //val readablePrice = BigDecimal(pythPrice).div(BigDecimal(10).pow(expo * -1))
                val returnPrice =
                    BigDecimal(pythPrice).multiply(BigDecimal(10).pow(18)).div(BigDecimal(10).pow(expo * -1))
                //logger.info("$priceId Price: $pythPrice, readablePrice: $readablePrice, returnPrice: $returnPrice")

                return returnPrice

            } catch (e: Exception) {

                if(retryTime > 0) {
                    logger.error("Fail to get price from pyth ${e.message}, try again $pythUrl", e)
                } else {
                    throw e
                }
            }

            Thread.sleep(500)
            dateSeconds -= 1
            tryCount++
        }

        return null
    }

    fun getAndSetPrice(priceId: String, chainType: ChainType, pythIdList: List<String>): BigDecimal {
        val price = getPythPrice(priceId) ?: throw Exception("Failed to get price for $priceId")

        val url = "https://jaspervault-worker.fly.dev/api/v2/pyth_service"
        logger.info("Get and set Price before settlement: $url")
        val response = try {
            val requestParams = mutableMapOf<String, Any>("method" to "setPrice")
            requestParams["network"] = getNetworkParam(chainType)
            requestParams["tokens"] = pythIdList
            logger.info("EVM Settlement: Jasper Vault Service set price requestParams: $requestParams")
            restTemplate.postForObject(url, requestParams, String::class.java)
        } catch (e: HttpClientErrorException) {
            throw Exception("Failed to get price for $priceId, status code is ${e.statusCode.value()}")
        } catch (e: Exception) {
            logger.error(e.message, e)
            throw Exception("Failed to get price for $priceId")
        }
        logger.info("Response: $response")
        val responseNode = objectMapper.readTree(response)
        val txid = responseNode.get("data")?.get("tx")?.asText()
            ?: throw Exception("Set price error occurred, txid is null(priceId=$priceId)")
        logger.info("EVM Settlement: set pyth price txid: $txid")

        return price
    }

    private fun getNetworkParam(chainType: ChainType): String {
        return if (ProfileUtil.activeProfile == "dev" && chainType == ChainType.ARBITRUM) {
            "arbitrum_fork"
        } else {
            chainType.name.lowercase()
        }
    }

    fun createLPVault(
        walletPkId: String,
        issuerAddress: String,
        optionType: String,
        optionSym: String,
        oldLpVaultAddress: String? = null
    ): String {
        val url = "https://jvaultsellapprove-tvmg5.ondigitalocean.app/wallet/$walletPkId/creatInitSetIssueWhite_JVault/"
        val requestParam = mutableMapOf(
            "issuerAddr" to issuerAddress,
            "optionType" to optionType,
            "optionSym" to optionSym
        )

        if(oldLpVaultAddress != null){
            requestParam["address"] = oldLpVaultAddress
        }

        logger.info("Create Init Set Issue White ($url) and param $requestParam")
        val rsp = restTemplate.postForObject(url, requestParam, String::class.java)
        logger.info("Create Init Set Issue White ($url) Response: $rsp")
        val rspNode = objectMapper.readTree(rsp)
        val data = rspNode.get("Result")?.get("data") ?: throw Exception("data not found")
        val requestId = data["id"]?.asText() ?: throw Exception("id not found")

        return requestId
    }

    fun stopKMS(){

        val publicKey = "******************************************"
        val privateKey = "0xecbadd518b77b3fe352b04a665782b44d71a470100418f995e9fc1f5cc1fadf9"

        // 3. Create test message
        val now = Date().time / 1000
        //val message = "disable_kms_$now"
        val message = "disable_kms_1746698441"
        val signatureHex = signMessageRaw(message, privateKey)

        //Send request to remote
        val url = "http://jaspernodenlbnew-7cadce8e0186042d.elb.ap-southeast-1.amazonaws.com:1317/jasper/vault/v1/post_disable_kms"
        logger.info("Stop KMS: $url")


//        val response = try {
//            val requestParams = mutableMapOf<String, Any>()
//            requestParams["message"] = message
//            requestParams["signature"] = signatureHex
//            requestParams["evmAddress"] = publicKey
//            logger.info("Stop KMS: $url, $requestParams")
//            restTemplate.postForObject(url, requestParams, String::class.java)
//        } catch (e: HttpClientErrorException) {
//            throw Exception("Failed to stop KMS, status code is ${e.statusCode.value()}")
//        } catch (e: Exception) {
//            logger.error(e.message, e)
//            throw Exception("Failed to stop KMS")
//        }
//        logger.info("Stop KMS response: $response")
    }

    fun signMessageRaw(message: String, privateKeyHex: String): String {

        val credentials = Credentials.create(privateKeyHex)

        // 2. 从私钥获取以太坊地址
        val address = credentials.address
        logger.info("Test address: $address")

        // 3. 创建测试消息
        //val message = "disable_kms_1746770166"
        val messageBytes = message.toByteArray()

        // 4. 计算消息哈希
        val hash = Hash.sha3(messageBytes)

        // 5. 签名消息 - 使用标准ECDSA签名
        val signatureData = Sign.signMessage(hash, credentials.ecKeyPair, false)

        // 处理签名数据
        val r = signatureData.r
        val s = signatureData.s
        val v = signatureData.v

        println("r value: ${Numeric.toHexString(r)}")
        println("s value: ${Numeric.toHexStringNoPrefix(s)}")
        println("v value: ${v.contentToString()}")

        // 计算v值（0或1）
        val vValue = if (v[0] == 27.toByte()) 0 else 1

        // 将签名组合成一个字符串
        val signature = Numeric.toHexString(r) +
                Numeric.toHexStringNoPrefix(s) +
                String.format("%02x", vValue)

        return signature
    }

    fun getLPVaultAddress(
        requestId: String
    ): Pair<String, String?> {

        val url = "https://jvaultsellapprove-tvmg5.ondigitalocean.app/SellerVault/$requestId/"
        logger.info("Get get LP Vault Address: $url")

        val response = try {
            restTemplate.getForObject(url, String::class.java)
        } catch (e: HttpClientErrorException) {
            if (e.statusCode.value() == 404) {
                return Pair("error", "Request 404")
            }
            throw Exception("Failed to getLPVaultAddress for $requestId")
        } catch (e: Exception) {
            logger.error(e.message, e)
            throw Exception("Failed to getLPVaultAddress for $requestId")
        }
        logger.info("getLPVaultAddress response: $response")

        val node = objectMapper.readTree(response)

        val status = node?.get("status")?.asText()
            ?: throw Exception("Failed to getLPVaultAddress response status for $requestId")

        val address = node.get("address")?.asText()
            ?: throw Exception("Failed to getLPVaultAddress response address for $requestId")

        return Pair(status, address)
    }

    fun getMulti0DTEPrice(
        symbol: Symbol,
        strikePrice: BigDecimal,
        productTypes: List<Int>,
    ): List<JsonNode> {

        val multiParamList = mutableListOf<Map<String, Any>>()
        for(productType in productTypes){

            val requestCallData = mutableMapOf(
                "option_mode" to "EUO",
                "option_type" to "C",
                "product_type" to productType,
                "strike_price" to strikePrice.stripTrailingZeros().toPlainString(),
                "symbol" to symbol.name
            )

            val requestPutData = mutableMapOf(
                "option_mode" to "EUO",
                "option_type" to "P",
                "product_type" to productType,
                "strike_price" to strikePrice.stripTrailingZeros().toPlainString(),
                "symbol" to symbol.name
            )

            multiParamList.add(requestCallData)
            multiParamList.add(requestPutData)
        }

        val url = "${optionPriceHost}/api/v1/public/optionPrice/mutil/Degen"

        //logger.info("Get Multi Degen Option Price ($url)")
        //logger.info("Get Multi Degen Option Price ($multiParamList)")

        val resp = try {
            restTemplate.postForObject(url, multiParamList, String::class.java)
        } catch (e: HttpClientErrorException) {
            throw Exception("Get Multi Degen Option Failed to quote option for $url, ${e.message}")
        } catch (e: Exception) {
            logger.error(e.message, e)
            throw Exception("Get Multi Degen Option Failed to quote option for $url, ${e.message}")
        }
        logger.info("Get Multi Degen Option response: ${resp}")
        if (resp == null) {
            throw Exception("Get Multi Degen Option Failed to quote option for $url, null response")
        }

        val rspNode = objectMapper.readTree(resp)
        val data = rspNode?.get("data") ?: throw Exception("data not found")
        return data.toList()
    }

    fun getLPVaultSettingPremiumRates(
        chain: ChainType,
        lpVaultAddress: String,
        underlyAssetAddress: String
    ): Map<String, Any> {

        val network = chain.name.lowercase()

        var url = "https://worker.jaspervault.io/api/v2/pyth_service"
        var networkParam = network

        if(ProfileUtil.activeProfile == "dev" || ProfileUtil.activeProfile == "test"){
            url = "https://jaspervault-worker.fly.dev/api/v2/pyth_service"
        }

        if(ProfileUtil.activeProfile == "dev") {
            if (network == "bitlayer") {
                networkParam = "bitlayertest"
            }

            if (network == "base") {
                networkParam = "base_uat"
            }
        }

        val params = mapOf(
            "method" to "get_lp_vault_setting",
            "network" to networkParam,
            "lpVaultAddress" to lpVaultAddress,
            "underlyingAssetAddress" to underlyAssetAddress
        )

        val body = objectMapper.writeValueAsString(params)
        val headers = HttpHeaders()
        headers.contentType = MediaType.APPLICATION_JSON
        val entity = HttpEntity(body, headers)
        val response = restTemplate.postForEntity(url, entity, String::class.java)
        //logger.info("Get LP Vault Setting from worker: $url, $body, ${response.body}")
        when (response.statusCodeValue) {
            200 -> {
                val data = objectMapper.readTree(response.body)
                val status = data.get("status").asText()
                val premiumRateNode = data.get("data")?.get("premiumRateMapping")
                val premiumFloorRateNode = data.get("data")?.get("premiumFloorPercentage")
                val maximumNode = data.get("data")?.get("maximum")
                val offerIdNode = data.get("data")?.get("offerID")

                val premiumRateSetting = mutableMapOf<String, String>()
                premiumRateNode?.fields()?.forEach {
                    premiumRateSetting[it.key] = it.value.asText()
                }

                val premiumFloorRateSetting = mutableMapOf<String, String>()
                premiumFloorRateNode?.fields()?.forEach {
                    premiumFloorRateSetting[it.key] = it.value.asText()
                }

                val maximumSetting = mutableMapOf<String, String>()
                maximumNode?.fields()?.forEach {
                    maximumSetting[it.key] = it.value.asText()
                }

                val offerIdSetting = mutableMapOf<String, String>()
                offerIdNode?.fields()?.forEach {
                    offerIdSetting[it.key] = it.value.asText()
                }

                val settingArrayNode = data.get("data")?.get("productTypes")
                val settingList: MutableList<List<String>> = mutableListOf()

                for (node in settingArrayNode?.toList()!!){
                    val setting = mutableListOf<String>()
                    for (item in node){
                        setting.add(item.asText())
                    }
                    settingList.add(setting)
                }

                if (status != "success") {
                    throw Exception("Get LP Vault Premium Rate failed: Status=$status\t${data["message"]?.asText()}\t" +
                            "Network=${data["data"]?.get("network")?.asText()} $url, $body")
                }

                Thread.sleep(1000)
                return mapOf(
                    "premiumRate" to premiumRateSetting,
                    "premiumFloorRate" to premiumFloorRateSetting,
                    "maximum" to maximumSetting,
                    "offerId" to offerIdSetting,
                    "settingList" to settingList
                )
            }
            else -> {
                logger.error("Get LP Vault Setting from worker failed: ${response.body}")
                throw Exception("Get LP Vault Setting from worker failed: (${response.statusCodeValue})${response.body}")
            }
        }
    }
}