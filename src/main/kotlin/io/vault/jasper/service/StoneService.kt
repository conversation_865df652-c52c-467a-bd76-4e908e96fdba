package io.vault.jasper.service

import io.vault.jasper.enums.ChainType
import io.vault.jasper.enums.OptionDirection
import io.vault.jasper.enums.Symbol
import io.vault.jasper.model.*
import io.vault.jasper.repository.*
import io.vault.jasper.service.blockchain.BitlayerService
import org.slf4j.LoggerFactory
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.data.repository.findByIdOrNull
import org.springframework.stereotype.Service
import java.math.BigDecimal
import java.math.BigInteger
import java.time.LocalDateTime

@Service
class StoneService @Autowired constructor(
    private val moonlightRebateRecordRepository: MoonlightRebateRecordRepository,
    private val moonlightCampaignParameterRepository: MoonlightCampaignParameterRepository,
    private val galaRebateRecordRepository: GalaRebateRecordRepository,
    private val optionOrderService: OptionOrderService
){

    private val logger = LoggerFactory.getLogger(this::class.java)

    private val allowChains = listOf(
        ChainType.BITLAYER,
        ChainType.ARBITRUM,
//        ChainType.BASE,
//        ChainType.BSC
    )

    /**
     * 获取空投参数对象
     */
    fun getCampaignParameter(): MoonlightCampaignParameter {
        val params = moonlightCampaignParameterRepository.findAll()

        return if (params.isEmpty()) {
            val p = MoonlightCampaignParameter(null)
            moonlightCampaignParameterRepository.save(p)
        } else {
            params.first()
        }
    }

    fun isMiniAppOptionOrder(optionOrder: OptionOrder): Boolean{

        if(optionOrder.chain !in allowChains){
            //logger.info("Stone Service: Not allow chain ${optionOrder.chain} ${optionOrder.id}")
            return false
        }

//        if(optionOrder.status != OptionStatus.SETTLED){
//            logger.info("Stone Service: status is not settled ${optionOrder.status} ${optionOrder.id}")
//            return false
//        }

        // 是否来自Mini_app 或者 BTC_FI 的订单
        if(optionOrder.product != OptionOrderProduct.MINI_APP && optionOrder.product != OptionOrderProduct.BTC_FI){
            //logger.info("Stone Service: not from bitstone ${optionOrder.product} ${optionOrder.id}")
            return false
        }

        if(optionOrder.chain == ChainType.BITLAYER && optionOrder.bidAsset != Symbol.BTC){
            //logger.info("Stone Service: not BITLAYER BTC ${optionOrder.id}")
            return false
        }

        if(optionOrder.chain == ChainType.ARBITRUM && optionOrder.bidAsset != Symbol.WBTC){
            //logger.info("Stone Service: not ARBITRUM WBTC ${optionOrder.id}")
            return false
        }

        if(optionOrder.chain == ChainType.BASE && optionOrder.bidAsset != Symbol.CBBTC){
            //logger.info("Stone Service: not BASE CBBTC ${optionOrder.id}")
            return false
        }

        if(optionOrder.chain == ChainType.BSC && optionOrder.bidAsset != Symbol.BTCB){
            //logger.info("Stone Service: not BSC BTCB ${optionOrder.id}")
            return false
        }

        // 暂时屏蔽 0.01 以下碎片的发放
//        if(optionOrder.bidAmount!!.compareTo(BigDecimal("0.2")) != 0 &&
//            optionOrder.bidAmount!!.compareTo(BigDecimal("0.05")) != 0 &&
//            optionOrder.bidAmount!!.compareTo(BigDecimal("0.01")) != 0){
//            logger.info("Stone Service: bitAmount not correct ${optionOrder.bidAmount} ${optionOrder.id}")
//            return false
//        }

        if(optionOrder.bidAmount!!.compareTo(BigDecimal("0.01")) != 0){
            logger.info("Stone Service: bitAmount not correct ${optionOrder.bidAmount} ${optionOrder.id}")
            return false
        }

        if(optionOrder.expiryInHour != "0.5" && optionOrder.expiryInHour != "2"){
            //logger.info("Stone Service: expiryInHour not correct ${optionOrder.expiryInHour} ${optionOrder.id}")
            return false
        }

        // 使用道具的排除
        if(optionOrder.usedMoonlightBox != null){
            //logger.info("Stone Service: use moonlightbox ${optionOrder.id}")
            return false
        }

        if(optionOrder.usedPowerStone != null){
            //logger.info("Stone Service: use powerstone ${optionOrder.id}")
            return false
        }

        if(optionOrder.usedRealityStone != null){
            //logger.info("Stone Service: use realitiystone ${optionOrder.id}")
            return false
        }

        if(optionOrder.usedSpaceStone != null){
            //logger.info("Stone Service: use spacestone ${optionOrder.id}")
            return false
        }

        if(optionOrder.usedTimeStone != null){
            //logger.info("Stone Service: use timestone ${optionOrder.id}")
            return false
        }

        //排除免单的使用
        if(optionOrder.premiumFeePay == BigDecimal.ZERO){
            //logger.info("Stone Service: user pay premium is zero ${optionOrder.id}")
            return false
        }

        return true
    }

    private fun canGetRealityStonePiece(optionOrder: OptionOrder): Boolean{

        if(!isMiniAppOptionOrder(optionOrder)){
            return false
        }

        if(optionOrder.buyerProfit == null || optionOrder.buyerProfit!! > BigDecimal.ZERO){
            logger.info("Stone Service: realitystone piece buyer profit is null or positive ${optionOrder.id}")
            return false
        }

        return true
    }

    private fun canGetPowerStonePiece(optionOrder: OptionOrder): Boolean{

        if(!isMiniAppOptionOrder(optionOrder)){
            return false
        }

        if(optionOrder.buyerProfit == null){
            logger.info("Stone Service: power stone piece buyer profit is null ${optionOrder.id}")
            return false
        }

        val netProfit = optionOrderService.calculateNetProfit(optionOrder)
        if(netProfit >= BigDecimal.ZERO){
            logger.info("Stone Service: power stone piece net_profit is positive ${optionOrder.id}")
            return false
        }

        return true
    }

    private fun canGetTimeStonePiece(optionOrder: OptionOrder): Boolean{

        if(!isMiniAppOptionOrder(optionOrder)){
            return false
        }

        if(optionOrder.buyerProfit == null){
            logger.info("Stone Service: timestone piece buyer profit is null ${optionOrder.id}")
            return false
        }

        val timeStoneStartDate = LocalDateTime.of(2025, 3, 26, 8, 0, 0)
        if(optionOrder.created.isBefore(timeStoneStartDate)){
            return false
        }

        val netProfit = optionOrderService.calculateNetProfit(optionOrder)
        if(netProfit >= BigDecimal.ZERO){
            logger.info("Stone Service: Not allow timestone piece ${optionOrder.id}")
            return false
        }

        return true
    }

    fun getNftIdFromOptionOrder(optionOrder: OptionOrder): List<BigInteger> {
        val nftIds = mutableListOf<BigInteger>()

        if(canGetRealityStonePiece(optionOrder)){

            if(optionOrder.bidAmount!!.compareTo(BigDecimal("0.01")) == 0) {
                nftIds.add(BigInteger("2"))
            }
            // 暂时屏蔽 0.01 以下碎片的发放
//            if(optionOrder.bidAmount!!.compareTo(BigDecimal("0.05")) == 0){
//                nftIds.add(BigInteger("3"))
//            }
//            if(optionOrder.bidAmount!!.compareTo(BigDecimal("0.2")) == 0){
//                nftIds.add(BigInteger("6"))
//            }
        } else if(canGetPowerStonePiece(optionOrder)){
            if(optionOrder.bidAmount!!.compareTo(BigDecimal("0.01")) == 0) {
                nftIds.add(BigInteger("4"))
            }
            // 暂时屏蔽 0.01 以下碎片的发放
//            if(optionOrder.bidAmount!!.compareTo(BigDecimal("0.05")) == 0){
//                nftIds.add(BigInteger("5"))
//            }
//            if(optionOrder.bidAmount!!.compareTo(BigDecimal("0.2")) == 0){
//                nftIds.add(BigInteger("7"))
//            }
        }

        // 可以同时获得时间宝石碎片
        if(canGetTimeStonePiece(optionOrder)) {
            if (optionOrder.bidAmount!!.compareTo(BigDecimal("0.01")) == 0) {
                nftIds.add(BigInteger("11"))
            }
            // 暂时屏蔽 0.01 以下碎片的发放
//            if(optionOrder.bidAmount!!.compareTo(BigDecimal("0.05")) == 0){
//                nftIds.add(BigInteger("12"))
//            }
//            if(optionOrder.bidAmount!!.compareTo(BigDecimal("0.2")) == 0){
//                nftIds.add(BigInteger("13"))
//            }
        }

        // 暂时屏蔽 2 小时宝石碎片的发放
//        if(optionOrder.expiryInHour == "2"){
//            //nftIds 元素加30
//            nftIds.forEachIndexed { index, nftId ->
//                nftIds[index] = nftId.add(BigInteger("30"))
//            }
//        }

        return nftIds
    }

    fun createStoneRebateRecord(
        optionOrder: OptionOrder
    ){

        // 是否已经创建了订单
        val existingRecord = moonlightRebateRecordRepository.findByOptionOrderId(
            optionOrder.id!!
        )

        if(existingRecord != null){
            logger.info("Stone Service Already add rebate record ${optionOrder.buyer} ${optionOrder.id}")
            return
        }

        val nftIds = getNftIdFromOptionOrder(optionOrder)
        if(nftIds.isEmpty()){
            logger.info("Stone Service Not create rebate record ${optionOrder.buyer} ${optionOrder.id}")
            return
        }

        var maxPieceCount = 7
        if(optionOrder.expiryInHour == "0.5"){
            maxPieceCount = 3
        }

        for (nftId in nftIds){

            logger.info("Begin create stone rebate record ${optionOrder.buyer} ${optionOrder.id} ${nftId}")

            val records = moonlightRebateRecordRepository.findByChainAndBuyerAddressIgnoreCaseAndSbtNFTIdAndStatusIn(
                optionOrder.chain,
                optionOrder.buyer!!,
                nftId,
                listOf(MoonlightRebateRecordStatus.EXECUTED)
            )

            if(records.isNotEmpty()){
                val record = records.first()
                record.stonePieceCount += 1
                record.stoneRelatedOptionOrderIds!!.add(optionOrder.id)

                if(record.stonePieceCount >= maxPieceCount){
                    record.status = MoonlightRebateRecordStatus.CREATED
                }

                moonlightRebateRecordRepository.save(record)
            } else {

                var stoneRelatedOptionOrderIds = mutableListOf(optionOrder.id)
                // 检查是不是第一单亏损，如果是，赠送两个碎片
                val rebateRecords = moonlightRebateRecordRepository.findByBuyerAddressIgnoreCase(
                    optionOrder.buyer!!
                )

                if(rebateRecords.isEmpty()){
                    stoneRelatedOptionOrderIds = mutableListOf("0","0",optionOrder.id)
                }

                logger.info("Begin create stone rebate record create moonlight record ${optionOrder.buyer} ${optionOrder.id} ${nftId}")

                val record = MoonlightRebateRecord(
                    optionOrderId = optionOrder.id,
                    buyerAddress = optionOrder.buyer!!,
                    direction = optionOrder.direction!!,
                    bidAmount = optionOrder.bidAmount!!,
                    expiryInHour = optionOrder.expiryInHour!!,
                    sbtNFTId = nftId,
                    stonePieceCount = stoneRelatedOptionOrderIds.size,
                    stoneRelatedOptionOrderIds = stoneRelatedOptionOrderIds,
                    chain = optionOrder.chain,
                    bidAsset = optionOrder.bidAsset!!,
                )

                if(record.stonePieceCount >= maxPieceCount){
                    record.status = MoonlightRebateRecordStatus.CREATED
                }

                moonlightRebateRecordRepository.save(record)
            }
        }
    }

    fun getClaimRebateTotalCount(): Int {
        var count = moonlightRebateRecordRepository.findByStatusAndSettleTxIdIsNotNull(
            MoonlightRebateRecordStatus.SETTLED
        ).size


        count += galaRebateRecordRepository.findByStatusAndSettleTxIdIsNotNull(
            GalaRebateRecordStatus.SETTLED
        ).size

        return count
    }

    fun getUserClaimRebateTotalCount(
        user: User
    ): Int {
        var count = moonlightRebateRecordRepository.findByBuyerAddressIgnoreCaseAndStatusAndSettleTxIdIsNotNull(
            user.address,
            MoonlightRebateRecordStatus.SETTLED
        ).size

        count += moonlightRebateRecordRepository.findByBuyerAddressIgnoreCaseAndStatus(
            user.address,
            MoonlightRebateRecordStatus.CLAIMED
        ).size

        count += galaRebateRecordRepository.findByBuyerAddressIgnoreCaseAndStatusAndSettleTxIdIsNotNull(
            user.address,
            GalaRebateRecordStatus.SETTLED
        ).size

        count += galaRebateRecordRepository.findByBuyerAddressIgnoreCaseAndStatus(
            user.address,
            GalaRebateRecordStatus.CLAIMED
        ).size

        return count
    }

    fun getUnclaimedRebateRecord(
        user: User
    ): List<MoonlightRebateRecord> {
        return moonlightRebateRecordRepository.findByBuyerAddressIgnoreCaseAndStatus(
            user.address,
            MoonlightRebateRecordStatus.CREATED
        )
    }

    fun getUnclaimedGalaRebateRecord(
        user: User
    ): List<GalaRebateRecord> {
        return galaRebateRecordRepository.findByBuyerAddressIgnoreCaseAndStatus(
            user.address,
            GalaRebateRecordStatus.CREATED
        )
    }
}