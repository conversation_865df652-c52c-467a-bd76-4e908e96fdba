package io.vault.jasper.repository

import io.vault.jasper.model.KolRebateRecord
import io.vault.jasper.model.KolRebateRecordStatus
import org.springframework.data.domain.Page
import org.springframework.data.domain.Pageable
import org.springframework.data.mongodb.repository.MongoRepository
import org.springframework.stereotype.Repository
import java.time.LocalDateTime

@Repository
interface KolRebateRecordRepository : MongoRepository<KolRebateRecord, String> {
    fun findByKolIdAndCreatedIsGreaterThanEqual(kolId: String, created: LocalDateTime, page: Pageable): Page<KolRebateRecord>
    fun findByKolId(kolId: String, page: Pageable): Page<KolRebateRecord>
    fun findByStatus(status: KolRebateRecordStatus): List<KolRebateRecord>
    fun findFirstByOptionOrderId(optionOrderId: String): KolRebateRecord?
    fun findByStatusAndCreatedGreaterThan(status: KolRebateRecordStatus, created: LocalDateTime): List<KolRebateRecord>
}