package io.vault.jasper.service

import io.vault.jasper.enums.ChainType
import io.vault.jasper.enums.OptionDirection
import io.vault.jasper.enums.Symbol
import io.vault.jasper.model.*
import io.vault.jasper.repository.*
import org.slf4j.LoggerFactory
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.data.repository.findByIdOrNull
import org.springframework.stereotype.Service
import java.math.BigDecimal
import java.time.LocalDateTime

@Service
class BaseCampaignService @Autowired constructor(
    private val baseTradeRebateRecordRepository: BaseTradeRebateRecordRepository,
    private val baseCampaignParameterRepository: BaseCampaignParameterRepository,
    private val optionOrderRepository: OptionOrderRepository,
    private val userRepository: UserRepository,
    private val orderRepository: OrderRepository,
    private val discordLevelRepository: DiscordLevelRepository,
    private val currencyService: CurrencyService
){

    private val logger = LoggerFactory.getLogger(this::class.java)

    /**
     * 获取空投参数对象
     */
    fun getCampaignParameter(): BaseCampaignParameter {
        val params = baseCampaignParameterRepository.findAll()

        return if (params.isEmpty()) {
            val p = BaseCampaignParameter(null)
            baseCampaignParameterRepository.save(p)
        } else {
            params.first()
        }
    }

    /**
     * 判断是否活动内首单
     */
    fun isFirstOrderInCampaign(
        optionOrder: OptionOrder
    ): Boolean{

        if(optionOrder.status != OptionStatus.SETTLED &&
            optionOrder.status != OptionStatus.EXECUTED &&
            optionOrder.status != OptionStatus.SETTLE_FAILED){
            return false
        }

        val parameter = getCampaignParameter()

        // 符合 2H，0.01 CBBTC 使用 BTC 支付期权费 规格的订单
        if(optionOrder.bidAsset == null || optionOrder.bidAsset != Symbol.CBBTC){
            logger.info("BaseCampaignService Service Not CBBTC ${optionOrder.buyer} ${optionOrder.id}")
            return false
        }

        if(optionOrder.expiryInHour == null || optionOrder.expiryInHour != "2"){
            logger.info("BaseCampaignService Service Not 2 hours ${optionOrder.buyer} ${optionOrder.id}")
            return false
        }

        if(optionOrder.bidAmount == null || optionOrder.bidAmount!!.compareTo(parameter.firstTradeDegenQuantity) != 0){
            logger.info("BaseCampaignService Service Not 0.2 ETH ${optionOrder.buyer} ${optionOrder.id}")
            return false
        }

        val firstOptionOrder = optionOrderRepository.findFirstByChainAndBuyer(
            ChainType.BASE, optionOrder.buyer!!,
        )

        if(firstOptionOrder == null){
            return false
        }

        if(firstOptionOrder.id!! != optionOrder.id!!){
            return false
        }

        if(optionOrder.created.isBefore(parameter.startDate)){
            return false
        }

        return true
    }

    fun createBaseTradeRebateRecord(
        optionOrder: OptionOrder
    ){

        val parameter = getCampaignParameter()
        val rebateCount = getFirstTradeRebateCount()
        if( rebateCount >= parameter.firstTradeRebateCount){
            logger.info("BaseCampaignService first trade rebate task reach max count")
            return
        }

        val now = LocalDateTime.now()
        if(now.isAfter(parameter.endDate)){
            logger.info("BaseCampaignService first trade rebate task $now reach end date ${parameter.endDate}")
            return
        }

        // 是否已经创建了订单
        val existingRecord = baseTradeRebateRecordRepository.findByOptionOrderId(
            optionOrder.id!!
        )

        if(existingRecord != null){
            logger.info("BaseCampaignService Service Already add rebate record ${optionOrder.buyer} ${optionOrder.id}")
            return
        }

        // 是否有同样地址的订单
        val existingAddressRecords = baseTradeRebateRecordRepository.findByBuyerAddressIgnoreCase(
            optionOrder.buyer!!
        )

        if(existingAddressRecords.isNotEmpty()){
            logger.info("BaseCampaignService Service Already same address rebate record ${optionOrder.buyer} ${optionOrder.id}")
            return
        }

        val user = userRepository.findByAddressIgnoreCase(optionOrder.buyer!!)
        if(user == null){
            logger.info("BaseCampaignService Service can not find user record ${optionOrder.buyer} ${optionOrder.id}")
            return
        }

        val record = BaseTradeRebateRecord(
            optionOrderId = optionOrder.id,
            buyerAddress = optionOrder.buyer!!,
            direction = optionOrder.direction!!,
            bidAmount = optionOrder.bidAmount!!,
            retweetBaseTime = user.retweetBaseTime,
            discordLevel = getDiscordLevel(user),
        )

        baseTradeRebateRecordRepository.save(record)
    }

    fun checkBaseTradeRebate(
        record: BaseTradeRebateRecord
    ){

        val optionOrder = optionOrderRepository.findByIdOrNull(record.optionOrderId)
        if(optionOrder == null){
            logger.info("BaseCampaignService Service can not find order record ${record.buyerAddress} ${record.optionOrderId}")
            return
        }

        if(optionOrder.status == OptionStatus.SETTLE_FAILED){
            logger.info("BaseCampaignService Service order settle failed ${optionOrder.buyer} ${optionOrder.id}")
            record.status = BaseTradeRebateRecordStatus.SETTLE_FAILED
            baseTradeRebateRecordRepository.save(record)
            return
        }

        if(optionOrder.status != OptionStatus.SETTLED){
            logger.info("BaseCampaignService Service Not Settled order ${optionOrder.buyer} ${optionOrder.id}")
            return
        }

        //是否是新用户
        val user = userRepository.findByAddressIgnoreCase(optionOrder.buyer!!)
        if(user == null){
            logger.debug("BaseCampaignService Service can not find user ${optionOrder.buyer} ${optionOrder.id}")
            return
        }

        val order = orderRepository.findByIdOrNull(optionOrder.orderId)
        if(order == null){
            logger.debug("BaseCampaignService Service can not find order record ${optionOrder.buyer} ${optionOrder.id}")
            return
        }

        logger.info("BaseCampaignService Service check rebate record ${optionOrder.buyer} ${optionOrder.id}")

        record.strikePrice = order.strikePrice.movePointLeft(18)
        record.settlementPrice = BigDecimal(optionOrder.marketPriceAtSettlement!!).movePointLeft(18)

        val usdcDecimal = currencyService.getCurrencyDecimal(
            ChainType.BASE, Symbol.USDC
        )
        val cbbtcDecimal = currencyService.getCurrencyDecimal(
            ChainType.BASE, Symbol.CBBTC
        )

        record.premiumFee = optionOrder.premiumFeePay!!
        record.premiumAsset = Symbol.valueOf(optionOrder.premiumAsset!!.asset)

        if(record.premiumAsset == Symbol.CBBTC){
            record.premiumFeeInBtc = optionOrder.premiumFeePay!!
        } else {
            val readablePremiumFee = optionOrder.premiumFeePay!!.movePointLeft(usdcDecimal)
            val readableFeeInBtc = readablePremiumFee.divide(record.strikePrice, cbbtcDecimal, BigDecimal.ROUND_HALF_UP)
            record.premiumFeeInBtc = readableFeeInBtc.movePointRight(cbbtcDecimal).setScale(0, BigDecimal.ROUND_HALF_UP)
        }

        record.premiumFeeInUsdt = optionOrder.premiumFeePayInUsdt!!

        record.profit = optionOrder.buyerProfit!!

        if(record.direction == OptionDirection.CALL){
            record.profitAsset = Symbol.CBBTC
            record.profitInBtc = optionOrder.buyerProfit!!
            val readableBtcProfit = record.profitInBtc.movePointLeft(cbbtcDecimal)
            val readableUsdcProfit = readableBtcProfit.multiply(record.settlementPrice).setScale(usdcDecimal, BigDecimal.ROUND_HALF_UP)
            record.profitInUsdt = readableUsdcProfit.movePointRight(usdcDecimal).setScale(0, BigDecimal.ROUND_HALF_UP)
        } else {
            record.profitAsset = Symbol.USDC
            record.profitInUsdt = optionOrder.buyerProfit!!
            val readableUsdcProfit = record.profitInUsdt.movePointLeft(usdcDecimal)
            val readableBtcProfit = readableUsdcProfit.divide(record.settlementPrice, cbbtcDecimal, BigDecimal.ROUND_HALF_UP)
            record.profitInBtc = readableBtcProfit.movePointRight(cbbtcDecimal).setScale(0, BigDecimal.ROUND_HALF_UP)
        }

        val discordLevel = getDiscordLevel(user)
        record.discordLevel = discordLevel

        record.netProfit = record.profitInUsdt.subtract(record.premiumFeeInUsdt).setScale(0, BigDecimal.ROUND_HALF_UP)
        if(record.netProfit.compareTo(BigDecimal.ZERO) == -1){
            record.rebateAmount = record.netProfit.multiply(BigDecimal("-1"))
            record.status = BaseTradeRebateRecordStatus.CREATED
        } else {
            record.status = BaseTradeRebateRecordStatus.SETTLED
        }

        baseTradeRebateRecordRepository.save(record)
    }

    fun getDiscordLevel(
        user: User
    ): Int {

        val discordId = user.discordInfo?.userId ?: return 0
        val level = discordLevelRepository.findByDiscordId(discordId)
        if(level == null){
            return 0
        }

        return level.level
    }

    fun getFirstTradeRebateCount(): Int {
        return baseTradeRebateRecordRepository.findByStatusAndSettleTxIdIsNotNull(
            BaseTradeRebateRecordStatus.SETTLED
        ).size
    }

    fun getClaimRebateTotalCount(): Int {
        return baseTradeRebateRecordRepository.findAll().size
    }

    fun getFirstBaseTradesCount(
        bidAmount: BigDecimal
    ): Int {
        return optionOrderRepository.findByChainAndStatusInAndBidAssetAndBidAmountEqualsAndExpiryInHour(
            ChainType.BITLAYER,
            listOf(OptionStatus.SETTLED, OptionStatus.EXECUTED, OptionStatus.SETTLE_FAILED),
            Symbol.BTC,
            bidAmount,
            "2"
        ).size
    }

    fun getUnclaimedRebateRecord(
        user: User
    ): List<BaseTradeRebateRecord> {
        return baseTradeRebateRecordRepository.findByBuyerAddressIgnoreCaseAndStatus(
            user.address,
            BaseTradeRebateRecordStatus.CREATED
        )
    }
}