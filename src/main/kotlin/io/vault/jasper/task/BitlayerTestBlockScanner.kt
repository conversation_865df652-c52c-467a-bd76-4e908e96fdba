package io.vault.jasper.task

import io.vault.jasper.service.MonitorService
import io.vault.jasper.service.blockchain.BitlayerTestService
import org.slf4j.LoggerFactory
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.beans.factory.annotation.Value
import org.springframework.context.annotation.Profile
import org.springframework.scheduling.annotation.Scheduled
import org.springframework.stereotype.Service


@Service
@Profile("test")
class BitlayerTestBlockScanner @Autowired constructor(
    private val monitorService: MonitorService,
    private val bitlayerTestService: BitlayerTestService
) {

    private val logger = LoggerFactory.getLogger(this::class.java)

    @Value("\${schedule_task_on}")
    lateinit var scheduleOn: String

    //@Scheduled(fixedDelay = 3 * 1000, initialDelay = 5 * 1000)
    fun run() {

        if(scheduleOn == "false"){
            return
        }

        try {
            bitlayerTestService.scanBlocks()
        } catch (e: Exception) {
            logger.error(e.message, e)
        }
        monitorService.taskMonitor(this::class.simpleName)
    }
}