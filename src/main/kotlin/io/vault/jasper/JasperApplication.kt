package io.vault.jasper

import org.springframework.boot.autoconfigure.SpringBootApplication
import org.springframework.boot.runApplication
import org.springframework.retry.annotation.EnableRetry
import org.springframework.scheduling.annotation.EnableAsync
import org.springframework.scheduling.annotation.EnableScheduling
import org.springframework.web.bind.annotation.RestController

@RestController
@SpringBootApplication
@EnableScheduling
@EnableAsync
@EnableRetry
open class JasperApplication

fun main(args: Array<String>) {
    runApplication<JasperApplication>(*args)
}
