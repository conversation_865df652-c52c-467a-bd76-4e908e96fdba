package io.vault.jasper.repository.activity

import io.vault.jasper.model.activity.CampaignRewardRecord
import io.vault.jasper.model.activity.RewardRecordStatus
import io.vault.jasper.model.activity.RewardType
import org.springframework.data.mongodb.repository.MongoRepository
import org.springframework.stereotype.Repository
import java.time.LocalDateTime
import java.util.Optional

@Repository
interface CampaignRewardRecordRepository : MongoRepository<CampaignRewardRecord, String> {
    fun findByUserIdAndCampaignId(userId: String, campaignId: String): List<CampaignRewardRecord>
    
    fun findByUserIdAndCampaignIdAndOptionOrderId(
        userId: String,
        campaignId: String,
        optionOrderId: String
    ): Optional<CampaignRewardRecord>
    
    fun findByStatus(status: RewardRecordStatus): List<CampaignRewardRecord>
    
    fun findByStatusAndScheduledTimeBefore(
        status: RewardRecordStatus,
        time: LocalDateTime
    ): List<CampaignRewardRecord>
    
    fun findByCampaignIdAndRewardType(activityId: String, rewardType: RewardType): List<CampaignRewardRecord>

    fun countByCampaignIdAndStatus(
        campaignId: String,
        status: RewardRecordStatus
    ): Long

    fun countByCampaignId(
        campaignId: String
    ): Long
}
