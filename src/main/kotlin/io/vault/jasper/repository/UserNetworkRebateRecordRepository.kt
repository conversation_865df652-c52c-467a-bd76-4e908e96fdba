package io.vault.jasper.repository

import io.vault.jasper.model.UserNetworkRebateRecord
import io.vault.jasper.model.UserNetworkRebateRecordStatus
import org.springframework.data.domain.Page
import org.springframework.data.domain.Pageable
import org.springframework.data.mongodb.repository.MongoRepository
import org.springframework.stereotype.Repository
import java.time.LocalDateTime

@Repository
interface UserNetworkRebateRecordRepository : MongoRepository<UserNetworkRebateRecord, String> {

    fun findFirstByOptionOrderId(optionOrderId: String): UserNetworkRebateRecord?
    fun findByStatus(status: UserNetworkRebateRecordStatus): List<UserNetworkRebateRecord>
    fun findByOptionOrderIdAndUserNetworkId(
        optionOrderId: String,
        userNetworkId: String
    ): UserNetworkRebateRecord?
    fun findByGrade0AddressIsNull(): List<UserNetworkRebateRecord>
    fun findByOptionOrderIdAndUserAddress(
        optionOrderId: String,
        userAddress: String
    ): UserNetworkRebateRecord?

    fun findByOptionOrderId(
        optionOrderId: String
    ): List<UserNetworkRebateRecord>
}