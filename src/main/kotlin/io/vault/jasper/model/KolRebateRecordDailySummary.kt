package io.vault.jasper.model

import org.springframework.data.annotation.CreatedDate
import org.springframework.data.annotation.Id
import org.springframework.data.mongodb.core.index.CompoundIndex
import org.springframework.data.mongodb.core.index.CompoundIndexes
import org.springframework.data.mongodb.core.mapping.Document
import java.math.BigDecimal
import java.math.BigInteger
import java.time.LocalDate
import java.time.LocalDateTime

@Document("kol_rebate_record_daily_summary")
@CompoundIndexes(
    CompoundIndex(name = "kolId_recordDate", def = "{'kolId': 1, 'recordDate': 1}", unique = true)
)
data class KolRebateRecordDailySummary(
    @Id
    val id: String? = null,
    val recordDate: LocalDate,
    val kolId: String,
    var totalPremiums: BigDecimal,
    var totalRebate: BigDecimal,
    var activeUsers: Int,
    @CreatedDate
    val created: LocalDateTime = LocalDateTime.now()
) {}
