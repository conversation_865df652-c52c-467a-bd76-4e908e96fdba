package io.vault.jasper.response

import io.swagger.annotations.ApiModel
import io.swagger.annotations.ApiModelProperty
import io.vault.jasper.enums.ChainType
import io.vault.jasper.enums.OptionDirection
import io.vault.jasper.enums.Symbol
import io.vault.jasper.model.OrderStatus
import io.vault.jasper.model.OrderType
import io.vault.jasper.model.PremiumAsset
import io.vault.jasper.model.PremiumFeeInfo

@ApiModel
data class ReferralDirectoryResponse(
    @ApiModelProperty("Referee（用户地址）", required = true)
    val address: String,

    @ApiModelProperty("Date（注册时间）", required = true)
    val registerTime: Long,

    @ApiModelProperty("Total Premium（总共支付的权利金）", required = true)
    val totalPremium: String,
)
