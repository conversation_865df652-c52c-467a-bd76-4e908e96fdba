package io.vault.jasper.task

import io.vault.jasper.model.*
import io.vault.jasper.repository.*
import io.vault.jasper.service.*
import io.vault.jasper.service.blockchain.BlockchainServiceFactory
import io.vault.jasper.service.blockchain.EvmService
import org.slf4j.LoggerFactory
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.beans.factory.annotation.Value
import org.springframework.context.annotation.Profile
import org.springframework.scheduling.annotation.Scheduled
import org.springframework.stereotype.Service
import java.time.LocalDateTime
import java.util.*

@Service
@Profile("prod", "test")
class MiniBridgeOrderProcessor @Autowired constructor(
    private val miniBridgeSwapOrderRepository: MiniBridgeSwapOrderRepository,
    private val miniBridgeSwapOrderService: MiniBridgeSwapOrderService,
    private val blockchainServiceFactory: BlockchainServiceFactory,
    private val currencyService: CurrencyService,
) {

    private val logger = LoggerFactory.getLogger(this::class.java)

    @Value("\${schedule_task_on}")
    lateinit var scheduleOn: String

    // 每分钟执行一次
    @Scheduled(fixedDelay = 5 * 1000, initialDelay = 30 * 1000)
    fun run() {

        if(scheduleOn == "false"){
            return
        }

        val parameter = miniBridgeSwapOrderService.getParameter()
        if(parameter.enableSwap == false){
            logger.info("Mini Bridge Swap Order is disabled")
            return
        }

        try {
            checkDepositTimeout()
        } catch (e: Exception) {
            logger.error(e.message, e)
        }

        try {
            checkDeposit()
        } catch (e: Exception) {
            logger.error(e.message, e)
        }

        try{
            processWithdraw()
        } catch (e: Exception) {
            logger.error(e.message, e)
        }

        try{
            checkWithdraw()
        } catch (e: Exception){
            logger.error(e.message, e)
        }
    }

    private fun checkDepositTimeout() {

        val allPendingDepositOrders = miniBridgeSwapOrderRepository.findByStatusAndDepositTxHashIsNull(
            MiniBridgeSwapOrderStatus.PENDING_DEPOSIT
        )

        //logger.info("Mini Bridge Order Processor checkDepositTimeout start ${allPendingDepositOrders.size}")

        for(order in allPendingDepositOrders) {
            if(order.created.isBefore(LocalDateTime.now().minusMinutes(30))){
                order.status = MiniBridgeSwapOrderStatus.DEPOSIT_TIMEOUT
                miniBridgeSwapOrderRepository.save(order)
            }
        }

        //logger.info("Mini Bridge Order Processor checkDepositTimeout end")
    }

    private fun checkDeposit() {

        //logger.info("Mini Bridge Order Processor checkDeposit start")

        val allPendingDepositOrders = miniBridgeSwapOrderRepository.findByStatusAndDepositTxHashIsNotNull(
            MiniBridgeSwapOrderStatus.PENDING_DEPOSIT
        )

        for(order in allPendingDepositOrders) {
            try {
                if(miniBridgeSwapOrderService.checkDepositReceipt(order)){
                    order.status = MiniBridgeSwapOrderStatus.PENDING_WITHDRAW
                    miniBridgeSwapOrderRepository.save(order)
                }
            } catch (e: Exception) {
                logger.error("Mini Bridge Order Processor checkDeposit failed, order: ${order.id}", e)
                order.status = MiniBridgeSwapOrderStatus.DEPOSIT_FAILED
                order.errorMessage = e.message
                miniBridgeSwapOrderRepository.save(order)
            }
        }

        //logger.info("Mini Bridge Order Processor checkDeposit end")
    }

    private fun processWithdraw() {

        //logger.info("Mini Bridge Order Processor processWithdraw start")

        val allPendingWithdrawOrders = miniBridgeSwapOrderRepository.findByStatus(
            MiniBridgeSwapOrderStatus.PENDING_WITHDRAW
        )

        for(order in allPendingWithdrawOrders) {
            // Send withdraw tx
            try {

                val currencyAddress = currencyService.getCurrencyContract(
                    order.toChain,
                    order.toAsset,
                )
                val currencyDecimal = currencyService.getCurrencyDecimal(
                    order.toChain,
                    order.toAsset,
                )

                val requestId = miniBridgeSwapOrderService.sendFordefiRequest(
                    order.toChain,
                    currencyAddress,
                    currencyDecimal,
                    order.toAddress,
                    order.actualWithdrawAmount!!
                )
                if(requestId != null) {
                    order.withdrawFordefiId = requestId
                    order.status = MiniBridgeSwapOrderStatus.WITHDRAW_PROCESSING
                    miniBridgeSwapOrderRepository.save(order)
                } else {
                    order.errorMessage = "Fordefi withdraw request id is null"
                    order.status = MiniBridgeSwapOrderStatus.WITHDRAW_FAILED
                    miniBridgeSwapOrderRepository.save(order)
                }

            } catch (e: Exception) {
                logger.error(e.message, e)
                order.errorMessage = e.message
                order.status = MiniBridgeSwapOrderStatus.WITHDRAW_FAILED
                miniBridgeSwapOrderRepository.save(order)
            }
        }

        //logger.info("Mini Bridge Order Processor processWithdraw end")
    }

    private fun checkWithdraw() {
        //logger.info("Mini Bridge Order Processor checkWithdraw start")

        val allPendingWithdrawOrders = miniBridgeSwapOrderRepository.findByStatus(
            MiniBridgeSwapOrderStatus.WITHDRAW_PROCESSING
        )

        for(order in allPendingWithdrawOrders){

            try {
                if (order.withdrawTxHash == null) {
                    order.withdrawTxHash = miniBridgeSwapOrderService.getFordefiTransactionHashById(
                        order.withdrawFordefiId!!
                    )
                }

                //Check Withdraw transaction
                val blockchainService = blockchainServiceFactory.getBlockchainService(order.toChain) as EvmService
                val evmUtil = blockchainService.evmUtil
                val txReceipt = evmUtil.web3j.ethGetTransactionReceipt(order.withdrawTxHash)
                    .send().transactionReceipt.get()

                if(txReceipt.status == "0x1"){
                    order.withdrawSettled = true
                    order.status = MiniBridgeSwapOrderStatus.SUCCESS
                    miniBridgeSwapOrderRepository.save(order)
                } else {
                    order.status = MiniBridgeSwapOrderStatus.WITHDRAW_FAILED
                    order.errorMessage = "Withdraw transaction failed"
                    miniBridgeSwapOrderRepository.save(order)
                }

            } catch (e: Exception) {
                logger.error(e.message, e)
            }
        }

        //logger.info("Mini Bridge Order Processor checkWithdraw end")
    }
}