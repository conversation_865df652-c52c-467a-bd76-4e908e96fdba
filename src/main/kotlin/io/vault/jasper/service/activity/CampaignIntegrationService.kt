package io.vault.jasper.service.activity

import io.vault.jasper.model.OptionOrder
import io.vault.jasper.model.OptionStatus
import io.vault.jasper.service.UserService
import org.slf4j.LoggerFactory
import org.springframework.stereotype.Service

/**
 * Service to integrate the activity system with other parts of the application
 */
@Service
class CampaignIntegrationService(
    private val campaignService: CampaignService,
    private val campaignRewardService: CampaignRewardService,
    private val userService: UserService
) {
    private val logger = LoggerFactory.getLogger(this::class.java)
    
    /**
     * Process an option order for campaigns and rewards
     */
    fun processOptionOrder(optionOrder: OptionOrder) {
        // Only process orders that buyer profit is not null
        if (optionOrder.buyerProfit == null) {
            logger.info("CampaignIntegrationService Option order ${optionOrder.id} buyer profit is null, skipping")
            return
        }
        
        // Get the user
        val user = optionOrder.buyer?.let { userService.getByAddress(it) }
        
        if (user == null) {
            logger.warn("CampaignIntegrationService User not found for option order ${optionOrder.id}")
            return
        }
        
        try {
            // Process the order for campaign tasks
            campaignService.processOptionOrder(user, optionOrder)
            
            // Process the order for rewards
            campaignRewardService.processOptionOrderForReward(user, optionOrder)
        } catch (e: Exception) {
            logger.error("CampaignIntegrationService Error processing option order ${optionOrder.id} for campaigns", e)
        }
    }
}
