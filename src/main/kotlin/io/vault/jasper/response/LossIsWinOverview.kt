package io.vault.jasper.response

import io.swagger.annotations.ApiModel
import io.swagger.annotations.ApiModelProperty
import java.math.BigDecimal

@ApiModel
data class LossIsWinOverview(
    @ApiModelProperty("所有用户总交易次数")
    val totalTransactionCount: Int,

    @ApiModelProperty("总交易人数")
    val totalUserCount: Int,

    @ApiModelProperty("当前用户交易次数")
    val userTransactionCount: Int,

    @ApiModelProperty("交易奖励BTR数量")
    val userBtrReward: BigDecimal,

    @ApiModelProperty("所有用户总亏损数量（美元）")
    val totalLossAmount: BigDecimal,

    @ApiModelProperty("总亏损人数")
    val totalLossUserCount: Int,

    @ApiModelProperty("当前用户亏损奖励（估算，美元）")
    val userLossAmount: BigDecimal,

    @ApiModelProperty("每单奖励池总量")
    val totalBtrPerOrderPool: BigDecimal,

    @ApiModelProperty("亏损奖励池总量")
    val totalBtrLossPool: BigDecimal
)