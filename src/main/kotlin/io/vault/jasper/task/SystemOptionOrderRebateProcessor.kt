package io.vault.jasper.task

import io.vault.jasper.enums.ChainType
import io.vault.jasper.enums.Symbol
import io.vault.jasper.model.*
import io.vault.jasper.repository.*
import io.vault.jasper.service.*
import io.vault.jasper.service.blockchain.ArbitrumService
import io.vault.jasper.service.blockchain.BlockchainServiceFactory
import io.vault.jasper.service.blockchain.EvmService
import org.slf4j.LoggerFactory
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.beans.factory.annotation.Value
import org.springframework.context.annotation.Profile
import org.springframework.scheduling.annotation.Scheduled
import org.springframework.stereotype.Service
import java.math.BigInteger
import java.util.*

@Service
@Profile("prod")
class SystemOptionOrderRebateProcessor @Autowired constructor(
    private val blockchainServiceFactory: BlockchainServiceFactory,
    private val optionOrderService: OptionOrderService,
    private val currencyService: CurrencyService,
    private val systemOptionOrderRebateRecordRepository: SystemOptionOrderRebateRecordRepository,
) {

    private val logger = LoggerFactory.getLogger(this::class.java)

    @Value("\${schedule_task_on}")
    lateinit var scheduleOn: String

    private val reportWallet = "******************************************"

    // ******************************************
    private val reportWalletKey = "fe3b0cb08c52dde979f2263efad4d198dbf2a0c429b364e9cdd905b5ee1fa65a"

    // 每分钟执行一次
    //@Scheduled(fixedDelay = 60 * 1000, initialDelay = 30 * 1000)
    fun run() {

        if(scheduleOn == "false"){
            return
        }

        try {
            exec()
        } catch (e: Exception) {
            logger.error(e.message, e)
        }
    }

    private fun exec() {

        logger.info("System Option Order trade processor start")

        checkExecutedRecords()

        val allClaimedRecords = systemOptionOrderRebateRecordRepository.findByStatus(
            SystemOptionOrderRebateRecordStatus.CLAIMED
        )

        val chainType = ChainType.BITLAYER
        val evmService = blockchainServiceFactory.getBlockchainService(chainType) as EvmService
        val evmUtil = evmService.evmUtil

        for (record in allClaimedRecords) {

            try {

                var txHash: String? = null

                if(record.profitAsset == Symbol.BTC){

                    txHash = evmUtil.sendGas(
                        Pair(reportWallet, reportWalletKey),
                        record.buyerAddress,
                        record.rebateAmount.movePointLeft(18),
                    )
                } else {

                    val decimal = currencyService.getCurrencyDecimal(
                        chainType,
                        record.profitAsset,
                    )
                    val contractAddress = currencyService.getCurrencyContract(
                        chainType,
                        record.profitAsset,
                    )

                    txHash = evmService.transferERC20Token(
                        record.buyerAddress,
                        record.rebateAmount.movePointLeft(decimal),
                        contractAddress,
                        reportWallet,
                        reportWalletKey,
                        200000
                    )
                }

                record.status = SystemOptionOrderRebateRecordStatus.SETTLED
                record.settleTxId = txHash
                systemOptionOrderRebateRecordRepository.save(record)

            } catch (e: Exception) {

                record.status = SystemOptionOrderRebateRecordStatus.SETTLE_FAILED
                record.errorMsg = e.message
                systemOptionOrderRebateRecordRepository.save(record)
                logger.error("System Option Order first trade discord level failed, optionOrderId: ${record.optionOrderId}", e)
            }

            Thread.sleep(15 * 1000)
        }

        checkSettleSuccess()

        logger.info("System Option Order first trade processor end")
    }

    private fun checkExecutedRecords(){

        logger.info("System Option Order first trade handlePendingRecords start")

        val allExecutedRecords = systemOptionOrderRebateRecordRepository.findByStatus(
            SystemOptionOrderRebateRecordStatus.EXECUTED
        )

        for (record in allExecutedRecords) {

            try {
                optionOrderService.checkSystemOptionRebate(record)
            } catch (e: Exception) {
                logger.error("System Option Order first trade handlePendingRecords failed, optionOrderId: ${record.optionOrderId}", e)
            }
        }
    }

    private fun checkSettleSuccess() {
        val allRecords = systemOptionOrderRebateRecordRepository.findByStatusAndSettleTxIdIsNotNullAndSettled(
            SystemOptionOrderRebateRecordStatus.SETTLED,
            false
        )

        val chainType = ChainType.BITLAYER
        val evmService = blockchainServiceFactory.getBlockchainService(chainType) as EvmService
        val evmUtil = evmService.evmUtil

        for (record in allRecords) {
            try {
                val txReceipt = evmUtil.web3j.ethGetTransactionReceipt(record.settleTxId)
                    .send().transactionReceipt.get()
                if (txReceipt.status == "0x1") {
                    record.settled = true
                    systemOptionOrderRebateRecordRepository.save(record)
                } else {
                    logger.info("System Option Order first trade checkSettleSuccess failed, txReceipt: ${record.settleTxId}")

                    record.settled = false
                    record.status = SystemOptionOrderRebateRecordStatus.CLAIMED
                    systemOptionOrderRebateRecordRepository.save(record)
                }
            } catch (e: Exception) {
                logger.error("System Option Order first trade checkSettleSuccess failed, optionOrderId: ${record.optionOrderId}", e)
            }
        }
    }
}