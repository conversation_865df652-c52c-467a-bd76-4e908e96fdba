package io.vault.jasper.task

import io.vault.jasper.enums.ChainType
import io.vault.jasper.enums.Symbol
import io.vault.jasper.model.*
import io.vault.jasper.repository.*
import io.vault.jasper.service.*
import io.vault.jasper.service.blockchain.ArbitrumService
import org.slf4j.LoggerFactory
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.beans.factory.annotation.Value
import org.springframework.context.annotation.Profile
import org.springframework.data.repository.findByIdOrNull
import org.springframework.scheduling.annotation.Scheduled
import org.springframework.stereotype.Service
import java.math.BigDecimal
import java.math.BigInteger

@Service
@Profile("prod")
class KolSettleRebateProcessor @Autowired constructor(
    private val kolRepository: KolRepository,
    private val userRepository: UserRepository,
    private val kolRebateRecordRepository: KolRebateRecordRepository,
    private val arbitrumService: ArbitrumService,
    private val reportPremiumParameterRepository: ReportPremiumParameterRepository,
    private val currencyService: CurrencyService
) {

    private val logger = LoggerFactory.getLogger(this::class.java)

    @Value("\${kol.report_contract}")
    private lateinit var reportContract: String

    @Value("\${kol.report_wallet_primary_key}")
    private lateinit var reportWalletKey: String

    @Value("\${schedule_task_on}")
    lateinit var scheduleOn: String

    // 每15分钟执行一次
    //@Scheduled(fixedDelay = 15 * 60 * 1000, initialDelay = 120 * 1000)
    //@Scheduled(cron = "0 0 * * * ?") // 每小时整点执行一次
    fun run() {
        if(scheduleOn == "false"){
            return
        }
        try {
            exec()
        } catch (e: Exception) {
            logger.error(e.message, e)
        }
    }

    private fun exec() {
        val parameter = getParameter(ChainType.ARBITRUM)
        val usdtDecimals = currencyService.getCurrencyDecimal(ChainType.ARBITRUM, Symbol.USDT)

        while (true) {
            val allRecords = kolRebateRecordRepository.findByStatus(KolRebateRecordStatus.PENDING)
            if (allRecords.isEmpty()) break

            val records = allRecords.take(parameter.countPerTransaction)
            logger.info("Pending rebate records found: ${records.size}")
            val kolRebateMap = mutableMapOf<String, BigInteger>()
            records.forEach {
                var amount = it.incentiveAmount.movePointRight(usdtDecimals).toBigInteger()
                if (it.kolId in kolRebateMap) {
                    amount += kolRebateMap[it.kolId]!!
                }
                kolRebateMap[it.kolId] = amount
            }
            val inputData = kolRebateMap.mapNotNull { (kolId, amount) ->
                val kol = kolRepository.findByIdOrNull(kolId) ?: return@mapNotNull null
                val kolUser = userRepository.findByAddressIgnoreCase(kol.wallet) ?: return@mapNotNull null
                Pair(kolUser.address, amount)
            }
            // 将inputData分成两个数组
            val addressList = inputData.map { it.first }
            val amountList = inputData.map { it.second }

            // 发起链上交易
            val settleTxHash = arbitrumService.settleKolRebate(
                contractAddress = reportContract,
                privateKey = reportWalletKey,
                addressList = addressList,
                amountList = amountList,
                gasLimit = parameter.gasLimit
            )
            logger.info("KOL rebate Settle transaction hash: $settleTxHash")
            records.forEach {
                it.status = KolRebateRecordStatus.SETTLED
                it.settleTxId = settleTxHash
            }
            kolRebateRecordRepository.saveAll(records)
            Thread.sleep(5000)
        }

    }

    /**
     * 获取系统参数对象
     */
    private fun getParameter(chain: ChainType): ReportPremiumParameter {
        val params = reportPremiumParameterRepository.findByChain(chain)

        return if (params.isEmpty()) {
            val p = ReportPremiumParameter(null)
            p.chain = chain
            reportPremiumParameterRepository.save(p)
        } else {
            params.first()
        }
    }
}