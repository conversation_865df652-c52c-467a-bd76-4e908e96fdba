package io.vault.jasper.controller.pub

import EVMAddress
import io.swagger.annotations.ApiOperation
import io.vault.jasper.ApiResponse
import io.vault.jasper.annotation.ApiCache
import io.vault.jasper.dto.AuthDTO
import io.vault.jasper.enums.ChainType
import io.vault.jasper.enums.Symbol
import io.vault.jasper.model.*
import io.vault.jasper.repository.*
import io.vault.jasper.response.*
import io.vault.jasper.service.*
import io.vault.jasper.utils.AuthUtil
import io.vault.jasper.utils.DateTimeUtil
import org.slf4j.LoggerFactory
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.data.domain.PageImpl
import org.springframework.data.domain.PageRequest
import org.springframework.data.domain.Sort
import org.springframework.data.mongodb.core.MongoTemplate
import org.springframework.data.mongodb.core.query.Criteria
import org.springframework.data.mongodb.core.query.Query
import org.springframework.data.repository.findByIdOrNull
import org.springframework.web.bind.annotation.*
import org.web3j.crypto.Keys
import java.math.BigDecimal
import java.util.*
import javax.servlet.http.HttpServletRequest

@RestController
@RequestMapping("/echooo_campaign")
class EchoooCampaignController @Autowired constructor(
    private val authUtil: AuthUtil,
    private val optionOrderRepository: OptionOrderRepository,
    private val userCampaignInfoRepository: UserCampaignInfoRepository,
    private val echoooCampaignService: EchoooCampaignService,
    private val echoooTradeRebateRecordRepository: EchoooTradeRebateRecordRepository,
    private val orderRepository: OrderRepository,
    private val airdropService: AirDropService,
    private val echoooRetweetUserIdRepository: EchoooRetweetUserIdRepository,
) {

    private var logger = LoggerFactory.getLogger(this::class.java)

    @ApiOperation("Request Echooo Campaign Info of User")
    @CrossOrigin
    @ApiCache(key = "echooo:info", dynamicKey = ["address"], expire = 60)
    @GetMapping("/info")
    fun info(
        request: HttpServletRequest,
        @RequestParam("address") @EVMAddress address: String,
    ): ApiResponse<EchoooCampaignInfoResponse> {

        authUtil.filterIPBlacklist(request)
        val user = authUtil.auth(request, address)

        val echoooCampaignParameter = echoooCampaignService.getCampaignParameter()
        var userCampaignInfo = airdropService.getUserCampaignInfo(user)

        var canTrade = user.created.isAfter(echoooCampaignParameter.startDate)

        var rebateCount = echoooCampaignService.getFirstTradeRebateCount()
        if(rebateCount >= 10000){
            rebateCount = 10000
        }

        val firstTradeRebateRecords = echoooTradeRebateRecordRepository.findByBuyerAddressIgnoreCase(
            user.address
        )

        var firstTradePNL = "0"
        var firstTradeIs2H = false
        var firstTradeProfitRate = "0"
        var firstTradePremium = "0"
        var firstTradePremiumAsset = Symbol.USDT
        var firstTradeAmount = "0"
        var tradeInfo: ExecutedOrderResponse? = null

        if(firstTradeRebateRecords.isNotEmpty()) {
            canTrade = false
            val rebateRecord = firstTradeRebateRecords[0]
            firstTradePNL = rebateRecord.netProfit.movePointLeft(6).stripTrailingZeros().toPlainString()
            firstTradeIs2H = true

            var roi = BigDecimal.ZERO
            if(rebateRecord.premiumFee.compareTo(BigDecimal.ZERO) != 0){
                roi = rebateRecord.profit.divide(rebateRecord.premiumFee, 8, BigDecimal.ROUND_HALF_UP)
            }

            firstTradeProfitRate = roi.stripTrailingZeros().toPlainString()
            firstTradeAmount = rebateRecord.rebateAmount.stripTrailingZeros().toPlainString()
        }

        val hasJoinDiscord = user.discordInfo?.inGuild ?: false
        if(userCampaignInfo.joinDiscordTime == 0L && hasJoinDiscord) {
            userCampaignInfo.joinDiscordTime = Date().time
        }

        if(userCampaignInfo.followEchoooTime == 0L && user.twitterAccountId != null) {
            userCampaignInfo.twitterAccountId = user.twitterAccountId
            userCampaignInfo.followEchoooTime = Date().time
        }

        if(userCampaignInfo.followEchoooJasperVaultTime == 0L && user.twitterAccountId != null) {
            userCampaignInfo.twitterAccountId = user.twitterAccountId
            userCampaignInfo.followEchoooJasperVaultTime = Date().time
        }

        if(userCampaignInfo.retweetEchoooTime == 0L && user.twitterAccountId != null) {
            val retweetUserId = echoooRetweetUserIdRepository.findByRetweetUserId(user.twitterAccountId!!)
            if(retweetUserId != null){
                userCampaignInfo.retweetEchoooTime = Date().time
            }
        }

        userCampaignInfo = userCampaignInfoRepository.save(userCampaignInfo)

        val discordLevel = echoooCampaignService.getDiscordLevel(user)

        val isEchoooTwitterFollowed = (user.twitterAccountId != null && userCampaignInfo.followEchoooTime > 0)
        val isJasperTwitterFollowed = (user.twitterAccountId != null && userCampaignInfo.followEchoooJasperVaultTime > 0)
        val isTwitterRetweet = userCampaignInfo.retweetEchoooTime > 0

        return ApiResponse.success(
            EchoooCampaignInfoResponse(
                address = user.address,
                isEchoooTwitterFollowed = isEchoooTwitterFollowed,
                isJasperTwitterFollowed = isJasperTwitterFollowed,
                isJasperTwitterRetweet = isTwitterRetweet,
                isJoinDiscord = hasJoinDiscord,
                firstTradePNL = firstTradePNL,
                firstTradeIs2HETH = firstTradeIs2H,
                retweetLink = echoooCampaignParameter.retweetLink,
                canTrade = canTrade,
                firstTradeRebateCount = rebateCount,
                tradeInfo = tradeInfo,
                firstTradeAmount = firstTradeAmount,
                firstTradeGrossProfitRate = firstTradeProfitRate,
                firstTradePremium = firstTradePremium,
                firstTradePremiumAsset = firstTradePremiumAsset,
                discordLevel = discordLevel
            )
        )
    }

    @ApiOperation("返回推特帖子链接")
    @CrossOrigin
    @ApiCache(key = "echooo:retweet_link", dynamicKey = ["address"], expire = 60)
    @GetMapping("/retweet_link")
    fun retweetEchooo(
        request: HttpServletRequest,
        @RequestParam("address") @EVMAddress address: String,
    ): ApiResponse<String> {

        authUtil.filterIPBlacklist(request)
        val parameter = echoooCampaignService.getCampaignParameter()
        return ApiResponse.success(parameter.retweetLink)
    }

    @ApiOperation("返回echooo 主页链接")
    @CrossOrigin
    @ApiCache(key = "echooo:echooo_link", dynamicKey = ["address"], expire = 60)
    @GetMapping("/echooo_link")
    fun echoooLink(
        request: HttpServletRequest,
        @RequestParam("address") @EVMAddress address: String,
    ): ApiResponse<String> {

        authUtil.filterIPBlacklist(request)
        val user = authUtil.auth(request, address)

        val userCampaignInfo = airdropService.getUserCampaignInfo(user)
        if(userCampaignInfo.followEchoooTime == 0L) {
            userCampaignInfo.followEchoooTime = Date().time
            userCampaignInfoRepository.save(userCampaignInfo)
        }

        val parameter = echoooCampaignService.getCampaignParameter()
        return ApiResponse.success(parameter.twitterEchoooLink)
    }

    @ApiOperation("返回 jasper 主页链接")
    @CrossOrigin
    @ApiCache(key = "echooo:jasper_link", dynamicKey = ["address"], expire = 60)
    @GetMapping("/jasper_link")
    fun jasperVaultLink(
        request: HttpServletRequest,
        @RequestParam("address") @EVMAddress address: String,
    ): ApiResponse<String> {

        authUtil.filterIPBlacklist(request)
        val user = authUtil.auth(request, address)

        val userCampaignInfo = airdropService.getUserCampaignInfo(user)
        if(userCampaignInfo.followEchoooJasperVaultTime == 0L) {
            userCampaignInfo.followEchoooJasperVaultTime = Date().time
            userCampaignInfoRepository.save(userCampaignInfo)
        }

        val parameter = echoooCampaignService.getCampaignParameter()
        return ApiResponse.success(parameter.twitterJasperLink)
    }
}
