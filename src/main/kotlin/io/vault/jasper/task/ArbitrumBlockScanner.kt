package io.vault.jasper.task

import io.vault.jasper.service.blockchain.ArbitrumService
import io.vault.jasper.service.MonitorService
import org.slf4j.LoggerFactory
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.beans.factory.annotation.Value
import org.springframework.context.annotation.Profile
import org.springframework.scheduling.annotation.Scheduled
import org.springframework.stereotype.Service
import javax.annotation.PostConstruct


@Service
@Profile("prod", "test", "dev")
class ArbitrumBlockScanner @Autowired constructor(
    private val monitorService: MonitorService,
    private val arbitrumService: ArbitrumService
) {

    private val logger = LoggerFactory.getLogger(this::class.java)

    @Value("\${schedule_task_on}")
    lateinit var scheduleOn: String

    @PostConstruct
    fun init() {
        // arbitrumService.resetLastScanBlockHeight()
    }

    @Scheduled(fixedDelay = 1 * 1000, initialDelay = 5 * 1000)
    fun run() {

        if(scheduleOn == "false"){
            //logger.info("Schedule on switch $scheduleOn")
            return
        }
        try {
            arbitrumService.scanBlocks()
        } catch (e: Exception) {
            logger.error(e.message, e)
        }
        monitorService.taskMonitor(this::class.simpleName)
    }
}