package io.vault.jasper.dto

import EVMAddress
import io.swagger.annotations.ApiModel
import io.swagger.annotations.ApiModelProperty
import io.vault.jasper.enums.ChainType

@ApiModel("Referral 基本信息请求")
data class ReferralInfoDTO(
    @ApiModelProperty("钱包地址", required = false)
    @field:EVMAddress
    val address: String?,
    @ApiModelProperty("链", required = true)
    val chain: ChainType,
    @ApiModelProperty("邀请码", required = false)
    val inviteCode: String?,
    val timestamp: Long = 0
)
