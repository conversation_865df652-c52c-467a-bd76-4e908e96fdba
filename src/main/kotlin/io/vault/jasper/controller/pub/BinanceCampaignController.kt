package io.vault.jasper.controller.pub

import EVMAddress
import io.swagger.annotations.ApiOperation
import io.vault.jasper.ApiResponse
import io.vault.jasper.annotation.ApiCache
import io.vault.jasper.dto.AuthDTO
import io.vault.jasper.enums.ChainType
import io.vault.jasper.enums.Symbol
import io.vault.jasper.exception.BusinessException
import io.vault.jasper.exception.ResultEnum
import io.vault.jasper.model.*
import io.vault.jasper.repository.*
import io.vault.jasper.response.*
import io.vault.jasper.service.*
import io.vault.jasper.utils.AuthUtil
import io.vault.jasper.utils.DateTimeUtil
import io.vault.jasper.utils.ProfileUtil
import org.slf4j.LoggerFactory
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.data.domain.PageImpl
import org.springframework.data.domain.PageRequest
import org.springframework.data.domain.Sort
import org.springframework.data.mongodb.core.MongoTemplate
import org.springframework.data.mongodb.core.query.Criteria
import org.springframework.data.mongodb.core.query.Query
import org.springframework.data.repository.findByIdOrNull
import org.springframework.web.bind.annotation.*
import org.web3j.crypto.Keys
import java.math.BigDecimal
import java.time.LocalDateTime
import java.util.*
import javax.servlet.http.HttpServletRequest
import javax.validation.Valid

@RestController
@RequestMapping("/binance_campaign")
class BinanceCampaignController @Autowired constructor(
    private val authUtil: AuthUtil,
    private val binanceCampaignService: BinanceCampaignService,
    private val binanceTradeRebateRecordRepository: BinanceTradeRebateRecordRepository,
    private val airdropService: AirDropService,
    private val userCampaignInfoRepository: UserCampaignInfoRepository,
) {

    private var logger = LoggerFactory.getLogger(this::class.java)

    @ApiOperation("Request Binance Campaign Info of User")
    @CrossOrigin
    @ApiCache(key = "binance:info", dynamicKey = ["address"], expire = 5)
    @GetMapping("/info")
    fun info(
        request: HttpServletRequest,
        @RequestParam("address") @EVMAddress address: String,
    ): ApiResponse<BinanceCampaignInfoResponse> {

        authUtil.filterIPBlacklist(request)
        val user = authUtil.auth(request, address)
        
        val userCampaignInfo = airdropService.getUserCampaignInfo(user)

        //val rebateCount = binanceCampaignService.getFirstTradeRebateCount()
        val firstTradeRebateRecords = binanceTradeRebateRecordRepository.findByBuyerAddressIgnoreCase(
            user.address
        )

        var canTrade = true
        var firstTradePNL = "0"
        var firstTradeIs2H = false
        var firstTradeProfitRate = "0"
        val firstTradePremium = "0"
        val firstTradePremiumAsset = Symbol.USDT
        val tradeInfo: ExecutedOrderResponse? = null
        var canClaimMoonlightBox = false
        var canClaimBitlayerPoint = false
        val hasClaimedBitlayerPoint = userCampaignInfo.claimBitlayerBinancePointTime > 0

        val hasBindDiscord = user.discordInfo != null
        val hasJoinDiscord = user.discordInfo?.inGuild ?: false
        if (userCampaignInfo.joinDiscordTime == 0L && hasJoinDiscord) {
            userCampaignInfo.joinDiscordTime = Date().time
        }

        val discordLevel = binanceCampaignService.getDiscordLevel(user)

        val isJasperTwitterRetweet = (userCampaignInfo.retweetBinanceTime > 0)

        if (firstTradeRebateRecords.isNotEmpty()) {
            canTrade = false
            val rebateRecord = firstTradeRebateRecords[0]
            firstTradePNL = rebateRecord.netProfit.movePointLeft(6).stripTrailingZeros().toPlainString()
            firstTradeIs2H = true

            var roi = BigDecimal.ZERO
            if (rebateRecord.premiumFee.compareTo(BigDecimal.ZERO) != 0) {
                roi = rebateRecord.profit.divide(rebateRecord.premiumFee, 8, BigDecimal.ROUND_HALF_UP)
            }

            firstTradeProfitRate = roi.stripTrailingZeros().toPlainString()

            if(rebateRecord.status == BinanceTradeRebateRecordStatus.CREATED) {
                canClaimMoonlightBox = true
            }

            if(rebateRecord.settleNftTxId != null) {
                canClaimBitlayerPoint = true
            }
        }

        return ApiResponse.success(
            BinanceCampaignInfoResponse(
                address = user.address,
                hasBindDiscord = hasBindDiscord,
                isJoinDiscord = hasJoinDiscord,
                firstTradePNL = firstTradePNL,
                firstTradeIs2HETH = firstTradeIs2H,
                canTrade = canTrade,
                firstTradeRebateCount = 0,
                tradeInfo = tradeInfo,
                firstTradeGrossProfitRate = firstTradeProfitRate,
                firstTradePremium = firstTradePremium,
                firstTradePremiumAsset = firstTradePremiumAsset,
                discordLevel = discordLevel,
                isJasperTwitterRetweet = isJasperTwitterRetweet,
                canClaimMoonlightBox = canClaimMoonlightBox,
                canClaimBitlayerPoint = canClaimBitlayerPoint,
                hasClaimBitlayerPoint = hasClaimedBitlayerPoint
            )
        )
    }

    @ApiOperation("返回推特帖子链接")
    @CrossOrigin
    @ApiCache(key = "binance:retweet_link", dynamicKey = ["address"], expire = 60)
    @GetMapping("/retweet_link")
    fun retweetBinance(
        request: HttpServletRequest,
        @RequestParam("address") @EVMAddress address: String,
    ): ApiResponse<String> {

        authUtil.filterIPBlacklist(request)
        val user = authUtil.auth(request, address)

        val userCampaignInfo = airdropService.getUserCampaignInfo(user)
        if(userCampaignInfo.retweetBinanceTime == 0L) {
            userCampaignInfo.retweetBinanceTime = Date().time
            userCampaignInfoRepository.save(userCampaignInfo)
        }

        val parameter = binanceCampaignService.getCampaignParameter()
        return ApiResponse.success(parameter.retweetLink)
    }

    @PostMapping("/claim_moonlight_box")
    @ApiOperation("Claim First Trade Rebate")
    @CrossOrigin
    fun claimMoonlightRebate(
        request: HttpServletRequest,
        @Valid @RequestBody dto: AuthDTO
    ): ApiResponse<Boolean> {

        authUtil.filterIPBlacklist(request)
        val user = authUtil.auth(request, dto.address)

        val unclaimedRecords = binanceCampaignService.getUnclaimedRecords(user)
        for(record in unclaimedRecords) {

            record.status = BinanceTradeRebateRecordStatus.CLAIMED
            binanceTradeRebateRecordRepository.save(record)
        }

        return ApiResponse.success(true)
    }

    @PostMapping("/claim_bitlayer_point")
    @ApiOperation("Claim Bitlayer Point")
    @CrossOrigin
    fun claimBitlayerPoint(
        request: HttpServletRequest,
        @Valid @RequestBody dto: AuthDTO
    ): ApiResponse<Boolean> {

        authUtil.filterIPBlacklist(request)
        val user = authUtil.auth(request, dto.address)

        val userCampaignInfo = airdropService.getUserCampaignInfo(user)
        if(userCampaignInfo.claimBitlayerBinancePointTime == 0L) {
            userCampaignInfo.claimBitlayerBinancePointTime = Date().time
            userCampaignInfoRepository.save(userCampaignInfo)
        }

        return ApiResponse.success(true)
    }

    @ApiOperation("返回地址是否已经提交了活动交易")
    @CrossOrigin
    @ApiCache(key = "binance:has_trade", dynamicKey = ["address"], expire = 10)
    @GetMapping("/has_trade")
    fun hasCreateTrade(
        request: HttpServletRequest,
        @RequestParam("address") @EVMAddress address: String,
    ): ApiResponse<Boolean> {

        authUtil.filterIPBlacklist(request)

        val now = LocalDateTime.now()
        val binanceCampaignParameter = binanceCampaignService.getCampaignParameter()
        if(now.isBefore(binanceCampaignParameter.startDate)) {
            return ApiResponse.success(true)
        }

        val records = binanceTradeRebateRecordRepository.findByBuyerAddressIgnoreCase(
            address
        )
        return ApiResponse.success(records.isNotEmpty())
    }

    @ApiOperation("返回活动参与人数")
    @CrossOrigin
    @ApiCache(key = "binance:total_count", expire = 60)
    @GetMapping("/total_count")
    fun totalCount(
        request: HttpServletRequest
    ): ApiResponse<Int> {

        authUtil.filterIPBlacklist(request)

        val rebateCount = binanceCampaignService.getFirstTradeRebateCount()
        return ApiResponse.success(rebateCount)
    }
}
