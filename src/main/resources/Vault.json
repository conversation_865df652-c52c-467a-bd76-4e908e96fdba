[{"inputs": [{"internalType": "address", "name": "_vault", "type": "address"}], "name": "<PERSON><PERSON><PERSON>", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "_module", "type": "address"}, {"internalType": "uint256", "name": "_value", "type": "uint256"}, {"internalType": "bytes", "name": "func", "type": "bytes"}], "name": "validVaultModuleV2", "outputs": [], "stateMutability": "nonpayable", "type": "function"}]