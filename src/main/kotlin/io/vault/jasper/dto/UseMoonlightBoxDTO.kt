package io.vault.jasper.dto

import EVMAddress
import io.swagger.annotations.ApiModel
import io.swagger.annotations.ApiModelProperty
import io.vault.jasper.enums.ChainType
import javax.validation.constraints.NotNull

@ApiModel("使用月光宝盒")
data class UseMoonlightBoxDTO(

    @ApiModelProperty("用户地址", required = true)
    @field:NotNull
    @field:EVMAddress
    val address: String,

    @ApiModelProperty("链", required = true)
    @field:NotNull
    val chain: ChainType,

    @ApiModelProperty("链上订单ID", required = true)
    @field:NotNull
    val onChainOrderId: String

)