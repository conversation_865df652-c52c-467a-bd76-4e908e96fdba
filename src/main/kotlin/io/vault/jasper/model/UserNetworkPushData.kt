package io.vault.jasper.model

import org.springframework.data.annotation.CreatedDate
import org.springframework.data.annotation.Id
import org.springframework.data.annotation.LastModifiedDate
import org.springframework.data.mongodb.core.index.Indexed
import org.springframework.data.mongodb.core.mapping.Document
import org.springframework.data.mongodb.core.mapping.Field
import org.springframework.data.mongodb.core.mapping.FieldType
import java.math.BigDecimal
import java.time.LocalDateTime

@Document("user_network_push_data")
data class UserNetworkPushData(
    @Id
    val id: String? = null,
    @Indexed(background = true)
    val path: UserNetworkPushDataPath,
    val param: Map<String, Any?>,
    @CreatedDate
    val created: LocalDateTime = LocalDateTime.now(),
    @LastModifiedDate
    val updated: LocalDateTime = LocalDateTime.now(),
    var requestTime: LocalDateTime? = null,
    var responseTime: LocalDateTime? = null,
    var response: String? = null,
    var pushed: Boolean = false,
)

enum class UserNetworkPushDataPath(val path: String) {
    USER_INFO("/jq_data/user_info"),
    USER_RATE_CHANGE_LOG("/jq_data/commission_ratio"),
    USER_NETWORK_TRANSACTION("/jq_data/transaction_record"),
    USER_NETWORK_REBATE_RECORD("/jq_data/commission_record"),
    USER_NETWORK_POINT_RECORD("/jq_data/point_record"),
}