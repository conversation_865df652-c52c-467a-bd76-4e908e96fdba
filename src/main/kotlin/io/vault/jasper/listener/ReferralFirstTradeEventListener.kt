package io.vault.jasper.listener

import io.vault.jasper.event.OptionExecutedEvent
import io.vault.jasper.repository.OptionOrderRepository
import io.vault.jasper.repository.UserRepository
import io.vault.jasper.service.ReferralInfoService
import org.slf4j.LoggerFactory
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.context.event.EventListener
import org.springframework.data.repository.findByIdOrNull
import org.springframework.stereotype.Component

@Component
class ReferralFirstTradeEventListener @Autowired constructor(
    private val optionOrderRepository: OptionOrderRepository,
    private val userRepository: UserRepository,
    private val referralInfoService: ReferralInfoService
) {

    private val logger = LoggerFactory.getLogger(this::class.java)

    @EventListener
    fun handleOptionExecutedEvent(event: OptionExecutedEvent) {
        logger.debug("ReferralFirstTradeEventListener: Option execute completed for order: ${event.orderId}")
        
        val optionOrder = optionOrderRepository.findByIdOrNull(event.orderId) ?: run {
            logger.debug("Option order not found: ${event.orderId}")
            return
        }
        
        val buyerAddress = optionOrder.buyer ?: run {
            logger.debug("Buyer address is null for order: ${event.orderId}")
            return
        }
        
        val user = userRepository.findByAddressIgnoreCase(buyerAddress) ?: run {
            logger.debug("User not found for address: $buyerAddress")
            return
        }
        
        // 检查是否是首次交易，如果是，给邀请人发送奖励
//        try {
//            referralInfoService.checkFirstTradeReward(user, optionOrder)
//        } catch (e: Exception) {
//            logger.error("Error checking first trade reward for user ${user.address}: ${e.message}", e)
//        }
    }
}
