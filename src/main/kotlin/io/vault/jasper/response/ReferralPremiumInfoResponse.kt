package io.vault.jasper.response

import io.swagger.annotations.ApiModel
import io.swagger.annotations.ApiModelProperty
import java.math.BigDecimal

@ApiModel("邀请人支付权利金信息")
data class ReferralPremiumInfoResponse(
    @ApiModelProperty("被邀请人地址", required = true)
    val address: String,
    @ApiModelProperty("支付权利金时间", required = true)
    val createDate: Long,
    @ApiModelProperty("权利金金额", required = true)
    val premiumAmount: String,
    @ApiModelProperty("权利金金额", required = true)
    val protocolFee: String,
    @ApiModelProperty("期权交易额", required = true)
    val optionVolume: String,
    @ApiModelProperty("期权交易订单数", required = false)
    val optionOrderCount: String? = null
)