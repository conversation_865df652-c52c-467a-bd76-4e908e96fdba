package io.vault.jasper.controller.pub

import EVMAddress
import io.swagger.annotations.ApiOperation
import io.vault.jasper.ApiResponse
import io.vault.jasper.dto.AuthDTO
import io.vault.jasper.dto.KytDTO
import io.vault.jasper.dto.LoginDTO
import io.vault.jasper.exception.BusinessException
import io.vault.jasper.exception.ResultEnum
import io.vault.jasper.model.DiscordInfo
import io.vault.jasper.model.User
import io.vault.jasper.model.UserNetworkGrade
import io.vault.jasper.repository.*
import io.vault.jasper.response.UserInfoResponse
import io.vault.jasper.response.UserPointsResponse
import io.vault.jasper.service.DiscordService
import io.vault.jasper.service.MistTrackService
import io.vault.jasper.service.UserNetworkService
import io.vault.jasper.service.UserService
import io.vault.jasper.utils.AuthUtil
import io.vault.jasper.utils.ProfileUtil
import org.slf4j.LoggerFactory
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.data.mongodb.core.MongoTemplate
import org.springframework.data.mongodb.core.query.Criteria
import org.springframework.data.mongodb.core.query.Query
import org.springframework.data.redis.core.RedisTemplate
import org.springframework.web.bind.annotation.*
import org.springframework.web.servlet.view.RedirectView
import org.web3j.crypto.Hash
import org.web3j.crypto.Keys
import org.web3j.crypto.Sign
import org.web3j.utils.Numeric
import java.math.BigDecimal
import java.time.LocalDateTime
import java.util.*
import java.util.concurrent.TimeUnit
import javax.servlet.http.HttpServletRequest
import javax.validation.Valid

@RestController
@RequestMapping("/auth")
class AuthController @Autowired constructor(
    private val redisTemplate: RedisTemplate<String, String>,
    private val authUtil: AuthUtil,
    private val mistTrackService: MistTrackService,
    private val userService: UserService,
    private val userNetworkService: UserNetworkService,
    private val discordService: DiscordService,
    private val userRepository: UserRepository,
    private val userNetworkRepository: UserNetworkRepository,
    private val mongoTemplate: MongoTemplate
) {

    private var logger = LoggerFactory.getLogger(this::class.java)

    @ApiOperation("Web3Login: Get authorization code")
    @CrossOrigin
    @GetMapping("/login/authorizationCode")
    fun getAuthorizationCode(
        @RequestParam @EVMAddress address: String,
        request: HttpServletRequest
    ): ApiResponse<String> {
        authUtil.filterIPBlacklist(request)
        return ApiResponse.success(generateSignMessage(address))
    }

    private fun generateSignMessage(address: String): String{
        val uuid = java.util.UUID.randomUUID().toString()
        val code = "Welcome to Jasper Vault! Please click \"Sign\" to sign in. This request will not trigger a blockchain transaction or cost any gas fees. Wallet address: $address Nonce: $uuid"
        val ops = redisTemplate.opsForValue()
        val key = "auth:code:$address"
        ops.set(key, code, 2, TimeUnit.MINUTES)
        return code
    }

    private fun genRandomString(): String {
        val length = 32
        val charset = ('a'..'z') + ('A'..'Z') + ('0'..'9')
        return (1..length)
            .map { charset.random() }
            .joinToString("")
    }

    @ApiOperation("User Info")
    @CrossOrigin
    @PostMapping("/info")
    fun info(
        request: HttpServletRequest,
        @Valid @RequestBody dto: AuthDTO
    ): ApiResponse<UserInfoResponse> {
        authUtil.filterIPBlacklist(request)
        val user = authUtil.auth(request, dto.address)

        val parentUser = userService.findUserParent(user)
        val parentAddress = parentUser?.address
        val parentReferral = parentUser?.inviteCode

        val sPoint = user.sPoint ?: BigDecimal.ZERO
        val jPoint = user.jPoint ?: BigDecimal.ZERO
        val lPoint = user.lPoint ?: BigDecimal.ZERO

        return ApiResponse.success(
            UserInfoResponse(
                user.inviteCode,
                parentAddress,
                parentReferral,
                address = user.address,
                useKolRebate = user.useKolRebate,
                sPoint = sPoint.stripTrailingZeros().toPlainString(),
                jPoint = jPoint.stripTrailingZeros().toPlainString(),
                lPoint = lPoint.stripTrailingZeros().toPlainString()
            )
        )
    }

    @ApiOperation("User Points")
    @CrossOrigin
    @GetMapping("/points")
    fun points(
        request: HttpServletRequest,
        @RequestParam @EVMAddress address: String,
    ): ApiResponse<UserPointsResponse> {
        authUtil.filterIPBlacklist(request)
        val user = userRepository.findByAddressIgnoreCase(address) ?: throw BusinessException(ResultEnum.USER_NOT_FOUND)

        val sPoint = user.sPoint ?: BigDecimal.ZERO
        val jPoint = user.jPoint ?: BigDecimal.ZERO
        val lPoint = user.lPoint ?: BigDecimal.ZERO

        return ApiResponse.success(
            UserPointsResponse(
                sPoint = sPoint.stripTrailingZeros().toPlainString(),
                jPoint = jPoint.stripTrailingZeros().toPlainString(),
                lPoint = lPoint.stripTrailingZeros().toPlainString()
            )
        )
    }

    @PostMapping("/has_bind_discord")
    @ApiOperation("Check whether user has bind discord")
    @CrossOrigin
    fun hasBindDiscord(
        request: HttpServletRequest,
        @Valid @RequestBody dto: AuthDTO
    ): ApiResponse<Boolean> {

        authUtil.filterIPBlacklist(request)
        val user = authUtil.auth(request, dto.address)

        return ApiResponse.success(user.discordInfo != null)
    }

    @ApiOperation("Web3Login: Authenticate")
    @CrossOrigin
    @PostMapping("/login")
    fun login(
        @Valid @RequestBody dto: LoginDTO,
        request: HttpServletRequest
    ): ApiResponse<String> {
        authUtil.filterIPBlacklist(request)
        val ops = redisTemplate.opsForValue()
        val key = "auth:code:${dto.address}"
        val message = ops.get(key) ?: throw BusinessException(ResultEnum.INVALID_AUTHORIZATION_CODE)

        val messagePrefix = "\u0019Ethereum Signed Message:\n"
        val messageSize = message.toByteArray().size
        val prefix = (messagePrefix + messageSize).toByteArray()
        val messageWithPrefix = ByteArray(prefix.size + messageSize) {
            if (it < prefix.size) prefix[it] else message[it - prefix.size].toByte()
        }
        val messageHash = Hash.sha3(messageWithPrefix)

        logger.info("Signature: ${dto.signature.substring(2)}")
        val r = dto.signature.substring(2, 66)
        val s = dto.signature.substring(66, 130)
        val v = dto.signature.substring(130, 132)
        //logger.info("r: 0x$r\ts: 0x$s\tv: 0x$v")
        val rB = Numeric.hexStringToByteArray(r)
        val sB = Numeric.hexStringToByteArray(s)
        val vB = Numeric.hexStringToByteArray(v)
        val signature = Sign.SignatureData(vB, rB, sB)

        val publicKey = Sign.signedMessageHashToKey(messageHash, signature)
        val inferAddress = "0x" + Keys.getAddress(publicKey)
        logger.info("登录: 请求地址=${dto.address}\t推断地址=$inferAddress, inviteCode=${dto.invitedCode}")

        if (inferAddress.lowercase() == dto.address.lowercase()) {
            val accessToken = genRandomString()
            authUtil.setAccessToken(Keys.toChecksumAddress(dto.address), accessToken)

            if(dto.invitedCode != null) {

                try{

                    val existingUserNetwork = userNetworkRepository.findByAddressIgnoreCase(dto.address)
                    if(existingUserNetwork != null){
                        logger.info("登录: User network already exists for user: ${existingUserNetwork.address}")
                        return ApiResponse.success(accessToken)
                    }

                    userNetworkService.bindUserNetworkInviteCode(
                        dto.address,
                        dto.invitedCode
                    )
                } catch (e: Exception) {
                    //logger.error("Bind user network invite code failed. ${e.message}")
                    logger.info("登录: Not User Network invite code, goto user register flow ${e.message}, $e")

                    val invitor = userRepository.findByInviteCode(dto.invitedCode)
                    if(invitor == null){
                        throw BusinessException(ResultEnum.INVITE_CODE_NOT_VALID)
                    }

                    if(invitor.useKolRebate == false){
                        val invitorNetwork = userNetworkRepository.findByAddressIgnoreCase(invitor.address)
                        if(invitorNetwork == null){
                            throw BusinessException(ResultEnum.INVITE_CODE_NOT_VALID)
                        }

                        if(invitorNetwork.grade != UserNetworkGrade.GRADE2 && invitorNetwork.grade != UserNetworkGrade.USER){
                            throw BusinessException(ResultEnum.INVITE_CODE_NOT_VALID)
                        }
                    }

                    userService.register(dto.address, dto.invitedCode)
                }
            }else {
                // Register user
                userService.register(dto.address, null)
            }

            return ApiResponse.success(accessToken)
        } else {
            throw BusinessException(ResultEnum.LOGIN_FAILED)
        }

    }

    @ApiOperation("Check whether KYT is high risk")
    @CrossOrigin
    @PostMapping("/kyt_high_risk")
    fun kyt(
        @Valid @RequestBody dto: KytDTO,
        request: HttpServletRequest
    ): ApiResponse<Boolean> {
        val ipAddress = authUtil.filterIPBlacklist(request)
        return ApiResponse.success(mistTrackService.isAddressHighRisk(
            dto.address,
            dto.channel,
            ipAddress
        ))
    }

    @ApiOperation("[DISCORD] Oauth2回调(设置于discord后台) 用户是否加入了Jasper Vault服务器")
    @CrossOrigin
    @GetMapping("/discord/oauth2/callback")
    fun discordCallback(
        request: HttpServletRequest,
        @RequestParam code: String,
        @RequestParam state: String?
    //): String {
    ): RedirectView {

        logger.info("Discord Oauth2 Callback. Code = $code, State = $state")

        val user = userService.getByDiscordSession(state ?: "") ?: run {
            logger.error("User not found. Discord session = $state")
            throw BusinessException(ResultEnum.USER_NOT_FOUND)
        }
        val tokenInfo = discordService.getAccessToken(code)
        val accessToken = tokenInfo["access_token"]?.asText() ?: throw BusinessException(ResultEnum.BAD_REQUEST)
        val tokenType = tokenInfo["token_type"].asText()
        val refreshToken = tokenInfo["refresh_token"].asText()
        // val scope = tokenInfo["scope"]?.asText()
        val expiresIn = tokenInfo["expires_in"].asLong()
        val inGuild = discordService.inGuild(tokenType, accessToken)
        val userInfo = discordService.getMe(tokenType, accessToken)
            ?: throw BusinessException(ResultEnum.AUTHORIZATION_FAILED)
        val discordUserId = userInfo["id"].asText()

        val query = Query().addCriteria(Criteria.where("discordInfo.userId").`is`(discordUserId))
        mongoTemplate.findOne(query, User::class.java)?.let {
            throw BusinessException(ResultEnum.DISCORD_USER_ALREADY_BOUND)
        }

        val avatarHash = userInfo["avatar"]?.asText()
        val discordInfo = DiscordInfo(
            tokenType = tokenType,
            accessToken = accessToken,
            refreshToken = refreshToken,
            grantTime = LocalDateTime.now(),
            expiresIn = expiresIn,
            inGuild = inGuild,
            userId = discordUserId,
            username = userInfo["username"].asText(),
            avatar = avatarHash?.let { discordService.getAvatarUrl(discordUserId, it) }
        )
        user.discordInfo = discordInfo
        user.discordSession = null
        userRepository.save(user)

        var returnUrl = "https://uat.jasper.finance/campaignAirdrop/Arbitrum"
        if(ProfileUtil.activeProfile == "prod"){
            returnUrl = "https://app.jaspervault.io/campaignAirdrop/Arbitrum"
        }

        return RedirectView(returnUrl)
        //return "Bind discord success, please join Jasper Vault discord channel and upgrade to level 1."
    }
}