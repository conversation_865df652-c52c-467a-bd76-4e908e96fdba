package io.vault.jasper.repository.activity

import io.vault.jasper.model.activity.UserCampaignProgress
import org.springframework.data.mongodb.repository.MongoRepository
import org.springframework.stereotype.Repository
import java.util.Optional

@Repository
interface UserCampaignProgressRepository : MongoRepository<UserCampaignProgress, String> {
    fun findByUserIdAndCampaignId(userId: String, campaignId: String): Optional<UserCampaignProgress>
    
    fun findByUserId(userId: String): List<UserCampaignProgress>
    
    fun findByCampaignId(campaignId: String): List<UserCampaignProgress>
    
    fun countByCampaignId(campaignId: String): Long
}
