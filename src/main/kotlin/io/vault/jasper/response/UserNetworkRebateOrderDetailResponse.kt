package io.vault.jasper.response

import io.swagger.annotations.ApiModel
import io.swagger.annotations.ApiModelProperty
import java.math.BigDecimal

@ApiModel("用户返佣记录详情")
data class UserNetworkRebateOrderDetailResponse(
    @ApiModelProperty("期权订单ID", required = true)
    val optionOrderId: String,
    @ApiModelProperty("下单用户", required = true)
    val buyer: String,
    @ApiModelProperty("返佣时间", required = true)
    val created: Long,
    @ApiModelProperty("权利金", required = true)
    val premiumFee: String,
    @ApiModelProperty("用户返佣金额", required = true)
    val userRebate: String,
    @ApiModelProperty("用户返佣比例", required = true)
    val userPercentage: String,
    @ApiModelProperty("Grade2 地址", required = false)
    var grade2Address: String? = null,
    @ApiModelProperty("Grade2 返佣金额", required = false)
    var grade2Rebate: String? = null,
    @ApiModelProperty("Grade2 返佣比例", required = false)
    var grade2Percentage: String? = null,
    @ApiModelProperty("Grade1 地址", required = false)
    var grade1Address: String? = null,
    @ApiModelProperty("Grade1 返佣金额", required = false)
    var grade1Rebate: String? = null,
    @ApiModelProperty("Grade1 返佣比例", required = false)
    var grade1Percentage: String? = null,
    @ApiModelProperty("Grade0 地址", required = false)
    var grade0Address: String? = null,
    @ApiModelProperty("Grade0 返佣金额", required = false)
    var grade0Rebate: String? = null,
    @ApiModelProperty("Grade0 返佣比例", required = false)
    var grade0Percentage: String? = null,
)