[{"inputs": [], "stateMutability": "nonpayable", "type": "constructor"}, {"anonymous": false, "inputs": [{"indexed": false, "internalType": "address", "name": "previousAdmin", "type": "address"}, {"indexed": false, "internalType": "address", "name": "newAdmin", "type": "address"}], "name": "Admin<PERSON><PERSON>ed", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "beacon", "type": "address"}], "name": "BeaconUpgraded", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": false, "internalType": "uint8", "name": "version", "type": "uint8"}], "name": "Initialized", "type": "event"}, {"anonymous": false, "inputs": [{"components": [{"internalType": "uint256", "name": "price", "type": "uint256"}, {"internalType": "uint256", "name": "timestamp", "type": "uint256"}], "indexed": false, "internalType": "struct IPriceOracle.HistoryPrice[]", "name": "historyPrice", "type": "tuple[]"}], "name": "ReadHistoryPrice", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": false, "internalType": "address[]", "name": "_masterTokens", "type": "address[]"}, {"indexed": false, "internalType": "address[]", "name": "_quoteTokens", "type": "address[]"}, {"indexed": false, "internalType": "bytes32[]", "name": "_oracles", "type": "bytes32[]"}], "name": "SetOralces", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "implementation", "type": "address"}], "name": "Upgraded", "type": "event"}, {"inputs": [], "name": "diamond", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "_fee", "type": "uint256"}, {"components": [{"internalType": "bytes[]", "name": "updateData", "type": "bytes[]"}, {"internalType": "bytes32[]", "name": "priceIds", "type": "bytes32[]"}, {"internalType": "uint64", "name": "minPublishTime", "type": "uint64"}, {"internalType": "uint64", "name": "maxPublishTime", "type": "uint64"}], "internalType": "struct IPythAdapter.PythData", "name": "_pythData", "type": "tuple"}], "name": "getHistoryPriceFromPyth", "outputs": [{"components": [{"internalType": "bytes32", "name": "id", "type": "bytes32"}, {"components": [{"internalType": "int64", "name": "price", "type": "int64"}, {"internalType": "uint64", "name": "conf", "type": "uint64"}, {"internalType": "int32", "name": "expo", "type": "int32"}, {"internalType": "uint256", "name": "publishTime", "type": "uint256"}], "internalType": "struct PythStructs.Price", "name": "price", "type": "tuple"}, {"components": [{"internalType": "int64", "name": "price", "type": "int64"}, {"internalType": "uint64", "name": "conf", "type": "uint64"}, {"internalType": "int32", "name": "expo", "type": "int32"}, {"internalType": "uint256", "name": "publishTime", "type": "uint256"}], "internalType": "struct PythStructs.Price", "name": "emaPrice", "type": "tuple"}], "internalType": "struct PythStructs.PriceFeed[]", "name": "priceFeed", "type": "tuple[]"}], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "_diamond", "type": "address"}, {"internalType": "address", "name": "_pyth", "type": "address"}, {"internalType": "address", "name": "_usdToken", "type": "address"}], "name": "initialize", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "", "type": "address"}, {"internalType": "address", "name": "", "type": "address"}], "name": "oralces", "outputs": [{"internalType": "bytes32", "name": "", "type": "bytes32"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "proxiableUUID", "outputs": [{"internalType": "bytes32", "name": "", "type": "bytes32"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "pyth", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "_masterToken", "type": "address"}, {"internalType": "address", "name": "_quoteToken", "type": "address"}], "name": "read", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "_masterToken", "type": "address"}, {"internalType": "bytes[]", "name": "_data", "type": "bytes[]"}], "name": "readHistoryPrice", "outputs": [{"components": [{"internalType": "uint256", "name": "price", "type": "uint256"}, {"internalType": "uint256", "name": "timestamp", "type": "uint256"}], "internalType": "struct IPriceOracle.HistoryPrice[]", "name": "historyPrice", "type": "tuple[]"}], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address[]", "name": "_masterTokens", "type": "address[]"}, {"internalType": "address[]", "name": "_quoteTokens", "type": "address[]"}, {"internalType": "bytes32[]", "name": "_oracles", "type": "bytes32[]"}], "name": "setOralceList", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "bytes[]", "name": "_priceUpdateData", "type": "bytes[]"}], "name": "setPrice", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "_addr", "type": "address"}], "name": "<PERSON><PERSON><PERSON><PERSON>", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "newImplementation", "type": "address"}], "name": "upgradeTo", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "newImplementation", "type": "address"}, {"internalType": "bytes", "name": "data", "type": "bytes"}], "name": "upgradeToAndCall", "outputs": [], "stateMutability": "payable", "type": "function"}, {"inputs": [], "name": "usdToken", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"stateMutability": "payable", "type": "receive"}]