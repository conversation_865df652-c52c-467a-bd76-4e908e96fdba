package io.vault.jasper.service

import org.slf4j.LoggerFactory
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.stereotype.Service
import java.util.concurrent.atomic.AtomicBoolean

@Service
class AlertService  @Autowired constructor(
    private val jasperVaultService: JasperVaultService
) {

    private val logger = LoggerFactory.getLogger(this::class.java)

    companion object {
        private const val LOG_PREFIX = "[AlertService]"
    }

    // 使用AtomicBoolean确保线程安全
    var emergencyEnabled = AtomicBoolean(true)
    var alertEnabled = AtomicBoolean(true)
    var warningEnabled = AtomicBoolean(true)

    /**
     * 发送严重警报
     * @param reason 警报原因
     */
    fun emergency(reason: String) {
        emergencyEnabled.set(false)
        logger.info("$LOG_PREFIX Emergency alerts disabled. Reason: $reason")
        jasperVaultService.stopKMS()
    }

    /**
     * 发送警报
     * @param reason 警报原因
     */
    fun alert(reason: String) {
        alertEnabled.set(false)
        logger.info("$LOG_PREFIX Alert notifications disabled. Reason: $reason")
    }

    /**
     * 发送通知
     * @param reason 通知原因
     */
    fun warning(reason: String) {
        warningEnabled.set(false)
        logger.info("$LOG_PREFIX Warning notifications disabled. Reason: $reason")
    }

    /**
     * 重置所有状态
     */
    fun resetAllStates() {
        emergencyEnabled.set(true)
        alertEnabled.set(true)
        warningEnabled.set(true)
    }
}