package io.vault.jasper.aspect

import com.fasterxml.jackson.databind.ObjectMapper
import io.vault.jasper.annotation.ApiCache
import org.aspectj.lang.ProceedingJoinPoint
import org.aspectj.lang.annotation.Around
import org.aspectj.lang.annotation.Aspect
import org.aspectj.lang.reflect.MethodSignature
import org.springframework.context.ApplicationContext
import org.springframework.core.Ordered
import org.springframework.core.annotation.Order
import org.springframework.data.redis.core.StringRedisTemplate
import org.springframework.stereotype.Component
import java.util.concurrent.TimeUnit

@Aspect
@Component
@Order(Ordered.HIGHEST_PRECEDENCE)
class ApiCacheAspect(
    private val redisTemplate: StringRedisTemplate,
    private val objectMapper: ObjectMapper,
    private val applicationContext: ApplicationContext
) {
    @Around("@annotation(io.vault.jasper.annotation.ApiCache) && execution(* *(..))")
    fun around(joinPoint: ProceedingJoinPoint): Any? {
        val signature = joinPoint.signature as MethodSignature
        val method = signature.method
        val apiCache = method.getAnnotation(ApiCache::class.java)
        
        // 构建缓存key
        val cacheKey = buildCacheKey(joinPoint, apiCache)
        
        // 尝试从缓存获取
        val cachedValue = redisTemplate.opsForValue().get(cacheKey)
        if (!cachedValue.isNullOrEmpty()) {
            // 从缓存返回
            return objectMapper.readValue(cachedValue, method.returnType)
        }
        
        val result = joinPoint.proceed()
        
        // 存入缓存
        redisTemplate.opsForValue().set(
            cacheKey,
            objectMapper.writeValueAsString(result),
            apiCache.expire,
            TimeUnit.SECONDS
        )
        
        return result
    }
    
    private fun buildCacheKey(joinPoint: ProceedingJoinPoint, apiCache: ApiCache): String {
        val signature = joinPoint.signature as MethodSignature
        val methodName = signature.method.name
        val parameterNames = signature.parameterNames
        val args = joinPoint.args
        
        val keyBuilder = StringBuilder()
        keyBuilder.append("api:cache:")
        keyBuilder.append(if (apiCache.key.isNotEmpty()) apiCache.key else methodName)
        
        // 添加动态参数到key
        if (apiCache.dynamicKey.isNotEmpty()) {
            for (paramName in apiCache.dynamicKey) {
                val paramIndex = parameterNames.indexOf(paramName)
                if (paramIndex >= 0 && paramIndex < args.size) {
                    keyBuilder.append(":").append(args[paramIndex])
                }
            }
        }
        
        return keyBuilder.toString()
    }
} 