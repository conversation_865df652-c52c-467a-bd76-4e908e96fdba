package io.vault.jasper.response

import io.swagger.annotations.ApiModel
import io.swagger.annotations.ApiModelProperty
import io.vault.jasper.enums.ChainType
import io.vault.jasper.enums.OptionDirection
import io.vault.jasper.enums.Symbol
import io.vault.jasper.model.KolApplyStatus
import io.vault.jasper.model.OptionStatus

@ApiModel
data class KolApplyStatusResponse(

    @ApiModelProperty("申请钱包地址", required = true)
    val address: String,

    @ApiModelProperty("申请状态", required = true)
    val status: KolApplyStatus,

    @ApiModelProperty("申请时间", required = true)
    val created: Long
)
