version: '3.1'

services:
  jasper:
    build: ./
    image: jvault:1.0
    container_name: jvault
    restart: unless-stopped
    volumes:
      - ./log:/app/log
    ports:
      - "8888:${PORT}"
    environment:
      # TZ: Asia/Shanghai
      PORT: ${PORT}
      DB_HOST: ${DB_HOST}
      DB_PORT: ${DB_PORT}
      DB_DATABASE: ${DB_DATABASE}
      DB_AUTHENTICATION_DATABASE: ${DB_AUTHENTICATION_DATABASE}
      DB_USERNAME: ${DB_USERNAME}
      DB_PASSWORD: ${DB_PASSWORD}
      REDIS_HOST: ${REDIS_HOST}
      REDIS_PORT: ${REDIS_PORT}
      REDIS_PASSWORD: ${REDIS_PASSWORD}
      LOGGING_LEVEL: ${LOGGING_LEVEL}
      DEVTOOLS_RESTART_ENABLED: ${DEVTOOLS_RESTART_ENABLED}
      SWAGGER_ENABLED: ${SWAGGER_ENABLED}
      POLYGON_ENDPOINT: ${POLYGON_ENDPOINT}
      POLYGON_CHAIN_ID: ${POLYGON_CHAIN_ID}
      POLYGON_OPTIONS_CONTRACT: ${POLYGON_OPTIONS_CONTRACT}
      JASPER_VAULT_API_URL: ${JASPER_VAULT_API_URL}
      KNIFE4J_ENABLED: ${KNIFE4J_ENABLED}
      ARBITRUM_ENDPOINT: ${ARBITRUM_ENDPOINT}
      ARBITRUM_CHAIN_ID: ${ARBITRUM_CHAIN_ID}
      ARBITRUM_CONTRACT_OPTION_MODULE: ${ARBITRUM_CONTRACT_OPTION_MODULE}
      ARBITRUM_CONTRACT_OPTION_SERVICE: ${ARBITRUM_CONTRACT_OPTION_SERVICE}
      ARBITRUM_CONTRACT_ENTRY_POINT: ${ARBITRUM_CONTRACT_ENTRY_POINT}
      SETTLEMENT_WALLET: ${SETTLEMENT_WALLET}
      LOG_CONFIG_FILE: ${LOG_CONFIG_FILE}
      LOGTAIL_SOURCE_TOKEN: ${LOGTAIL_SOURCE_TOKEN}
      API_V1_HOST: ${API_V1_HOST}
      MONITOR_SWITCH: ${MONITOR_SWITCH}
      SPRING_PROFILES_ACTIVE: ${SPRING_PROFILES_ACTIVE}
      OPTION_PRICE_HOST: ${OPTION_PRICE_HOST}
