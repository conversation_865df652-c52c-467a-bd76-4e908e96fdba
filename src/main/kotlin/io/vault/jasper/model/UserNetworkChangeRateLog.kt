package io.vault.jasper.model

import org.springframework.data.annotation.CreatedDate
import org.springframework.data.annotation.Id
import org.springframework.data.annotation.LastModifiedDate
import org.springframework.data.mongodb.core.index.Indexed
import org.springframework.data.mongodb.core.mapping.Document
import org.springframework.data.mongodb.core.mapping.Field
import org.springframework.data.mongodb.core.mapping.FieldType
import java.math.BigDecimal
import java.time.LocalDateTime

@Document("user_network_change_rate_log")
data class UserNetworkChangeRateLog(
    @Id
    val id: String? = null,
    @Indexed(background = true)
    val address: String, // 大小写敏感
    @Field("before_rate")
    val beforeRate: BigDecimal,
    @Field("after_rate")
    val afterRate: BigDecimal,
    @CreatedDate
    val created: LocalDateTime = LocalDateTime.now(),
    @LastModifiedDate
    val updated: LocalDateTime = LocalDateTime.now(),
)