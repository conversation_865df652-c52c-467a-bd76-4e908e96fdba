package io.vault.jasper.model

import io.vault.jasper.enums.Symbol
import org.springframework.data.annotation.CreatedDate
import org.springframework.data.annotation.Id
import org.springframework.data.annotation.LastModifiedDate
import org.springframework.data.mongodb.core.index.CompoundIndex
import org.springframework.data.mongodb.core.index.Indexed
import org.springframework.data.mongodb.core.mapping.Document
import org.springframework.data.mongodb.core.mapping.Field
import org.springframework.data.mongodb.core.mapping.FieldType
import java.math.BigDecimal
import java.math.BigInteger
import java.time.LocalDateTime

@Document("user_network_rebate_summary")
@CompoundIndex(
    name = "user_buyer_address",
    def = "{'user_address' : 1, 'buyer_address': 1}",
    background = true, unique = true
)
data class UserNetworkRebateSummary(
    @Id
    val id: String? = null,
    @Indexed(background = true)
    val userId: String,
    @Indexed(background = true)
    @Field("user_network_id")
    val userNetworkId: String,
    @Field("invited_network_id")
    @Indexed(background = true)
    var invitedNetworkId: String,
    @Indexed(background = true)
    @Field("user_address")
    val userAddress: String,
    @Field("team_leader_address")
    val teamLeaderAddress: String,
    @Field("team_premium",targetType = FieldType.DECIMAL128)
    var teamPremium: BigDecimal,
    @Field("team_rebate",targetType = FieldType.DECIMAL128)
    var teamRebate: BigDecimal,
    @CreatedDate
    val created: LocalDateTime = LocalDateTime.now(),
    @LastModifiedDate
    val updated: LocalDateTime = LocalDateTime.now()
) {}