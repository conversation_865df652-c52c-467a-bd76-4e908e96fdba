package io.vault.jasper.repository

import io.vault.jasper.model.UserTradeRebateRecord
import io.vault.jasper.model.UserTradeRebateStatus
import org.springframework.data.mongodb.repository.MongoRepository
import org.springframework.stereotype.Repository

@Repository
interface UserTradeRebateRecordRepository : MongoRepository<UserTradeRebateRecord, String> {

    fun findByOptionOrderId(optionOrderId: String): UserTradeRebateRecord?
    
    fun findByBuyerAddressIgnoreCase(buyerAddress: String): List<UserTradeRebateRecord>
    
    fun findByBuyerUserIdAndStatus(
        buyerUserId: String,
        status: UserTradeRebateStatus
    ): List<UserTradeRebateRecord>
    
    fun findByStatus(status: UserTradeRebateStatus): List<UserTradeRebateRecord>
}
