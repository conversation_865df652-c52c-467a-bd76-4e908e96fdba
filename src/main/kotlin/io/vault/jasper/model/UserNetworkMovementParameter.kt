package io.vault.jasper.model

import org.springframework.data.annotation.CreatedDate
import org.springframework.data.annotation.Id
import org.springframework.data.annotation.LastModifiedDate
import org.springframework.data.mongodb.core.mapping.Document
import org.springframework.data.mongodb.core.mapping.Field
import java.time.LocalDateTime

/**
 * 用户网络迁移参数
 * 用于控制定时任务是否执行以及设置rootAddress
 */
@Document("user_network_movement_parameter")
data class UserNetworkMovementParameter(
    @Id
    val id: String? = null,
    
    /**
     * 任务开关
     */
    @Field("task_switch")
    var taskSwitch: Boolean = false,
    
    /**
     * 根地址
     */
    @Field("root_address")
    var rootAddress: String = "0x55E13E114d4fa80c1dE6F84Ae1ADE25D578B402b",

    /**
     * 根地址
     */
    @Field("process_address")
    var processAddress: String = "0x1C0a66bd873E6c25A17277F5223CB09dD80150Ee",
    
    /**
     * 每次处理的用户数量限制
     */
    @Field("batch_size")
    var batchSize: Int = 1000,
    
    /**
     * 上次处理的用户ID
     * 用于断点续传
     */
    @Field("last_processed_user_id")
    var lastProcessedUserId: String? = null,
    
    /**
     * 已处理的用户数量
     */
    @Field("processed_count")
    var processedCount: Int = 0,
    
    /**
     * 上次执行时间
     */
    @Field("last_execution_time")
    var lastExecutionTime: LocalDateTime? = null,
    
    @CreatedDate
    val created: LocalDateTime = LocalDateTime.now(),
    
    @LastModifiedDate
    val updated: LocalDateTime = LocalDateTime.now()
)
