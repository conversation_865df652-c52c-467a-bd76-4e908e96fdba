package io.vault.jasper.controller

import io.vault.jasper.ApiResponse
import io.vault.jasper.exception.BusinessException
import io.vault.jasper.exception.ParticleException
import org.slf4j.LoggerFactory
import org.springframework.http.HttpStatus
import org.springframework.http.converter.HttpMessageNotReadableException
import org.springframework.validation.FieldError
import org.springframework.web.bind.MethodArgumentNotValidException
import org.springframework.web.bind.annotation.CrossOrigin
import org.springframework.web.bind.annotation.ExceptionHandler
import org.springframework.web.bind.annotation.ResponseStatus
import org.springframework.web.bind.annotation.RestControllerAdvice
import javax.servlet.http.HttpServletRequest


@RestControllerAdvice
class ExceptionController {

    private val logger = LoggerFactory.getLogger(this::class.java)

    @ExceptionHandler(ParticleException::class)
    @ResponseStatus(HttpStatus.INTERNAL_SERVER_ERROR)
    @CrossOrigin
    fun handleParticleException(ce: ParticleException): String{
        return "Internal Server Error"
    }

    @ExceptionHandler(Exception::class)
    @ResponseStatus(HttpStatus.OK)
    @CrossOrigin
    fun handleException(ce: Exception, request: HttpServletRequest): ApiResponse<String?> {
        logger.error(ce.message, ce)
        return ApiResponse.error(500, ce.message ?: "")
    }

    @ExceptionHandler(HttpMessageNotReadableException::class)
    @ResponseStatus(HttpStatus.OK)
    @CrossOrigin
    fun handleHttpMessageNotReadableException(ce: HttpMessageNotReadableException): ApiResponse<String?> {
        logger.error(ce.message, ce)
        return ApiResponse.error(500, ce.message ?: "")
    }

    @ExceptionHandler(MethodArgumentNotValidException::class)
    @ResponseStatus(HttpStatus.OK)
    @CrossOrigin
    fun handleMethodArgumentNotValidException(ce: MethodArgumentNotValidException): ApiResponse<String?> {
        logger.error(ce.message, ce)
        val errors = ce.bindingResult.allErrors
        var message = ""
        errors.forEach { it ->
            if (it is FieldError) {
                message += "${it.field}: ${it.defaultMessage};"
            }
        }
        if (message.isBlank()) {
            message = "Request parameters error"
        }

        return ApiResponse.error(500, message)
    }

    @ExceptionHandler(BusinessException::class)
    @ResponseStatus(HttpStatus.OK)
    @CrossOrigin
    fun handleBusinessException(ce: BusinessException): ApiResponse<String?> {
        logger.error(ce.message, ce)
        return ApiResponse.error(ce.code, ce.message)
    }
}