package io.vault.jasper.controller.admin

import io.swagger.annotations.Api
import io.swagger.annotations.ApiOperation
import io.vault.jasper.exception.BusinessException
import io.vault.jasper.exception.ResultEnum
import io.vault.jasper.model.activity.*
import io.vault.jasper.ApiResponse
import io.vault.jasper.service.activity.CampaignRewardService
import io.vault.jasper.service.activity.CampaignService
import org.springframework.web.bind.annotation.*

@RestController
@RequestMapping("/api/admin/campaigns")
@Api(tags = ["Campaigns Admin API"])
class CampaignAdminController(
    private val campaignService: CampaignService,
    private val campaignRewardService: CampaignRewardService
) {
    
    @PostMapping
    @ApiOperation("Create a new campaign")
    fun createCampaign(@RequestBody campaign: Campaign): ApiResponse<Campaign> {
        return ApiResponse.success(campaignService.createCampaign(campaign))
    }
    
    @GetMapping("/{id}")
    @ApiOperation("Get an campaign by ID")
    fun getCampaign(@PathVariable id: String): ApiResponse<Campaign> {
        val activity = campaignService.getCampaignById(id)
            ?: throw BusinessException(ResultEnum.CAMPAIGN_NOT_FOUND)
        
        return ApiResponse.success(activity)
    }
    
    @GetMapping
    @ApiOperation("Get all active campaigns")
    fun getActiveCampaigns(): ApiResponse<List<Campaign>> {
        return ApiResponse.success(campaignService.getActiveCampaigns())
    }
    
    @GetMapping("/{id}/tasks")
    @ApiOperation("Get tasks for an campaign")
    fun getTasksForCampaign(@PathVariable id: String): ApiResponse<List<Task>> {
        return ApiResponse.success(campaignService.getTasksForCampaign(id))
    }
    
    @GetMapping("/{id}/participants")
    @ApiOperation("Get participant count for an activity")
    fun getParticipantCount(@PathVariable id: String): ApiResponse<Long> {
        return ApiResponse.success(campaignService.getParticipantCount(id))
    }
    
    @PostMapping("/rewards")
    @ApiOperation("Create a new campaign reward")
    fun createCampaignReward(@RequestBody campaignReward: CampaignReward): ApiResponse<CampaignReward> {
        return ApiResponse.success(campaignRewardService.createCampaignReward(campaignReward))
    }
    
    @GetMapping("/{id}/rewards")
    @ApiOperation("Get rewards for an activity")
    fun getRewardsForCampaign(@PathVariable id: String): ApiResponse<List<CampaignReward>> {
        return ApiResponse.success(campaignRewardService.getRewardsForCampaign(id))
    }
    
    @GetMapping("/{id}/rewards/distributed")
    @ApiOperation("Get total rewards distributed for an campaign")
    fun getTotalRewardsDistributedForCampaign(@PathVariable id: String): ApiResponse<String> {
        val total = campaignRewardService.getTotalRewardsDistributedForCampaign(id)
        return ApiResponse.success(total.toPlainString())
    }
}
