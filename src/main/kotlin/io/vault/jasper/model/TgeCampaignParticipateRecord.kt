package io.vault.jasper.model

import io.vault.jasper.enums.ChainType
import org.springframework.data.annotation.Id
import org.springframework.data.mongodb.core.index.Indexed
import org.springframework.data.mongodb.core.mapping.Document
import org.springframework.data.mongodb.core.mapping.Field
import java.math.BigDecimal
import java.time.LocalDateTime

@Document("tge_campaign_participate_record")
data class TgeCampaignParticipateRecord(
    @Id
    val id: String? = null,
    
    @Field("address")
    @Indexed(background = true)
    val address: String,

    @Field("option_order_id")
    @Indexed(background = true)
    val optionOrderId: String,

    val chain: ChainType? = null,

    @Field("retweet_time")
    var retweetTime : Long = 0L,

    @Field("bitlayer_trade_option_order_id")
    var bitlayerTradeOptionOrderId : String? = null,

    var discordLevel: Int = 0,

    @Indexed(background = true)
    var status: TgeCampaignParticipateRecordStatus = TgeCampaignParticipateRecordStatus.PENDING,
    
    @Field("created")
    val created: LocalDateTime = LocalDateTime.now(),

    @Field("updated")
    val updated: LocalDateTime = LocalDateTime.now(),

    @Field("settle_tx_id")
    var settleTxId: String? = null,
    @Field("error_msg")
    var errorMsg: String? = null,
    @Field("settled")
    var settled: Boolean = false
)

enum class TgeCampaignParticipateRecordStatus {
    PENDING,
    CREATED,
    CLAIMED,
    SUCCESS,
    FAILED
}