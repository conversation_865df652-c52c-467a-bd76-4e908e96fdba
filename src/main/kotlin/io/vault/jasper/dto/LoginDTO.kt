package io.vault.jasper.dto

import EVMAddress
import io.swagger.annotations.ApiModel
import io.swagger.annotations.ApiModelProperty

@ApiModel("登录信息")
data class LoginDTO(
    @ApiModelProperty("钱包地址", required = true)
    @field:EVMAddress
    val address: String,
    @ApiModelProperty("签名信息", required = true)
    val signature: String,
    @ApiModelProperty("邀请码", required = false)
    val invitedCode: String? = null
)
