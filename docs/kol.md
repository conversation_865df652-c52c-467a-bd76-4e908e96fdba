apply_for_kol_form
- x_handle
- discord_id
- wallet_address
- chain
- what_do_u_know_about_jasper_vault
- created
- status: SUBMITTED/PENDING_REVIEW/IN_REVIEW/APPROVED/REJECTED

<NAME_EMAIL> and <EMAIL>

kol
- level
- wallet
- chain
- referral_code
- incentive
- created
- status: ACTIVE/INACTIVE/SUSPENDED/DELETED/BANNED/ARCHIVED/LOCKED/PENDING


kol_level
- name
- conditions: json string
- incentive

kol_rebate_record
- option_order_id
  - kol_id
- incentive
- created
- premium_fee
- premium_fee_rate
- incentive_rate


1. KOL提交表单；
2. 表单发送到2个邮箱；
3. 审核后，触发【审核通过】接口，KOL用户升级到Lv1；
4. 每天检查一次条件，达标就升级，不达标就降级


## KOL等级
- 等级1
    * 人工审核
- 等级2
    * 升级条件：下线累积支付120000
    * 维持条件：30天日均活跃交易地址数>=500
- 等级3


- ~~申请表单的提交，邮件的发送；~~
- ~~KOL用户查看等级的接口（KOL表）；~~
- ~~定时任务：每日检查等级是否达标，升级或降级；~~
- ~~每笔期权合约结算时，触发佣金计算，每一笔记录到表中；~~
- ~~每日统计过去一天每个KOL的总返佣数量，总活跃用户量；~~
- ~~每小时将过去未上报的返佣通过合约上报到链上；~~
- ~~如何绑定KOL和用户的关系？~~