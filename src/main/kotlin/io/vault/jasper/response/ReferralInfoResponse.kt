package io.vault.jasper.response

import io.swagger.annotations.ApiModel
import io.swagger.annotations.ApiModelProperty
import io.vault.jasper.enums.ChainType
import io.vault.jasper.enums.Symbol
import io.vault.jasper.model.OrderType
import java.math.BigDecimal

@ApiModel("邀请信息")
data class ReferralInfoResponse(
    @ApiModelProperty("邀请总数", required = true)
    val refereeCount: Int,
    @ApiModelProperty("邀请用户总消费的权利金总数", required = true)
    val totalPremiumFee: String,
    @ApiModelProperty("邀请码", required = true)
    val inviteCode: String,
    @ApiModelProperty("父级钱包地址", required = false)
    val parentAddress: String? = null,
    @ApiModelProperty("父级邀请码", required = false)
    val parentReferral: String? = null,
    @ApiModelProperty("邀请用户总的期权交易量", required = false)
    val totalOptionVolume: String? = null,
    @ApiModelProperty("Rebate 总数", required = false)
    val totalRebate: String? = null,
    @ApiModelProperty("未rebate数量", required = false)
    val unclaimRebate: String? = null,
    @ApiModelProperty("用户等级", required = true)
    val userLevel: String = "0",
    @ApiModelProperty("用户地址", required = false)
    val address: String? = null,
    @ApiModelProperty("用户是否使用kol返佣制度", required = false)
    val useKolRebate: Boolean = true,
)