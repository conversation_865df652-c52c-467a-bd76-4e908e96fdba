package io.vault.jasper.model

import org.springframework.data.annotation.Id
import org.springframework.data.annotation.LastModifiedDate
import org.springframework.data.mongodb.core.index.Indexed
import org.springframework.data.mongodb.core.mapping.Document
import org.springframework.data.mongodb.core.mapping.Field
import java.time.LocalDateTime

@Document("channel_partner")
data class ChannelPartner(
    @Id
    val id: String? = null,
    val channel: UserChannel,
    @Indexed(unique = true, background = true)
    @Field("api_key")
    val apiKey: String,
    val created: LocalDateTime = LocalDateTime.now(),
    @LastModifiedDate
    val updated: LocalDateTime = LocalDateTime.now()
)