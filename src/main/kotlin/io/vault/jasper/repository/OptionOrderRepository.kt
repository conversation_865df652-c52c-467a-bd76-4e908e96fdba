package io.vault.jasper.repository

import io.vault.jasper.enums.ChainType
import io.vault.jasper.enums.OptionDirection
import io.vault.jasper.enums.Symbol
import io.vault.jasper.model.*
import org.springframework.data.domain.Page
import org.springframework.data.domain.Pageable
import org.springframework.data.domain.Sort
import org.springframework.data.mongodb.repository.MongoRepository
import org.springframework.data.mongodb.repository.Query
import org.springframework.stereotype.Repository
import java.math.BigDecimal
import java.time.LocalDateTime

@Repository
interface OptionOrderRepository : MongoRepository<OptionOrder, String> {
    // fun findByChainAndBuyerAndStatusAndJvaultIsTrue(
    //     chain: ChainType,
    //     buyer: String,
    //     status: OptionStatus,
    //     sort: Sort = Sort.by(Sort.Direction.DESC, OptionOrder::id.name)
    // ): List<OptionOrder>
    fun findByChainAndOrderTypeAndBuyerAndStatus(
        chain: ChainType,
        orderType: OrderType,
        buyer: String,
        status: OptionStatus,
        sort: Sort = Sort.by(Sort.Direction.DESC, OptionOrder::id.name)
    ): List<OptionOrder>
    // fun findByChainAndOrderTypeAndBuyerAndStatusIn(
    //     chain: ChainType,
    //     orderType: OrderType,
    //     buyer: String,
    //     statusIn: List<OptionStatus>,
    //     sort: Sort = Sort.by(Sort.Direction.DESC, OptionOrder::id.name)
    // ): List<OptionOrder>
    // fun findByChainAndOrderTypeAndBuyerAndStatusInAndBidAsset(
    //     chain: ChainType,
    //     orderType: OrderType,
    //     buyer: String,
    //     statusIn: List<OptionStatus>,
    //     bidAsset: Symbol,
    //     page: Pageable
    // ): Page<OptionOrder>
    @Query("{ chain: ?0, ${'$'}or: [{buyer: ?1}, {seller: ?2}] }")
    fun findByChainAndBuyerOrSeller(chain: ChainType, buyer: String, seller: String, page: Pageable): Page<OptionOrder>
    @Query("{ chain: ?0, status: ?1, ${'$'}or: [{buyer: ?2}, {seller: ?3}] }")
    fun findByChainAndStatusAndBuyerOrSeller(chain: ChainType, status: OptionStatus, buyer: String, seller: String, page: Pageable): Page<OptionOrder>
    fun findByStatus(status: OptionStatus): List<OptionOrder>
    fun findByStatusAndSettlementHashCheckedNot(status: OptionStatus, checked: Boolean): List<OptionOrder>
    fun findByStatusAndExpiryDateGreaterThan(status: OptionStatus, expiryDate: LocalDateTime): List<OptionOrder>
    fun findByChainAndStatus(chain: ChainType, status: OptionStatus): List<OptionOrder>
    fun findByChainAndStatusAndExpiryDateLessThan(
        chain: ChainType,
        status: OptionStatus,
        expiryDate: LocalDateTime,
        pageable: Pageable
    ): Page<OptionOrder>
    fun findByChainInAndStatus(chainList: List<ChainType>, status: OptionStatus): List<OptionOrder>
    fun findByChainInAndStatusAndExpiryDateLessThan(
        chainList: List<ChainType>,
        status: OptionStatus,
        expiryDate: LocalDateTime,
        page: Pageable
    ): Page<OptionOrder>
    fun findByChainAndStatusNot(chain: ChainType, status: OptionStatus): List<OptionOrder>
    fun findByChainNotAndStatus(chain: ChainType, status: OptionStatus): List<OptionOrder>
    fun findByChainNotInAndStatus(chainNotIn: List<ChainType>, status: OptionStatus): List<OptionOrder>
    fun findByChainNotInAndStatusAndExpiryDateLessThan(
        chainNotIn: List<ChainType>,
        status: OptionStatus,
        expiryDate: LocalDateTime,
        page: Pageable
    ): Page<OptionOrder>
    fun findByStatusAndBuyerProfitIsNull(status: OptionStatus): List<OptionOrder>
    fun findByChainAndStatusIn(chain: ChainType, status: List<OptionStatus>): List<OptionOrder>
    fun findByStatusAndTxHashNotNull(status: OptionStatus): List<OptionOrder>
    fun findByChainAndStatusIn(chain: ChainType, status: List<OptionStatus>, page: Pageable): Page<OptionOrder>
    fun findByStatusAndBuyerNotNull(
        status: OptionStatus,
        page: Pageable
    ): Page<OptionOrder>
    fun findByChainAndDirectionAndBuyerAndBuyerVaultAndStrikeAssetAddressAndLiquidateModeAndExpiryDateAndStatus(
        chain: ChainType,
        direction: OptionDirection,
        buyer: String,
        buyerVault: String,
        strikeAssetAddress: String,
        liquidateMode: String,
        expiryDate: LocalDateTime,
        status: OptionStatus,
        sort: Sort = Sort.by(Sort.Direction.DESC, OptionOrder::id.name)
    ): List<OptionOrder>
    fun findByTxHash(txHash: String): List<OptionOrder>
    fun findByTxHashAndCreatedLessThan(
        txHash: String,
        created: LocalDateTime
    ): List<OptionOrder>
    fun findFirstByTxHash(txHash: String): OptionOrder?
    fun existsByChainAndTxHash(chain: ChainType, txHash: String): Boolean
    fun existsByChainAndTxHashAndOnChainOrderId(chain: ChainType, txHash: String, onChainOrderId: String): Boolean
    fun findFirstByChainAndOnChainOrderId(chain: ChainType, onChainOrderId: String): OptionOrder?
    fun findByBidAssetIsNull(): List<OptionOrder>
    fun findByBidAmountIsNull(): List<OptionOrder>
    fun findByBuyerInAndStatusIn(buyers: List<String>, status: List<OptionStatus>, page: Pageable): Page<OptionOrder>
    fun findByChainAndStatusInAndCreatedGreaterThan(
        chain: ChainType,
        status: List<OptionStatus>,
        created: LocalDateTime,
    ): List<OptionOrder>
    @Query("{ status: {${'$'}in: ?0}, ${'$'}or: [{buyer: {${'$'}in: ?1}}, {seller: {${'$'}in: ?2}}], created: {${'$'}gte: ?3} }")
    fun findByStatusInAndUserAndCreated(
        status: List<OptionStatus>,
        buyerIn: Set<String>,
        sellerIn: Set<String>,
        createdTime: LocalDateTime
    ): List<OptionOrder>

    fun findByBuyerAndStatus(buyer: String, status: OptionStatus): List<OptionOrder>
    fun findByChainAndSellerVaultAndStatusIn(
        chain: ChainType,
        sellerVault: String,
        status: List<OptionStatus>
    ): List<OptionOrder>

    fun findByCreatedGreaterThan(created: LocalDateTime): List<OptionOrder>

    fun findByCreatedGreaterThanAndBuyerNotIn(
        created: LocalDateTime,
        buyers: List<String>,
        sort: Sort = Sort.by(Sort.Direction.DESC, OptionOrder::id.name)
    ): List<OptionOrder>

    fun findFirstByCreatedGreaterThanAndBuyerNotIn(
        created: LocalDateTime,
        buyers: List<String>,
        sort: Sort = Sort.by(Sort.Direction.DESC, OptionOrder::id.name)
    ): OptionOrder?

    fun findByCreatedGreaterThanAndChain(
        created: LocalDateTime,
        chain: ChainType
    ): List<OptionOrder>

    fun findByBuyerIgnoreCaseAndStatusIn(
        buyer: String,
        status: List<OptionStatus>,
    ): List<OptionOrder>

    fun findByOrderId(orderId: String): List<OptionOrder>

    fun findByPremiumFeePayInUsdt(
        premiumPay : BigDecimal?
    ): List<OptionOrder>
    fun findByStatusAndSettlementTimeIsNull(status: OptionStatus): List<OptionOrder>
    fun findByStatusAndExpiryDateGreaterThanEqualAndExpiryDateLessThan(
        status: OptionStatus,
        expiryDateBegin: LocalDateTime,
        expiryDateEnd: LocalDateTime
    ): List<OptionOrder>

    fun findByBuyerIgnoreCaseAndStatusInAndCreatedGreaterThan(
        buyer: String,
        status: List<OptionStatus>,
        created: LocalDateTime
    ): List<OptionOrder>

    fun findByChainAndOnChainOrderIdGreaterThan(
        chain: ChainType,
        onChainOrderId: String
    ): List<OptionOrder>

    fun findFirstByChainAndBuyer(chain: ChainType, buyer: String): OptionOrder?
    fun findFirstByChainAndBuyerIgnoreCase(
        chain: ChainType,
        buyer: String
    ): OptionOrder?

    fun findFirstByChainAndBidAssetAndBuyerIgnoreCaseAndCreatedGreaterThan(
        chain: ChainType,
        bidAsset: Symbol,
        buyer: String,
        created: LocalDateTime
    ): OptionOrder?

    fun findByBuyer(buyer: String): List<OptionOrder>
    fun findFirstByBuyer(buyer: String): OptionOrder?

    fun findFirstByChainAndBuyerIgnoreCaseAndStatusIn(
        chain: ChainType,
        buyer: String,
        status: List<OptionStatus>
    ): OptionOrder?

    fun findFirstByChainAndBuyerIgnoreCaseAndStatusInAndCreatedGreaterThan(
        chain: ChainType,
        buyer: String,
        status: List<OptionStatus>,
        created: LocalDateTime
    ): OptionOrder?

    fun findFirstByChainAndBuyerIgnoreCaseAndStatusInAndCreatedGreaterThanAndUsedMoonlightBoxIsNotNullOrderByCreatedDesc(
        chain: ChainType,
        buyer: String,
        status: List<OptionStatus>,
        created: LocalDateTime
    ): OptionOrder?

    fun findFirstByChainAndBuyerIgnoreCaseAndStatusInAndCreatedGreaterThanAndUsedMoonlightBoxIsNullOrderByCreatedDesc(
        chain: ChainType,
        buyer: String,
        status: List<OptionStatus>,
        created: LocalDateTime
    ): OptionOrder?

    fun findByChainAndStatusInAndBidAssetAndBidAmountEqualsAndExpiryInHour(
        chain: ChainType,
        status: List<OptionStatus>,
        bidAsset: Symbol,
        bidAmount: BigDecimal,
        expiryInHour: String
    ): List<OptionOrder>

    fun findByChainAndStatusInAndBuyerIgnoreCase(
        chain: ChainType,
        status: List<OptionStatus>,
        buyer: String,
        pageable: Pageable): Page<OptionOrder>

    fun findFirstByChainAndTxHashAndOnChainOrderIdNotAndUsedMoonlightBoxNotNull(
        chain: ChainType,
        txHash: String,
        onChainOrderId: String
    ): OptionOrder?

    fun findByChainAndTxHashAndBuyerAndDirectionNotAndOnChainOrderIdNot(
        chain: ChainType,
        txHash: String,
        buyer: String,
        direction: OptionDirection,
        onChainOrderId: String
    ): List<OptionOrder>

    fun findByChainAndTxHashAndBuyerAndDirectionAndOnChainOrderIdNot(
        chain: ChainType,
        txHash: String,
        buyer: String,
        direction: OptionDirection,
        onChainOrderId: String
    ): List<OptionOrder>

    /**
     * 获取最新一条chain=arbitrum的订单
     */
    fun findFirstByChainOrderByCreatedDesc(chain: ChainType): OptionOrder?

    fun findByStatusAndBuyerProfitInUsdIsNull(status: OptionStatus): List<OptionOrder>

    fun findByChainAndOnChainOrderId(
        chain: ChainType,
        onChainOrderId: String
    ): OptionOrder?

    fun findByChainAndStatusAndExpiryDateLessThan(
        chain: ChainType,
        status: OptionStatus,
        expiryDate: LocalDateTime
    ): List<OptionOrder>

    fun findFirstByChainAndBidAssetAndExpiryInHourAndBidAmountAndBuyerIgnoreCaseAndCreatedGreaterThan(
        chain: ChainType,
        bidAsset: Symbol,
        expiryInHour: String,
        bidAmount: BigDecimal,
        buyer: String,
        created: LocalDateTime
    ): OptionOrder?

    fun findFirstByChainAndBidAssetAndBidAmountAndBuyerIgnoreCaseAndCreatedGreaterThan(
        chain: ChainType,
        bidAsset: Symbol,
        bidAmount: BigDecimal,
        buyer: String,
        created: LocalDateTime
    ): OptionOrder?

    fun findFirstByChainAndBuyerIgnoreCaseAndChannelAndCreatedAfter(
        chain: ChainType,
        buyer: String,
        channel: UserChannel,
        created: LocalDateTime
    ): OptionOrder?

    fun findFirstByChainAndBuyerIgnoreCaseAndChannelAndWalletAndCreatedAfter(
        chain: ChainType,
        buyer: String,
        channel: UserChannel,
        wallet: UserWallet,
        created: LocalDateTime
    ): OptionOrder?

    fun findAllByCreatedBetween(start: LocalDateTime, end: LocalDateTime): List<OptionOrder>

    fun findByChainAndCreatedBetween(
        chain: ChainType,
        start: LocalDateTime,
        end: LocalDateTime
    ): List<OptionOrder>

    fun findByChainAndBlockHeight(chain: ChainType, blockHeight: Long): List<OptionOrder>
    fun existsByBuyerIgnoreCaseAndStatusIn(
        buyer: String,
        status: List<OptionStatus>
    ): Boolean

    fun findByBuyerAndCreatedBetween(
        buyer: String,
        start: LocalDateTime,
        end: LocalDateTime
    ): List<OptionOrder>

    fun countByBuyerIgnoreCaseAndCreatedBefore(
        buyer: String,
        created: LocalDateTime
    ): Long
}