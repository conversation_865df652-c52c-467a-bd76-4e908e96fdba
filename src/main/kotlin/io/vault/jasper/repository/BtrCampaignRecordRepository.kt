package io.vault.jasper.repository

import io.vault.jasper.model.BtrCampaignRecord
import org.springframework.data.mongodb.repository.MongoRepository
import org.springframework.stereotype.Repository

@Repository
interface BtrCampaignRecordRepository : MongoRepository<BtrCampaignRecord, String> {
    fun countByAddressIgnoreCase(address: String): Long
    fun existsByOptionOrderId(optionOrderId: String): Boolean
    fun findByAddressIgnoreCase(address: String): List<BtrCampaignRecord>
    fun findFirstByOptionOrderId(optionOrderId: String): BtrCampaignRecord?
} 