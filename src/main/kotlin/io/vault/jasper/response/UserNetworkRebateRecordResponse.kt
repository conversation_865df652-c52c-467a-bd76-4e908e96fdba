package io.vault.jasper.response

import io.swagger.annotations.ApiModel
import io.swagger.annotations.ApiModelProperty
import io.vault.jasper.model.UserNetworkGrade
import java.math.BigDecimal

@ApiModel("用户返佣记录")
data class UserNetworkRebateRecordResponse(
    @ApiModelProperty("下单用户", required = true)
    val buyer: String,
    @ApiModelProperty("下单用户等级", required = true)
    val buyerGrade: UserNetworkGrade?,
    @ApiModelProperty("返佣时间", required = true)
    val created: Long,
    @ApiModelProperty("权利金", required = true)
    val premiumFee: String,
    @ApiModelProperty("返佣金额", required = true)
    val rebate: String,
    @ApiModelProperty("返佣比例", required = true)
    val percentage: String,
    @ApiModelProperty("返佣期权订单ID", required = true)
    val optionOrderId: String
)