package io.vault.jasper.service.blockchain

import io.vault.jasper.blockchain.ArbitrumBlockchainUtil
import io.vault.jasper.blockchain.BlockchainUtilFactory
import io.vault.jasper.blockchain.evm.arbitrum.ArbitrumScanApi
import io.vault.jasper.enums.OptionDirection
import io.vault.jasper.enums.Symbol
import io.vault.jasper.exception.BusinessException
import io.vault.jasper.exception.ResultEnum
import io.vault.jasper.model.OptionOrder
import io.vault.jasper.repository.*
import io.vault.jasper.service.*
import io.vault.jasper.task.AsyncEvmScanner
import org.slf4j.LoggerFactory
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.beans.factory.annotation.Value
import org.springframework.context.ApplicationEventPublisher
import org.springframework.stereotype.Service
import org.web3j.abi.FunctionEncoder
import org.web3j.abi.TypeReference
import org.web3j.abi.datatypes.Address
import org.web3j.abi.datatypes.DynamicArray
import org.web3j.abi.datatypes.Function
import org.web3j.abi.datatypes.Type
import org.web3j.abi.datatypes.generated.Uint256
import org.web3j.crypto.Credentials
import org.web3j.crypto.Keys
import org.web3j.crypto.RawTransaction
import org.web3j.crypto.TransactionEncoder
import org.web3j.protocol.Web3j
import org.web3j.utils.Numeric
import java.math.BigInteger

@Service
class ArbitrumService @Autowired constructor(
    blockchainUtil: ArbitrumBlockchainUtil,
    scanApi: ArbitrumScanApi,
    optionOrderRepository: OptionOrderRepository,
    optionOrderInfoRepository: OptionOrderInfoRepository,
    orderRepository: OrderRepository,
    blockchainRepository: BlockchainRepository,
    asyncEvmScanner: AsyncEvmScanner,
    jasperVaultService: JasperVaultService,
    eventPublisher: ApplicationEventPublisher,
    lpVaultRepository: LPVaultRepository,
    contractEventRepository: ContractEventRepository,
    currencyService: CurrencyService,
    optionOrderService: OptionOrderService,
    blockchainUtilFactory: BlockchainUtilFactory,
    subgraphService: SubgraphService,
    historyService: HistoryService,
    miniBridgeSwapOrderRepository: MiniBridgeSwapOrderRepository,
    alertService: AlertService
) : EvmService(
    blockchainUtil,
    blockchainRepository,
    asyncEvmScanner,
    orderRepository,
    optionOrderRepository,
    optionOrderInfoRepository,
    jasperVaultService,
    eventPublisher,
    lpVaultRepository,
    contractEventRepository,
    currencyService,
    optionOrderService,
    blockchainUtilFactory,
    scanApi,
    subgraphService,
    historyService,
    miniBridgeSwapOrderRepository,
    alertService
) {

    private val logger = LoggerFactory.getLogger(this::class.java)

    @Value("\${blockchain.arbitrum.contract.vault_factory}")
    override lateinit var vaultFactoryContract: String

    @Value("\${blockchain.arbitrum.contract.option_service}")
    override lateinit var optionServiceContract: String

    @Value("\${blockchain.arbitrum.contract.diamond}")
    public override lateinit var addOrderLogAddress: String

    @Value("\${blockchain.arbitrum.contract.issuance_module}")
    override lateinit var issuanceModuleContract: String

    /**
     * 结算KOL返佣
     */
    fun settleKolRebate(
        contractAddress: String,
        privateKey: String,
        addressList: List<String>,
        amountList: List<BigInteger>,
        gasLimit: Int,
        gasPrice: BigInteger? = null
    ): String {
        val inputs: List<Type<*>> = listOf(
            DynamicArray(Address::class.java, addressList.map { Address(it) }),
            DynamicArray(Uint256::class.java, amountList.map { Uint256(it) })
        )
        val outputs: MutableList<TypeReference<*>> = ArrayList()
        val method = "settle"
        val function = Function(method, inputs, outputs)
        val data = FunctionEncoder.encode(function)
        val credentials = Credentials.create(privateKey)
        val rawTransaction = RawTransaction.createTransaction(
            evmUtil.getNonce(credentials.address),
            gasPrice ?: evmUtil.getGasPrice(""),
            gasLimit.toBigInteger(),
            contractAddress,
            BigInteger.valueOf(0),
            data
        )
        val signMessage = TransactionEncoder.signMessage(rawTransaction, evmUtil.chainId, credentials)
        val toHexString = Numeric.toHexString(signMessage)
        val ethCall = evmUtil.web3j.ethSendRawTransaction(toHexString).sendAsync().get()
        if (ethCall.hasError()) {
            val errorMsg = ethCall.error.message
            // max fee per gas less than block base fee: address ******************************************, maxFeePerGas: 10000000 baseFee: 10041000
            if (errorMsg.startsWith("max fee per gas less than block base fee")) {
                val regex = Regex("baseFee:\\s*(\\d+)")
                regex.find(errorMsg)?.let {
                    val baseFee = it.groupValues[1].toBigInteger()
                    logger.debug("current baseFee: $baseFee")
                    val move = baseFee.toString(10).length - 2
                    val divisor = BigInteger.TEN.pow(move)
                    val newBaseFee = (baseFee.divide(divisor) + BigInteger.ONE).multiply(divisor)
                    logger.debug("new baseFee: $newBaseFee")
                    Thread.sleep(3000)

                    return settleKolRebate(
                        contractAddress,
                        privateKey,
                        addressList,
                        amountList,
                        gasLimit,
                        newBaseFee
                    )
                }
            } else {
                logger.error("Error Message: ${ethCall.error.message}")
                throw Exception("Failed to send transaction")
            }
        }
        val txHash = ethCall.transactionHash
        logger.info("method: $method\t txid: $txHash")

        return txHash
    }

    /**
     * 结算KOL返佣
     */
    fun settleUserNetworkRebate(
        contractAddress: String,
        privateKey: String,
        addressList: List<String>,
        amountList: List<BigInteger>,
        gasLimit: Int,
        gasPrice: BigInteger? = null
    ): String {
        val inputs: List<Type<*>> = listOf(
            DynamicArray(Address::class.java, addressList.map { Address(it) }),
            DynamicArray(Uint256::class.java, amountList.map { Uint256(it) })
        )
        val outputs: MutableList<TypeReference<*>> = ArrayList()
        val method = "settle"
        val function = Function(method, inputs, outputs)
        val data = FunctionEncoder.encode(function)
        val credentials = Credentials.create(privateKey)
        val rawTransaction = RawTransaction.createTransaction(
            evmUtil.getNonce(credentials.address),
            gasPrice ?: evmUtil.getGasPrice(""),
            gasLimit.toBigInteger(),
            contractAddress,
            BigInteger.valueOf(0),
            data
        )
        val signMessage = TransactionEncoder.signMessage(rawTransaction, evmUtil.chainId, credentials)
        val toHexString = Numeric.toHexString(signMessage)
        val ethCall = evmUtil.web3j.ethSendRawTransaction(toHexString).sendAsync().get()
        if (ethCall.hasError()) {
            val errorMsg = ethCall.error.message
            // max fee per gas less than block base fee: address ******************************************, maxFeePerGas: 10000000 baseFee: 10041000
            if (errorMsg.startsWith("max fee per gas less than block base fee")) {
                val regex = Regex("baseFee:\\s*(\\d+)")
                regex.find(errorMsg)?.let {
                    val baseFee = it.groupValues[1].toBigInteger()
                    logger.debug("current baseFee: $baseFee")
                    val move = baseFee.toString(10).length - 2
                    val divisor = BigInteger.TEN.pow(move)
                    val newBaseFee = (baseFee.divide(divisor) + BigInteger.ONE).multiply(divisor)
                    logger.debug("new baseFee: $newBaseFee")
                    Thread.sleep(3000)

                    return settleUserNetworkRebate(
                        contractAddress,
                        privateKey,
                        addressList,
                        amountList,
                        gasLimit,
                        newBaseFee
                    )
                }
            } else {
                logger.error("Error Message: ${ethCall.error.message}")
                throw Exception("Failed to send transaction")
            }
        }
        val txHash = ethCall.transactionHash
        logger.info("method: $method\t txid: $txHash")

        return txHash
    }

    /**
     * 推送 Trading Credit
     */
    fun settleTradingCredit(
        contractAddress: String,
        privateKey: String,
        addressList: List<String>,
        amountList: List<BigInteger>,
        gasLimit: Int,
        gasPrice: BigInteger? = null
    ): String {
        val inputs: List<Type<*>> = listOf(
            DynamicArray(Address::class.java, addressList.map { Address(it) }),
            DynamicArray(Uint256::class.java, amountList.map { Uint256(it) })
        )
        val outputs: MutableList<TypeReference<*>> = ArrayList()
        val method = "addTradingCreditsToUser"
        val function = Function(method, inputs, outputs)
        val data = FunctionEncoder.encode(function)
        val credentials = Credentials.create(privateKey)
        val rawTransaction = RawTransaction.createTransaction(
            evmUtil.getNonce(credentials.address),
            gasPrice ?: evmUtil.getGasPrice(""),
            gasLimit.toBigInteger(),
            contractAddress,
            BigInteger.valueOf(0),
            data
        )
        val signMessage = TransactionEncoder.signMessage(rawTransaction, evmUtil.chainId, credentials)
        val toHexString = Numeric.toHexString(signMessage)
        val ethCall = evmUtil.web3j.ethSendRawTransaction(toHexString).sendAsync().get()
        if (ethCall.hasError()) {
            val errorMsg = ethCall.error.message
            // max fee per gas less than block base fee: address ******************************************, maxFeePerGas: 10000000 baseFee: 10041000
            if (errorMsg.startsWith("max fee per gas less than block base fee")) {
                val regex = Regex("baseFee:\\s*(\\d+)")
                regex.find(errorMsg)?.let {
                    val baseFee = it.groupValues[1].toBigInteger()
                    logger.debug("current baseFee: $baseFee")
                    val move = baseFee.toString(10).length - 2
                    val divisor = BigInteger.TEN.pow(move)
                    val newBaseFee = (baseFee.divide(divisor) + BigInteger.ONE).multiply(divisor)
                    logger.debug("new baseFee: $newBaseFee")
                    Thread.sleep(3000)

                    return settleTradingCredit(
                        contractAddress,
                        privateKey,
                        addressList,
                        amountList,
                        gasLimit,
                        newBaseFee
                    )
                }
            } else {
                logger.error("Error Message: ${ethCall.error.message}")
                throw Exception("Failed to send transaction")
            }
        }
        val txHash = ethCall.transactionHash
        logger.info("Contract: ${contractAddress}\tmethod: $method\t txid: $txHash")

        return txHash
    }

    /**
     * 结算空投首单永赚
     */
    fun settleAirdropebate(
        contractAddress: String,
        privateKey: String,
        addressList: List<String>,
        amountList: List<BigInteger>,
        gasLimit: Int,
        gasPrice: BigInteger? = null
    ): String {
        val inputs: List<Type<*>> = listOf(
            DynamicArray(Address::class.java, addressList.map { Address(it) }),
            DynamicArray(Uint256::class.java, amountList.map { Uint256(it) })
        )
        val outputs: MutableList<TypeReference<*>> = ArrayList()
        val method = "settle"
        val function = Function(method, inputs, outputs)
        val data = FunctionEncoder.encode(function)
        val credentials = Credentials.create(privateKey)
        val rawTransaction = RawTransaction.createTransaction(
            evmUtil.getNonce(credentials.address),
            gasPrice ?: evmUtil.getGasPrice(""),
            gasLimit.toBigInteger(),
            contractAddress,
            BigInteger.valueOf(0),
            data
        )
        val signMessage = TransactionEncoder.signMessage(rawTransaction, evmUtil.chainId, credentials)
        val toHexString = Numeric.toHexString(signMessage)
        val ethCall = evmUtil.web3j.ethSendRawTransaction(toHexString).sendAsync().get()
        if (ethCall.hasError()) {
            val errorMsg = ethCall.error.message
            // max fee per gas less than block base fee: address ******************************************, maxFeePerGas: 10000000 baseFee: 10041000
            if (errorMsg.startsWith("max fee per gas less than block base fee")) {
                val regex = Regex("baseFee:\\s*(\\d+)")
                regex.find(errorMsg)?.let {
                    val baseFee = it.groupValues[1].toBigInteger()
                    logger.debug("current baseFee: $baseFee")
                    val move = baseFee.toString(10).length - 2
                    val divisor = BigInteger.TEN.pow(move)
                    val newBaseFee = (baseFee.divide(divisor) + BigInteger.ONE).multiply(divisor)
                    logger.debug("new baseFee: $newBaseFee")
                    Thread.sleep(3000)

                    return settleAirdropebate(
                        contractAddress,
                        privateKey,
                        addressList,
                        amountList,
                        gasLimit,
                        newBaseFee
                    )
                }
            } else {
                logger.error("Error Message: ${ethCall.error.message}")
                throw Exception("Failed to send transaction")
            }
        }
        val txHash = ethCall.transactionHash
        logger.info("method: $method\t txid: $txHash")

        return txHash
    }

    /**
     * 结算空投首单永赚
     */
    fun settleArbAirdrop(
        contractAddress: String,
        privateKey: String,
        addressList: List<String>,
        gasLimit: Int,
        gasPrice: BigInteger? = null
    ): String {
        val inputs: List<Type<*>> = listOf(
            DynamicArray(Address::class.java, addressList.map { Address(it) })
        )
        val outputs: MutableList<TypeReference<*>> = ArrayList()
        val method = "settle"
        val function = Function(method, inputs, outputs)
        val data = FunctionEncoder.encode(function)
        val credentials = Credentials.create(privateKey)
        val rawTransaction = RawTransaction.createTransaction(
            evmUtil.getNonce(credentials.address),
            gasPrice ?: evmUtil.getGasPrice(""),
            gasLimit.toBigInteger(),
            contractAddress,
            BigInteger.valueOf(0),
            data
        )
        val signMessage = TransactionEncoder.signMessage(rawTransaction, evmUtil.chainId, credentials)
        val toHexString = Numeric.toHexString(signMessage)
        val ethCall = evmUtil.web3j.ethSendRawTransaction(toHexString).sendAsync().get()
        if (ethCall.hasError()) {
            val errorMsg = ethCall.error.message
            // max fee per gas less than block base fee: address ******************************************, maxFeePerGas: 10000000 baseFee: 10041000
            if (errorMsg.startsWith("max fee per gas less than block base fee")) {
                val regex = Regex("baseFee:\\s*(\\d+)")
                regex.find(errorMsg)?.let {
                    val baseFee = it.groupValues[1].toBigInteger()
                    logger.debug("current baseFee: $baseFee")
                    val move = baseFee.toString(10).length - 2
                    val divisor = BigInteger.TEN.pow(move)
                    val newBaseFee = (baseFee.divide(divisor) + BigInteger.ONE).multiply(divisor)
                    logger.debug("new baseFee: $newBaseFee")
                    Thread.sleep(3000)

                    return settleArbAirdrop(
                        contractAddress,
                        privateKey,
                        addressList,
                        gasLimit,
                        newBaseFee
                    )
                }
            } else {
                logger.error("Error Message: ${ethCall.error.message}")
                throw Exception("Failed to send transaction")
            }
        }
        val txHash = ethCall.transactionHash
        logger.info("method: $method\t txid: $txHash")

        return txHash
    }
}