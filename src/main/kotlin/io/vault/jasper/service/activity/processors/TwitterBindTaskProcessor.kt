package io.vault.jasper.service.activity.processors

import io.vault.jasper.model.User
import io.vault.jasper.model.activity.Task
import io.vault.jasper.model.activity.TaskType
import io.vault.jasper.service.activity.TaskProcessor
import org.springframework.stereotype.Component

/**
 * Processor for BIND_TWITTER_ACCOUNT task type
 */
@Component
class TwitterBindTaskProcessor : TaskProcessor {
    override fun getTaskType(): TaskType = TaskType.BIND_TWITTER_ACCOUNT
    
    override fun isTaskCompleted(user: User, task: Task, context: Map<String, Any>): Pair<Boolean, Map<String, Any>> {
        // Check if the user has bound a Twitter account
        val isCompleted = user.twitterAccountId != null
        
        val completionData = if (isCompleted) {
            mapOf(
                "twitterAccountId" to (user.twitterAccountId ?: ""),
                "completedAt" to System.currentTimeMillis().toString()
            )
        } else {
            emptyMap()
        }
        
        return Pair(isCompleted, completionData)
    }
}
