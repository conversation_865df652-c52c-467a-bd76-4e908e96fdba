package io.vault.jasper.model

import io.vault.jasper.enums.OptionDirection
import io.vault.jasper.enums.Symbol
import org.springframework.data.annotation.CreatedDate
import org.springframework.data.annotation.Id
import org.springframework.data.annotation.LastModifiedDate
import org.springframework.data.mongodb.core.index.Indexed
import org.springframework.data.mongodb.core.mapping.Document
import org.springframework.data.mongodb.core.mapping.Field
import java.math.BigDecimal
import java.time.LocalDateTime

@Document("gala_rebate_record")
data class GalaRebateRecord(
    @Id
    val id: String? = null,
    @Field("buyer_address")
    @Indexed(background = true)
    val buyerAddress: String,
    @Field("start_date")
    var startDate: LocalDateTime,
    @Field("end_date")
    var endDate: LocalDateTime,
    @Field("nft_id")
    @Indexed(background = true)
    var nftId: String,
    @Field("jump_gala_three_link_time")
    var jumpGalaThreeLinkTime: Long = 0L,
    var status: GalaRebateRecordStatus = GalaRebateRecordStatus.CREATED,
    @Field("settle_tx_id")
    var settleTxId: String? = null,
    @CreatedDate
    val created: LocalDateTime = LocalDateTime.now(),
    @LastModifiedDate
    val updated: LocalDateTime = LocalDateTime.now(),
    var errorMsg: String? = null,
) {}

enum class GalaRebateRecordStatus {
     CREATED, CLAIMED, PENDING, SETTLED, SETTLE_FAILED
}