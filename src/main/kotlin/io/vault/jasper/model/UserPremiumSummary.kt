package io.vault.jasper.model

import org.springframework.data.annotation.Id
import org.springframework.data.mongodb.core.index.Indexed
import org.springframework.data.mongodb.core.mapping.Document
import org.springframework.data.mongodb.core.mapping.Field
import org.springframework.data.mongodb.core.mapping.FieldType
import java.math.BigDecimal
import java.time.LocalDateTime

@Document
data class UserPremiumSummary(
    @Id
    val id: String? = null,
    @Indexed(background = true)
    val userId: String, // 用户ID
    @Indexed(background = true)
    val evmAddress: String, // 用户地址
    val registerTime: LocalDateTime, // 用户注册时间
    @Field(targetType = FieldType.DECIMAL128)
    var totalPremium: BigDecimal = BigDecimal.ZERO, // 总共支付的权利金
    @Field(targetType = FieldType.DECIMAL128)
    var totalVolume: BigDecimal = BigDecimal.ZERO, // 总共交易的期权量
)