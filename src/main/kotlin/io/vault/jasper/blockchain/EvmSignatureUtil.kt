package io.vault.jasper.blockchain

import org.slf4j.LoggerFactory
import org.springframework.stereotype.Service
import org.web3j.crypto.Hash
import org.web3j.crypto.Keys
import org.web3j.crypto.Sign
import org.web3j.utils.Numeric

@Service
class EvmSignatureUtil : ISignatureUtil {

    private val logger = LoggerFactory.getLogger(this::class.java)

    override fun getAddressFromSignature(plainText: ByteArray, signature: String): String {
        val chunkSize = 64
        val signDataList = Numeric.cleanHexPrefix(signature).chunked(chunkSize)
        val (r, s, v) = signDataList.map { Numeric.hexStringToByteArray(it) }.take(3)
        val sign = Sign.SignatureData(v, r, s)
        val publicKey = Sign.signedMessageHashToKey(plainText, sign)
        val address = Numeric.prependHexPrefix(Keys.getAddress(publicKey))
        logger.info("PublicKey=${Numeric.toHexStringNoPrefix(publicKey)}, Address=$address")

        return address
    }

    fun buildMessage(content: String): ByteArray {
        val prefix = "\u0019Ethereum Signed Message:\n"
        val message = prefix.toByteArray() + content.toByteArray()

        return Hash.sha3(message)
    }
}