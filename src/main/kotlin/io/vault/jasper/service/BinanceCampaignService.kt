package io.vault.jasper.service

import io.vault.jasper.enums.ChainType
import io.vault.jasper.enums.OptionDirection
import io.vault.jasper.enums.Symbol
import io.vault.jasper.model.*
import io.vault.jasper.repository.*
import org.slf4j.LoggerFactory
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.data.mongodb.core.MongoTemplate
import org.springframework.data.mongodb.core.query.Criteria
import org.springframework.data.mongodb.core.query.Query
import org.springframework.data.repository.findByIdOrNull
import org.springframework.stereotype.Service
import java.math.BigDecimal
import java.time.LocalDateTime
import java.util.*

@Service
class BinanceCampaignService @Autowired constructor(
    private val binanceTradeRebateRecordRepository: BinanceTradeRebateRecordRepository,
    private val binanceCampaignParameterRepository: BinanceCampaignParameterRepository,
    private val optionOrderRepository: OptionOrderRepository,
    private val userRepository: UserRepository,
    private val orderRepository: OrderRepository,
    private val discordLevelRepository: DiscordLevelRepository,
    private val currencyService: CurrencyService,
    private val mongoTemplate: MongoTemplate,
){

    private val logger = LoggerFactory.getLogger(this::class.java)

    /**
     * 获取空投参数对象
     */
    fun getCampaignParameter(): BinanceCampaignParameter {
        val params = binanceCampaignParameterRepository.findAll()

        return if (params.isEmpty()) {
            val p = BinanceCampaignParameter(null)
            binanceCampaignParameterRepository.save(p)
        } else {
            params.first()
        }
    }

    /**
     * 判断是否活动内首单
     */
    fun isFirstOrderInCampaign(
        optionOrder: OptionOrder
    ): Boolean{

        if(optionOrder.status != OptionStatus.SETTLED &&
            optionOrder.status != OptionStatus.EXECUTED &&
            optionOrder.status != OptionStatus.SETTLE_FAILED){
            return false
        }

        val parameter = getCampaignParameter()

        // 符合下单数量大于 0.01 BTC的订单
        if(optionOrder.bidAsset == null || optionOrder.bidAsset != Symbol.BTC){
            logger.info("BinanceCampaignService Service Not BTC ${optionOrder.buyer} ${optionOrder.id}")
            return false
        }

        if(optionOrder.bidAmount == null || optionOrder.bidAmount!! < parameter.firstTradeDegenQuantity){
            logger.info("BinanceCampaignService Service Not gt 0.01 BTC ${optionOrder.buyer} ${optionOrder.id}")
            return false
        }

        if(optionOrder.channel == UserChannel.MINI_APP){
            logger.info("BinanceCampaignService Service Is Mini App Order ${optionOrder.buyer} ${optionOrder.id}")
            return false
        }

        if(optionOrder.created.isBefore(parameter.startDate)){
            logger.info("BinanceCampaignService Service Order Before Start Date ${optionOrder.buyer} ${optionOrder.id} ${optionOrder.created}")
            return false
        }

        // 活动开始后的第一单 DEGEN ETH
//        val firstOptionOrder = optionOrderRepository.findFirstByChainAndBuyerIgnoreCaseAndChannelAndCreatedAfter(
//            ChainType.BITLAYER,
//            optionOrder.buyer!!,
//            UserChannel.JASPER_VAULT,
//            parameter.startDate
//        )
//
//        if(firstOptionOrder == null){
//            logger.info("BinanceCampaignService can not get option orders from db ${optionOrder.buyer} ${optionOrder.id}")
//            return false
//        }
//
//        if(firstOptionOrder.id!! != optionOrder.id!!){
//            logger.info("BinanceCampaignService first order id not match ${optionOrder.buyer} ${optionOrder.id}")
//            return false
//        }

        return true
    }

    fun createTradeRebateRecord(
        optionOrder: OptionOrder
    ){

        val parameter = getCampaignParameter()

        val now = LocalDateTime.now()
        if(now.isAfter(parameter.endDate)){
            logger.info("BinanceCampaignService first trade rebate task $now reach end date ${parameter.endDate}")
            return
        }

        // 是否已经创建了订单
        val existingRecord = binanceTradeRebateRecordRepository.findByOptionOrderId(
            optionOrder.id!!
        )

        if(existingRecord != null){
            logger.info("BinanceCampaignService Service Already add rebate record ${optionOrder.buyer} ${optionOrder.id}")
            return
        }

        // 是否有同样地址的订单
        val existingAddressRecords = binanceTradeRebateRecordRepository.findByBuyerAddressIgnoreCase(
            optionOrder.buyer!!
        )

        if(existingAddressRecords.isNotEmpty()){
            logger.info("BinanceCampaignService Service Already same address rebate record ${optionOrder.buyer} ${optionOrder.id}")
            return
        }

        if(!isFirstOrderInCampaign(optionOrder)){
            logger.info("BinanceCampaignService Service Not First Order ${optionOrder.buyer} ${optionOrder.id}")
            return
        }

        val record = BinanceTradeRebateRecord(
            optionOrderId = optionOrder.id,
            txHash = optionOrder.txHash!!,
            buyerAddress = optionOrder.buyer!!,
            direction = optionOrder.direction!!,
            bidAmount = optionOrder.bidAmount!!,
            expiryInHour = optionOrder.expiryInHour
        )

        binanceTradeRebateRecordRepository.save(record)
    }

    fun checkBinanceTradeRebate(
        record: BinanceTradeRebateRecord
    ){

        val optionOrder = optionOrderRepository.findByIdOrNull(record.optionOrderId)
        if(optionOrder == null){
            logger.info("BinanceCampaignService Service can not find order record ${record.buyerAddress} ${record.optionOrderId}")
            return
        }

        if(optionOrder.status == OptionStatus.SETTLE_FAILED){
            logger.info("BinanceCampaignService Service order settle failed ${optionOrder.buyer} ${optionOrder.id}")
            record.status = BinanceTradeRebateRecordStatus.SETTLE_FAILED
            binanceTradeRebateRecordRepository.save(record)
            return
        }

        if(optionOrder.status != OptionStatus.SETTLED){
            logger.info("BinanceCampaignService Service Not Settled order ${optionOrder.buyer} ${optionOrder.id}")
            return
        }

        val order = orderRepository.findByIdOrNull(optionOrder.orderId)
        if(order == null){
            logger.debug("BinanceCampaignService Service can not find order record ${optionOrder.buyer} ${optionOrder.id}")
            return
        }

        record.strikePrice = order.strikePrice.movePointLeft(18)
        record.settlementPrice = BigDecimal(optionOrder.marketPriceAtSettlement!!).movePointLeft(18)

        val usdtDecimal = currencyService.getCurrencyDecimal(
            ChainType.BITLAYER, Symbol.USDT
        )
        val ethDecimal = currencyService.getCurrencyDecimal(
            ChainType.BITLAYER, Symbol.BTC
        )

        record.premiumFee = optionOrder.premiumFeePay!!
        record.premiumAsset = Symbol.valueOf(optionOrder.premiumAsset!!.asset)

        record.premiumFeeInUsdt = optionOrder.premiumFeePayInUsdt!!

        record.profit = optionOrder.buyerProfit!!

        if(record.direction == OptionDirection.CALL){
            record.profitAsset = Symbol.BTC
            val readableProfit = record.profit.movePointLeft(ethDecimal)
            val readableUsdtProfit = readableProfit.multiply(record.settlementPrice).setScale(usdtDecimal, BigDecimal.ROUND_HALF_UP)
            record.profitInUsdt = readableUsdtProfit.movePointRight(usdtDecimal).setScale(0, BigDecimal.ROUND_HALF_UP)
        } else {
            record.profitAsset = Symbol.USDT
            record.profitInUsdt = optionOrder.buyerProfit!!
        }

        record.netProfit = record.profitInUsdt.subtract(record.premiumFeeInUsdt).setScale(0, BigDecimal.ROUND_HALF_UP)
        binanceTradeRebateRecordRepository.save(record)
    }

    fun getDiscordLevel(
        user: User
    ): Int {

        val discordId = user.discordInfo?.userId ?: return 0
        val level = discordLevelRepository.findByDiscordId(discordId)
        if(level == null){
            return 0
        }

        return level.level
    }

    fun getFirstTradeRebateCount(): Int {

        val criteria = Criteria()
        val query = Query(criteria)

        val total = mongoTemplate.count(query, BinanceTradeRebateRecord::class.java)
        return total.toInt()
    }

    fun getUnclaimedRecords(
        user: User
    ): List<BinanceTradeRebateRecord> {
        return binanceTradeRebateRecordRepository.findByBuyerAddressIgnoreCaseAndStatus(
            user.address,
            BinanceTradeRebateRecordStatus.CREATED
        )
    }
}