package io.vault.jasper.repository


import io.vault.jasper.model.*
import org.springframework.data.mongodb.repository.MongoRepository
import org.springframework.stereotype.Repository

@Repository
interface EchoooTradeRebateRecordRepository : MongoRepository<EchoooTradeRebateRecord, String> {
    fun findByOptionOrderId(optionOrderId: String): EchoooTradeRebateRecord?
    fun findTopByOrderByOptionOrderIdDesc(): EchoooTradeRebateRecord?
    fun findByStatus(
        status: EchoooTradeRebateRecordStatus
    ): List<EchoooTradeRebateRecord>

    fun findByBuyerAddress(
        buyerAddress: String
    ): List<EchoooTradeRebateRecord>

    fun findByBuyerAddressIgnoreCase(
        buyerAddress: String
    ): List<EchoooTradeRebateRecord>

    fun findByBuyerAddressIgnoreCaseAndStatus(
        buyerAddress: String,
        status: EchoooTradeRebateRecordStatus
    ): List<EchoooTradeRebateRecord>

    fun findByStatusAndSettleTxIdIsNotNull(
        status: EchoooTradeRebateRecordStatus
    ): List<EchoooTradeRebateRecord>

    fun findByStatusInAndSettleTxIdIsNotNull(
        statuses: List<EchoooTradeRebateRecordStatus>,
    ): List<EchoooTradeRebateRecord>

    fun findByStatusAndSettleTxIdIsNotNullAndSettled(
        status: EchoooTradeRebateRecordStatus,
        settled: Boolean
    ): List<EchoooTradeRebateRecord>
}