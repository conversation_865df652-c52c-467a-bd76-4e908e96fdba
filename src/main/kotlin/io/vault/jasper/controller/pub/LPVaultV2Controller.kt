package io.vault.jasper.controller.pub

import EVMAddress
import io.swagger.annotations.ApiImplicitParam
import io.swagger.annotations.ApiImplicitParams
import io.swagger.annotations.ApiOperation
import io.vault.jasper.ApiResponse
import io.vault.jasper.annotation.ApiCache
import io.vault.jasper.dto.ActivateLPDTO
import io.vault.jasper.dto.CreateLPVaultDTOV2
import io.vault.jasper.enums.ChainType
import io.vault.jasper.enums.OptionDirection
import io.vault.jasper.enums.Symbol
import io.vault.jasper.exception.BusinessException
import io.vault.jasper.exception.ResultEnum
import io.vault.jasper.model.*
import io.vault.jasper.repository.DegenConfigRepository
import io.vault.jasper.repository.LPVaultRepository
import io.vault.jasper.repository.OrderRepository
import io.vault.jasper.response.ExecutedOrderResponse
import io.vault.jasper.response.LPTransactionListResponse
import io.vault.jasper.response.LPVaultInfoResponse
import io.vault.jasper.response.PageResponse
import io.vault.jasper.service.CurrencyService
import io.vault.jasper.service.OptionOrderService
import io.vault.jasper.service.StoneService
import io.vault.jasper.service.SystemService
import io.vault.jasper.service.blockchain.BlockchainServiceFactory
import io.vault.jasper.service.blockchain.EvmService
import io.vault.jasper.utils.AuthUtil
import io.vault.jasper.utils.DateTimeUtil
import org.slf4j.LoggerFactory
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.data.domain.PageImpl
import org.springframework.data.domain.PageRequest
import org.springframework.data.domain.Sort
import org.springframework.data.mongodb.core.MongoTemplate
import org.springframework.data.mongodb.core.query.Criteria
import org.springframework.data.mongodb.core.query.Query
import org.springframework.web.bind.annotation.*
import java.math.BigDecimal
import javax.servlet.http.HttpServletRequest
import javax.validation.Valid


@RestController
@RequestMapping("/lpvault/v2")
class LPVaultV2Controller @Autowired constructor(
    private val authUtil: AuthUtil,
    private val lpVaultRepository: LPVaultRepository,
    private val systemService: SystemService,
    private val blockchainServiceFactory: BlockchainServiceFactory,
    private val currencyService: CurrencyService,
    private val optionOrderService: OptionOrderService,
    private val mongoTemplate: MongoTemplate,
    private val orderRepository: OrderRepository,
    private val degenConfigRepository: DegenConfigRepository,
    private val stoneService: StoneService
) {

    private var logger = LoggerFactory.getLogger(this::class.java)

    @ApiOperation("Authorise LP activation code")
    @CrossOrigin
    @PostMapping("/activate_lp")
    fun activateLP(
        @Valid @RequestBody dto: ActivateLPDTO,
        request: HttpServletRequest
    ): ApiResponse<Boolean> {
        authUtil.filterIPBlacklist(request)

        val lpVaultParameter = systemService.getLPVaultParameter()

        if(dto.activateCode != lpVaultParameter.activationCode){
            throw BusinessException(ResultEnum.INVALID_LP_ACTIVATE_CODE)
        }

        return ApiResponse.success(true)
    }

    @ApiOperation("LP Vault Supported Token")
    @CrossOrigin
    @ApiCache(key = "lpvault:v2:supported_tokens", expire = 300)
    @GetMapping("/supported_tokens")
    fun supportedTokens(
        request: HttpServletRequest
    ): ApiResponse<List<Symbol>> {
        authUtil.filterIPBlacklist(request)

        val list = mutableListOf<Symbol>()
        degenConfigRepository.findAll().forEach {
            list.add(it.bidAsset)
        }

        return ApiResponse.success(list)
    }

    @ApiOperation("Create LP Vault")
    @CrossOrigin
    @PostMapping("/create_lp_vault")
    fun createLPVault(
        request: HttpServletRequest,
        @Valid @RequestBody dto: CreateLPVaultDTOV2
    ): ApiResponse<String> {
        authUtil.filterIPBlacklist(request)
        authUtil.auth(request, dto.address)

        val vault = lpVaultRepository.save(
            LPVault(
                lpEoaAddress = dto.address,
                optionType = dto.optionDirection,
                optionSymbol = dto.optionSymbol.name,
                status = LPVaultStatus.DONE,
                chain = dto.chain,
                permissionType = dto.permissionType,
                expiryInHour = BigDecimal(dto.optionsHours),
                name = dto.vaultName,
                premiumOracleType = dto.premiumOracleType,
                poolAsset = dto.optionSymbol,
                feeTier = BigDecimal(dto.feeTier),
                premium = BigDecimal(dto.premium),
                premiumRates = BigDecimal(dto.premiumRate),
                premiumFloor = BigDecimal(dto.premiumFloor)
            )
        )

        return ApiResponse.success(vault.id!!)
    }

    @ApiOperation("LP Vault 订单成交记录")
    @ApiImplicitParams(
        ApiImplicitParam(name = "address", value = "卖方EOA钱包地址", dataType = "String", paramType = "query", required = true, allowMultiple = false),
        ApiImplicitParam(name = "lpVaultAddress", value = "卖方LPVault地址", dataType = "String", paramType = "query", required = true, allowMultiple = false),
        ApiImplicitParam(name = "startTime", value = "订单下单时间晚于某个时间，startTime和endTime必须同时传入，秒为单位", dataType = "String", paramType = "query", required = false, allowMultiple = false),
        ApiImplicitParam(name = "endTime", value = "订单下单时间早于某个时间，startTime和endTime必须同时传入，秒为单位", dataType = "String", paramType = "query", required = false, allowMultiple = false),
    )
    @CrossOrigin
    @ApiCache(
        key = "lpvault:v2:activity_list",
        dynamicKey = ["address", "lpVaultAddress", "chain", "startTime", "endTime", "direction", "orderType", "page", "pageSize"],
        expire = 10
    )
    @GetMapping("/activity_list")
    fun activityList(
        @RequestParam(required = true) @EVMAddress address: String,
        @RequestParam(required = true) @EVMAddress lpVaultAddress: String,
        @RequestParam(required = true) chain: ChainType,
        @RequestParam(required = false) startTime: Long?,
        @RequestParam(required = false) endTime: Long?,
        @RequestParam(required = false) direction: OptionDirection?, // CALL or PUT
        @RequestParam(required = false) orderType: OrderType?, // DEGEN or SWAP
        @RequestParam(required = false) page: Int?,
        @RequestParam(required = false) pageSize: Int?,
        request: HttpServletRequest
    ): ApiResponse<PageResponse<ExecutedOrderResponse>> {
        authUtil.filterIPBlacklist(request)
        val p = page ?: 1
        val ps = pageSize ?: 50
        val pageable = PageRequest.of(p - 1, ps, Sort.by(Sort.Order.desc(OptionOrder::id.name)))

        val query = Query()
        query.addCriteria(Criteria.where(OptionOrder::chain.name).`is`(chain))
        query.addCriteria(Criteria.where(OptionOrder::sellerVault.name).`is`(lpVaultAddress))
        query.addCriteria(Criteria.where(OptionOrder::seller.name).`is`(address))

        if (direction != null) {
            query.addCriteria(Criteria.where(OptionOrder::direction.name).`is`(direction))
        }

        if (orderType != null) {
            query.addCriteria(Criteria.where(OptionOrder::orderType.name).`is`(orderType))
        }

        if(startTime != null && endTime != null){
            query.addCriteria(
                Criteria.where(OptionOrder::created.name).gte(DateTimeUtil.convertTimestampToLocalDateTime(startTime))
                    .lt(DateTimeUtil.convertTimestampToLocalDateTime(endTime))
            )
        }

        val total = mongoTemplate.count(query, OptionOrder::class.java)
        query.with(pageable)
        val resultSet = mongoTemplate.find(query, OptionOrder::class.java)
        val optionsPage = PageImpl(resultSet, pageable, total)

        val optionsList = optionsPage.content.mapNotNull {
            //val order = orderRepository.findByIdOrNull(it.orderId) ?: return@mapNotNull null

            var lockDateTimeStamp = DateTimeUtil.convertLocalDateTimeToTimestamp(it.expiryDate)
            if(it.lockDate != null){
                lockDateTimeStamp = DateTimeUtil.convertLocalDateTimeToTimestamp(it.lockDate)
            }

            val stonePieces = stoneService.getNftIdFromOptionOrder(it)
            ExecutedOrderResponse(
                id = it.id!!,
                orderId = it.orderId,
                orderType = it.orderType,
                chain = it.chain,
                onChainOrderId = it.onChainOrderId,
                buyerAddress = it.buyer,
                buyerVault = it.buyerVault,
                buyerVaultSalt = it.buyerVaultSalt,
                sellerAddress = it.seller,
                businessType = it.direction!!,

                bidAsset = it.bidAsset,
                bidAmount = it.bidAmount?.stripTrailingZeros()?.toPlainString(),

                underlyingAsset = it.underlyingAsset,
                underlyingAssetAddress = it.underlyingAssetAddress,
                underlyingAssetAmount = it.amount.stripTrailingZeros().toPlainString(),

                strikeAsset = it.strikeAsset,
                strikeAmount = it.strikeAmount.stripTrailingZeros().toPlainString(),
                strikePrice = it.strikePrice!!.stripTrailingZeros().toPlainString(),
                actualStrikeAmount = it.actualStrikeAmount?.stripTrailingZeros()?.toPlainString(),

                premiumFeesAsset = if (it.premiumAsset?.asset != null) Symbol.valueOf(it.premiumAsset!!.asset) else Symbol.USDT,
                premiumFee = it.premiumFeePay?.stripTrailingZeros()?.toPlainString(),
                premiumFeeInUsdt = it.premiumFeePayInUsdt?.stripTrailingZeros()?.toPlainString(),
                premiumFeeShouldPay = it.premiumFeeShouldPay?.stripTrailingZeros()?.toPlainString(),
                premiumFeeShouldPayInUsdt = it.premiumFeeShouldPayInUsdt?.stripTrailingZeros()?.toPlainString(),

                createDate = DateTimeUtil.convertLocalDateTimeToTimestamp(it.created),
                expiryDate = DateTimeUtil.convertLocalDateTimeToTimestamp(it.expiryDate),
                lockDate = lockDateTimeStamp,
                status = it.status,
                degen = it.jvault,
                marketPriceAtSettlement = it.marketPriceAtSettlement,
                settlementHash = it.settlementHash,
                buyerProfit = it.buyerProfit?.stripTrailingZeros()?.toPlainString(),
                expiryInHour = it.expiryInHour,
                usedMoonlightBox = it.usedMoonlightBox,
                usedRealityStone = it.usedRealityStone ?: false,
                usedPowerStone = it.usedPowerStone ?: false,
                usedSpaceStone = it.usedSpaceStone ?: false,
                usedTimeStone = it.usedTimeStone ?: false,
                stoneActivityNftId = it.stoneActivityNftId,
                txHash = it.txHash,
                premiumFeeLog = it.premiumFeeLog,
                awardStonePieceNftIds = stonePieces
            )
        }

        return ApiResponse.success(
            PageResponse(
                total = optionsPage.totalElements,
                page = p,
                pageSize = ps,
                totalPage = optionsPage.totalPages,
                hasNext = optionsPage.hasNext(),
                data = optionsList
            )
        )
    }

    @ApiOperation("LP Vault 链上交易记录(Deposit / Withdraw)")
    @ApiImplicitParams(
        ApiImplicitParam(name = "address", value = "卖方EOA钱包地址", dataType = "String", paramType = "query", required = true, allowMultiple = false),
        ApiImplicitParam(name = "lpVaultAddress", value = "卖方LPVault地址", dataType = "String", paramType = "query", required = true, allowMultiple = false),
        ApiImplicitParam(name = "startTime", value = "链上交易时间晚于某个时间，startTime和endTime必须同时传入，秒为单位", dataType = "String", paramType = "query", required = false, allowMultiple = false),
        ApiImplicitParam(name = "endTime", value = "链上交易时间早于某个时间，startTime和endTime必须同时传入，秒为单位", dataType = "String", paramType = "query", required = false, allowMultiple = false),
    )
    @CrossOrigin
    @ApiCache(
        key = "lpvault:v2:transaction_list",
        dynamicKey = ["address", "lpVaultAddress", "chain", "startTime", "endTime", "page", "pageSize"],
        expire = 10
    )
    @GetMapping("/transaction_list")
    fun transactionList(
        @RequestParam(required = true) @EVMAddress address: String,
        @RequestParam(required = true) @EVMAddress lpVaultAddress: String,
        @RequestParam(required = true) chain: ChainType,
        @RequestParam(required = false) startTime: Long?,
        @RequestParam(required = false) endTime: Long?,
        @RequestParam(required = false) page: Int?,
        @RequestParam(required = false) pageSize: Int?,
        request: HttpServletRequest
    ): ApiResponse<PageResponse<LPTransactionListResponse>> {
        authUtil.filterIPBlacklist(request)
        val p = page ?: 1
        val ps = pageSize ?: 50
        val pageable = PageRequest.of(p - 1, ps, Sort.by(Sort.Order.desc(OptionOrder::id.name)))

        val query = Query()
        query.addCriteria(Criteria.where(LpVaultRecord::chain.name).`is`(chain))
        query.addCriteria(Criteria.where(LpVaultRecord::from.name).`is`(address))

        if(startTime != null && endTime != null){
            query.addCriteria(
                Criteria.where(LpVaultRecord::blockTime.name).gte(DateTimeUtil.convertTimestampToLocalDateTime(startTime))
                    .lt(DateTimeUtil.convertTimestampToLocalDateTime(endTime))
            )
        }

        val total = mongoTemplate.count(query, LpVaultRecord::class.java)
        query.with(pageable)
        val resultSet = mongoTemplate.find(query, LpVaultRecord::class.java)
        val optionsPage = PageImpl(resultSet, pageable, total)

        val optionsList = optionsPage.content.mapNotNull {
            LPTransactionListResponse(
                time = DateTimeUtil.convertLocalDateTimeToTimestamp(it.blockTime),
                tokenValue = it.lpAmount.toString(),
                tokenAmount = it.assetAmount.toString(),
                wallet = it.from!!
            )
        }

        return ApiResponse.success(
            PageResponse(
                total = optionsPage.totalElements,
                page = p,
                pageSize = ps,
                totalPage = optionsPage.totalPages,
                hasNext = optionsPage.hasNext(),
                data = optionsList
            )
        )
    }

    @ApiOperation("Query lp vault list")
    @ApiImplicitParams(
        ApiImplicitParam(name = "address", value = "卖方EOA钱包地址", dataType = "String", paramType = "query", required = false, allowMultiple = false),
        ApiImplicitParam(name = "orderBy", value = "按照什么参数排序", dataType = "String", paramType = "query", required = false, allowMultiple = false),
        ApiImplicitParam(name = "orderAsc", value = "排序方式", dataType = "Boolean", paramType = "query", required = false, allowMultiple = false),
    )
    @CrossOrigin
    @ApiCache(
        key = "lpvault:v2:lp_vault_list",
        dynamicKey = ["address", "chain", "symbol", "direction", "orderBy", "orderAsc", "page", "pageSize"],
        expire = 10
    )
    @GetMapping("/lp_vault_list")
    fun lpVaultList(
        @RequestParam(required = false) @EVMAddress address: String?, // EOA Address
        @RequestParam(required = false) chain: ChainType?,
        @RequestParam(required = false) symbol: Symbol?, // 期权币种
        @RequestParam(required = false) direction: OptionDirection?, // Sell CALL or Sell PUT
        @RequestParam(required = false) orderBy: String?,  // 按照什么参数排序
        @RequestParam(required = false) orderAsc: Boolean?, // 排序方式
        @RequestParam(required = false) page: Int?,
        @RequestParam(required = false) pageSize: Int?,
        request: HttpServletRequest
    ): ApiResponse<PageResponse<LPVaultInfoResponse>> {
        authUtil.filterIPBlacklist(request)
        val p = page ?: 1
        val ps = pageSize ?: 50

        var orderField = LPVault::id.name
        if(orderBy != null){
            orderField = orderBy
        }

        val order = if(orderAsc == null || orderAsc){
            Sort.Order.asc(orderField)
        } else {
            Sort.Order.desc(orderField)
        }

        val pageable = PageRequest.of(p - 1, ps, Sort.by(order))

        val query = Query()

        if(address != null){
            query.addCriteria(Criteria.where(LPVault::lpEoaAddress.name).`is`(address))
        }

        if(chain != null){
            query.addCriteria(Criteria.where(LPVault::chain.name).`is`(chain))
        }

        if(symbol != null){
            query.addCriteria(Criteria.where(LPVault::optionSymbol.name).`is`(symbol))
        }

        if(direction != null){
            query.addCriteria(Criteria.where(LPVault::optionType.name).`is`(direction))
        }

        val total = mongoTemplate.count(query, LPVault::class.java)
        query.with(pageable)
        val resultSet = mongoTemplate.find(query, LPVault::class.java)
        val optionsPage = PageImpl(resultSet, pageable, total)

        val chainType = chain?: ChainType.ARBITRUM
        val evmService = blockchainServiceFactory.getBlockchainService(chainType) as EvmService
        val usdtContract = currencyService.getCurrencyContract(
            chainType,
            Symbol.USDT
        )

        val resultList = mutableListOf<LPVaultInfoResponse>()
        optionsPage.content.mapNotNull {

            val vaultAddress = it.lpVaultAddress!!
            val optionDirection = it.optionType
            val optionSymbol = it.optionSymbol

            var lockSymbol = optionSymbol
            if(optionDirection == OptionDirection.PUT){
                lockSymbol = Symbol.USDT.name
            }

            val lockContract = currencyService.getCurrencyContract(
                chainType,
                Symbol.valueOf(lockSymbol)
            )

            val lockSymbolBalance = evmService.evmUtil.getBalance(
                vaultAddress,
                lockContract
            )

            val openInterest = optionOrderService.getSellerOpenInterest(
                chainType,
                vaultAddress,
                Symbol.valueOf(lockSymbol)
            )

            val totalAmount = lockSymbolBalance.add(openInterest)

            val usdtBalance = evmService.evmUtil.getBalance(
                vaultAddress,
                usdtContract
            )

            val premiumEarn = optionOrderService.getSellerPremiumEarn(
                chainType,
                vaultAddress
            )

            val tradingVolume = optionOrderService.getSellerOptionVolumeTotal(
                chainType,
                vaultAddress
            )

            val infoResponse = LPVaultInfoResponse(
                vaultAddress,
                Symbol.valueOf(optionSymbol),
                optionDirection,
                Symbol.valueOf(lockSymbol),
                lockSymbolBalance.stripTrailingZeros().toPlainString(),
                "0",
                openInterest.stripTrailingZeros().toPlainString(),
                totalAmount.stripTrailingZeros().toPlainString(),
                usdtBalance.stripTrailingZeros().toPlainString(),
                premiumEarn.stripTrailingZeros().toPlainString(),
                tradingVolume.stripTrailingZeros().toPlainString(),
                DateTimeUtil.convertLocalDateTimeToTimestamp(it.created),
                expiryInHour = it.expiryInHour.stripTrailingZeros().toPlainString(),
                regularPool = it.regularPool
            )

            resultList.add(infoResponse)
        }

        return ApiResponse.success(
            PageResponse(
                total = optionsPage.totalElements,
                page = p,
                pageSize = ps,
                totalPage = optionsPage.totalPages,
                hasNext = optionsPage.hasNext(),
                data = resultList
            )
        )
    }
}