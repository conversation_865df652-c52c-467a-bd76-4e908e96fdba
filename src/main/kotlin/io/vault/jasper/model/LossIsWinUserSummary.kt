package io.vault.jasper.model

import org.springframework.data.annotation.Id
import org.springframework.data.annotation.Version
import org.springframework.data.mongodb.core.index.Indexed
import org.springframework.data.mongodb.core.mapping.Document
import org.springframework.data.mongodb.core.mapping.Field
import org.springframework.data.mongodb.core.mapping.FieldType
import java.math.BigDecimal

/**
 * Loss is Win用户汇总数据实体类
 * 用于存储用户在Loss is Win活动中的参与数据统计
 */
@Document("loss_is_win_user_summary")
data class LossIsWinUserSummary(

    @Id
    val id: String? = null,

    /**
     * 用户钱包地址
     * 使用唯一索引确保每个地址只有一条记录
     */
    @Field("address")
    @Indexed(unique = true)
    val address: String,

    /**
     * 用户参与的交易次数
     */
    @Field("transaction_count")
    var transactionCount: Int = 0,

    /**
     * 用户累计获得的BTR代币数量
     */
    @Field("total_btr_earned", targetType = FieldType.DECIMAL128)
    @Indexed
    var totalBtrEarned: BigDecimal = BigDecimal.ZERO,

    /**
     * 用户累计亏损金额
     */
    @Field("total_loss", targetType = FieldType.DECIMAL128)
    @Indexed
    var totalLoss: BigDecimal = BigDecimal.ZERO,

    /**
     * 版本号，用于乐观锁控制
     */
    @Version
    var version: Long? = null
)