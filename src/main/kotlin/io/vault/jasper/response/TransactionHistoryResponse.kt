package io.vault.jasper.response

import io.swagger.annotations.ApiModel
import io.swagger.annotations.ApiModelProperty
import io.vault.jasper.enums.ChainType
import io.vault.jasper.enums.OptionDirection
import io.vault.jasper.enums.Symbol
import io.vault.jasper.model.OptionStatus

@ApiModel
data class TransactionHistoryResponse(
    @ApiModelProperty("挂单ID", required = true)
    val id: String,
    @ApiModelProperty("链类型", required = true)
    val chain: ChainType,
    @ApiModelProperty("买家用户地址", required = true)
    val creatorAddress: String,
    @ApiModelProperty("买家Vault地址", required = true)
    val creatorVaultAddress: String,
    @ApiModelProperty("卖家地址", required = true)
    val sellerAddress: String,
    @ApiModelProperty("卖家Vault地址", required = true)
    val sellerVaultAddress: String,
    @ApiModelProperty("订单方向", required = true)
    val businessType: OptionDirection,

    /**
     * 标的信息
     */
    @ApiModelProperty("标的资产", required = false)
    val bidAsset: Symbol?,
    @ApiModelProperty("标的资产数量", required = false)
    val bidAmount: String?,

    /**
     * 抵押信息
     */
    @ApiModelProperty("抵押资产", required = true)
    val underlyingAsset: Symbol,
    @ApiModelProperty("抵押资产合约地址(可为空)", required = false)
    val underlyingAssetAddress: String?,
    @ApiModelProperty("抵押资产数量", required = true)
    val underlyingAssetAmount: String,

    /**
     * 行权信息
     */
    @ApiModelProperty("行权资产", required = true)
    val strikeAsset: Symbol,
    @ApiModelProperty("行权数量", required = true)
    val strikeAmount: String,
    @ApiModelProperty("行权价格", required = true)
    val strikePrice: String,

    /**
     * 权利金信息
     */
    @ApiModelProperty("权利金资产", required = true)
    val premiumFeesAsset: Symbol?,
    @ApiModelProperty("权利金", required = true)
    val premiumFee: String?,

    /**
     * 挂单信息
     */
    @ApiModelProperty("挂单时间", required = true)
    val createDate: Long,
    @ApiModelProperty("合约到期日", required = true)
    val expiryDate: Long,
    @ApiModelProperty("订单状态", required = true)
    val status: OptionStatus,
    @ApiModelProperty("是否极速订单", required = true)
    val degen: Boolean,
    @ApiModelProperty("0DTE时间限制", required = true)
    val expiryInHour: String? = "2",
)
