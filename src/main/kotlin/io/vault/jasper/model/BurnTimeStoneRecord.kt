package io.vault.jasper.model

import io.vault.jasper.enums.ChainType
import org.springframework.data.annotation.Id
import org.springframework.data.annotation.LastModifiedDate
import org.springframework.data.mongodb.core.index.Indexed
import org.springframework.data.mongodb.core.mapping.Document
import org.springframework.data.mongodb.core.mapping.Field
import java.time.LocalDateTime

@Document("burn_time_stone_record")
data class BurnTimeStoneRecord(
    @Id
    val id: String? = null,
    @Field("option_order_id")
    @Indexed(unique = true)
    var optionOrderId: String,
    var chain: ChainType,
    var buyer: String,
    @Field("nft_id")
    var nftId: String,
    @Field("tx_hash")
    var txHash: String,
    @Field("tx_settled")
    var txSettled: Boolean = false,
    var created: LocalDateTime = LocalDateTime.now(),
    @LastModifiedDate
    var updated: LocalDateTime = LocalDateTime.now()
)