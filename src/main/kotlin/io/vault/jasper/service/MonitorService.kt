package io.vault.jasper.service

import io.vault.jasper.task.*
import org.slf4j.LoggerFactory
import org.springframework.beans.factory.annotation.Value
import org.springframework.stereotype.Service
import org.springframework.web.client.RestTemplate

@Service
class MonitorService {
    private val logger = LoggerFactory.getLogger(this::class.java)

    private val restTemplate = RestTemplate()

    @Value("\${monitor_switch}")
    private lateinit var monitorSwitch: String

    private val taskMonitorUrl = mapOf(
        ArbitrumBlockScanner::class.simpleName to "https://uptime.betterstack.com/api/v1/heartbeat/************************",
        PendingExecutedOrders::class.simpleName to "https://uptime.betterstack.com/api/v1/heartbeat/************************",
        EvmSettlement::class.simpleName to "https://uptime.betterstack.com/api/v1/heartbeat/************************",
        UpdateOptionsProfit::class.simpleName to "https://uptime.betterstack.com/api/v1/heartbeat/************************",
    )

    fun taskMonitor(taskName: String?) {
        if (monitorSwitch == "false") return
        val url = taskMonitorUrl[taskName] ?: kotlin.run {
            //logger.warn("Task monitor: Task name not found: $taskName")
            return
        }
        val response = restTemplate.getForObject(url, String::class.java)
        //logger.info("Task[$taskName] monitor: Url: $url response: $response")
    }
}