package io.vault.jasper.dto

import EVMAddress
import io.swagger.annotations.ApiModel
import io.swagger.annotations.ApiModelProperty
import io.vault.jasper.enums.ChainType
import io.vault.jasper.enums.OptionDirection
import io.vault.jasper.enums.Symbol
import io.vault.jasper.model.activity.TaskType

@ApiModel("完成任务DTO")
data class CampaignCompleteTaskDTO(
    @ApiModelProperty("钱包地址", required = true)
    @field:EVMAddress
    val address: String,
    @ApiModelProperty("任务id", required = true)
    val taskId: String,
    @ApiModelProperty("活动id", required = true)
    val campaignId: String
)
