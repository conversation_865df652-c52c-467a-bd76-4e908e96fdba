package io.vault.jasper.task

import io.vault.jasper.enums.Symbol
import io.vault.jasper.exception.BusinessException
import io.vault.jasper.model.*
import io.vault.jasper.repository.*
import io.vault.jasper.service.*
import io.vault.jasper.service.blockchain.BlockchainServiceFactory
import io.vault.jasper.service.blockchain.EvmService
import org.slf4j.LoggerFactory
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.beans.factory.annotation.Value
import org.springframework.context.annotation.Profile
import org.springframework.data.mongodb.core.MongoTemplate
import org.springframework.data.mongodb.core.query.Criteria
import org.springframework.data.mongodb.core.query.Query
import org.springframework.data.repository.findByIdOrNull
import org.springframework.scheduling.annotation.Scheduled
import org.springframework.stereotype.Service
import java.math.BigDecimal
import java.time.LocalDateTime


@Service
@Profile("prod")
class PushJasperQuantDataTask @Autowired constructor(
    private val userNetworkPushDataService: UserNetworkPushDataService,
    private val mongoTemplate: MongoTemplate,
    private val userNetworkService: UserNetworkService,
    private val userNetworkPushDataTaskParameterRepository: UserNetworkPushDataTaskParameterRepository
) {

    private val logger = LoggerFactory.getLogger(this::class.java)

    private val JASPER_QUANT_WUJIE = "0x83DF61B43a6d7f7501F62B9b53B21FCb44cE35d7"

    private val batchCount = 100

    @Value("\${schedule_task_on}")
    lateinit var scheduleOn: String

    @Scheduled(fixedDelay = 60 * 1000, initialDelay = 5 * 1000)
    fun run() {
        if(scheduleOn == "false"){
            return
        }

        try{
            handleUserNetworkData()
        } catch (e: Exception) {
            logger.error(e.message, e)
        }

        try{
            handleUserNetworkChangeLogData()
        } catch (e: Exception) {
            logger.error(e.message, e)
        }

        try{
            handleTransactionRecordData()
        } catch (e: Exception) {
            logger.error(e.message, e)
        }

        try{
            handleRebateRecordData()
        } catch (e: Exception) {
            logger.error(e.message, e)
        }

        try{
            handleUserPointData()
        } catch (e: Exception) {
            logger.error(e.message, e)
        }

        try{
            pushDataToJasperQuant()
        } catch (e: Exception) {
            logger.error(e.message, e)
        }
    }

    private fun handleUserNetworkData() {

        val taskParameter = userNetworkPushDataService.getTaskParameter()
        if(taskParameter.prepareDataSwitch == false){
            return
        }

        val query = Query()

        if(taskParameter.userNetworkLastPushTime != null) {
            query.addCriteria(Criteria.where(UserNetwork::created.name).gt(taskParameter.userNetworkLastPushTime!!))
        }

        query.limit(batchCount)
        val results: List<UserNetwork> = mongoTemplate.find(query, UserNetwork::class.java)

        //logger.info("Push Jasper Quant Data for ${results.size} user network records")

        results.forEach { userNetwork ->
            try {
                val userAddress = userNetwork.address

                if(isJasperQuantUser(userAddress)) {
                    userNetworkPushDataService.handleUserNetworkData(userNetwork)
                }

            } catch (e: Exception) {
                logger.error("Failed to handleUserNetworkData: ${userNetwork.id}", e)
            }

            taskParameter.userNetworkLastPushTime = userNetwork.created
        }

        userNetworkPushDataTaskParameterRepository.save(taskParameter)
    }

    private fun handleUserNetworkChangeLogData() {

        val taskParameter = userNetworkPushDataService.getTaskParameter()
        if(taskParameter.prepareDataSwitch == false){
            return
        }

        val query = Query()

        if(taskParameter.userCommissionLastPushTime != null) {
            query.addCriteria(Criteria.where(UserNetworkChangeRateLog::created.name).gt(taskParameter.userCommissionLastPushTime!!))
        }

        query.limit(batchCount)
        val results: List<UserNetworkChangeRateLog> = mongoTemplate.find(query, UserNetworkChangeRateLog::class.java)

        //logger.info("Push Jasper Quant Data for ${results.size} UserNetworkChangeRateLog")

        results.forEach { changeLog ->
            try {
                val userAddress = changeLog.address

                if(isJasperQuantUser(userAddress)) {
                    userNetworkPushDataService.handleCommissionRateLog(changeLog)
                }

            } catch (e: Exception) {
                logger.error("Failed to handleUserNetworkChangeLogData: ${changeLog.id}", e)
            }

            taskParameter.userCommissionLastPushTime = changeLog.created
        }

        userNetworkPushDataTaskParameterRepository.save(taskParameter)
    }

    private fun handleTransactionRecordData() {

        val taskParameter = userNetworkPushDataService.getTaskParameter()
        if(taskParameter.prepareDataSwitch == false){
            return
        }

        val endTime = LocalDateTime.now().minusMinutes(5)

        val query = Query()
        query.addCriteria(Criteria.where(OptionOrder::expiryDate.name).gt(taskParameter.userNetworkTransactionLastPushTime).lt(endTime))
        query.addCriteria(Criteria.where(OptionOrder::status.name).`is`(OptionStatus.SETTLED))

        query.limit(batchCount)
        val results: List<OptionOrder> = mongoTemplate.find(query, OptionOrder::class.java)

        //logger.info("Push Jasper Quant Data for ${results.size} OptionOrder")

        results.forEach { oo ->
            try {
                val userAddress = oo.buyer!!

                if(isJasperQuantUser(userAddress)) {
                    userNetworkPushDataService.handleTransactionRecord(oo)
                }

            } catch (e: Exception) {
                logger.error("Failed to handleTransactionRecordData: ${oo.id}", e)
            }

            taskParameter.userNetworkTransactionLastPushTime = oo.expiryDate
        }

        userNetworkPushDataTaskParameterRepository.save(taskParameter)
    }

    private fun handleRebateRecordData() {

        val taskParameter = userNetworkPushDataService.getTaskParameter()
        if(taskParameter.prepareDataSwitch == false){
            return
        }

        val query = Query()

        if(taskParameter.userNetworkRebateRecordLastPushTime != null) {
            query.addCriteria(Criteria.where(UserNetworkRebateRecord::created.name).gt(taskParameter.userNetworkRebateRecordLastPushTime!!))
        }

        query.limit(batchCount)
        val results: List<UserNetworkRebateRecord> = mongoTemplate.find(query, UserNetworkRebateRecord::class.java)

        //logger.info("Push Jasper Quant Data for ${results.size} UserNetworkRebateRecord")

        results.forEach { rebateRecord ->
            try {
                val userAddress = rebateRecord.userAddress

                if(isJasperQuantUser(userAddress)) {
                    userNetworkPushDataService.handleRebateRecord(rebateRecord)
                }

            } catch (e: Exception) {
                logger.error("Failed to handleRebateRecordData: ${rebateRecord.id}", e)
            }

            taskParameter.userNetworkRebateRecordLastPushTime = rebateRecord.created
        }

        userNetworkPushDataTaskParameterRepository.save(taskParameter)
    }

    private fun handleUserPointData() {

        val taskParameter = userNetworkPushDataService.getTaskParameter()
        if(taskParameter.prepareDataSwitch == false){
            return
        }

        val query = Query()
        query.addCriteria(Criteria.where(UserPointRecord::created.name).gt(taskParameter.userNetworkPointRecordLastPushTime!!))

        query.limit(batchCount)
        val results: List<UserPointRecord> = mongoTemplate.find(query, UserPointRecord::class.java)

        //logger.info("Push Jasper Quant Data for ${results.size} User Point Records")

        results.forEach { pointRecord ->
            try {
                val userAddress = pointRecord.address

                if(isJasperQuantUser(userAddress)) {
                    userNetworkPushDataService.handlePointRecord(pointRecord)
                }

            } catch (e: Exception) {
                logger.error("Failed to handleUserPointData: ${pointRecord.id}", e)
            }

            taskParameter.userNetworkPointRecordLastPushTime = pointRecord.created
        }

        userNetworkPushDataTaskParameterRepository.save(taskParameter)
    }

    fun isJasperQuantUser(address: String): Boolean {

        try{
            val result = userNetworkService.isAncestor(
                JASPER_QUANT_WUJIE,
                address,
            )

            return result

        } catch (e: Exception) {
            //logger.error("Failed to check isJasperQuantUser: $address", e)
            return false
        }
    }

    private fun pushDataToJasperQuant() {

        val taskParameter = userNetworkPushDataService.getTaskParameter()
        if(taskParameter.pushDataSwitch == false){
            return
        }

        val host = taskParameter.jasperQuantHost

        val pushDataTypes = listOf(
            UserNetworkPushDataPath.USER_INFO,
            UserNetworkPushDataPath.USER_RATE_CHANGE_LOG,
            UserNetworkPushDataPath.USER_NETWORK_TRANSACTION,
            UserNetworkPushDataPath.USER_NETWORK_REBATE_RECORD,
            UserNetworkPushDataPath.USER_NETWORK_POINT_RECORD
        )

        for(pushDataType in pushDataTypes) {
            try {
                val url = "$host${pushDataType.path}"

                val query = Query()
                query.addCriteria(Criteria.where(UserNetworkPushData::path.name).`is`(pushDataType))
                query.addCriteria(Criteria.where(UserNetworkPushData::pushed.name).`is`(false))
                query.limit(batchCount)

                val results: List<UserNetworkPushData> = mongoTemplate.find(query, UserNetworkPushData::class.java)

                //logger.info("Push Jasper Quant Data for ${results.size} $pushDataType records")

                if(results.isNotEmpty()) {
                    val response = userNetworkPushDataService.sendJasperQuantData(results, url)
                    //logger.info("Push Jasper Quant Data for $pushDataType: $response")
                }
            } catch (e: Exception) {
                logger.error("Failed to pushDataToJasperQuant: $pushDataType", e)
            }
        }
    }
}