package io.vault.jasper.model

import io.vault.jasper.enums.OptionDirection
import io.vault.jasper.enums.Symbol
import org.springframework.data.annotation.CreatedDate
import org.springframework.data.annotation.Id
import org.springframework.data.annotation.LastModifiedDate
import org.springframework.data.mongodb.core.index.Indexed
import org.springframework.data.mongodb.core.mapping.Document
import org.springframework.data.mongodb.core.mapping.Field
import java.math.BigDecimal
import java.math.BigInteger
import java.time.LocalDateTime

@Document("airdrop_nft_rebate_record")
data class AirdropNFTRebateRecord(
    @Id
    val id: String? = null,
    @Indexed(unique = true, background = true)
    @Field("user_id")
    val userId: String,
    @Indexed(unique = true, background = true)
    val address: String,
    @Field("discord_level")
    var discordLevel: Int = 0,
    @Field("twitter_account_id")
    var twitterAccountId: String? = null,
    @Field("twitter_task_finished")
    var twitterTaskFinished: Boolean = false,
    @Field("has_join_discord")
    var hasJoinDiscord : Boolean = false,
    @Field("has_retweet_nft_tweet")
    var hasRetweetNftTweet: Boolean = false,
    var status: AirdropNFTRebateRecordStatus = AirdropNFTRebateRecordStatus.CREATED,
    @Field("settle_tx_id")
    var settleTxId: String? = null,
    @CreatedDate
    val created: LocalDateTime = LocalDateTime.now(),
    @LastModifiedDate
    val updated: LocalDateTime = LocalDateTime.now()
) {}

enum class AirdropNFTRebateRecordStatus {
     CREATED, PENDING, CLAIMED, SETTLED
}