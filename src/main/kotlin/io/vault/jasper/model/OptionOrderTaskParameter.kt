package io.vault.jasper.model

import io.vault.jasper.enums.ChainType
import org.springframework.data.annotation.Id
import org.springframework.data.mongodb.core.mapping.Document
import java.time.LocalDateTime

@Document("option_order_task_parameter")
data class OptionOrderTaskParameter(
    @Id
    val id: String? = null,
    var lastUpdatePremiumOrderId: String? = null,
    var lastUpdatePremiumOrderTime: LocalDateTime? = null,
    var updatePremiumTaskEndDate: LocalDateTime = LocalDateTime.of(2024, 9, 30, 0, 0, 0),
    var lastUpdateCreateOrderTime: LocalDateTime? = LocalDateTime.of(2024, 9, 1, 0, 0, 0),
    var lastUpdateZeroDTETime: LocalDateTime? = LocalDateTime.of(2024, 9, 1, 0, 0, 0),
    var countMissingOrderChain: ChainType = ChainType.ARBITRUM,
    var countMissingOrderStartId: String? = null,
    var countMissingOrderEndId: String? = null,
)