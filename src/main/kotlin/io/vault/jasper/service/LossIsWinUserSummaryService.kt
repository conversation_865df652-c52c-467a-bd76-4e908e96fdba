package io.vault.jasper.service

import io.vault.jasper.model.LossIsWinUserSummary
import io.vault.jasper.repository.LossIsWinRewardRecordRepository
import io.vault.jasper.repository.LossIsWinUserSummaryRepository
import io.vault.jasper.repository.OptionOrderRepository
import org.springframework.retry.annotation.Retryable
import org.springframework.dao.OptimisticLockingFailureException
import org.springframework.data.repository.findByIdOrNull
import org.springframework.stereotype.Service
import java.math.BigDecimal

@Service
class LossIsWinUserSummaryService(
    private val lossIsWinUserSummaryRepository: LossIsWinUserSummaryRepository,
    private val lossIsWinRewardRecordRepository: LossIsWinRewardRecordRepository,
    private val optionOrderRepository: OptionOrderRepository,
    private val userService: UserService
) {
    @Retryable(value = [OptimisticLockingFailureException::class], maxAttempts = 3)
    fun updateBtrEarned(address: String, btrEarned: BigDecimal, transactionCount: Int? = null) {
        val userSummary = lossIsWinUserSummaryRepository.findFirstByAddress(address) ?: run {
            LossIsWinUserSummary(address = address)
        }

        userSummary.totalBtrEarned = userSummary.totalBtrEarned + btrEarned
        transactionCount?.let {
            userSummary.transactionCount += it
        }

        lossIsWinUserSummaryRepository.save(userSummary)
    }

    @Retryable(value = [OptimisticLockingFailureException::class], maxAttempts = 3)
    fun updateLoss(optionOrderId: String, address: String, loss: BigDecimal) {
        // Check if the option order is eligible for reward
        if (!lossIsWinRewardRecordRepository.existsByOptionOrderId(optionOrderId)) {
            return
        }

        val userSummary = lossIsWinUserSummaryRepository.findFirstByAddress(address) ?: run {
            LossIsWinUserSummary(address = address)
        }

        userSummary.totalLoss = userSummary.totalLoss + loss

        lossIsWinUserSummaryRepository.save(userSummary)
    }

    @Retryable(value = [OptimisticLockingFailureException::class], maxAttempts = 3)
    fun calculateAndUpdateAddressSummary(address: String) {
        val records = lossIsWinRewardRecordRepository.findByAddress(address)
        if (records.isEmpty()) {
            return
        }

        // Calculate total BTR earned
        val totalBtrEarned = records.sumOf { it.btrAmount }

        // Calculate total loss in USD
        val totalLossInUsd = records.mapNotNull { record ->
            optionOrderRepository.findByIdOrNull(record.optionOrderId)?.lossInUsd
        }.fold(BigDecimal.ZERO) { acc, loss ->
            acc.add(loss ?: BigDecimal.ZERO)
        }

        // Update summary
        val userSummary = lossIsWinUserSummaryRepository.findFirstByAddress(address) ?: run {
            LossIsWinUserSummary(address = address)
        }
        userSummary.totalBtrEarned = totalBtrEarned
        userSummary.totalLoss = totalLossInUsd
        userSummary.transactionCount = records.size

        lossIsWinUserSummaryRepository.save(userSummary)
    }

}