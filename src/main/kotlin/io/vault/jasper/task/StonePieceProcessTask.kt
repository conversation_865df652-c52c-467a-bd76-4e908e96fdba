package io.vault.jasper.task

import io.vault.jasper.service.StonePieceProcessParameterService
import io.vault.jasper.service.StonePieceProcessService
import org.slf4j.LoggerFactory
import org.springframework.beans.factory.annotation.Value
import org.springframework.boot.ApplicationArguments
import org.springframework.boot.ApplicationRunner
import org.springframework.scheduling.annotation.Scheduled
import org.springframework.stereotype.Service

/**
 * 石头碎片处理定时任务
 * Scheduled task to process failed stone piece records
 */
@Service
class StonePieceProcessTask(
    private val stonePieceProcessParameterService: StonePieceProcessParameterService,
    private val stonePieceProcessService: StonePieceProcessService
) : ApplicationRunner {

    private val logger = LoggerFactory.getLogger(this::class.java)

    @Value("\${schedule_task_on:true}")
    private lateinit var scheduleOn: String

    /**
     * 应用启动时初始化默认配置
     */
    override fun run(args: ApplicationArguments?) {
        try {
            logger.info("Initializing stone piece process configuration...")
            stonePieceProcessParameterService.initializeConfig()
            logger.info("Stone piece process configuration initialized successfully")
        } catch (e: Exception) {
            logger.error("Failed to initialize stone piece process configuration", e)
        }
    }

    /**
     * 定时处理石头碎片失败记录
     * Process failed stone piece records every minute
     */
    @Scheduled(fixedRate = 60000)
    fun run() {
        if (scheduleOn == "false") {
            return
        }

        try {
            exec()
        } catch (e: Exception) {
            logger.error("Error processing stone piece failed records: ${e.message}", e)
        }
    }

    private fun exec() {
        logger.info("Starting stone piece process task")

        try {
            // 获取所有启用的配置
            val enabledConfigs = stonePieceProcessParameterService.getEnabledConfigs()

            if (enabledConfigs.isEmpty()) {
                logger.debug("No enabled stone piece process configurations found")
                return
            }

            logger.info("Found ${enabledConfigs.size} enabled stone piece process configurations")

            var totalProcessedAddresses = 0
            var totalCreatedRecords = 0
            var totalDeletedRecords = 0
            var totalProcessedOrderIds = 0

            // 处理每个启用的配置
            enabledConfigs.forEach { config ->
                try {
                    logger.info("Processing config: ${config.id} with NFT IDs: ${config.nftIds}")

                    if (config.nftIds.isEmpty()) {
                        logger.warn("No NFT IDs configured for config: ${config.configName}")
                        return@forEach
                    }

                    // 处理该配置下的每个NFT ID
                    config.nftIds.forEach { nftId ->
                        try {
                            val result = stonePieceProcessService.processStonePieceFailedRecordsForNftId(nftId, config)
                            totalProcessedAddresses += result.processedAddresses
                            totalCreatedRecords += result.createdRecords
                            totalDeletedRecords += result.deletedRecords
                            totalProcessedOrderIds += result.processedOrderIds
                        } catch (e: Exception) {
                            logger.error("Error processing NFT ID: $nftId in config: ${config.configName}", e)
                        }
                    }
                } catch (e: Exception) {
                    logger.error("Error processing config: ${config.configName}", e)
                }
            }

            if (totalProcessedAddresses > 0 || totalCreatedRecords > 0 || totalDeletedRecords > 0) {
                logger.info(
                    "Stone piece process task completed. " +
                    "Processed ${totalProcessedAddresses} addresses, " +
                    "created ${totalCreatedRecords} new records, " +
                    "deleted ${totalDeletedRecords} old records, " +
                    "processed ${totalProcessedOrderIds} order IDs"
                )
            }

        } catch (e: Exception) {
            logger.error("Error in stone piece process task", e)
        }

        logger.info("End stone piece process task")
    }

    /**
     * 手动触发石头碎片处理任务
     * Manual trigger for stone piece process task
     */
    fun triggerManualProcess(): String {
        return try {
            logger.info("Manual trigger of stone piece process task")
            exec()
            "Stone piece process task triggered successfully"
        } catch (e: Exception) {
            logger.error("Error in manual trigger of stone piece process task", e)
            "Failed to trigger stone piece process task: ${e.message}"
        }
    }
}
