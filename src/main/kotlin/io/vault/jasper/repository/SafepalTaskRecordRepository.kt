package io.vault.jasper.repository

import io.vault.jasper.model.KYT
import io.vault.jasper.model.SafepalTaskRecord
import io.vault.jasper.model.SafepalTaskStatus
import org.springframework.data.mongodb.repository.MongoRepository
import org.springframework.stereotype.Repository
import java.time.LocalDateTime

@Repository
interface SafepalTaskRecordRepository : MongoRepository<SafepalTaskRecord, String> {
    fun findByAddressIgnoreCase(address: String): SafepalTaskRecord?
    fun findByStatus(status: SafepalTaskStatus): List<SafepalTaskRecord>
    fun findByStatusAndFinishTask(
        status: SafepalTaskStatus,
        finishTask: Boolean
    ): List<SafepalTaskRecord>
    fun findByFinishTask(
        finishTask: Boolean
    ): List<SafepalTaskRecord>
}