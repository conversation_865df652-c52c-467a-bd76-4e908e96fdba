package io.vault.jasper.service

import org.web3j.crypto.Credentials
import org.web3j.protocol.Web3j
import org.web3j.tx.Contract
import org.web3j.tx.ReadonlyTransactionManager
import org.web3j.tx.gas.ContractGasProvider
import java.math.BigInteger

class OptionsContract : Contract {

    constructor(
        contractAddress: String,
        web3: Web3j,
        address: String,
        gasProvider: ContractGasProvider
    ) : super(
        "Bin file was not provided",
        contractAddress,
        web3,
        ReadonlyTransactionManager(web3, address),
        gasProvider
    )
}