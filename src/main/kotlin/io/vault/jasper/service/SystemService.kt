package io.vault.jasper.service

import io.vault.jasper.model.LPVaultParameter
import io.vault.jasper.model.SystemParameter
import io.vault.jasper.repository.LPVaultParameterRepository
import io.vault.jasper.repository.SystemParameterRepository
import org.slf4j.LoggerFactory
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.data.mongodb.core.MongoTemplate
import org.springframework.data.mongodb.core.query.Query
import org.springframework.data.mongodb.core.query.Update
import org.springframework.stereotype.Service

@Service
class SystemService @Autowired constructor(
    private val systemParameterRepository: SystemParameterRepository,
    private val lpVaultParameterRepository: LPVaultParameterRepository,
    private val mongoTemplate: MongoTemplate
){

    private val logger = LoggerFactory.getLogger(this::class.java)

    /**
     * 获取系统参数对象
     */
    fun getParameter(): SystemParameter {
        val params = systemParameterRepository.findAll()

        return if (params.isEmpty()) {
            val p = SystemParameter(null)
            systemParameterRepository.save(p)
        } else {
            params.first()
        }
    }

    fun getAccessTokenExpire(): Long {
        return getParameter().userAccessTokenTimeout
    }

    /**
     * 获取系统参数对象
     */
    fun getLPVaultParameter(): LPVaultParameter {
        val params = lpVaultParameterRepository.findAll()

        return if (params.isEmpty()) {
            val p = LPVaultParameter(null)
            lpVaultParameterRepository.save(p)
        } else {
            params.first()
        }
    }

    fun updateFields(data: Map<String, Any>): Long {
        val update = Update()
        data.forEach { (fieldName, value) -> update.set(fieldName, value) }
        val query = Query()
        val updateResult = mongoTemplate.updateFirst(query, update, SystemParameter::class.java)

        return updateResult.modifiedCount
    }
}