package io.vault.jasper.controller.pub

import EVMAddress
import io.swagger.annotations.ApiOperation
import io.vault.jasper.ApiResponse
import io.vault.jasper.annotation.ApiCache
import io.vault.jasper.dto.ActivateLPDTO
import io.vault.jasper.dto.AuthDTO
import io.vault.jasper.dto.CreateLPVaultDTO
import io.vault.jasper.enums.ChainType
import io.vault.jasper.enums.OptionDirection
import io.vault.jasper.enums.Symbol
import io.vault.jasper.exception.BusinessException
import io.vault.jasper.exception.ResultEnum
import io.vault.jasper.model.*
import io.vault.jasper.repository.LPVaultRepository
import io.vault.jasper.repository.OrderRepository
import io.vault.jasper.response.ExecutedOrderResponse
import io.vault.jasper.response.LPVaultInfoResponse
import io.vault.jasper.response.PageResponse
import io.vault.jasper.service.CurrencyService
import io.vault.jasper.service.OptionOrderService
import io.vault.jasper.service.StoneService
import io.vault.jasper.service.SystemService
import io.vault.jasper.service.blockchain.BlockchainServiceFactory
import io.vault.jasper.service.blockchain.EvmService
import io.vault.jasper.utils.AuthUtil
import io.vault.jasper.utils.DateTimeUtil
import org.slf4j.LoggerFactory
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.data.domain.PageImpl
import org.springframework.data.domain.PageRequest
import org.springframework.data.domain.Sort
import org.springframework.data.mongodb.core.MongoTemplate
import org.springframework.data.mongodb.core.query.Criteria
import org.springframework.data.mongodb.core.query.Query
import org.springframework.web.bind.annotation.*
import javax.servlet.http.HttpServletRequest
import javax.validation.Valid


@RestController
@RequestMapping("/lpvault")
class LPVaultController @Autowired constructor(
    private val authUtil: AuthUtil,
    private val lpVaultRepository: LPVaultRepository,
    private val systemService: SystemService,
    private val blockchainServiceFactory: BlockchainServiceFactory,
    private val currencyService: CurrencyService,
    private val optionOrderService: OptionOrderService,
    private val mongoTemplate: MongoTemplate,
    private val orderRepository: OrderRepository,
    private val stoneService: StoneService
) {

    private var logger = LoggerFactory.getLogger(this::class.java)

    @ApiOperation("Authorise LP activation code")
    @CrossOrigin
    @PostMapping("/activate_lp")
    fun activateLP(
        @Valid @RequestBody dto: ActivateLPDTO,
        request: HttpServletRequest
    ): ApiResponse<Boolean> {
        authUtil.filterIPBlacklist(request)
        //authUtil.auth(request, dto.address)

        val lpVaultParameter = systemService.getLPVaultParameter()

        if(dto.activateCode != lpVaultParameter.activationCode){
            throw BusinessException(ResultEnum.INVALID_LP_ACTIVATE_CODE)
        }

        return ApiResponse.success(true)
    }

    @ApiOperation("Get LP Vault Address")
    @CrossOrigin
    @PostMapping("/lp_vault_address")
    fun getLPVaultAddress(
        request: HttpServletRequest,
        @Valid @RequestBody dto: AuthDTO
    ): ApiResponse<Map<String, String>> {
        authUtil.filterIPBlacklist(request)
        authUtil.auth(request, dto.address)

        val lpVaultList = lpVaultRepository.findByLpEoaAddressIgnoreCaseAndStatusAndChain(
            dto.address,
            LPVaultStatus.DONE,
            dto.chain
        )

        if(lpVaultList.size == 0){
            throw BusinessException(ResultEnum.LP_VAULT_NOT_FOUND)
        }

        val responseMap = mutableMapOf<String, String>()

        for(lpVault in lpVaultList){
            if(lpVault.status == LPVaultStatus.PENDING || lpVault.status == LPVaultStatus.REQUESTED){
                throw BusinessException(ResultEnum.LP_VAULT_STILL_CREATING)
            }

            responseMap[lpVault.optionSymbol] = lpVault.lpVaultAddress!!
        }

        return ApiResponse.success(responseMap)
    }

    @ApiOperation("Create LP Vault and add it to whitelist")
    @CrossOrigin
    @PostMapping("/create_lp_vault")
    fun createLPVault(
        request: HttpServletRequest,
        @Valid @RequestBody dto: CreateLPVaultDTO
    ): ApiResponse<String> {
        authUtil.filterIPBlacklist(request)
        authUtil.auth(request, dto.address)

        val vault = lpVaultRepository.save(
            LPVault(
                lpEoaAddress = dto.address,
                optionType = dto.optionDirection,
                optionSymbol = dto.optionSymbol.name,
                status = LPVaultStatus.PENDING,
                chain = dto.chain
            )
        )

        return ApiResponse.success(vault.id!!)
    }

    @ApiOperation("Query lp vault list")
    @CrossOrigin
    @ApiCache(key = "lpvault:lp_vault_list", dynamicKey = ["address", "chain"], expire = 60)
    @GetMapping("/lp_vault_list")
    fun lpVaultList(
        request: HttpServletRequest,
        @RequestParam @EVMAddress address: String,
        @RequestParam chain: ChainType
    ): ApiResponse<List<LPVaultInfoResponse>> {
        authUtil.filterIPBlacklist(request)
        //authUtil.auth(request, address)

        val lpVaultList = lpVaultRepository.findByLpEoaAddressIgnoreCaseAndStatusAndChain(
            address,
            LPVaultStatus.DONE,
            chain
        )

        //val chainType = chain
        val resultList: MutableList<LPVaultInfoResponse> = mutableListOf()

        for(lpVault in lpVaultList){
            val vaultAddress = lpVault.lpVaultAddress!!
            val optionDirection = lpVault.optionType
            val optionSymbol = lpVault.optionSymbol

            var lockSymbol = optionSymbol
            if(optionDirection == OptionDirection.PUT){
                lockSymbol = "USDT"
                if(chain == ChainType.BASE){
                    lockSymbol = "USDC"
                }
            }
            val lockSymbolBalance = lpVault.lockSymbolBalance

            val openInterest = lpVault.oi
            val totalAmount = lockSymbolBalance.add(openInterest)

            val usdtBalance = lpVault.usdtBalance

            val premiumEarn = lpVault.premiumEarn
            val tradingVolume = lpVault.tradingVolume

            val infoResponse = LPVaultInfoResponse(
                vaultAddress,
                Symbol.valueOf(optionSymbol),
                optionDirection,
                Symbol.valueOf(lockSymbol),
                lockSymbolBalance.stripTrailingZeros().toPlainString(),
                "0",
                openInterest.stripTrailingZeros().toPlainString(),
                totalAmount.stripTrailingZeros().toPlainString(),
                usdtBalance.stripTrailingZeros().toPlainString(),
                premiumEarn.stripTrailingZeros().toPlainString(),
                tradingVolume.stripTrailingZeros().toPlainString(),
                DateTimeUtil.convertLocalDateTimeToTimestamp(lpVault.created)
            )

            resultList.add(infoResponse)
        }

        return ApiResponse.success(resultList)
    }

    @ApiOperation("Query lp vault request status")
    @CrossOrigin
    @ApiCache(key = "lpvault:lp_vault_request_status", dynamicKey = ["address", "requestId"], expire = 60)
    @GetMapping("/lp_vault_request_status")
    fun lpVaultRequestStatus(
        request: HttpServletRequest,
        @RequestParam @EVMAddress address: String,
        @RequestParam requestId: String,
    ): ApiResponse<LPVaultStatus> {
        authUtil.filterIPBlacklist(request)
        authUtil.auth(request, address)

        val vault = lpVaultRepository.findById(requestId).orElseThrow {
            BusinessException(ResultEnum.LP_VAULT_NOT_FOUND)
        }

        return ApiResponse.success(vault.status)
    }

    @ApiOperation("LP Vault 订单成交记录")
    @CrossOrigin
    @ApiCache(
        key = "lpvault:oi_list",
        dynamicKey = ["address", "lpVaultAddress", "chain", "direction", "orderType", "page", "pageSize"],
        expire = 10
    )
    @GetMapping("/oi_list")
    fun OpenInterestList(
        @RequestParam @EVMAddress address: String,
        @RequestParam @EVMAddress lpVaultAddress: String,
        @RequestParam chain: ChainType,
        @RequestParam direction: OptionDirection?, // CALL or PUT
        @RequestParam orderType: OrderType?, // DEGEN or SWAP
        @RequestParam page: Int?,
        @RequestParam pageSize: Int?,
        request: HttpServletRequest
    ): ApiResponse<PageResponse<ExecutedOrderResponse>> {
        authUtil.filterIPBlacklist(request)
        val p = page ?: 1
        val ps = pageSize ?: 50
        val pageable = PageRequest.of(p - 1, ps, Sort.by(Sort.Order.desc(OptionOrder::id.name)))

        val query = Query()
        query.addCriteria(Criteria.where(OptionOrder::chain.name).`is`(chain))
        query.addCriteria(Criteria.where(OptionOrder::sellerVault.name).`is`(lpVaultAddress))
        query.addCriteria(Criteria.where(OptionOrder::seller.name).`is`(address))

        if (direction != null) {
            query.addCriteria(Criteria.where(OptionOrder::direction.name).`is`(direction))
        }

        if (orderType != null) {
            query.addCriteria(Criteria.where(OptionOrder::orderType.name).`is`(orderType))
        }

        val total = mongoTemplate.count(query, OptionOrder::class.java)
        query.with(pageable)
        val resultSet = mongoTemplate.find(query, OptionOrder::class.java)
        val optionsPage = PageImpl(resultSet, pageable, total)

        val optionsList = optionsPage.content.mapNotNull {
            //val order = orderRepository.findByIdOrNull(it.orderId) ?: return@mapNotNull null

            var lockDateTimeStamp = DateTimeUtil.convertLocalDateTimeToTimestamp(it.expiryDate)
            if(it.lockDate != null){
                lockDateTimeStamp = DateTimeUtil.convertLocalDateTimeToTimestamp(it.lockDate)
            }

            val stonePieces = stoneService.getNftIdFromOptionOrder(it)
            ExecutedOrderResponse(
                id = it.id!!,
                orderId = it.orderId,
                orderType = it.orderType,
                chain = it.chain,
                onChainOrderId = it.onChainOrderId,
                buyerAddress = it.buyer,
                buyerVault = it.buyerVault,
                buyerVaultSalt = it.buyerVaultSalt,
                sellerAddress = it.seller,
                businessType = it.direction!!,

                bidAsset = it.bidAsset,
                bidAmount = it.bidAmount?.stripTrailingZeros()?.toPlainString(),

                underlyingAsset = it.underlyingAsset,
                underlyingAssetAddress = it.underlyingAssetAddress,
                underlyingAssetAmount = it.amount.stripTrailingZeros().toPlainString(),

                strikeAsset = it.strikeAsset,
                strikeAmount = it.strikeAmount.stripTrailingZeros().toPlainString(),
                strikePrice = it.strikePrice!!.stripTrailingZeros().toPlainString(),
                actualStrikeAmount = it.actualStrikeAmount?.stripTrailingZeros()?.toPlainString(),

                premiumFeesAsset = if (it.premiumAsset?.asset != null) Symbol.valueOf(it.premiumAsset!!.asset) else Symbol.USDT,
                premiumFee = it.premiumFeePay?.stripTrailingZeros()?.toPlainString(),
                premiumFeeInUsdt = it.premiumFeePayInUsdt?.stripTrailingZeros()?.toPlainString(),
                premiumFeeShouldPay = it.premiumFeeShouldPay?.stripTrailingZeros()?.toPlainString(),
                premiumFeeShouldPayInUsdt = it.premiumFeeShouldPayInUsdt?.stripTrailingZeros()?.toPlainString(),

                createDate = DateTimeUtil.convertLocalDateTimeToTimestamp(it.created),
                expiryDate = DateTimeUtil.convertLocalDateTimeToTimestamp(it.expiryDate),
                lockDate = lockDateTimeStamp,
                status = it.status,
                degen = it.jvault,
                marketPriceAtSettlement = it.marketPriceAtSettlement,
                settlementHash = it.settlementHash,
                buyerProfit = it.buyerProfit?.stripTrailingZeros()?.toPlainString(),
                expiryInHour = it.expiryInHour,
                usedMoonlightBox = it.usedMoonlightBox,
                usedRealityStone = it.usedRealityStone ?: false,
                usedPowerStone = it.usedPowerStone ?: false,
                usedSpaceStone = it.usedSpaceStone ?: false,
                usedTimeStone = it.usedTimeStone ?: false,
                stoneActivityNftId = it.stoneActivityNftId,
                txHash = it.txHash,
                premiumFeeLog = it.premiumFeeLog,
                awardStonePieceNftIds = stonePieces
            )
        }

        return ApiResponse.success(
            PageResponse(
                total = optionsPage.totalElements,
                page = p,
                pageSize = ps,
                totalPage = optionsPage.totalPages,
                hasNext = optionsPage.hasNext(),
                data = optionsList
            )
        )
    }
}