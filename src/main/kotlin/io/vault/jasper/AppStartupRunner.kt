package io.vault.jasper

import com.fasterxml.jackson.databind.ObjectMapper
import io.vault.jasper.enums.ChainType
import io.vault.jasper.enums.OptionDirection
import io.vault.jasper.enums.Symbol
import io.vault.jasper.model.*
import io.vault.jasper.repository.*
import io.vault.jasper.service.*
import io.vault.jasper.service.benefit.BenefitService
import io.vault.jasper.utils.DateTimeUtil
import io.vault.jasper.utils.ProfileUtil
import org.slf4j.LoggerFactory
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.ApplicationRunner
import org.springframework.data.mongodb.core.MongoTemplate
import org.springframework.stereotype.Component
import java.math.BigDecimal
import java.time.LocalDateTime

@Component
class AppStartupRunner @Autowired constructor(
    private val userPointDailySummaryRepository: UserPointDailySummaryRepository,
    private val ipBlackListRepository: IPBlackListRepository,
    private val kolLevelRepository: KolLevelRepository,
    private val systemService: SystemService,
    private val systemParameterRepository: SystemParameterRepository,
    private val contractEventRepository: ContractEventRepository,
    private val zeroPremiumBenefitRepository: ZeroPremiumBenefitRepository,
    private val userPointService: UserPointService,
    private val degenConfigService: DegenConfigService,
    private val currencyService: CurrencyService,
    private val activityRepository: ActivityRepository,
    private val mongoTemplate: MongoTemplate,
    private val benefitService: BenefitService,
    private val degenLPVaultConfigRepository: DegenLPVaultConfigRepository,
    private val chainRepository: ChainRepository,
    private val channelPartnerRepository: ChannelPartnerRepository,
) : ApplicationRunner {

    private val logger = LoggerFactory.getLogger(this::class.java)

    private fun initDegenConfig() {
        degenConfigService.initDegenConfig(Symbol.ETH)
        degenConfigService.initDegenConfig(Symbol.WBTC)
        degenConfigService.initDegenConfig(Symbol.ARB)
        degenConfigService.initDegenConfig(Symbol.DLCBTC)
        degenConfigService.initDegenConfig(Symbol.WSOL)
    }

    private fun initCurrency() {

        currencyService.initCurrency(
            ChainType.ARBITRUM,
            Symbol.USDT,
            "******************************************",
            6,
            "0x2b89b9dc8fdf9f34709a5b106b472f0f39bb6ca9ce04b0fd7f2e971688e2e53b",
            "0x00039a0c0be4e43cacda1599ac414205651f4a62b614b6be9e5318a182c33eb0"
        )

        currencyService.initCurrency(
            ChainType.ARBITRUM,
            Symbol.WBTC,
            "******************************************",
            8,
            "0xc9d8b075a5c69303365ae23633d4e085199bf5c520a3b90fed1322a0342ffc33"
        )

        currencyService.initCurrency(
            ChainType.ARBITRUM,
            Symbol.ETH,
            "******************************************",
            18,
            "0xff61491a931112ddf1bd8147cd1b641375f79f5825126d665480874634fd0ace"
        )

        currencyService.initCurrency(
            ChainType.ARBITRUM,
            Symbol.ARB,
            "******************************************",
            18,
            "0x3fa4252848f9f0a1480be62745a4629d9eb1322aebab8a791e344b3b9c1adcf5"
        )

        currencyService.initCurrency(
            ChainType.ARBITRUM,
            Symbol.DLCBTC,
            "******************************************",
            8,
            "0xe62df6c8b4a85fe1a67db44dc12de5db330f7ac66b72dc658afedf0f4a415b43"
        )

        currencyService.initCurrency(
            ChainType.BASE,
            Symbol.USDC,
            "******************************************",
            6,
            "0xeaa020c61cc479712813461ce153894a96a6c00b21ed0cfc2798d1f9a9e9c94a"
        )

        currencyService.initCurrency(
            ChainType.BASE,
            Symbol.USDT,
            "******************************************",
            6,
            "0x2b89b9dc8fdf9f34709a5b106b472f0f39bb6ca9ce04b0fd7f2e971688e2e53b",
            "0x00039a0c0be4e43cacda1599ac414205651f4a62b614b6be9e5318a182c33eb0"
        )
        currencyService.initCurrency(
            ChainType.BASE,
            Symbol.ETH,
            "******************************************",
            18,
            "0xff61491a931112ddf1bd8147cd1b641375f79f5825126d665480874634fd0ace"
        )
        currencyService.initCurrency(
            ChainType.BASE,
            Symbol.CBBTC,
            "******************************************",
            8,
            "0xe62df6c8b4a85fe1a67db44dc12de5db330f7ac66b72dc658afedf0f4a415b43"
        )
        currencyService.initCurrency(
            ChainType.BITLAYER,
            Symbol.BTC,
            "******************************************",
            18,
            "0xe62df6c8b4a85fe1a67db44dc12de5db330f7ac66b72dc658afedf0f4a415b43",
            "0x0003665949c883f9e0f6f002eac32e00bd59dfe6c34e92a91c37d6a8322d6489"
        )
        currencyService.initCurrency(
            ChainType.BITLAYER,
            Symbol.USDT,
            "******************************************",
            6,
            "0x2b89b9dc8fdf9f34709a5b106b472f0f39bb6ca9ce04b0fd7f2e971688e2e53b",
            "0x00039a0c0be4e43cacda1599ac414205651f4a62b614b6be9e5318a182c33eb0"
        )
        // BSC
        currencyService.initCurrency(
            ChainType.BSC,
            Symbol.BNB,
            "******************************************",
            18,
            "0x2f95862b045670cd22bee3114c39763a4a08beeb663b145d283c31d7d1101c4f"
        )
        currencyService.initCurrency(
            ChainType.BSC,
            Symbol.ETH,
            "******************************************",
            18,
            "0xff61491a931112ddf1bd8147cd1b641375f79f5825126d665480874634fd0ace"
        )
        currencyService.initCurrency(
            ChainType.BSC,
            Symbol.BTCB,
            "******************************************",
            18,
            "0xe62df6c8b4a85fe1a67db44dc12de5db330f7ac66b72dc658afedf0f4a415b43"
        )
        currencyService.initCurrency(
            ChainType.BSC,
            Symbol.USDT,
            "******************************************",
            18,
            "0x2b89b9dc8fdf9f34709a5b106b472f0f39bb6ca9ce04b0fd7f2e971688e2e53b",
            "0x00039a0c0be4e43cacda1599ac414205651f4a62b614b6be9e5318a182c33eb0"
        )
    }

    private fun initContractEvents() {
        contractEventRepository.findFirstByEvent(EventName.REDEEM_NFT) ?: run {
            when (ProfileUtil.activeProfile) {
                "prod" -> {
                    val ce = contractEventRepository.save(ContractEvent(
                        chain = ChainType.ARBITRUM,
                        contract = "******************************************",
                        event = EventName.REDEEM_NFT,
                        desc = "Redeem NFT",
                        logAddress = "******************************************",
                        fnHash = "0x47cee97cb7acd717b3c0aa1435d004cd5b3c8c57d70dbceb4e4458bbd60e39d4"
                    ))
                    logger.info("Contract event REDEEM_NFT in ${ce.chain} initialized")
                }
                else -> {
                    val ce = contractEventRepository.save(ContractEvent(
                        chain = ChainType.ARBITRUM,
                        contract = "0x312155f3604514aaCcBAD0824d33820601057eEC",
                        event = EventName.REDEEM_NFT,
                        desc = "Redeem NFT",
                        logAddress = "0x312155f3604514aaccbad0824d33820601057eec",
                        fnHash = "0x47cee97cb7acd717b3c0aa1435d004cd5b3c8c57d70dbceb4e4458bbd60e39d4"
                    ))
                    logger.info("Contract event REDEEM_NFT in ${ce.chain} initialized")
                }
            }
        }
        contractEventRepository.findFirstByEventAndChain(EventName.COIN98, ChainType.ARBITRUM) ?: run {
            when (ProfileUtil.activeProfile) {
                "prod" -> {
                    val ce = contractEventRepository.save(ContractEvent(
                        chain = ChainType.ARBITRUM,
                        contract = "0x2f2Cb9dfCF09D8Ae6e6290c43eFA83B45F931B88",
                        event = EventName.COIN98,
                        desc = "Coin98",
                        logAddress = "0x2f2Cb9dfCF09D8Ae6e6290c43eFA83B45F931B88",
                        fnHash = "0x093735df998d32fd314c762c52484446a7e68b999603a10880722bf3488f35c5"
                    ))
                    logger.info("Contract event COIN98 in ${ce.chain} initialized")
                }
                else -> {
                    val ce = contractEventRepository.save(ContractEvent(
                        chain = ChainType.ARBITRUM,
                        contract = "0xd0e58A8243f16E9F3Ff9985ccaE3661a85D62b9a",
                        event = EventName.COIN98,
                        desc = "Coin98",
                        logAddress = "0xd0e58A8243f16E9F3Ff9985ccaE3661a85D62b9a",
                        fnHash = "0x093735df998d32fd314c762c52484446a7e68b999603a10880722bf3488f35c5"
                    ))
                    logger.info("Contract event COIN98 in ${ce.chain} initialized")
                }
            }
        }

        contractEventRepository.findFirstByEventAndChain(EventName.BITLAYER_BINANCE, ChainType.BITLAYER) ?: run {
            when (ProfileUtil.activeProfile) {
                "dev" -> {
                    val ce = contractEventRepository.save(ContractEvent(
                        chain = ChainType.BITLAYER,
                        contract = "0xC54822B674d01816BA00430B22CE1aEA62Cf9024",
                        event = EventName.BITLAYER_BINANCE,
                        desc = "Bitlayer Binance Campaign",
                        logAddress = "0xc54822b674d01816ba00430b22ce1aea62cf9024",
                        fnHash = "0x54c646d529c26feaabee0ff910ecddbbe4d4e71357cf70844d2976d0b59bdd08"
                    ))
                    logger.info("Contract event COIN98 in ${ce.chain} initialized")
                }
                else -> {
                    val ce = contractEventRepository.save(ContractEvent(
                        chain = ChainType.BITLAYER,
                        contract = "0x94Efe901a7017E898A1A43108f54D02CEbbbfB00",
                        event = EventName.BITLAYER_BINANCE,
                        desc = "Bitlayer Binance Campaign",
                        logAddress = "0x94efe901a7017e898a1a43108f54d02cebbbfb00",
                        fnHash = "0x54c646d529c26feaabee0ff910ecddbbe4d4e71357cf70844d2976d0b59bdd08"
                    ))
                    logger.info("Contract event Bitlayer Binance in ${ce.chain} initialized")
                }
            }
        }
    }

    //加载IP黑名单
    private fun loadIpBlackList() {
        val ipBlackList = ipBlackListRepository.findAll()
        //logger.info("Loading IP black list ${ipBlackList.size}...")
        ipBlackList.forEach {
            Blacklist.add(it.ipRange)
        }
        //logger.info("Loading IP black list finish ${Blacklist.getBlackListSize()}...")
    }

    private fun initKolLevels() {
        val levels = listOf(
            listOf(0, "0", mapOf(
                "max_users" to 11
            )),
            listOf(1, "0.12", mapOf(
                "min_users" to 12,
                "max_users" to 23
            )),
            listOf(2, "0.24", mapOf(
                "total_premium" to "120000",
                "active_addresses" to "500",
                "min_users" to 24,
                "max_users" to 47
            )),
            listOf(3, "0.48", mapOf(
                "total_premium" to "300000",
                "active_addresses" to "1000",
                "min_users" to 48
            ))
        )
        val objectMapper = ObjectMapper()
        levels.forEachIndexed { index, it ->
            val level = (it[0] as Int).toString()
            val feeRate = (it[1] as String).toBigDecimal()
            val conditions = it[2] as? Map<String, String>
            val config = kolLevelRepository.findFirstByName(level)
            if (config == null) {
                kolLevelRepository.save(KolLevel(
                    order = index,
                    name = level,
                    conditions = objectMapper.writeValueAsString(conditions),
                    incentiveRate = feeRate
                ))
            } else if (config.incentiveRate.compareTo(feeRate) != 0 ||
                (config.conditions == "null" && conditions != null)) {
                config.incentiveRate = feeRate
                config.conditions = objectMapper.writeValueAsString(conditions)
                kolLevelRepository.save(config)
            }
        }
    }

    private fun initKolMails() {
        val systemParameter = systemService.getParameter()
        if (systemParameter.kolApplyReceivingMails.isEmpty()) {
            systemParameter.kolApplyReceivingMails = when (ProfileUtil.activeProfile) {
                "prod" -> listOf("<EMAIL>", "<EMAIL>")
                else -> listOf("<EMAIL>")
            }
            systemParameterRepository.save(systemParameter)
        }
    }

    private fun initZeroPremiumBenefits() {
        listOf(
            listOf(1, Symbol.WBTC, "0.01", OptionDirection.CALL),
            listOf(1, Symbol.WBTC, "0.01", OptionDirection.PUT),
            listOf(1, Symbol.ETH, "0.01", OptionDirection.CALL),
            listOf(1, Symbol.ETH, "0.01", OptionDirection.PUT)
        ).forEach { it ->
            val (times, underlyingAsset, amount, optionDirection) = it
            val name = "${underlyingAsset}_${optionDirection}_${amount}_$times"
            zeroPremiumBenefitRepository.findFirstByName(name) ?: run {
                zeroPremiumBenefitRepository.save(ZeroPremiumBenefit(
                    name = name,
                    times = times as Int,
                    underlyingAsset = underlyingAsset as Symbol,
                    amount = amount as String,
                    optionDirection = optionDirection as OptionDirection
                ))
                logger.info("Zero premium benefit $name initialized")
            }
        }
    }

    //补充用户积分记录的 Premium 数据
    private fun updateZeroPremiumFeeUserPointRecords(){

        val zeroPremiumRecords = userPointDailySummaryRepository.findByPremiumPointIsNot(
            BigDecimal.ZERO
        )

        logger.info("Zero premium fee user point records count: ${zeroPremiumRecords.size}")

        for(record in zeroPremiumRecords){

            if(record.premiumFees != BigDecimal.ZERO){
                continue
            }

            val date = LocalDateTime.parse(record.dateString + "T10:00:00")

            val dailySummaryTime = DateTimeUtil.getDailySummaryTime(date)
            val start = dailySummaryTime.second
            val end = dailySummaryTime.third

            val summary = userPointService.sumPremiumPointByAddressAndCreatedBetween(
                record.address,
                start,
                end
            )

            record.premiumPoint = summary.first
            record.premiumFees = summary.second

            userPointDailySummaryRepository.save(record)
        }
    }

    private fun initActivity() {
        val defaultName = BenefitService.ACTIVITY_NAME_NFT
        val defaultNftId = "1"
        val defaultActivity = activityRepository.findFirstByName(defaultName) ?: run {
            val newActivity = activityRepository.save(Activity(
                name = defaultName,
                startTime = LocalDateTime.now(),
                nftId = defaultNftId
            ))
            logger.info("Activity ${newActivity.id} - $defaultName initialized!!")
            newActivity
        }
        if (defaultActivity.nftId != defaultNftId) {
            defaultActivity.nftId = defaultNftId
            activityRepository.save(defaultActivity)
        }
        zeroPremiumBenefitRepository.findByActivityIdIsNull().forEach {
            it.activityId = defaultActivity.id
            zeroPremiumBenefitRepository.save(it)
            logger.info("Zero premium benefit ${it.id} updated activity id to ${defaultActivity.id}")
        }

        val coin98Name = "COIN98"
        val coin98NftId = "2"
        val coin98Activity = activityRepository.findFirstByName(coin98Name) ?: run {
            val newActivity = activityRepository.save(Activity(
                name = coin98Name,
                startTime = LocalDateTime.now(),
                nftId = coin98NftId
            ))
            logger.info("Activity ${newActivity.id} - $coin98Name initialized!!")
            newActivity
        }
        if (coin98Activity.nftId != coin98NftId) {
            coin98Activity.nftId = coin98NftId
            activityRepository.save(coin98Activity)
        }
        zeroPremiumBenefitRepository.findByActivityId(coin98Activity.id).isEmpty().let { a ->
            if (a) {
                val newBenefit = zeroPremiumBenefitRepository.save(ZeroPremiumBenefit(
                    activityId = coin98Activity.id,
                    underlyingAsset = Symbol.WBTC,
                    amount = "0.01",
                    expiryInHour = "2",
                    times = 1
                ))
                logger.info("Zero premium benefit ${newBenefit.id} initialized with activity id ${coin98Activity.id}")
            }
        }

        // NFT 3~6, 共4个活动（其实归属于同一活动）
        val benefitsConfig = mapOf(
            "3" to listOf(
                ZeroPremiumBenefit(underlyingAsset = Symbol.ETH, amount = "1", expiryInHour = "2", times = 2),
                ZeroPremiumBenefit(underlyingAsset = Symbol.WBTC, amount = "0.1", expiryInHour = "2", times = 2),
            ),
            "4" to listOf(
                ZeroPremiumBenefit(underlyingAsset = Symbol.ETH, amount = "0.5", expiryInHour = "2", times = 2),
                ZeroPremiumBenefit(underlyingAsset = Symbol.WBTC, amount = "0.05", expiryInHour = "2", times = 2),
            ),
            "5" to listOf(
                ZeroPremiumBenefit(underlyingAsset = Symbol.ETH, amount = "0.2", expiryInHour = "2", times = 2),
                ZeroPremiumBenefit(underlyingAsset = Symbol.WBTC, amount = "0.01", expiryInHour = "2", times = 2),
            ),
            "6" to listOf(
                ZeroPremiumBenefit(underlyingAsset = Symbol.ETH, amount = "0.2", expiryInHour = "2", times = 2),
            ),
        )
        val nftIds = benefitsConfig.keys
        nftIds.forEach { id ->
            val nftName = benefitService.getActivityNameByNftId(id)
            val nftActivity = activityRepository.findFirstByName(nftName) ?: run {
                val newActivity = activityRepository.save(Activity(
                    name = nftName,
                    startTime = LocalDateTime.now(),
                    nftId = id
                ))
                logger.info("Activity ${newActivity.id} - $nftName initialized!!")
                newActivity
            }
            zeroPremiumBenefitRepository.findByActivityId(nftActivity.id!!).isEmpty().let { isEmpty ->
                if (isEmpty) {
                    val benefitList = benefitsConfig[id] ?: return@let
                    benefitList.forEach { bConfig ->
                        val b = zeroPremiumBenefitRepository.save(ZeroPremiumBenefit(
                            activityId = nftActivity.id,
                            underlyingAsset = bConfig.underlyingAsset,
                            amount = bConfig.amount,
                            optionDirection = bConfig.optionDirection,
                            expiryInHour = bConfig.expiryInHour,
                            times = bConfig.times
                        ))
                        val desc = "${b.underlyingAsset}|${b.amount}|${b.optionDirection ?: "_"}|${b.expiryInHour}|${b.times}"
                        logger.info("Activity=${nftActivity.name} benefit initialized: $desc")
                    }
                }
            }
        }
    }

    private fun removeCollectionIndex() {
        val collectionName = "zero_premium_benefit"
        val indexName = "name"
        val indexOps = mongoTemplate.indexOps(collectionName)
        indexOps.indexInfo.forEach {
            if (it.name == indexName) {
                indexOps.dropIndex(indexName)
                logger.info("Index $indexName removed from collection $collectionName")
            }
        }
    }

    private fun initDegenLPVaultConfig(){

        val allConfig = degenLPVaultConfigRepository.findAll()
        if(allConfig.size == 0){

            degenLPVaultConfigRepository.save(
                DegenLPVaultConfig(
                    chain = ChainType.ARBITRUM,
                    address = "******************************************",
                    optionType = OptionDirection.CALL,
                    optionSymbol = Symbol.ETH,
                    expireInHour = listOf("2", "8", "24"),
                    token = Symbol.ETH,
                    tokenAddress = "******************************************"
                )
            )

            degenLPVaultConfigRepository.save(
                DegenLPVaultConfig(
                    chain = ChainType.ARBITRUM,
                    address = "******************************************",
                    optionType = OptionDirection.PUT,
                    optionSymbol = Symbol.ETH,
                    expireInHour = listOf("2", "8", "24"),
                    token = Symbol.USDT,
                    tokenAddress = "******************************************"
                )
            )

            degenLPVaultConfigRepository.save(
                DegenLPVaultConfig(
                    chain = ChainType.BASE,
                    address = "******************************************",
                    optionType = OptionDirection.CALL,
                    optionSymbol = Symbol.ETH,
                    expireInHour = listOf("2", "8", "24"),
                    token = Symbol.ETH,
                    tokenAddress = "******************************************"
                )
            )

            degenLPVaultConfigRepository.save(
                DegenLPVaultConfig(
                    chain = ChainType.BASE,
                    address = "******************************************",
                    optionType = OptionDirection.PUT,
                    optionSymbol = Symbol.ETH,
                    expireInHour = listOf("2", "8", "24"),
                    token = Symbol.USDC,
                    tokenAddress = "******************************************"
                )
            )
        }
    }

    private fun initChain(){
        val chains = listOf(
            ChainType.ARBITRUM,
            ChainType.BASE,
            ChainType.BITLAYER,
            ChainType.BSC
        )

        val subgraphUrl = mapOf(
            ChainType.ARBITRUM to "https://arb.subgraph.jaspervault.io/",
            ChainType.BASE to "https://base.subgraph.jaspervault.io/",
            ChainType.BITLAYER to "https://bitlayer.subgraph.jaspervault.io/subgraphs/jaspervault-bitlayer",
            ChainType.BSC to "https://bsc.subgraph.jaspervault.io/",
        )

        val freeOptionContractAddress = mapOf(
            ChainType.ARBITRUM to "******************************************",
            ChainType.BASE to "******************************************",
            ChainType.BITLAYER to "0xbfc2bce6c3e071986927264F889B07dD6a2D8dE2",
            ChainType.BSC to "0xfb87Af57a213815fbD993DDe5F2E0b9040D0fc5f"
        )

        val tradingCreditContractAddress = mapOf(
            ChainType.ARBITRUM to "0x882d3F58c41C1Fa1a2dF86A5FA46E512F50685CF",
            ChainType.BASE to "0xe1598cBA8BF281b96A3F34Fa632f072879716daF",
            ChainType.BITLAYER to "0xc8d8A3BdEEEdE5D03e5D80c8C82aF163C9Af923a",
            ChainType.BSC to "0xCBaA3ECbB073Ced44CE19aE883c8f3D128d105D2"
        )

        val stoneContractAddress = mapOf(
            ChainType.ARBITRUM to "0xa9f9398D3357F544b17Faa30FeB100B52DA9Bbdf",
            ChainType.BASE to "0x5dd366e109287375f78f5372fCA2fe5aBAfAd97B",
            ChainType.BITLAYER to "0x5d23B93D82010535B8d6E9bCdDE45b2E8e6a8E12",
            ChainType.BSC to "0x9d0F47A00F8f020d83ef8B9bA5c7a5BdFc14e365"
        )

        val usernetworkRebateAddress = mapOf(
            ChainType.ARBITRUM to "0x30999fF7ca26800e619eE69443433bE78F12e027",
            ChainType.BASE to "0x30999fF7ca26800e619eE69443433bE78F12e027",
            ChainType.BITLAYER to "0x30999fF7ca26800e619eE69443433bE78F12e027",
            ChainType.BSC to "0x30999fF7ca26800e619eE69443433bE78F12e027"
        )

        chains.forEach {
            val chain = chainRepository.findByChain(it)
            if(chain == null){
                chainRepository.save(Chain(
                    chain = it,
                    logo = "https://jaspervault.io/images/chain/${it.name.toLowerCase()}.png",
                    subgraphUrl = subgraphUrl[it],
                    nftFreeOptionContractAddress = freeOptionContractAddress[it],
                    tradingCreditContractAddress = tradingCreditContractAddress[it],
                    stoneContractAddress = stoneContractAddress[it],
                    userNetworkRebateAddress = usernetworkRebateAddress[it]
                ))
            } else {
                if(chain.subgraphUrl == null){
                    chain.subgraphUrl = subgraphUrl[it]
                }

                if(chain.nftFreeOptionContractAddress == null){
                    chain.nftFreeOptionContractAddress = freeOptionContractAddress[it]
                }

                if(chain.tradingCreditContractAddress == null){
                    chain.tradingCreditContractAddress = tradingCreditContractAddress[it]
                }

                if(chain.stoneContractAddress == null){
                    chain.stoneContractAddress = stoneContractAddress[it]
                }

                if(chain.userNetworkRebateAddress == null){
                    chain.userNetworkRebateAddress = usernetworkRebateAddress[it]
                }

                chainRepository.save(chain)
            }
        }
    }

    private fun initPartner(
        apiKey: String,
        channel: UserChannel
    ){

        //var apiKey = "daef2f53-b99d-4b43-aa4c-83e7ac6422e4"
        var partner = channelPartnerRepository.findByApiKey(apiKey)
        if(partner == null){
            channelPartnerRepository.save(ChannelPartner(
                apiKey = apiKey,
                channel = channel
            ))
        }
    }

    override fun run(args: org.springframework.boot.ApplicationArguments?) {
        logger.info("Application startup runner...")
        initDegenConfig()
        //initCurrency()
        loadIpBlackList()
        //initKolLevels()
        //initKolMails()
        initContractEvents()
        //initZeroPremiumBenefits()
        //updateZeroPremiumFeeUserPointRecords()
        //initActivity()
        //initDegenLPVaultConfig()
        initChain()

        initPartner("daef2f53-b99d-4b43-aa4c-83e7ac6422e4", UserChannel.ECHOOO)
        initPartner("e3e715ba-c78c-4738-9544-c98e94dec4f4", UserChannel.MARKET_PLACE)
        initPartner("44260296-e406-4498-b266-d075e37368d5", UserChannel.MINI_APP)
        initPartner("47ec64b5-e6a4-4e01-92e0-6aa2999174fd", UserChannel.MINI_APP_BACKEND)
        initPartner("a9e6b087-ccce-43c0-8404-6c7cae3b6451", UserChannel.BTC_FI)
        initPartner("dce1a8be-7697-481a-9023-a3591710aafc", UserChannel.BTC_FI_BACKEND)

        logger.info("Application startup runner finished")
    }
}