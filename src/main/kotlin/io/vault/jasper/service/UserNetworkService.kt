package io.vault.jasper.service

import io.vault.jasper.enums.ChainType
import io.vault.jasper.enums.Symbol
import io.vault.jasper.exception.BusinessException
import io.vault.jasper.exception.ResultEnum
import io.vault.jasper.model.*
import io.vault.jasper.repository.*
import org.slf4j.LoggerFactory
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.data.mongodb.core.MongoTemplate
import org.springframework.data.mongodb.core.aggregation.Aggregation
import org.springframework.data.mongodb.core.aggregation.ConvertOperators
import org.springframework.data.mongodb.core.query.Criteria
import org.springframework.data.repository.findByIdOrNull
import org.springframework.stereotype.Service
import org.web3j.crypto.Keys
import java.math.BigDecimal
import java.math.BigInteger
import java.text.SimpleDateFormat
import java.time.LocalDateTime
import java.util.*
import java.util.concurrent.ThreadLocalRandom

@Service
class UserNetworkService @Autowired constructor(
    private val userNetworkRebateRecordRepository: UserNetworkRebateRecordRepository,
    private val userNetworkRepository: UserNetworkRepository,
    private val currencyService: CurrencyService,
    private val userNetworkInviteCodeRepository: UserNetworkInviteCodeRepository,
    private val userRepository: UserRepository,
    private val mongoTemplate: MongoTemplate,
    private val userNetworkRebateSummaryRepository: UserNetworkRebateSummaryRepository,
    private val userNetworkChangeRateLogRepository: UserNetworkChangeRateLogRepository,
    private val referralInfoService: ReferralInfoService,
    private val userNetworkTeamRebateDailySummaryRepository: UserNetworkTeamRebateDailySummaryRepository,
){

    private val logger = LoggerFactory.getLogger(this::class.java)

    private val ROOT_ADDRESS = "0x7c164c8b5Dc8E61B0cBCD8214063865Bd3df1366"

    private val maxAgentLevel = 3

    fun calculateRebateRecords(
        oo: OptionOrder,
        premiumRate: BigDecimal
    ) {

        logger.info("User Network Service begin to calculate rebate record for option order: ${oo.buyer}")

        val quoteAsset = currencyService.getOptionQuoteAsset(oo.chain, null)
        val decimals = currencyService.getCurrencyDecimal(oo.chain, quoteAsset)

        val childAddress = Keys.toChecksumAddress(oo.buyer!!)
        var childUserNetwork = userNetworkRepository.findByAddressIgnoreCase(childAddress) ?: return
        var childNetworkRate = BigDecimal.ZERO
        var firstRebateRecord: UserNetworkRebateRecord? = null

        while(childUserNetwork.invitedNetworkId != "root"){

            val parentUserNetwork = userNetworkRepository.findByIdOrNull(childUserNetwork.invitedNetworkId) ?: return

            val rebateRate = parentUserNetwork.protocolFeePercentage.subtract(childNetworkRate)

            logger.info("User Network Service get parent user network ${parentUserNetwork.address} and rebate rate ${rebateRate}")

            if(rebateRate.compareTo(BigDecimal.ZERO) > 0) {

                val incentiveAmountBigDecimal =
                    oo.premiumFeePayInUsdt!!.multiply(rebateRate).setScale(0, BigDecimal.ROUND_DOWN)
                val incentiveAmount = incentiveAmountBigDecimal.movePointLeft(decimals)

                val existingRecord = userNetworkRebateRecordRepository.findByOptionOrderIdAndUserNetworkId(
                    oo.id!!,
                    parentUserNetwork.id!!
                )

                if (existingRecord == null) {
                    val record = userNetworkRebateRecordRepository.save(
                        UserNetworkRebateRecord(
                            optionOrderId = oo.id,
                            buyerAddress = Keys.toChecksumAddress(oo.buyer!!),
                            userId = parentUserNetwork.userId,
                            userNetworkId = parentUserNetwork.id,
                            userAddress = parentUserNetwork.address,
                            asset = Symbol.USDT, // 目前返佣固定为USDT
                            incentiveAmount = incentiveAmount,
                            premiumFee = oo.premiumFeePay ?: BigDecimal.ZERO,
                            premiumFeeInUsdt = oo.premiumFeePayInUsdt ?: BigDecimal.ZERO,
                            premiumFeeRate = premiumRate,
                            incentiveRate = parentUserNetwork.protocolFeePercentage,
                            actualIncentiveRate = rebateRate,
                        )
                    )

                    //Update team rebate daily summary
                    updateTeamRebateDailySummary(childUserNetwork, parentUserNetwork, record)

                    if(firstRebateRecord == null){
                        firstRebateRecord = record
                    }
                }
            }

            childUserNetwork = parentUserNetwork
            childNetworkRate = childUserNetwork.protocolFeePercentage
        }

        if(firstRebateRecord != null){
            updateUserNetworkAfterNewRebateRecord(firstRebateRecord)
        }
    }

    fun updateTeamRebateDailySummary(
        userNetwork: UserNetwork,
        parentNetwork: UserNetwork,
        parentNetworkRebateRecord: UserNetworkRebateRecord
    ): UserNetworkTeamRebateDailySummary {

        val formatter = SimpleDateFormat("yyyy-MM-dd")
        val dateString = formatter.format(Date())

        var existingDailySummary = userNetworkTeamRebateDailySummaryRepository.findByDateStringAndUserAddressAndParentAddress(
            dateString,
            userNetwork.address,
            parentNetwork.address
        )

        if(existingDailySummary != null) {
            existingDailySummary.toParentTeamPremium =
                existingDailySummary.toParentTeamPremium.add(parentNetworkRebateRecord.premiumFeeInUsdt)
            existingDailySummary.toParentTeamRebate =
                existingDailySummary.toParentTeamRebate.add(parentNetworkRebateRecord.incentiveAmount)
            existingDailySummary = userNetworkTeamRebateDailySummaryRepository.save(existingDailySummary)
        } else {
            val summary = UserNetworkTeamRebateDailySummary(
                dateString = dateString,
                userId = userNetwork.userId!!,
                userNetworkId = userNetwork.id!!,
                invitedNetworkId = userNetwork.invitedNetworkId,
                userAddress = userNetwork.address,
                parentAddress = parentNetwork.address,
                toParentTeamPremium = parentNetworkRebateRecord.premiumFeeInUsdt,
                toParentTeamRebate = parentNetworkRebateRecord.incentiveAmount
            )

            existingDailySummary = userNetworkTeamRebateDailySummaryRepository.save(summary)
        }

        return existingDailySummary
    }

    fun updateUserNetworkAfterNewRebateRecord(
        userNetworkRebateRecord: UserNetworkRebateRecord
    ) {

        logger.info("UserNetwork Service Update User Network After New Rebate Record for user network rebate record: ${userNetworkRebateRecord.id}")

        var userNetwork = userNetworkRepository.findByAddressIgnoreCase(userNetworkRebateRecord.buyerAddress) ?: return
        updateUserNetworkStatistics(userNetwork)

        while(userNetwork.invitedNetworkId != "root"){

            val parentUserNetwork = userNetworkRepository.findByIdOrNull(userNetwork.invitedNetworkId) ?: return
            updateUserNetworkStatistics(parentUserNetwork)

            userNetwork = parentUserNetwork
        }
    }

    fun bindUserNetworkInviteCode(
        address: String,
        inviteCode: String
    ): UserNetwork {

        val userNetworkInviteCode = userNetworkInviteCodeRepository.findByInviteCode(inviteCode)
        var invitorUserNetwork: UserNetwork? = null
        var percentage = BigDecimal.ZERO

        if(userNetworkInviteCode == null){
            invitorUserNetwork = userNetworkRepository.findByInviteCode(inviteCode)

            if(invitorUserNetwork == null){
                throw BusinessException(ResultEnum.INVALID_INVITE_CODE)
            }

        } else {

            if (userNetworkInviteCode.address == address) {
                throw BusinessException(ResultEnum.CAN_NOT_BIND_YOURSELF)
            }

            if (userNetworkInviteCode.bindAddress != null) {
                throw BusinessException(ResultEnum.INVITE_CODE_ALREADY_BIND)
            }

            invitorUserNetwork = userNetworkRepository.findByIdOrNull(userNetworkInviteCode.userNetworkId)
            percentage = userNetworkInviteCode.protocolFeePercentage
        }

        if(invitorUserNetwork == null){
            throw BusinessException(ResultEnum.USER_NETWORK_NOT_FOUND)
        }

        logger.info("UserNetwork Service invitor user network is : ${invitorUserNetwork.address}")

        val userNetwork = userNetworkRepository.findByAddressIgnoreCase(address)
        // logger.info("待查询地址：$address, userNetwork: $userNetwork")
        var user = userRepository.findByAddressIgnoreCase(address)

        if (userNetwork != null) {
            // logger.info("UserNetwork not null with $address")
            throw BusinessException(ResultEnum.USER_NETWORK_ALREADY_EXIST)
        }

        if(user != null){

            logger.info("UserNetwork Service bind user network invite code: ${user.address}")

            if(user.invitedUserId != null || user.useKolRebate == false){
                throw BusinessException(ResultEnum.USER_ALREADY_EXIST)
            }

            user.invitedUserId = invitorUserNetwork.userId
            user.useKolRebate = false
            user = userRepository.save(user)

        } else {

            logger.info("UserNetwork Service bind user network invite code user not exist, create new user")
            val checksumAddress = Keys.toChecksumAddress(address)
            val code = generateUserNetworkCode()
            user = userRepository.save(
                User(
                    address = checksumAddress,
                    inviteCode = code,
                    invitedUserId = invitorUserNetwork.userId,
                    useKolRebate = false
                )
            )
        }

        logger.info("UserNetwork Service bind user network invite code 2: ${user!!.address}")

        val level = invitorUserNetwork.level + 1

        var newUserNetwork = UserNetwork(
            address = user.address,
            inviteCode = user.inviteCode,
            userId = user.id!!,
            invitedUserId = invitorUserNetwork.userId,
            invitedNetworkId = invitorUserNetwork.id!!,
            level = level,
            protocolFeePercentage = percentage,
            tag = userNetworkInviteCode?.tag
        )

        newUserNetwork = userNetworkRepository.save(newUserNetwork)

        if(userNetworkInviteCode != null) {
            userNetworkInviteCode.bindUserNetworkId = newUserNetwork.id
            userNetworkInviteCode.bindAddress = newUserNetwork.address
            userNetworkInviteCode.bindTime = LocalDateTime.now()
            userNetworkInviteCodeRepository.save(userNetworkInviteCode)
        }

        updateMemberCount(newUserNetwork)

        val invitor = userRepository.findByIdOrNull(invitorUserNetwork.userId)
        if(invitor != null) {
            referralInfoService.updateReferralCountAtDate(invitor)
            referralInfoService.updateUserReferralCount(invitor)
        }

        return newUserNetwork
    }

    fun updateMemberCount(
        userNetwork: UserNetwork
    ) {

        logger.info("UserNetwork Service Update Member Count for user network: ${userNetwork.id}")
        var parentUserNetwork = userNetworkRepository.findByIdOrNull(userNetwork.invitedNetworkId)
        if(parentUserNetwork == null){
            return
        }

        parentUserNetwork.directMemberCount = userNetworkRepository.countByInvitedNetworkId(parentUserNetwork.id!!).toInt()
        parentUserNetwork = userNetworkRepository.save(parentUserNetwork)

        while(parentUserNetwork != null){

            parentUserNetwork.teamMemberCount += 1
            userNetworkRepository.save(parentUserNetwork)

            parentUserNetwork = userNetworkRepository.findByIdOrNull(parentUserNetwork.invitedNetworkId)
        }
    }

    /**
     * 更新用户以及其上级的业绩统计
     */
    fun updatePremiumFee(
        userNetworkRebateRecord: UserNetworkRebateRecord
    ) {

        logger.info("UserNetwork Service Update premium fee for user network rebate record: ${userNetworkRebateRecord.id}")
        val buyerAddress = userNetworkRebateRecord.buyerAddress
        val childUserNetwork = userNetworkRepository.findByAddressIgnoreCase(buyerAddress) ?: return

        childUserNetwork.selfPremium = childUserNetwork.selfPremium.add(userNetworkRebateRecord.premiumFeeInUsdt)
        userNetworkRepository.save(childUserNetwork)

        var parentUserNetwork = userNetworkRepository.findByIdOrNull(childUserNetwork.invitedNetworkId)
        if(parentUserNetwork == null){
            return
        }

        parentUserNetwork.directPremium = parentUserNetwork.directPremium.add(userNetworkRebateRecord.premiumFeeInUsdt)
        parentUserNetwork = userNetworkRepository.save(parentUserNetwork)

        while(parentUserNetwork != null){

            parentUserNetwork.teamPremium = parentUserNetwork.teamPremium.add(userNetworkRebateRecord.premiumFeeInUsdt)
            userNetworkRepository.save(parentUserNetwork)

            parentUserNetwork = userNetworkRepository.findByIdOrNull(parentUserNetwork.invitedNetworkId)
        }
    }

    fun isAuthorizedToInviteAgent(
        userNetwork: UserNetwork,
    ): Boolean {

        if(userNetwork.level >= maxAgentLevel){
            return false
        }

        return true
    }

    fun isPercentageValid(
        userNetwork: UserNetwork,
        percentage: BigDecimal
    ): Boolean {

        if(percentage < BigDecimal.ZERO){
            return false
        }

        // 不能大于自身返佣比例
        if(percentage > userNetwork.protocolFeePercentage){
            return false
        }

        return true
    }

    fun setAgentPercentage(
        userNetwork: UserNetwork,
        agentUserNetwork: UserNetwork,
        percentage: BigDecimal
    ) {

        if(!isAuthorizedToInviteAgent(userNetwork)){
            throw BusinessException(ResultEnum.NOT_AUTHORIZE_TO_SET_PERCENTAGE)
        }

        if(agentUserNetwork.invitedNetworkId != userNetwork.id){
            throw BusinessException(ResultEnum.NOT_ALLOW_TO_SET_PERCENTAGE)
        }

        if(!isPercentageValid(userNetwork, percentage)) {
            throw BusinessException(ResultEnum.INVALID_PROTOCOL_FEE_PERCENTAGE)
        }

        // 不能小于代理下级的返佣比例
        val childUserNetworks = userNetworkRepository.findByInvitedNetworkId(agentUserNetwork.id!!)
        for(childUserNetwork in childUserNetworks){
            if(percentage < childUserNetwork.protocolFeePercentage){
                throw BusinessException(ResultEnum.INVALID_PROTOCOL_FEE_PERCENTAGE)
            }
        }

        // 不能小于现有未使用的邀请码的返佣比例
        val inviteCodes = userNetworkInviteCodeRepository.findByAddressIgnoreCaseAndBindAddressIsNull(
            agentUserNetwork.address
        )

        for(inviteCode in inviteCodes){
            if(percentage < inviteCode.protocolFeePercentage){
                throw BusinessException(ResultEnum.INVALID_PROTOCOL_FEE_PERCENTAGE)
            }
        }

        val beforeRate = agentUserNetwork.protocolFeePercentage

        agentUserNetwork.protocolFeePercentage = percentage
        userNetworkRepository.save(agentUserNetwork)

        val userNetworkInviteCodes = userNetworkInviteCodeRepository.findByBindUserNetworkId(agentUserNetwork.id!!)
        for(userNetworkInviteCode in userNetworkInviteCodes){
            userNetworkInviteCode.protocolFeePercentage = percentage
            userNetworkInviteCodeRepository.save(userNetworkInviteCode)
        }

        createChangeRateLog(agentUserNetwork.address, beforeRate, percentage)
    }

    fun generateInviteCode(
        userNetwork: UserNetwork,
        percentage: BigDecimal
    ): UserNetworkInviteCode {

        if(!isAuthorizedToInviteAgent(userNetwork) && percentage > BigDecimal.ZERO){
            throw BusinessException(ResultEnum.CAN_NOT_GEN_INVITE_CODE)
        }

        if(!isPercentageValid(userNetwork, percentage)) {
            throw BusinessException(ResultEnum.INVALID_PROTOCOL_FEE_PERCENTAGE)
        }

        val inviteCode = generateUserNetworkCode()
        val userNetworkInviteCode = UserNetworkInviteCode(
            address = userNetwork.address,
            userNetworkId = userNetwork.id!!,
            inviteCode = inviteCode,
            protocolFeePercentage = percentage,
            tag = userNetwork.tag,
            level = userNetwork.level + 1
        )
        return userNetworkInviteCodeRepository.save(userNetworkInviteCode)
    }

    public fun generateUserNetworkCode(): String{

        var tryCount = 10;
        var new_invite_code = ""

        while (tryCount > 0) {

            tryCount --

            val length = 6
            val characters = "abcdefghigkmnprstuvwxyz1234567890"
            new_invite_code = ""

            for (i in 1..length) {
                val index = ThreadLocalRandom.current().nextInt(0, characters.length)
                new_invite_code += characters.substring(index, index+1)
            }

            logger.info("The new invite code is : $new_invite_code")

            val existLeft = userNetworkInviteCodeRepository.findByInviteCode(new_invite_code)
            if(existLeft != null){
                continue
            }

            break
        }

        if(tryCount == 0){
            throw BusinessException(ResultEnum.INVITE_CODE_NOT_FOUND)
        }

        return new_invite_code
    }

    /**
     * 计算下属为我贡献的返佣
     */
    fun calculateTotalRebate(
        userNetwork: UserNetwork
    ): BigDecimal {

        // 普通客户不需要计算
//        if (userNetwork.grade == UserNetworkGrade.USER) {
//            return BigDecimal.ZERO
//        }

        val userAddress = userNetwork.address

        // 下属为我贡献的返佣
        val criteria = Criteria.where(UserNetworkRebateRecord::userAddress.name).`is`(
            userAddress
        )

        val agg = Aggregation.newAggregation(
            Aggregation.match(criteria),
            Aggregation.group().sum(
                ConvertOperators.valueOf(
                    UserNetworkRebateRecord::incentiveAmount.name
                ).convertToDecimal()
            ).`as`("totalRebate")
        )
        val groupResults = mongoTemplate.aggregate(agg, UserNetworkRebateRecord::class.java, Map::class.java)
        val result = groupResults.mappedResults.firstOrNull()
        val totalRebate = (result?.get("totalRebate") ?: "0").toString()

        //logger.info("UserNetwork Service calculate total rebate for user network: $userAddress $totalRebate")

        return BigDecimal(totalRebate)
    }

    /**
     *  计算我的团队为上级贡献的返佣
     */
    fun calculateTeamRebateToParent(
        userNetwork: UserNetwork
    ): BigDecimal {

        val parentUserNetwork = userNetworkRepository.findByIdOrNull(userNetwork.invitedNetworkId) ?: return BigDecimal.ZERO
        val parentRate = parentUserNetwork.protocolFeePercentage
        val selfRate = userNetwork.protocolFeePercentage

        // 找到我的团队为我贡献的交易量
        val criteria = Criteria.where(UserNetworkRebateRecord::userAddress.name).`is`(
            userNetwork.address
        )

        val agg = Aggregation.newAggregation(
            Aggregation.match(criteria),
            Aggregation.group().sum(
                ConvertOperators.valueOf(
                    UserNetworkRebateRecord::premiumFeeInUsdt.name
                ).convertToDecimal()).`as`("totalRebate")
        )
        val groupResults = mongoTemplate.aggregate(agg, UserNetworkRebateRecord::class.java, Map::class.java)
        val result = groupResults.mappedResults.firstOrNull()
        val totalRebate = (result?.get("totalRebate")?:"0").toString()

        val teamRebateAmount = BigDecimal(totalRebate)
        val teamRebate = teamRebateAmount.multiply(parentRate.subtract(selfRate)).movePointLeft(6)

        // 找我本身为上级贡献的返佣
        val criteria1 = Criteria.where(UserNetworkRebateRecord::userAddress.name).`is`(
            parentUserNetwork.address
        )

        criteria1.and(UserNetworkRebateRecord::buyerAddress.name).`is`(
            userNetwork.address
        )

        val agg1 = Aggregation.newAggregation(
            Aggregation.match(criteria1),
            Aggregation.group().sum(
                ConvertOperators.valueOf(
                    UserNetworkRebateRecord::incentiveAmount.name
                ).convertToDecimal()).`as`("selfTotalRebate")
        )
        val groupResults1 = mongoTemplate.aggregate(agg1, UserNetworkRebateRecord::class.java, Map::class.java)
        val result1 = groupResults1.mappedResults.firstOrNull()
        val selfRebate = (result1?.get("selfTotalRebate")?:"0").toString()

        //logger.info("UserNetwork Service calculate team rebate for user network: ${userNetwork.address}, teamRebate: $teamRebate, selfRebate: $selfRebate")

        return teamRebate.add(BigDecimal(selfRebate))
    }

    fun getAllRootAddress(): List<String>{
        return userNetworkRepository.findByInvitedNetworkId("root").map { it.address }
    }

    fun calculateTotalPremium(
        buyerAddress: String
    ): BigDecimal {

        val criteria = Criteria.where(UserNetworkRebateRecord::buyerAddress.name).`is`(buyerAddress)
        criteria.and(UserNetworkRebateRecord::userAddress.name).`in`(getAllRootAddress())

        val agg = Aggregation.newAggregation(
            Aggregation.match(criteria),
            Aggregation.group().sum(
                ConvertOperators.valueOf(
                    UserNetworkRebateRecord::premiumFeeInUsdt.name
                ).convertToDecimal()).`as`("totalPremium")
        )
        val groupResults = mongoTemplate.aggregate(agg, UserNetworkRebateRecord::class.java, Map::class.java)
        val result = groupResults.mappedResults.firstOrNull()
        val totalPremium = (result?.get("totalPremium")?:"0").toString()

        val decimal = currencyService.getCurrencyDecimal(ChainType.ARBITRUM, Symbol.USDT) // 权利金以USDT为单位
        val readableTotalPremium = BigDecimal(totalPremium).movePointLeft(decimal)

        //logger.info("UserNetwork Service Calculate Total Premium for user network: $buyerAddress, total premium: $readableTotalPremium")

        return readableTotalPremium
    }

    fun calculateUserNetworkMemberCountAndPremium(){

        val allUserNetworks = userNetworkRepository.findAll().sortedByDescending { it.level }

        for(userNetwork in allUserNetworks){
            updateUserNetworkStatistics(userNetwork)
        }
    }

    fun updateUserNetworkStatistics(
        userNetwork: UserNetwork
    ) {

        //logger.info("UserNetwork Service Update User Network Statistics for user network: ${userNetwork.address} level ${userNetwork.level}")

        val invitedUserNetworks = userNetworkRepository.findByInvitedNetworkId(userNetwork.id!!)
        val directMemberCount = invitedUserNetworks.size

        val selfPremium = calculateTotalPremium(userNetwork.address)
        var directPremium = BigDecimal.ZERO
        var teamPremium = BigDecimal.ZERO
        var teamMemberCount = 0

        for(invitedUserNetwork in invitedUserNetworks){
            teamPremium = teamPremium.add(invitedUserNetwork.teamPremium.add(invitedUserNetwork.selfPremium))
            directPremium = directPremium.add(invitedUserNetwork.selfPremium)
            teamMemberCount += invitedUserNetwork.teamMemberCount + 1
        }

        userNetwork.directMemberCount = directMemberCount
        userNetwork.teamMemberCount = teamMemberCount
        userNetwork.selfPremium = selfPremium
        userNetwork.directPremium = directPremium
        userNetwork.teamPremium = teamPremium

        userNetwork.parentRebate = calculateTeamRebateToParent(userNetwork)
        userNetwork.teamRebate = calculateTotalRebate(userNetwork)

        userNetworkRepository.save(userNetwork)
    }

    fun isAncestor(
        ancestorAddress: String,
        userAddress: String
    ): Boolean {

        val userNetwork = userNetworkRepository.findByAddressIgnoreCase(userAddress) ?: throw BusinessException(ResultEnum.USER_NETWORK_NOT_FOUND)
        val ancestorUserNetwork = userNetworkRepository.findByAddressIgnoreCase(ancestorAddress) ?: throw BusinessException(ResultEnum.USER_NETWORK_NOT_FOUND)

        if(userNetwork.id == ancestorUserNetwork.id){
            return true
        }

        var parentUserNetwork = userNetworkRepository.findByIdOrNull(userNetwork.invitedNetworkId)
        while(parentUserNetwork != null){

            if(parentUserNetwork.id == ancestorUserNetwork.id){
                return true
            }

            parentUserNetwork = userNetworkRepository.findByIdOrNull(parentUserNetwork.invitedNetworkId)
        }

        return false
    }

    fun createChangeRateLog(
        address: String,
        beforeRate: BigDecimal,
        afterRate: BigDecimal
    ): UserNetworkChangeRateLog {

        val log = UserNetworkChangeRateLog(
            address = address,
            beforeRate = beforeRate,
            afterRate = afterRate
        )

        return userNetworkChangeRateLogRepository.save(log)
    }

    fun userNetworkCanSetAgent(
        userNetwork: UserNetwork
    ): Boolean{

        if(userNetwork.protocolFeePercentage == BigDecimal.ZERO){
            return false
        }

        if(userNetwork.level >= maxAgentLevel){
            return false
        }

        return true
    }
}