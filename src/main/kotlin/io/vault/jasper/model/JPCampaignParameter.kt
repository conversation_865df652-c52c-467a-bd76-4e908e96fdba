package io.vault.jasper.model

import io.vault.jasper.enums.ChainType
import org.springframework.data.annotation.Id
import org.springframework.data.mongodb.core.mapping.Document
import org.springframework.data.mongodb.core.mapping.Field
import java.time.LocalDateTime

@Document("jp_campaign_parameter")
data class JPCampaignParameter(
    @Id
    val id: String? = null,
    @Field("start_date")
    var startDate: LocalDateTime = LocalDateTime.now(),
    @Field("rebate_chain")
    var rebateChain: ChainType = ChainType.ARBITRUM,
    @Field("rebate_contract_address")
    var rebateContractAddress: String = "0x943105D4Ff1436c79d1D62d65Fc55816485A89dd",
    @Field("rebate_count")
    var rebateCount: Int = 10000,
    @Field("rebate_task_switch")
    var rebateTaskSwitch: Boolean = true, // 推送包赔任务的开关
    @Field("retweet_id")
    var retweetId: String = "1825800581090545926",
    @Field("follow_link")
    var followLink: String = "https://twitter.com/JPNetworkDeFi/status/1447860000000000000",
    @Field("additional_rebate_count")
    var additionalRebateCount: Int = 100,
)