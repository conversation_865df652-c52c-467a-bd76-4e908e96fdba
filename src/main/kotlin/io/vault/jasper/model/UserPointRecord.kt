package io.vault.jasper.model

import io.vault.jasper.enums.OptionDirection
import io.vault.jasper.enums.Symbol
import org.springframework.data.annotation.Id
import org.springframework.data.mongodb.core.index.Indexed
import org.springframework.data.mongodb.core.mapping.Document
import org.springframework.data.mongodb.core.mapping.Field
import org.springframework.data.mongodb.core.mapping.FieldType
import java.math.BigDecimal
import java.time.LocalDateTime

@Document("user_point_record")
data class UserPointRecord(
    @Id
    val id: String? = null,
    @Indexed(background = true)
    @Field("address")
    val address: String, // 用户地址
    @Field("option_order_id")
    @Indexed(unique = true, background = true)
    val optionOrderId: String, // 期权订单ID
    @Field("option_order_action")
    val optionOrderAction: OptionOrderAction = OptionOrderAction.BUY, // 期权订单操作
    @Field("option_order_direction")
    val optionOrderDirection: OptionDirection, // 期权方向
    @Field("option_order_type")
    val optionOrderType: OrderType, // 期权类型
    @Field("underlying_symbol")
    val underlyingSymbol: Symbol, // 标的物
    @Field("expiry_in_hour")
    val expiryInHour: String, // 期权到期时间
    @Field("expiry_date")
    val expiryDate: LocalDateTime,
    val created: LocalDateTime,
    var status: OptionStatus = OptionStatus.EXECUTED, // 期权状态
    @Field(targetType = FieldType.DECIMAL128)
    var premium: BigDecimal = BigDecimal.ZERO, // 总共支付的权利金和费用
    @Field(name = "premium_in_usdt", targetType = FieldType.DECIMAL128)
    var premiumInUsdt: BigDecimal = BigDecimal.ZERO, // 总共支付的权利金和费用
    @Field(targetType = FieldType.DECIMAL128)
    var profit: BigDecimal = BigDecimal.ZERO, // 总共获得的利润
    @Field(name = "net_profit", targetType = FieldType.DECIMAL128)
    var netProfit: BigDecimal = BigDecimal.ZERO, // 净利润
    @Field(name = "premium_point", targetType = FieldType.DECIMAL128)
    var premiumPoint: BigDecimal = BigDecimal.ZERO, // 权利金积分 JPoint
    @Field(name = "net_profit_point", targetType = FieldType.DECIMAL128)
    var netProfitPoint: BigDecimal = BigDecimal.ZERO, // 净利润积分
    @Field(name = "total_point", targetType = FieldType.DECIMAL128)
    var totalPoint: BigDecimal = BigDecimal.ZERO, // 总积分
    @Field(name = "loyalty_point", targetType = FieldType.DECIMAL128)
    var loyaltyPoint: BigDecimal = BigDecimal.ZERO, // 忠诚积分
    @Field(targetType = FieldType.DECIMAL128)
    var volume: BigDecimal = BigDecimal.ZERO,
)