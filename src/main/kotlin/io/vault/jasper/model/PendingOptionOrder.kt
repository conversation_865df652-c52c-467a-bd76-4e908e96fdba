package io.vault.jasper.model

import io.vault.jasper.enums.ChainType
import io.vault.jasper.service.blockchain.EvmService
import org.springframework.data.annotation.Id
import org.springframework.data.mongodb.core.index.CompoundIndex
import org.springframework.data.mongodb.core.index.CompoundIndexes
import org.springframework.data.mongodb.core.index.Indexed
import org.springframework.data.mongodb.core.mapping.Document
import org.springframework.data.mongodb.core.mapping.Field
import java.time.LocalDateTime

@Document("pending_option_order")
// 组合索引
@CompoundIndexes(
    CompoundIndex(
        name = "chain_txhash_orderid",
        def = "{'chain': 1, 'tx_hash': 1, 'on_chain_order_id': 1}",
        background = true
    )
)
data class PendingOptionOrder(
    @Id
    val id: String? = null,

    val chain: ChainType,

    @Field("tx_hash")
    val txHash: String,

    @Indexed(background = true)
    var saved: Boolean = false,

    var message: String? = null,

    @Field("tx_block_height")
    var txBlockHeight: Long?,

    @Field("block_time")
    var blockTime: LocalDateTime?,

    @Field("option_order_struct")
    var optionOrderStruct: EvmService.OptionOrderStruct? = null,

    @Field("on_chain_order_id")
    var onChainOrderId: String? = null,

    val created: LocalDateTime = LocalDateTime.now()
)