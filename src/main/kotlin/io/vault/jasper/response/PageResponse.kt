package io.vault.jasper.response

import io.swagger.annotations.ApiModel
import io.swagger.annotations.ApiModelProperty

@ApiModel
data class PageResponse<T>(
    @ApiModelProperty("当前页", required = true)
    val page: Int,
    @ApiModelProperty("每页数量", required = true)
    val pageSize: Int,
    @ApiModelProperty("总页数", required = true)
    val totalPage: Int,
    @ApiModelProperty("总数量", required = true)
    val total: Long,
    @ApiModelProperty("是否有下一页", required = true)
    val hasNext: <PERSON><PERSON>an,
    @ApiModelProperty("数据", required = true)
    val data: List<T>
)
