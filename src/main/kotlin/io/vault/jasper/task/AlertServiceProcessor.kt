package io.vault.jasper.task

import io.vault.jasper.service.AlertService
import org.slf4j.LoggerFactory
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.beans.factory.annotation.Value
import org.springframework.context.annotation.Profile
import org.springframework.http.client.SimpleClientHttpRequestFactory
import org.springframework.scheduling.annotation.Scheduled
import org.springframework.stereotype.Service
import org.springframework.web.client.RestTemplate
import java.time.LocalDateTime

@Service
@Profile("prod")
class AlertServiceProcessor @Autowired constructor(
    val alertService: AlertService
) {

    companion object {
        // 严重警报
        const val EMERGENCY_URL = "https://uptime.betterstack.com/api/v1/heartbeat/************************"
        // 警报
        const val ALERT_URL = "https://uptime.betterstack.com/api/v1/heartbeat/************************"
        // 通知
        const val WARNING_URL = "https://uptime.betterstack.com/api/v1/heartbeat/************************"

        private const val TIMEOUT = 5000 // 5 seconds
    }

    private val logger = LoggerFactory.getLogger(this::class.java)
    private val restTemplate: RestTemplate = createRestTemplateWithTimeout()

    @Value("\${schedule_task_on}")
    lateinit var scheduleOn: String

    private fun createRestTemplateWithTimeout(): RestTemplate {
        val factory = SimpleClientHttpRequestFactory()
        factory.setConnectTimeout(TIMEOUT) // 连接超时5秒
        factory.setReadTimeout(TIMEOUT)    // 读取超时5秒

        val template = RestTemplate(factory)
        template.requestFactory = factory
        return template
    }

    @Scheduled(fixedDelay = 20 * 1000, initialDelay = 5 * 1000) // 每20秒执行一次
    fun sendHeartbeats() {
        if(scheduleOn == "false"){
            return
        }
        try {
            val timestamp = LocalDateTime.now()
            logger.info("Sending heartbeat requests at $timestamp")

            // 根据状态决定是否发送请求
            if (alertService.emergencyEnabled.get()) {
                restTemplate.getForObject(EMERGENCY_URL, String::class.java)
                logger.info("Emergency heartbeat sent")
            }

            if (alertService.alertEnabled.get()) {
                restTemplate.getForObject(ALERT_URL, String::class.java)
                logger.info("Alert heartbeat sent")
            }

            if (alertService.warningEnabled.get()) {
                restTemplate.getForObject(WARNING_URL, String::class.java)
                logger.info("Warning heartbeat sent")
            }

            logger.info("Heartbeat requests completed successfully")
        } catch (e: Exception) {
            logger.error("Failed to send heartbeat requests: ${e.message}", e)
        } finally {
            alertService.resetAllStates()
        }
    }

}