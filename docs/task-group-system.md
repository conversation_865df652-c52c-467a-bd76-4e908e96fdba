# 任务组系统

任务组系统是对现有活动系统的扩展，允许管理员创建任务组，并将任务分配到不同的任务组中。活动可以包含多个任务组，每个任务组可以包含多个任务。任务组和任务之间的关系是一对多的。

## 概述

任务组系统由以下组件组成：

1. **任务组(TaskGroup)**: 一个任务组包含一组相关的任务，例如"社交媒体任务"、"账户绑定任务"等。
2. **活动(Campaign)**: 一个活动可以包含多个任务组和直接关联的任务。

## 数据模型

### TaskGroup

```kotlin
@Document("task_groups")
data class TaskGroup(
    @Id
    val id: String? = null,
    
    @Indexed(background = true)
    val title: String,
    
    @Field("task_ids")
    val taskIds: List<String> = listOf(),
    
    @CreatedDate
    val created: LocalDateTime = LocalDateTime.now(),
    
    @LastModifiedDate
    val updated: LocalDateTime = LocalDateTime.now(),
    
    @Field("i18n_info")
    var i18nInfo: MutableMap<String, I18nTaskGroupInfo>? = null
)

data class I18nTaskGroupInfo(
    val title: String,
    val description: String,
    val icon: String = ""
)
```

### Campaign (更新)

```kotlin
@Document("campaigns")
data class Campaign(
    @Id
    val id: String? = null,
    
    @Indexed(background = true, unique = true)
    val name: String,
    
    @Field("start_time")
    val startTime: LocalDateTime,
    
    @Field("end_time")
    val endTime: LocalDateTime,
    
    @Field("task_ids")
    val taskIds: List<String> = listOf(),
    
    @Field("task_group_ids")
    val taskGroupIds: List<String> = listOf(),
    
    // ... 其他字段
)
```

## API 端点

### 任务组管理 API

- `POST /api/admin/task-groups`: 创建一个新的任务组
- `GET /api/admin/task-groups/{id}`: 获取一个任务组
- `POST /api/admin/task-groups/{taskGroupId}/tasks/{taskId}`: 将任务添加到任务组
- `DELETE /api/admin/task-groups/{taskGroupId}/tasks/{taskId}`: 从任务组中移除任务
- `GET /api/admin/task-groups/{taskGroupId}/tasks`: 获取任务组中的所有任务

### 活动 API (更新)

- `GET /campaigns/{id}/task-groups`: 获取活动的所有任务组
- `GET /campaigns/{id}/task-groups/{taskGroupId}/tasks`: 获取活动中特定任务组的所有任务

## 服务类

### TaskGroupService

提供以下功能：

- 创建任务组
- 获取任务组
- 将任务添加到任务组
- 从任务组中移除任务
- 获取任务组中的所有任务
- 获取任务所属的所有任务组

### CampaignService (更新)

更新以支持任务组：

- 创建活动时验证任务组 ID
- 获取活动的所有任务组
- 获取活动的所有任务（包括直接关联的任务和通过任务组关联的任务）
- 检查任务是否属于活动（直接关联或通过任务组关联）

## 多语言支持

任务组支持多语言，通过 `i18nInfo` 字段存储不同语言的标题、描述和图标：

```kotlin
@Field("i18n_info")
var i18nInfo: MutableMap<String, I18nTaskGroupInfo>? = null

data class I18nTaskGroupInfo(
    val title: String,
    val description: String,
    val icon: String = ""
)
```

## 使用示例

### 创建任务组

```kotlin
val socialMediaTaskGroup = TaskGroup(
    title = "Social Media Tasks",
    i18nInfo = mutableMapOf(
        "en" to I18nTaskGroupInfo(
            title = "Social Media Tasks",
            description = "Complete social media tasks to earn rewards",
            icon = "https://assets.jaspervault.io/icons/social-media.png"
        ),
        "zh" to I18nTaskGroupInfo(
            title = "社交媒体任务",
            description = "完成社交媒体任务以获得奖励",
            icon = "https://assets.jaspervault.io/icons/social-media.png"
        )
    )
)

val savedTaskGroup = taskGroupService.createTaskGroup(socialMediaTaskGroup)
```

### 将任务添加到任务组

```kotlin
// 方法1：通过TaskGroupService
val updatedTaskGroup = taskGroupService.addTaskToTaskGroup(taskGroupId, taskId)

// 方法2：直接更新TaskGroup
val taskGroup = taskGroupRepository.findById(taskGroupId).orElseThrow()
val updatedTaskIds = taskGroup.taskIds.toMutableList().apply { add(taskId) }
val updatedTaskGroup = taskGroup.copy(taskIds = updatedTaskIds)
taskGroupRepository.save(updatedTaskGroup)
```

### 创建包含任务组的活动

```kotlin
val campaign = Campaign(
    name = "Jasper Vault Campaign",
    startTime = LocalDateTime.parse("2025-05-22T00:00:00"),
    endTime = LocalDateTime.parse("2025-05-31T23:59:59"),
    taskIds = directTasks.map { it.id!! },
    taskGroupIds = taskGroups.map { it.id!! },
    totalRewardAmount = "10000",
    smartContractAddress = "******************************************",
    active = true
)

val savedCampaign = campaignService.createCampaign(campaign)
```

### 获取活动的所有任务

```kotlin
val allTasks = campaignService.getTasksForCampaign(campaignId)
```

这将返回活动直接关联的任务和通过任务组关联的任务的组合列表。
