package io.vault.jasper.repository

import io.vault.jasper.model.UserNetworkRebateRecord
import io.vault.jasper.model.UserNetworkRebateRecordStatus
import io.vault.jasper.model.UserNetworkRebateSummary
import org.springframework.data.domain.Page
import org.springframework.data.domain.Pageable
import org.springframework.data.mongodb.repository.MongoRepository
import org.springframework.stereotype.Repository
import java.time.LocalDateTime

@Repository
interface UserNetworkRebateSummaryRepository : MongoRepository<UserNetworkRebateSummary, String> {

    fun findByUserAddress(
        userAddress: String
    ): UserNetworkRebateSummary?
}