package io.vault.jasper.model

import io.vault.jasper.enums.ChainType
import org.springframework.data.annotation.Id
import org.springframework.data.mongodb.core.mapping.Document
import org.springframework.data.mongodb.core.mapping.Field
import java.math.BigDecimal
import java.time.LocalDateTime

@Document("airdrop_parameter")
data class AirdropParameter(
    @Id
    val id: String? = null,
    @Field("start_date")
    var startDate: LocalDateTime = LocalDateTime.now(),
    @Field("free_mint_chain")
    var freeMintChain: ChainType = ChainType.ARBITRUM,
    @Field("free_mint_contract_address")
    var freeMintContractAddress: String = "0x943105D4Ff1436c79d1D62d65Fc55816485A89dd",
    @Field("free_mint_task_switch")
    var freeMintTaskSwitch: Boolean = true, // 推送Free Mint任务的开关
    @Field("first_trade_rebate_task_switch")
    var firstTradeRebateTaskSwitch: Boolean = true, // 推送First Trade Rebate任务的开关
    @Field("arb_airdrop_task_switch")
    var arbAirdropTaskSwitch: Boolean = true, // 推送 Arb Airdrop 任务的开关
    @Field("user_campaign_task_switch")
    var userCampaignTaskSwitch: Boolean = true, // 统计用户空投活动的开关
    @Field("free_mint_nft_id")
    var freeMintNFTId: String = "8",
    @Field("free_mint_discount_id")
    var freeMintDiscountId: String = "3",
    @Field("free_mint_discount_count")
    var freeMintDiscountCount: Int = 1,
    @Field("free_trade_count")
    var freeTradeCount: Int = 2, // 伞下多少个不同的地址进行了交易，才能获得免费交易权益
    @Field("social_task_count")
    var socialTaskCount: Int = 50000,
    @Field("invitation_task_count")
    var invitationTaskCount: Int = 50000,
    @Field("trading_task_count")
    var tradingTaskCount: Int = 30000,
    @Field("first_trade_discord_level")
    var firstTradeDiscordLevel: Int = 1,
    @Field("retweet_id")
    var retweetId: String = "1825800581090545926"
)