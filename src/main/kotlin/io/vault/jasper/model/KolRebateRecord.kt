package io.vault.jasper.model

import io.vault.jasper.enums.Symbol
import org.springframework.data.annotation.CreatedDate
import org.springframework.data.annotation.Id
import org.springframework.data.annotation.LastModifiedDate
import org.springframework.data.mongodb.core.index.Indexed
import org.springframework.data.mongodb.core.mapping.Document
import java.math.BigDecimal
import java.math.BigInteger
import java.time.LocalDateTime

@Document("kol_rebate_record")
data class KolRebateRecord(
    @Id
    val id: String? = null,
    @Indexed(unique = true)
    val optionOrderId: String,
    val buyerAddress: String,
    val kolId: String,
    val kolLevel: String,
    val asset: Symbol,
    /**
     * 实际返佣
     */
    var incentiveAmount: BigDecimal,
    val premiumFee: BigDecimal,
    val premiumFeeInUsdt: BigDecimal? = null,
    val premiumFeeRate: BigDecimal,
    val incentiveRate: BigDecimal,
    var status: KolRebateRecordStatus = KolRebateRecordStatus.PENDING,
    var settleTxId: String? = null,
    @CreatedDate
    val created: LocalDateTime = LocalDateTime.now(),
    @LastModifiedDate
    val updated: LocalDateTime = LocalDateTime.now()
) {}

enum class KolRebateRecordStatus {
    PENDING,
    SETTLED
}