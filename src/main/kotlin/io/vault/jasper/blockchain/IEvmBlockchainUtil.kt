package io.vault.jasper.blockchain

import io.vault.jasper.enums.ChainType
import io.vault.jasper.repository.BlockchainGasPriceRepository
import io.vault.jasper.repository.BlockchainRepository
import okhttp3.OkHttpClient
import org.slf4j.LoggerFactory
import org.springframework.core.env.Environment
import org.springframework.core.env.get
import org.web3j.abi.FunctionEncoder
import org.web3j.abi.FunctionReturnDecoder
import org.web3j.abi.TypeReference
import org.web3j.abi.datatypes.Address
import org.web3j.abi.datatypes.Bool
import org.web3j.abi.datatypes.Function
import org.web3j.abi.datatypes.Type
import org.web3j.abi.datatypes.generated.Uint256
import org.web3j.contracts.eip20.generated.ERC20
import org.web3j.crypto.Credentials
import org.web3j.crypto.ECKeyPair
import org.web3j.crypto.RawTransaction
import org.web3j.crypto.TransactionEncoder
import org.web3j.protocol.Web3j
import org.web3j.protocol.core.DefaultBlockParameterName
import org.web3j.protocol.core.methods.request.Transaction
import org.web3j.protocol.core.methods.response.EthCall
import org.web3j.protocol.core.methods.response.TransactionReceipt
import org.web3j.protocol.http.HttpService
import org.web3j.tx.ReadonlyTransactionManager
import org.web3j.tx.Transfer
import org.web3j.tx.gas.ContractGasProvider
import org.web3j.utils.Convert
import org.web3j.utils.Numeric
import java.math.BigDecimal
import java.math.BigInteger
import java.util.concurrent.TimeUnit

abstract class IEvmBlockchainUtil(
    private val environment: Environment,
    private val blockchainGasPriceRepository: BlockchainGasPriceRepository
) : IBlockchainUtil, ContractGasProvider {

    private val logger = LoggerFactory.getLogger(this::class.java)

    abstract val chainType: ChainType

    private val envPrefix: String
        get() = "blockchain.${chainType.name.lowercase()}"

    val chainId: Long
        get() {
            val chainId = environment["${envPrefix}.chain_id"] ?: throw IllegalArgumentException("chainId is required")
            return chainId.toLong()
        }

    val endpoint: String
        get() = environment["${envPrefix}.endpoint"] ?: throw IllegalArgumentException("endpoint is required")

    private val username: String
        get() = environment["${envPrefix}.username"] ?: ""

    private val password: String
        get() = environment["${envPrefix}.password"] ?: ""

    val web3j: Web3j by lazy {
        val okClientBuilder = OkHttpClient().newBuilder()
            .connectTimeout(10, TimeUnit.SECONDS)
            .writeTimeout(10, TimeUnit.SECONDS)
            .readTimeout(10, TimeUnit.SECONDS)
        if (username.isNotBlank() && password.isNotBlank()) {
            okClientBuilder.addInterceptor(EvmAuthenticationInterceptor(username, password))
        }
        val okClient = okClientBuilder.build()
        Web3j.build(HttpService(endpoint, okClient))
    }

    val gasLimit: Long = 21_000L

    val emptyAddress = "0x0000000000000000000000000000000000000000"

    override fun getGasPrice(contractFunc: String?): BigInteger {
        val blockchain = blockchainGasPriceRepository.findFirstByChain(chainType) ?: throw IllegalArgumentException("Blockchain not found")

        return blockchain.gasPrice
    }

    @Deprecated("Deprecated in Java", ReplaceWith("getGasPrice(\"\")"))
    override fun getGasPrice() = getGasPrice("")

    override fun getGasLimit(contractFunc: String?): BigInteger {
        val blockchain = blockchainGasPriceRepository.findFirstByChain(chainType) ?: throw IllegalArgumentException("Blockchain not found")

        return if (contractFunc.isNullOrBlank()) {
            blockchain.gasLimit.toBigInteger()
        } else {
            blockchain.contractGasLimit.toBigInteger()
        }
    }

    @Deprecated("Deprecated in Java", ReplaceWith("getGasLimit(\"\")"))
    override fun getGasLimit() = getGasLimit("")

    override fun getTokenDecimals(contract: String): Int {
        val transactionManager = ReadonlyTransactionManager(web3j, emptyAddress)
        val erc20Contract = ERC20.load(contract, web3j, transactionManager, this)

        return erc20Contract.decimals().send().toInt()
    }

    override fun getGasBalance(address: String): BigDecimal {
        val result = web3j.ethGetBalance(address, DefaultBlockParameterName.LATEST).send()
        val balance = BigDecimal(result.balance)

        return Convert.fromWei(balance, Convert.Unit.ETHER)
    }

    override fun getTokenBalance(address: String, contract: String): BigDecimal {
        val transactionManager = ReadonlyTransactionManager(web3j, address)
        val erc20Contract = ERC20.load(contract, web3j, transactionManager, this)
        val decimals = erc20Contract.decimals().send().toInt()
        val balanceInBigInt = erc20Contract.balanceOf(address).send()

        return BigDecimal(balanceInBigInt).divide(BigDecimal.TEN.pow(decimals))
    }

    override fun getBalance(address: String, contract: String?): BigDecimal {
        //logger.info("Getting balance of $address in $contract")
        return when (contract) {
            null -> getGasBalance(address)
            "******************************************" -> getGasBalance(address)
            else -> getTokenBalance(address, contract)
        }
    }

    override fun getTokenBalanceBigInteger(address: String, contract: String): BigDecimal {
        val transactionManager = ReadonlyTransactionManager(web3j, address)
        val erc20Contract = ERC20.load(contract, web3j, transactionManager, this)
        val balanceInBigInt = erc20Contract.balanceOf(address).send()

        return BigDecimal(balanceInBigInt)
    }

    fun getNonce(wallet: String): BigInteger {
        return web3j.ethGetTransactionCount(wallet, DefaultBlockParameterName.PENDING).send().transactionCount
    }

    /**
     * 签名交易
     */
    private fun signTransaction(
        nonce: BigInteger, gasPrice: BigInteger, gasLimit: BigInteger, to: String?,
        value: BigInteger, data: String?, privateKey: String
    ): String? {
        val signedMessage: ByteArray
        val rawTransaction = RawTransaction.createTransaction(nonce, gasPrice, gasLimit, to, value, data)
        val ecKeyPair = ECKeyPair.create(Numeric.toBigInt(privateKey))
        val credentials = Credentials.create(ecKeyPair)

        // chainId=-1 means default network
        signedMessage = if (chainId > -1) {
            TransactionEncoder.signMessage(rawTransaction, chainId, credentials)
        } else {
            TransactionEncoder.signMessage(rawTransaction, credentials)
        }

        return Numeric.toHexString(signedMessage)
    }

    override fun sendGas(
        wallet: Pair<String, String>,
        to: String,
        amount: BigDecimal
        ): String {
        val (fromAddress, privateKey) = wallet
        val nonce = web3j.ethGetTransactionCount(fromAddress, DefaultBlockParameterName.PENDING).send().transactionCount
        val value = Convert.toWei(amount, Convert.Unit.ETHER).toBigInteger()
        val data = ""

        val signedData = signTransaction(
            nonce,
            getGasPrice(""),
            gasLimit.toBigInteger(),
            to,
            value,
            data,
            privateKey
        ) ?: throw Exception("Failed to sign transaction")
        val ethSendTransaction = web3j.ethSendRawTransaction(signedData).send()
        val hash = ethSendTransaction.transactionHash
        if (ethSendTransaction.error != null) {
            logger.info("Error Message: ${ethSendTransaction.error.message}")
        }

        return hash
    }

    override fun sendToken(wallet: Pair<String, String>, to: String, amount: BigDecimal, contract: String): String {
        val erc20Contract = ERC20.load(contract, web3j, Credentials.create(wallet.second), this)
        val decimals = erc20Contract.decimals().send().toInt()
        val amountInInteger = amount.multiply(BigDecimal.TEN.pow(decimals)).toBigInteger()
        logger.info("Sending ${erc20Contract.symbol()}: $amount to $to")
        val receipt = erc20Contract.transfer(to, amountInInteger).send()
        logger.info("Transaction hash: ${receipt.transactionHash}, Gas Used: ${receipt.gasUsed}")

        return receipt.transactionHash
    }

//    fun sendGasWithNonce(
//        wallet: Pair<String, String>,
//        to: String,
//        amount: BigDecimal,
//        nonce: BigInteger
//    ): String {
//        val (fromAddress, privateKey) = wallet
//
//        val value = Convert.toWei(amount, Convert.Unit.ETHER).toBigInteger()
//        val data = ""
//
//        val signedData = signTransaction(
//            nonce,
//            getGasPrice(""),
//            gasLimit.toBigInteger(),
//            to,
//            value,
//            data,
//            privateKey
//        ) ?: throw Exception("Failed to sign transaction")
//        val ethSendTransaction = web3j.ethSendRawTransaction(signedData).send()
//        val hash = ethSendTransaction.transactionHash
//        if (ethSendTransaction.error != null) {
//            logger.info("Error Message: ${ethSendTransaction.error.message}")
//        }
//
//        return hash
//    }

    fun sendGasWithNonce(
        wallet: Pair<String, String>,
        to: String,
        amount: BigDecimal,
        nonce: BigInteger
    ): String {

        val credentials: Credentials = Credentials.create(wallet.second)
        val transactionReceipt: TransactionReceipt = Transfer.sendFunds(
            web3j, credentials, to,
            amount, Convert.Unit.ETHER
        ).send()

        return transactionReceipt.transactionHash
    }

    fun sendSignTokenTransaction(fromAddress: String, privateKey: String,toAddress: String,
                                 contractAddress: String, sendAmount: BigDecimal, nonce: BigInteger) : String?{
        logger.info("Begin to send sign ERC20 Token transaction from " +
                fromAddress + " to " + toAddress + " amount " +
                sendAmount + " at contract " + contractAddress)

//        val nonce: BigInteger
//        var ethGetTransactionCount: EthGetTransactionCount? = null
//
//        try {
//            ethGetTransactionCount = web3j.ethGetTransactionCount(fromAddress, DefaultBlockParameterName.PENDING).send()
//        } catch (e: IOException) {
//            e.printStackTrace()
//        }
//        if (ethGetTransactionCount == null) return null
//
//        nonce = ethGetTransactionCount.transactionCount

        //val parameter = assemblePoolParameterManager.getParameter()
        //val defaultGasPrice = BigDecimal(parameter.ethGasPrice)
        //val defaultGasLimit = BigInteger.valueOf(parameter.ethGasLimit)
        val methodName = "transfer"
        val defaultGasPrice = BigDecimal(getGasPrice(methodName))
        val defaultGasLimit = getGasLimit(methodName)
        val decimals = 18

        logger.info("Gas Price is : " + defaultGasPrice)
        logger.info("Gas Limit is : " + defaultGasLimit)

        logger.info("Nonce : " + nonce)
        //val gasPrice = Convert.toWei(defaultGasPrice, Convert.Unit.GWEI).toBigInteger()
        val gasPrice = defaultGasPrice.toBigInteger()
        val gasLimit = defaultGasLimit
        val value = BigInteger.ZERO
        //token转账参数
        //val methodName = "transfer"
        val inputParameters: MutableList<Type<*>> = ArrayList()
        val outputParameters: MutableList<TypeReference<*>> = ArrayList()
        val tAddress = Address(toAddress)
        val tokenValue = Uint256(sendAmount.multiply(BigDecimal.TEN.pow(decimals)).toBigInteger())
        inputParameters.add(tAddress)
        inputParameters.add(tokenValue)
        val typeReference: TypeReference<Bool> = object : TypeReference<Bool>() {}
        outputParameters.add(typeReference)
        val function = Function(methodName, inputParameters, outputParameters)
        val data = FunctionEncoder.encode(function)

        try {

            val signedData = signTransaction(nonce, gasPrice, gasLimit, contractAddress, value,
                data, privateKey)
            if (signedData != null) {
                val ethSendTransaction = web3j.ethSendRawTransaction(signedData).send()
                logger.info("Send sign Token Transaction txid : " + ethSendTransaction.transactionHash)
                return ethSendTransaction.transactionHash
            }
        } catch (e: java.lang.Exception) {
            e.printStackTrace()
        }

        return null
    }

    fun ethCall(
        method: String,
        to: String,
        inputParameters: List<Type<*>>,
        outputParameters: List<TypeReference<*>>
    ): List<Type<*>>? {
        logger.info("Call method '${method}' in $to")
        val function = Function(method, inputParameters, outputParameters)
        val data = FunctionEncoder.encode(function)
        val transaction = Transaction.createEthCallTransaction(emptyAddress, to, data)
        val ethCall: EthCall
        val result = try {
            ethCall = web3j.ethCall(transaction, DefaultBlockParameterName.LATEST).send()
            if (ethCall.hasError()) {
                logger.error("ethCall failed: ${ethCall.error.message}")
                if (ethCall.error.message == "execution reverted") {
                    logger.warn("Execution reverted")
                    return null
                } else {
                    throw Exception(ethCall.error.message)
                }
            }
            FunctionReturnDecoder.decode(ethCall.value, function.outputParameters)
        } catch (e: Exception) {
            logger.error(e.message, e)
            null
        }

        return result
    }
}