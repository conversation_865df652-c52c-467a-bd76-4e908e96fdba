package io.vault.jasper.response

import io.swagger.annotations.ApiModel
import io.swagger.annotations.ApiModelProperty
import io.vault.jasper.enums.OptionDirection
import io.vault.jasper.enums.Symbol
import io.vault.jasper.model.OptionStatus
import org.web3j.abi.datatypes.Bool

@ApiModel
data class BitlayerCampaignInfoResponse(

    @ApiModelProperty("钱包地址", required = true)
    val address: String,

    @ApiModelProperty("活动中已领取的人数", required = true)
    val claimRebateTotalCount: Int = 0,

    @ApiModelProperty("是否已经从Bitlayer领卷", required = true)
    val hasClaimBitlayer: Boolean = false,

    @ApiModelProperty("首单订单信息", required = false)
    val tradeInfo: ExecutedOrderResponse? = null,

    @ApiModelProperty("是否已经加入了Discord", required = true)
    val isJoinDiscord: Boolean = false,

    @ApiModelProperty("是否允许Claim首单", required = false)
    val canClaimFirstTrade: Boolean = false,

    @ApiModelProperty("是否已经Claim首单", required = false)
    val hasClaimRebate: Boolean = false,

    @ApiModelProperty("允许Claim的时间", required = false)
    val claimTime: Long? = null,

    @ApiModelProperty("首单Gross Profit Rate", required = false)
    val firstTradeGrossProfitRate: String = "0",

    @ApiModelProperty("首单Premium", required = false)
    val firstTradePremium: String = "0",

    @ApiModelProperty("首单Premium 币种", required = false)
    val firstTradePremiumAsset: Symbol = Symbol.BTC,

    @ApiModelProperty("首单Rebate金额", required = false)
    val firstTradeAmount: String = "0",

    @ApiModelProperty("月光宝盒首单订单信息", required = false)
    val moonlightTradeInfo: ExecutedOrderResponse? = null,

    @ApiModelProperty("月光宝盒领取数量", required = false)
    val claimMoonlightTotalCount: Int = 0,

    @ApiModelProperty("用户跳转到GALA 3的时间", required = false)
    val jumpToGalaThreeTime: Long = 0,

    @ApiModelProperty("用户能否Claim月光宝盒", required = false)
    val canClaimMoonlightBox: Boolean = false,

    @ApiModelProperty("是否使用了月光宝盒模式下单", required = false)
    val hasUsedMoonlightBox: Boolean = false,

    @ApiModelProperty("领取4次月光宝盒的开始时间", required = false)
    val gala3StartDates: List<Long> = listOf(),

    @ApiModelProperty("领取4次月光宝盒的结束时间", required = false)
    val gala3EndDates: List<Long> = listOf(),
)

data class BitlayerFirstTradeInfoResponse(

    @ApiModelProperty("订单类型 CALL /PUT", required = true)
    val direction: OptionDirection,

    @ApiModelProperty("订单资产", required = true)
    val bidAsset: Symbol,

    @ApiModelProperty("订单数量", required = true)
    val bidAmount: String,

    @ApiModelProperty("订单到期结算时间", required = true)
    val expiryDate: Long,

    @ApiModelProperty("订单行权价格", required = true)
    val strikePrice: String,

    @ApiModelProperty("订单状态", required = true)
    val status: OptionStatus,
)
