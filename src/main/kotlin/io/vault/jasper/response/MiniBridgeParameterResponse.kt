package io.vault.jasper.response

import io.swagger.annotations.ApiModel
import io.swagger.annotations.ApiModelProperty
import io.vault.jasper.enums.Symbol
import org.springframework.data.mongodb.core.mapping.Field
import org.springframework.data.mongodb.core.mapping.FieldType
import java.math.BigDecimal

@ApiModel
data class MiniBridgeParameterResponse(

    @ApiModelProperty("价差", required = true)
    var priceGap: String,

    @ApiModelProperty("允许的最大值", required = true)
    var maxValue: String,

    @ApiModelProperty("允许的最小值", required = true)
    val minValue: String,

    @ApiModelProperty("手续费", required = true)
    val feeValue: String
)
