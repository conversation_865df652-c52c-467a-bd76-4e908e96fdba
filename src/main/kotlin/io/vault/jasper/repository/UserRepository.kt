package io.vault.jasper.repository

import io.vault.jasper.model.User
import org.springframework.data.domain.Sort
import org.springframework.data.mongodb.repository.MongoRepository
import org.springframework.stereotype.Repository
import java.time.LocalDateTime

@Repository
interface UserRepository : MongoRepository<User, String> {
    fun findByAddressIgnoreCase(address: String): User?
    fun findByInviteCode(inviteCode: String): User?
    fun findByInvitedUserId(userId: String): List<User>
    fun findByInvitedUserIdAndCreatedBetween(
        userId: String,
        start: LocalDateTime,
        end: LocalDateTime,
        sort: Sort = Sort.by(Sort.Order.desc(User::created.name))
    ): List<User>

    fun findByFreePremiumBenefit(benefit: Boolean): List<User>
    fun findByFreePremiumBenefitAndZeroPremiumBenefitsIsNotNull(benefit: Boolean): List<User>

    fun findByTwitterOAuthToken(oauthToken: String): User?
    fun findByTwitterAccountId(twitterAccountId: String): User?
    fun findFirstByDiscordSession(session: String): User?
    fun findByInvitedUserIdIsNullAndUseKolRebateIsTrue(): List<User>
    fun countByInvitedUserId(userId: String): Long
}