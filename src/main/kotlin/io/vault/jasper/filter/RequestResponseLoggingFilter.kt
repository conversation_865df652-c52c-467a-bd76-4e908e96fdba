package io.vault.jasper.filter

import org.slf4j.LoggerFactory
import org.springframework.stereotype.Component
import org.springframework.web.util.ContentCachingResponseWrapper
import java.io.IOException
import java.nio.charset.StandardCharsets
import javax.servlet.*
import javax.servlet.http.HttpServletRequest
import javax.servlet.http.HttpServletResponse


// @Component
class RequestResponseLoggingFilter : Filter {
    @Throws(ServletException::class)
    override fun init(filterConfig: FilterConfig) {
        // Initialization code if needed
    }

    @Throws(IOException::class, ServletException::class)
    override fun doFilter(request: ServletRequest, response: ServletResponse, chain: FilterChain) {
        val httpRequest = request as HttpServletRequest
        val responseWrapper = ContentCachingResponseWrapper(response as HttpServletResponse)
        val startTime = System.currentTimeMillis()
        try {
            chain.doFilter(request, responseWrapper)
        } finally {
            if (!httpRequest.requestURI.startsWith("/doc.html") &&
                !httpRequest.requestURI.startsWith("/webjars")) {
                logger.info("${httpRequest.requestURI}")
                val duration = System.currentTimeMillis() - startTime
                logger.info(
                    "Incoming request: method={}, URI={}, params={}, duration={} ms",
                    httpRequest.method,
                    httpRequest.requestURI,
                    httpRequest.parameterMap,
                    duration
                )
                val responseArray: ByteArray = responseWrapper.getContentAsByteArray()
                val responseBody = String(responseArray, StandardCharsets.UTF_8)
                logger.info(
                    "Outgoing response: status={}, body={}",
                    responseWrapper.getStatus(),
                    responseBody
                )
            }
            responseWrapper.copyBodyToResponse()
        }
    }

    override fun destroy() {
        // Cleanup code if needed
    }

    companion object {
        private val logger = LoggerFactory.getLogger(RequestResponseLoggingFilter::class.java)
    }
}
