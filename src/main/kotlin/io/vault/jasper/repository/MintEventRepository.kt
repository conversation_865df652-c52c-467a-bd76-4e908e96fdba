package io.vault.jasper.repository

import io.vault.jasper.model.MintEvent
import org.springframework.data.mongodb.repository.MongoRepository
import org.springframework.stereotype.Repository

@Repository
interface MintEventRepository : MongoRepository<MintEvent, String> {
    fun findFirstByTxHash(txHash: String): MintEvent?
    fun findByNftCodeIsNull(): List<MintEvent>
    fun findByNftCodeIsNullAndReceiptStatusIsNull(): List<MintEvent>
}