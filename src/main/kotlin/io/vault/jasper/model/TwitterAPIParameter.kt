package io.vault.jasper.model

import org.springframework.data.annotation.Id
import org.springframework.data.mongodb.core.mapping.Document
import org.springframework.data.mongodb.core.mapping.Field
import java.math.BigDecimal

@Document("twitter_api_parameter")
data class TwitterAPIParameter(
    @Id
    val id: String? = null,
    @Field("bear_token")
    val bearToken: String = "AAAAAAAAAAAAAAAAAAAAALA5vgEAAAAAS1teI5Wfurh%2Fzn%2FBULUO%2BdhyvUE%3DaiKBrkzWMsqA5OQNYyiizCvpoYzGuKXH6AYMGDJROkhYqVnUZo", // API Token
    @Field("retweet_id")
    val retweetId: String = "1825800581090545926", // 转发的帖子ID
    @Field("page_count")
    val pageCount: Int = 100, // 每页数量
    @Field("pagination_token")
    var paginationToken: String? = null, // 分页token
    @Field("task_switch")
    var taskSwitch: Boolean = false, // 任务开关
)