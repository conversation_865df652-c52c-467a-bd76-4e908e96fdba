package io.vault.jasper.repository

import io.vault.jasper.model.User
import io.vault.jasper.model.UserCampaignInfo
import io.vault.jasper.model.UserJPCampaignInfo
import org.springframework.data.domain.Sort
import org.springframework.data.mongodb.repository.MongoRepository
import org.springframework.stereotype.Repository
import java.time.LocalDateTime

@Repository
interface UserJPCampaignInfoRepository : MongoRepository<UserJPCampaignInfo, String> {

    fun findByUserId(userId: String): UserJPCampaignInfo?
    fun findByAddressIgnoreCase(address: String): UserJPCampaignInfo?
}