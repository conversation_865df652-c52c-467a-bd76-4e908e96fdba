package io.vault.jasper.repository

import io.vault.jasper.model.UserNetwork
import io.vault.jasper.model.UserNetworkChangeRateLog
import io.vault.jasper.model.UserNetworkGrade
import org.springframework.data.domain.Sort
import org.springframework.data.mongodb.repository.MongoRepository
import org.springframework.stereotype.Repository

@Repository
interface UserNetworkChangeRateLogRepository : MongoRepository<UserNetworkChangeRateLog, String> {

}