package io.vault.jasper.model

import org.springframework.data.annotation.CreatedDate
import org.springframework.data.annotation.Id
import org.springframework.data.annotation.LastModifiedDate
import org.springframework.data.mongodb.core.index.Indexed
import org.springframework.data.mongodb.core.mapping.Document
import org.springframework.data.mongodb.core.mapping.Field
import org.springframework.data.mongodb.core.mapping.FieldType
import java.math.BigDecimal
import java.time.LocalDateTime

enum class UserReferralTransactionType {
    FIRST_TRADE_REWARD
}

@Document("user_referral_transaction")
data class UserReferralTransaction(
    @Id
    val id: String? = null,
    @Field("user_id")
    @Indexed(background = true)
    val userId: String, // 获得奖励的用户ID（邀请人）
    @Indexed(background = true)
    val address: String, // 获得奖励的用户地址（邀请人）
    @Indexed(background = true)
    var type: UserReferralTransactionType = UserReferralTransactionType.FIRST_TRADE_REWARD,
    @Field(targetType = FieldType.DECIMAL128)
    var amount: BigDecimal = BigDecimal.ZERO, // 奖励金额
    @Field("option_order_id")
    @Indexed(background = true)
    val optionOrderId: String? = null, // 关联的交易订单ID
    @Field("referee_user_id")
    @Indexed(background = true)
    var refereeUserId: String? = null, // 被邀请人ID
    @Field("referee_address")
    @Indexed(background = true)
    var refereeAddress: String? = null, // 被邀请人地址
    @CreatedDate
    val created: LocalDateTime = LocalDateTime.now(),
    @LastModifiedDate
    val updated: LocalDateTime = LocalDateTime.now()
)
