package io.vault.jasper.service

import com.fasterxml.jackson.databind.ObjectMapper
import io.vault.jasper.service.blockchain.EvmService
import io.vault.jasper.utils.ProfileUtil
import org.slf4j.LoggerFactory
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.beans.factory.annotation.Value
import org.springframework.http.HttpEntity
import org.springframework.http.HttpHeaders
import org.springframework.http.MediaType
import org.springframework.http.client.SimpleClientHttpRequestFactory
import org.springframework.stereotype.Service
import org.springframework.web.client.RestTemplate
import org.web3j.abi.FunctionEncoder
import org.web3j.abi.FunctionReturnDecoder
import org.web3j.abi.TypeReference
import org.web3j.abi.datatypes.*
import org.web3j.abi.datatypes.generated.Uint256
import org.web3j.abi.datatypes.generated.Uint64
import org.web3j.abi.datatypes.generated.Uint8
import org.web3j.crypto.Credentials
import org.web3j.crypto.RawTransaction
import org.web3j.crypto.TransactionEncoder
import org.web3j.protocol.Web3j
import org.web3j.protocol.core.DefaultBlockParameterName
import org.web3j.protocol.core.methods.request.Transaction
import org.web3j.utils.Numeric
import java.math.BigDecimal
import java.math.BigInteger
import java.util.*

@Service
class BlockchainService {

    private val logger = LoggerFactory.getLogger(this::class.java)

    val emptyAddress = "******************************************"

    @Value("\${blockchain.arbitrum.chain_id}")
    private lateinit var arbitrumChainId: String

    private val restTemplate = RestTemplate()

    private val objectMapper = ObjectMapper()

    fun getEarningsAmount(
        web3Client: Web3j,
        contractAddress: String,
        underlyingAsset: String, // 抵押资产地址
        underlyingAmount: BigInteger, // 抵押资产数量
        strikeAsset: String, // 行权资产地址
        strikeNotionalAmount: BigInteger, // 行权资产数量
    ): BigInteger{

        logger.info("Get User getEarningsAmount - contractAddress: $contractAddress")
        logger.info("Get User getEarningsAmount - underlyingAsset: $underlyingAsset")
        logger.info("Get User getEarningsAmount - underlyingAmount: $underlyingAmount")
        logger.info("Get User getEarningsAmount - strikeAsset: $strikeAsset")
        logger.info("Get User getEarningsAmount - strikeNotionalAmount: $strikeNotionalAmount")

        val methodName = "getEarningsAmount"
        val fromAddr = emptyAddress
        val inputParameters: List<Type<*>> = listOf(
            Address(underlyingAsset),
            Uint256(underlyingAmount),
            Address(strikeAsset),
            Uint256(strikeNotionalAmount)
        )

        val outputParameters: MutableList<TypeReference<*>> = ArrayList()
        val typeReference: TypeReference<Uint256> = object : TypeReference<Uint256>() {}
        outputParameters.add(typeReference)
        val function = Function(methodName, inputParameters, outputParameters)
        val data = FunctionEncoder.encode(function)
        val transaction = Transaction.createEthCallTransaction(
            fromAddr,
            contractAddress,
            data
        )
        try {
            val ethCall = web3Client.ethCall(transaction, DefaultBlockParameterName.LATEST).sendAsync().get()
            val results = FunctionReturnDecoder.decode(ethCall.value, function.outputParameters)
            logger.info("Get User getEarningsAmount - results: $results")

            val stakedAmount = results[0].value as BigInteger
            val readableAmount = BigDecimal(stakedAmount).divide(BigDecimal(10).pow(18), 18, BigDecimal.ROUND_DOWN)
            return readableAmount.toBigInteger()

        } catch (e: Exception) {
            logger.error("查询用户 getEarningsAmount 失败: ${e.message}", e)
            throw Exception("查询用户 getEarningsAmount 失败: ${e.message}")
        }
    }

    /**
        _orderType: 0 call 1 put
        _orderID: 订单ID
        _type:
            NotExercising, 0 不行权
            Exercising, 1 实物交割
            ProfitTaking 2 利差行权
        _incomeAmount:
            _type === (1 || 0 ) ？0 : _type === 2 ？getEarningsAmount()
        _slippage: 滑点 10 ** 18
    */
    fun liquidateOption(
        web3j: Web3j,
        fromAddressPrivateKey: String,
        contractAddress: String,
        orderType: BigInteger,
        orderID: BigInteger,
        liquidateType: BigInteger,
        incomeAmount: BigInteger,
        slippage: BigInteger,
        nonce: BigInteger,
        gasPrice: BigInteger,
        gasLimit: BigInteger,
        slug: String,
        lastTime: Boolean = false,
        chainId: Long = arbitrumChainId.toLong()
    ): String {

        logger.info("EVM Settlement: Liquidate Option Params: $contractAddress, $nonce, $gasPrice, $gasLimit")
        logger.info("EVM Settlement: Liquidate Option Params: $chainId, $orderType, $orderID, $liquidateType, $incomeAmount, $slippage")

        val fromPkDecrypt = fromAddressPrivateKey
        val methodName = "liquidateOption"
        val inputParameters: List<Type<*>> = listOf(
            Uint8(orderType),
            Uint64(orderID),
            Uint8(liquidateType),
            Uint256(incomeAmount),
            Uint256(slippage)
        )
        val outputParameters: MutableList<TypeReference<*>> = ArrayList()

        val function = Function(methodName, inputParameters, outputParameters)
        val data = FunctionEncoder.encode(function)
        val credentials = Credentials.create(fromPkDecrypt)
        val rawTransaction = RawTransaction.createTransaction(
            nonce,
            gasPrice,
            gasLimit,
            contractAddress,
            BigInteger.valueOf(0),
            data
        )
        val signMessage = TransactionEncoder.signMessage(rawTransaction, chainId, credentials)
        val toHexString = Numeric.toHexString(signMessage)
        val ethCall = web3j.ethSendRawTransaction(toHexString).sendAsync().get()
        if (ethCall.hasError()) {
            val errorMsg = ethCall.error.message
            logger.warn("liquidateOption send failed: ${ethCall.error.message}")
            if (!lastTime) {
                if (errorMsg.startsWith("max fee per gas less than block base fee")) {
                    val regex = Regex("baseFee:\\s*(\\d+)")
                    regex.find(errorMsg)?.let {
                        val baseFee = it.groupValues[1].toBigInteger()
                        logger.warn("current baseFee: $baseFee")
                        // val move = baseFee.toString(10).length - 2
                        // val divisor = BigInteger.TEN.pow(move)
                        // val newBaseFee = (baseFee.divide(divisor) + BigInteger.ONE).multiply(divisor)
                        val newBaseFee = (BigDecimal(baseFee) * BigDecimal(1.1)).toBigInteger()
                        logger.info("new baseFee: $newBaseFee")
                        val newNonce = web3j.ethGetTransactionCount(credentials.address, DefaultBlockParameterName.PENDING)
                            .send().transactionCount
                        return liquidateOption(
                            web3j,
                            fromAddressPrivateKey,
                            contractAddress,
                            orderType,
                            orderID,
                            liquidateType,
                            incomeAmount,
                            slippage,
                            newNonce,
                            newBaseFee,
                            gasLimit,
                            slug,
                            false,
                            chainId
                        )
                    }
                } else {
                    logger.error("liquidateOption failed: ${ethCall.error.message}")
                }
            } else {
                logger.info("liquidateOption: Last time, won't try again")
                throw Exception(errorMsg)
            }
        }

        val txHash = ethCall.transactionHash
        logger.info("EVM Settlement: liquidateOption txid: $txHash")

        return txHash
    }

    fun liquidateOption2(
        network: String,
        tokens: List<String>,
        expirationDate: Long,
        orderType: Int,
        orderID: Long,
        liquidateType: Int,
        expiryInHour: String,
        totalPriceTime: Int? = null,
        specialUrl: String? = null
    ): String {

        var url = specialUrl ?: "https://worker.jaspervault.io/api/v2/pyth_service" // 可指定结算的接口
        var networkParam = network

        if(ProfileUtil.activeProfile == "dev"){
            url = "https://jaspervault-worker.fly.dev/api/v2/pyth_service"

            if(network == "bitlayer"){
                networkParam = "bitlayertest"
            }

            if(network == "base"){
                networkParam = "base_uat"
            }

            if(network == "arbitrum"){
                networkParam = "arbitrum_fork"
            }
        }

        // Temp test
        if(network == "bsc"){
            url = "https://jaspervault-worker.fly.dev/api/v2/pyth_service"
        }

        val params = mapOf(
            "method" to "liquidate",
            "network" to networkParam,
            "tokens" to tokens,
            "expirationDate" to expirationDate,
            "orderType" to orderType,
            "orderID" to orderID,
            "liquidateType" to liquidateType,
            "expiryInHour" to expiryInHour,
            "totalPriceTime" to totalPriceTime
        )
        val body = objectMapper.writeValueAsString(params)
        logger.info("EVM Settlement: 订单结算请求参数: $url, $body")
        val headers = HttpHeaders()
        headers.contentType = MediaType.APPLICATION_JSON
        val entity = HttpEntity(body, headers)

        val restTem = RestTemplate(SimpleClientHttpRequestFactory().apply {
            setConnectTimeout(5000)
            setReadTimeout(30000)
        })
        val response = restTem.postForEntity(url, entity, String::class.java)
        when (response.statusCodeValue) {
            200 -> {
                val data = objectMapper.readTree(response.body)
                val status = data.get("status").asText()
                val txHash = data.get("data")?.get("tx")?.asText()
                if (status != "success" || txHash.isNullOrBlank()) {
                    throw Exception("liquidateOption2 failed: Status=$status\t${data["message"]?.asText()}\t" +
                            "Network=${data["data"]?.get("network")?.asText()}")
                }

                logger.info("EVM Settlement: liquidateOption2 txid: $txHash")
                return txHash
            }
            else -> {
                logger.error("EVM Settlement: liquidateOption2 failed: ${response.body}")
                throw Exception("EVM Settlement: liquidateOption2 failed: (${response.statusCodeValue})${response.body}")
            }
        }
    }

    fun getInvitedCodeIsUsed(
        web3Client: Web3j,
        contractAddress: String,
        invitedCode: String
    ): Boolean{

        val methodName = "promoteCodeList"
        val fromAddr = emptyAddress
        val inputParameters: List<Type<*>> = listOf(
            Utf8String(invitedCode)
        )

        val outputParameters: MutableList<TypeReference<*>> = ArrayList()
        val typeReference: TypeReference<Bool> = object : TypeReference<Bool>() {}
        outputParameters.add(typeReference)
        val function = Function(methodName, inputParameters, outputParameters)
        val data = FunctionEncoder.encode(function)
        val transaction = Transaction.createEthCallTransaction(
            fromAddr,
            contractAddress,
            data
        )
        try {
            val ethCall = web3Client.ethCall(transaction, DefaultBlockParameterName.LATEST).sendAsync().get()
            val results = FunctionReturnDecoder.decode(ethCall.value, function.outputParameters)
            logger.info("Get User getEarningsAmount - results: $results")

            val hasUsed = results[0].value as Boolean
            return hasUsed

        } catch (e: Exception) {
            logger.error("查询 NFT 邀请码 失败: ${e.message}", e)
            throw Exception("查询NFT 邀请码  失败: ${e.message}")
        }
    }

    fun getKolTotalRebate(
        web3Client: Web3j,
        contractAddress: String,
        userAddress: String
    ): BigDecimal{

        val methodName = "totalClaimInfo"
        val fromAddr = emptyAddress
        val inputParameters: List<Type<*>> = listOf(
            Address(userAddress)
        )

        val outputParameters: MutableList<TypeReference<*>> = ArrayList()
        val typeReference: TypeReference<Uint256> = object : TypeReference<Uint256>() {}
        outputParameters.add(typeReference)
        val function = Function(methodName, inputParameters, outputParameters)
        val data = FunctionEncoder.encode(function)
        val transaction = Transaction.createEthCallTransaction(
            fromAddr,
            contractAddress,
            data
        )
        try {
            val ethCall = web3Client.ethCall(transaction, DefaultBlockParameterName.LATEST).sendAsync().get()
            val results = FunctionReturnDecoder.decode(ethCall.value, function.outputParameters)
            logger.info("Get User getKolTotalRebate - results: $results")

            val stakedAmount = results[0].value as BigInteger
            val readableAmount = BigDecimal(stakedAmount).divide(BigDecimal(10).pow(6), 6, BigDecimal.ROUND_DOWN)
            return readableAmount

        } catch (e: Exception) {
            logger.error("查询 getKolTotalRebate 失败: ${e.message}", e)
            throw Exception("查询 getKolTotalRebate 失败: ${e.message}")
        }
    }

    fun getKolUnclaimRebate(
        web3Client: Web3j,
        contractAddress: String,
        userAddress: String
    ): BigDecimal{

        val methodName = "claimInfo"
        val fromAddr = emptyAddress
        val inputParameters: List<Type<*>> = listOf(
            Address(userAddress)
        )

        val outputParameters: MutableList<TypeReference<*>> = ArrayList()
        val typeReference: TypeReference<Uint256> = object : TypeReference<Uint256>() {}
        outputParameters.add(typeReference)
        val function = Function(methodName, inputParameters, outputParameters)
        val data = FunctionEncoder.encode(function)
        val transaction = Transaction.createEthCallTransaction(
            fromAddr,
            contractAddress,
            data
        )
        try {
            val ethCall = web3Client.ethCall(transaction, DefaultBlockParameterName.LATEST).sendAsync().get()
            val results = FunctionReturnDecoder.decode(ethCall.value, function.outputParameters)
            logger.info("Get User getKolUnclaimRebate - results: $results")

            val stakedAmount = results[0].value as BigInteger
            val readableAmount = BigDecimal(stakedAmount).divide(BigDecimal(10).pow(6), 6, BigDecimal.ROUND_DOWN)
            return readableAmount

        } catch (e: Exception) {
            logger.error("查询 getKolUnclaimRebate 失败: ${e.message}", e)
            throw Exception("查询getKolUnclaimRebate  失败: ${e.message}")
        }
    }

//    fun getNonce(web3j: Web3j, address: String): BigInteger {
//        val ethGetTransactionCount = web3j.ethGetTransactionCount(address, DefaultBlockParameterName.PENDING).send()
//        return ethGetTransactionCount.transactionCount
//    }
}