version: '3.1'

services:
  jasper:
    image: openjdk:17-alpine
    container_name: jaspervault
    restart: unless-stopped
    volumes:
#      - ./application-dev.yml:/app/config/application-prod.yml
#      - ./fireblocks_secret.key:/app/config/fireblocks_secret.key
      - ./:/app
    working_dir: /app
    ports:
      - "8888:8111"
    environment:
      TZ: Asia/Shanghai
    command: >
      sh -c 'if [ -e "app.jar" ]; then
        cp app.jar app-back.jar;
      fi;
      cp jasper-1.0-SNAPSHOT.jar app.jar;
      java -jar -Djava.security.egd=file:/dev/./urandom app.jar'
