package io.vault.jasper.model

import io.vault.jasper.enums.ChainType
import io.vault.jasper.enums.OptionDirection
import io.vault.jasper.enums.Symbol
import org.springframework.data.annotation.CreatedDate
import org.springframework.data.annotation.Id
import org.springframework.data.annotation.LastModifiedDate
import org.springframework.data.mongodb.core.index.Indexed
import org.springframework.data.mongodb.core.mapping.Document
import org.springframework.data.mongodb.core.mapping.Field
import java.math.BigDecimal
import java.time.LocalDateTime

/**
 * Degen LP Vault 列表
 */

@Document("degen_lp_vault_config")
data class DegenLPVaultConfig(
    @Id
    val id: String? = null,
    @Field("order_type")
    val orderType: OrderType = OrderType.DEGEN,
    val chain: ChainType = ChainType.ARBITRUM, // 对应的链
    var address: String, // Vault 地址，大小写敏感
    @Field("option_type")
    var optionType: OptionDirection = OptionDirection.CALL, // Vault 期权类型
    @Field("option_symbol")
    var optionSymbol: Symbol = Symbol.ETH, // Vault 期权标的
    @Field("expire_in_hour")
    var expireInHour: List<String> = listOf("2", "8", "24"),
    var token: Symbol = Symbol.ETH,
    @Field("token_address")
    var tokenAddress: String = "",
)