package io.vault.jasper.dto

import EVMAddress
import io.swagger.annotations.ApiModel
import io.swagger.annotations.ApiModelProperty
import java.math.BigDecimal

@ApiModel("编辑代理分佣比例DTO")
data class EditUserNetworkPercentageDTO(
    @ApiModelProperty("钱包地址", required = true)
    @field:EVMAddress
    val address: String,
    @ApiModelProperty("代理钱包地址", required = true)
    val agentAddress: String,
    @ApiModelProperty("分佣比例", required = true)
    val percentage: BigDecimal
)
