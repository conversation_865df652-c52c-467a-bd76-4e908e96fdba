package io.vault.jasper.response

import io.swagger.annotations.ApiModel
import io.swagger.annotations.ApiModelProperty
import io.vault.jasper.enums.ChainType
import io.vault.jasper.enums.OptionDirection
import io.vault.jasper.enums.Symbol
import io.vault.jasper.model.OrderType
import java.math.BigDecimal

@ApiModel("LPVault 信息")
data class LPVaultInfoResponse(
    @ApiModelProperty("LP Vault 地址", required = true)
    val address: String,
    @ApiModelProperty("LP Vault 资产", required = true)
    val optionSymbol: Symbol,
    @ApiModelProperty("LP Vault 类型", required = true)
    val optionDirection: OptionDirection,
    @ApiModelProperty("锁定资产", required = true)
    val lockSymbol: Symbol,
    @ApiModelProperty("当前流动性", required = false)
    val liquidity: String? = null,
    @ApiModelProperty("当前流动性(USDT为单位)", required = false)
    val liquidityInUsdt: String? = null,
    @ApiModelProperty("正在开仓的仓位", required = false)
    val openInterest: String? = null,
    @ApiModelProperty("总流动性", required = true)
    val totalAmount: String,
    @ApiModelProperty("USDT 余额", required = true)
    val usdtAmount: String,
    @ApiModelProperty("总赚取的权利金", required = true)
    val premiumEarn: String,
    @ApiModelProperty("交易量", required = true)
    val tradingVolume: String,
    @ApiModelProperty("LP Vault 创建时间", required = true)
    val createAt: Long,
    @ApiModelProperty("APY", required = true)
    val apy: String? = null,
    @ApiModelProperty("iv", required = true)
    val iv: String? = null,
    @ApiModelProperty("交易时间", required = true)
    val expiryInHour: String? = null,
    @ApiModelProperty("Regular Pool", required = true)
    val regularPool: String? = null,
)