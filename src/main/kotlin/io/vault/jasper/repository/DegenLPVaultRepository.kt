package io.vault.jasper.repository


import io.vault.jasper.enums.ChainType
import io.vault.jasper.enums.OptionDirection
import io.vault.jasper.enums.Symbol
import io.vault.jasper.model.DegenLPVault
import io.vault.jasper.model.OrderType
import org.springframework.data.mongodb.repository.MongoRepository
import org.springframework.stereotype.Repository

@Repository
interface DegenLPVaultRepository : MongoRepository<DegenLPVault, String> {

    fun findByChainAndOptionTypeAndOptionSymbolAndExpireInHour(
        chain: ChainType,
        optionType: OptionDirection,
        optionSymbol: Symbol,
        expireInHour: String
    ): DegenLPVault?

    fun findByChainAndOptionTypeAndOptionSymbolAndExpireInHourAndOrderType(
        chain: ChainType,
        optionType: OptionDirection,
        optionSymbol: Symbol,
        expireInHour: String,
        orderType: OrderType
    ): DegenLPVault?

    fun findByOptionSymbol(
        optionSymbol: Symbol
    ): List<DegenLPVault>

    fun findByOptionSymbolAndExpireInHour(
        optionSymbol: Symbol,
        expireInHour: String
    ): List<DegenLPVault>
}