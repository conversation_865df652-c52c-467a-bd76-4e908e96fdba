package io.vault.jasper.model

import org.springframework.data.annotation.Id
import org.springframework.data.mongodb.core.mapping.Document
import org.springframework.data.mongodb.core.mapping.Field
import java.math.BigDecimal
import java.time.LocalDateTime

@Document("btr_campaign_config")
data class BtrCampaignConfig(
    @Id
    val id: String? = null,
    
    @Field("start_time")
    val startTime: LocalDateTime,
    
    @Field("end_time")
    val endTime: LocalDateTime,

    @Field("total_btr_amount")
    val totalBtrAmount: BigDecimal = BigDecimal("50000"),
    
    @Field("distributed_btr_amount")
    var distributedBtrAmount: BigDecimal = BigDecimal.ZERO,
    
    @Field("btr_amount_per_order")
    val btrAmountPerOrder: BigDecimal = BigDecimal("2"),
    
    @Field("enabled")
    var enabled: Boolean = true
) {
    fun isInCampaignPeriod(timestamp: LocalDateTime): Boolean {
        return timestamp in startTime..endTime
    }

    fun canDistributeBtr(): Boolean {
        return totalBtrAmount - distributedBtrAmount > btrAmountPerOrder
    }
} 