package io.vault.jasper.dto

import EVMAddress
import io.swagger.annotations.ApiModel
import io.swagger.annotations.ApiModelProperty
import io.vault.jasper.enums.ChainType
import io.vault.jasper.enums.OptionDirection
import io.vault.jasper.enums.Symbol
import io.vault.jasper.model.LPVaultPermissionType
import io.vault.jasper.model.LPVaultPremiumOracleType

@ApiModel("创建LP Vault请求")
data class CreateLPVaultDTOV2(
    @ApiModelProperty("钱包地址", required = true)
    @field:EVMAddress
    val address: String,
    @ApiModelProperty("链类型", required = true)
    val chain: ChainType,
    @ApiModelProperty("期权方向", required = true)
    val optionDirection: OptionDirection,
    @ApiModelProperty("期权币种", required = true)
    val optionSymbol: Symbol,
    @ApiModelProperty("添加类型", required = true)
    val permissionType: LPVaultPermissionType,
    @ApiModelProperty("添加到池子的资产", required = true)
    val poolAsset: String,
    @ApiModelProperty("期权时间", required = true)
    val optionsHours: String,
    @ApiModelProperty("Vault 名称", required = true)
    val vaultName: String,
    @ApiModelProperty("Oracle 类型", required = true)
    val premiumOracleType: LPVaultPremiumOracleType,
    @ApiModelProperty("Fee Tier", required = false)
    val feeTier: String? = null,
    @ApiModelProperty("Premium", required = false)
    val premium: String? = null,
    @ApiModelProperty("Premium Rate", required = false)
    val premiumRate: String? = null,
    @ApiModelProperty("Premium Rates", required = false)
    val premiumRates: String? = null,
    @ApiModelProperty("Premium Floor", required = false)
    val premiumFloor: String? = null,
)
