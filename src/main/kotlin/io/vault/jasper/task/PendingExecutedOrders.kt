package io.vault.jasper.task

import io.vault.jasper.exception.BusinessException
import io.vault.jasper.exception.ResultEnum
import io.vault.jasper.model.OptionOrder
import io.vault.jasper.model.OptionStatus
import io.vault.jasper.model.OrderStatus
import io.vault.jasper.repository.OptionOrderRepository
import io.vault.jasper.repository.OrderRepository
import io.vault.jasper.service.MonitorService
import io.vault.jasper.service.OptionOrderService
import io.vault.jasper.service.blockchain.BlockchainServiceFactory
import io.vault.jasper.service.blockchain.EvmService
import org.slf4j.LoggerFactory
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.beans.factory.annotation.Value
import org.springframework.data.repository.findByIdOrNull
import org.springframework.scheduling.annotation.Scheduled
import org.springframework.stereotype.Service


@Service
class PendingExecutedOrders @Autowired constructor(
    private val optionOrderRepository: OptionOrderRepository,
    private val orderRepository: OrderRepository,
    private val monitorService: MonitorService,
    private val blockchainServiceFactory: BlockchainServiceFactory,
    private val optionOrderService: OptionOrderService
) {

    private val logger = LoggerFactory.getLogger(this::class.java)

    @Value("\${schedule_task_on}")
    lateinit var scheduleOn: String

    @Scheduled(fixedDelay = 10 * 1000, initialDelay = 5 * 1000)
    fun run() {
        if(scheduleOn == "false"){
            return
        }
        try {
            toBeConfirmed()
        } catch (e: Exception) {
            logger.error(e.message, e)
        }
        monitorService.taskMonitor(this::class.simpleName)
    }

    private fun toBeConfirmed() {
        val optionOrders = optionOrderRepository.findByStatusAndTxHashNotNull(OptionStatus.TO_BE_CONFIRMED)
        if (optionOrders.isEmpty()) return
        optionOrders.forEach { oo ->
            val service = blockchainServiceFactory.getBlockchainService(oo.chain) ?: run {
                logger.warn("${oo.chain} Service not found, Skipped")
                return@forEach
            }
            if (service !is EvmService) {
                logger.warn("Not supported yet, Skipped")
                return@forEach
            }

            try {
                service.saveOptionOrderFromTxHash(oo.txHash!!, oo)
            } catch (e: BusinessException) {
                logger.error(e.message, e)
                when (e.code) {
                    ResultEnum.TRANSACTION_FAILED.code -> { // 链上成交失败
                        optionOrderService.updateFields(
                            mapOf(
                                OptionOrder::status.name to OptionStatus.FAILED,
                                OptionOrder::errorMsg.name to e.message
                            ),
                            oo.id!!
                        )
                        orderRepository.findByIdOrNull(oo.orderId)?.let { o ->
                            o.status = OrderStatus.FAILED
                            o.errorMsg = e.message
                            orderRepository.save(o)
                        }
                    }

                    else -> {
                        // 跳过，不改变订单状态
                        logger.warn("订单确认异常, txHash: ${oo.txHash}, code: ${e.code}, message: ${e.message}, Skipped")
                    }
                }
            } catch (e: Exception) {
                logger.error(e.message, e)
                // 跳过，不改变订单状态
            }
        }
    }
}