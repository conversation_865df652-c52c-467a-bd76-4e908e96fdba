package io.vault.jasper.listener

import io.vault.jasper.event.OptionExecutedEvent
import io.vault.jasper.repository.OptionOrderRepository
import io.vault.jasper.service.UserTradeRebateService
import org.slf4j.LoggerFactory
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.context.event.EventListener
import org.springframework.data.repository.findByIdOrNull
import org.springframework.stereotype.Component

@Component
class UserTradeRebateEventListener @Autowired constructor(
    private val optionOrderRepository: OptionOrderRepository,
    private val userTradeRebateService: UserTradeRebateService
) {

    private val logger = LoggerFactory.getLogger(this::class.java)

    @EventListener
    fun handleOptionExecutedEvent(event: OptionExecutedEvent) {
        logger.debug("UserTradeRebateEventListener: Option execute completed for order: ${event.orderId}")

        val optionOrder = optionOrderRepository.findByIdOrNull(event.orderId) ?: run {
            logger.debug("Option order not found: ${event.orderId}")
            return
        }

        try {
            // 处理用户交易返利
            userTradeRebateService.processTradeRebate(optionOrder)
        } catch (e: Exception) {
            logger.error("Error processing trade rebate for order ${optionOrder.id}: ${e.message}", e)
        }
    }
}
