package io.vault.jasper.repository

import io.vault.jasper.enums.OptionDirection
import io.vault.jasper.enums.Symbol
import io.vault.jasper.model.BenefitStatus
import io.vault.jasper.model.ZeroPremiumBenefit
import org.springframework.data.mongodb.repository.MongoRepository
import org.springframework.stereotype.Repository

@Repository
interface ZeroPremiumBenefitRepository : MongoRepository<ZeroPremiumBenefit, String> {
    fun findFirstByName(name: String): ZeroPremiumBenefit?
    fun findByUnderlyingAssetAndAmountAndOptionDirectionAndExpiryInHour(
        underlyingAsset: Symbol,
        amount: String,
        optionDirection: OptionDirection?,
        expiryInHour: String
    ): List<ZeroPremiumBenefit>
    fun findByActivityIdIsNull(): List<ZeroPremiumBenefit>
    fun findByActivityId(activityId: String?): List<ZeroPremiumBenefit>
    fun findByActivityIdAndStatus(
        activityId: String?,
        status: BenefitStatus = BenefitStatus.ACTIVE
    ): List<ZeroPremiumBenefit>
}