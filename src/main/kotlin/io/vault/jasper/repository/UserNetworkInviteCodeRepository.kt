package io.vault.jasper.repository

import io.vault.jasper.model.UserNetwork
import io.vault.jasper.model.UserNetworkInviteCode
import org.springframework.data.domain.Sort
import org.springframework.data.mongodb.repository.MongoRepository
import org.springframework.stereotype.Repository

@Repository
interface UserNetworkInviteCodeRepository : MongoRepository<UserNetworkInviteCode, String> {

    fun findByInviteCode(inviteCode: String): UserNetworkInviteCode?
    fun findByAddressIgnoreCase(address: String): List<UserNetworkInviteCode>
    fun findByBindAddressIgnoreCase(address: String): List<UserNetworkInviteCode>
    fun findByBindUserNetworkId(userNetworkId: String): List<UserNetworkInviteCode>
    fun findByAddressIgnoreCaseAndBindAddressIsNull(address: String): List<UserNetworkInviteCode>
}