package io.vault.jasper.service.blockchain.loglistener

import io.vault.jasper.enums.ChainType
import io.vault.jasper.repository.BlockchainRepository
import io.vault.jasper.repository.LpVaultRecordRepository
import io.vault.jasper.repository.PendingOptionOrderRepository
import io.vault.jasper.service.BlockchainConfigService
import io.vault.jasper.service.blockchain.BlockchainServiceFactory
import io.vault.jasper.service.blockchain.EvmService
import io.vault.jasper.utils.ProfileUtil
import org.slf4j.LoggerFactory
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.stereotype.Service
import javax.annotation.PostConstruct

@Service
class EvmLogListenerFactory @Autowired constructor(
    private val blockchainServiceFactory: BlockchainServiceFactory,
    private val blockchainRepository: BlockchainRepository,
    private val pendingOptionOrderRepository: PendingOptionOrderRepository,
    private val lpVaultRecordRepository: LpVaultRecordRepository,
    private val blockchainConfigService: BlockchainConfigService
) {

    private val logger = LoggerFactory.getLogger(this::class.java)

    private var evmLogListeners: MutableMap<ChainType, EvmLogListener> = mutableMapOf()

    // @PostConstruct
    fun init() {
        val supportChains = when (ProfileUtil.activeProfile) {
            "prod" -> listOf(ChainType.ARBITRUM, ChainType.BASE)
            else -> listOf(ChainType.ARBITRUM, ChainType.BASE)
        }
        supportChains.forEach { chainType ->
            val service = blockchainServiceFactory.getBlockchainService(chainType)
            if (service is EvmService && chainType !in evmLogListeners) {
                evmLogListeners[chainType] = EvmLogListener(
                    service,
                    blockchainRepository,
                    pendingOptionOrderRepository,
                    lpVaultRecordRepository,
                    blockchainConfigService
                )
            }
        }
    }
}