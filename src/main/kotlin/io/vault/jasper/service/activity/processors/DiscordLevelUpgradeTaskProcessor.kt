package io.vault.jasper.service.activity.processors

import io.vault.jasper.model.User
import io.vault.jasper.model.activity.Task
import io.vault.jasper.model.activity.TaskConfigParams
import io.vault.jasper.model.activity.TaskType
import io.vault.jasper.service.AirDropService
import io.vault.jasper.service.activity.TaskProcessor
import org.slf4j.LoggerFactory
import org.springframework.stereotype.Component

/**
 * Processor for DISCORD_LEVEL_UPGRADE task type
 */
@Component
class DiscordLevelUpgradeTaskProcessor(
    private val airDropService: AirDropService
) : TaskProcessor {

    private val logger = LoggerFactory.getLogger(this::class.java)

    override fun getTaskType(): TaskType = TaskType.DISCORD_LEVEL_UPGRADE
    
    override fun isTaskCompleted(user: User, task: Task, context: Map<String, Any>): Pair<Boolean, Map<String, Any>> {
        // Get the minimum Discord level required from the task config
        val minDiscordLevelStr = task.taskConfig[TaskConfigParams.MIN_DISCORD_LEVEL] as? String
            ?: return Pair(false, emptyMap())
        
        val minDiscordLevel = minDiscordLevelStr.toIntOrNull() ?: 1
        
        // Get the user's Discord level
        val userDiscordLevel = airDropService.getDiscordLevel(user)
        
        val isCompleted = userDiscordLevel >= minDiscordLevel

        if(!isCompleted){
            logger.info("DiscordLevelUpgradeTaskProcessor User ${user.address} level is $userDiscordLevel, required $minDiscordLevel")
        }
        
        val completionData = if (isCompleted) {
            mapOf(
                "userDiscordLevel" to userDiscordLevel.toString(),
                "requiredDiscordLevel" to minDiscordLevel.toString()
            )
        } else {
            emptyMap()
        }
        
        return Pair(isCompleted, completionData)
    }
}
