package io.vault.jasper.blockchain

import java.math.BigDecimal

interface IBlockchainUtil {

    fun getTokenDecimals(contract: String): Int

    fun getGasBalance(address: String): BigDecimal

    fun getTokenBalance(address: String, contract: String): BigDecimal

    fun getBalance(address: String, contract: String? = null): BigDecimal

    fun sendGas(wallet: Pair<String, String>, to: String, amount: BigDecimal): String

    fun sendToken(wallet: Pair<String, String>, to: String, amount: BigDecimal, contract: String): String

    fun getTokenBalanceBigInteger(address: String, contract: String): BigDecimal
}