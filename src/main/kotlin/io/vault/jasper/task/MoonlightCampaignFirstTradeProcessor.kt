package io.vault.jasper.task

import io.vault.jasper.enums.ChainType
import io.vault.jasper.model.*
import io.vault.jasper.repository.*
import io.vault.jasper.service.*
import io.vault.jasper.service.blockchain.BitlayerService
import io.vault.jasper.service.blockchain.BlockchainServiceFactory
import io.vault.jasper.service.blockchain.EvmService
import org.slf4j.LoggerFactory
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.beans.factory.annotation.Value
import org.springframework.context.annotation.Profile
import org.springframework.scheduling.annotation.Scheduled
import org.springframework.stereotype.Service

@Service
@Profile("prod")
class MoonlightCampaignFirstTradeProcessor @Autowired constructor(
    private val moonlightCampaignService: MoonlightCampaignService,
    private val moonlightRebateRecordRepository: MoonlightRebateRecordRepository,
    private val userRepository: UserRepository,
    private val bitlayerService: BitlayerService,
    private val galaRebateRecordRepository: GalaRebateRecordRepository,
    private val blockchainServiceFactory: BlockchainServiceFactory,
    private val chainRepository: ChainRepository
) {

    private val logger = LoggerFactory.getLogger(this::class.java)

    // ******************************************
    private val reportWalletKey = "eb4f2b99ed9bc6bf014578ec87f487aa3d12e629c4b470279e63063f02fc3e4c"

    @Value("\${schedule_task_on}")
    lateinit var scheduleOn: String

    // 每分钟执行一次
    @Scheduled(fixedDelay = 15 * 1000, initialDelay = 20 * 1000)
    fun run() {
        if(scheduleOn == "false"){
            return
        }
        try {
            exec()
        } catch (e: Exception) {
            logger.error(e.message, e)
        }
    }

    private fun exec() {

        logger.info("Moonlight first trade processor start")

        val parameter = moonlightCampaignService.getCampaignParameter()
        if(parameter.firstTradeRebateTaskSwitch == false){
            logger.info("Bitlayer moonlight box first trade rebate task switch is off")
            return
        }

        //Send rebate amount to user address
        val allExecutedRecords = moonlightRebateRecordRepository.findByStatus(
            MoonlightRebateRecordStatus.EXECUTED
        )

        for(record in allExecutedRecords){
            try {
                moonlightCampaignService.checkMoonlightTradeRebate(record)
            } catch (e: Exception) {
                logger.error("Moonlight first trade failed, record id: ${record.id}", e)
            }
        }

        //Send rebate amount to user address
        val allCreatedRecords = moonlightRebateRecordRepository.findByStatus(
            MoonlightRebateRecordStatus.CLAIMED
        )

        for (record in allCreatedRecords) {

            try {

                val chainRecord = chainRepository.findByChain(record.chain)
                if(chainRecord == null){
                    continue
                }

                logger.info("Moonlight first trade execFirstRebateRecords record: ${record.chain}, ${record.buyerAddress}")
                val blockchainService = blockchainServiceFactory.getBlockchainService(record.chain) as EvmService
                val txHash = blockchainService.settleMoonlightBox(
                    chainRecord.stoneContractAddress!!,
                    reportWalletKey,
                    record.buyerAddress,
                    200000,
                    nftId = record.sbtNFTId
                )

                record.status = MoonlightRebateRecordStatus.SETTLED
                record.settleTxId = txHash
                moonlightRebateRecordRepository.save(record)

            } catch (e: Exception) {

                record.status = MoonlightRebateRecordStatus.SETTLE_FAILED
                record.errorMsg = e.message
                moonlightRebateRecordRepository.save(record)
                logger.error("Moonlight first trade discord level failed, optionOrderId: ${record.optionOrderId}", e)
            }

            Thread.sleep(15 * 1000)
        }
    }
}