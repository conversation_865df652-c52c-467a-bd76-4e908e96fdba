package io.vault.jasper.model

import io.vault.jasper.enums.ChainType
import org.springframework.data.annotation.Id
import org.springframework.data.mongodb.core.mapping.Document
import org.springframework.data.mongodb.core.mapping.Field
import java.math.BigDecimal
import java.time.LocalDateTime

@Document("moonlight_campaign_parameter")
data class MoonlightCampaignParameter(
    @Id
    val id: String? = null,
    @Field("start_date")
    var startDate: LocalDateTime = LocalDateTime.now(),
    @Field("end_date")
    var endDate: LocalDateTime = LocalDateTime.of(2024, 12, 30, 0, 0, 0),
    @Field("trade_rebate_chain")
    var tradeRebateChain: ChainType = ChainType.BITLAYER,
    @Field("trade_rebate_contract_address")
    var tradeRebateContractAddress: String = "0x2c9f3AfB4492a4Adf155e0a3E88394b24dA74926",
    @Field("first_trade_rebate_task_switch")
    var firstTradeRebateTaskSwitch: Boolean = true, // 推送First Trade Rebate任务的开关
    @Field("first_trade_rebate_count")
    var firstTradeRebateCount: Int = 10000,
    @Field("first_trade_degen_quantity")
    var firstTradeDegenQuantity: BigDecimal = BigDecimal("0.01"),
    @Field("gala_three_link")
    var galaThreeLink: String = "https://www.bitlayer.org/ready-player-one/dapps-center",
    @Field("start_date_list")
    val startDateList: List<LocalDateTime> = listOf(
        LocalDateTime.of(2024, 10, 25, 18, 0, 0)
    ),
    @Field("end_date_list")
    val endDateList: List<LocalDateTime> = listOf(
        LocalDateTime.of(2024, 11, 25, 19, 59, 59),
    ),
    @Field("nft_ids")
    val nftIds: List<String> = listOf(
        "1"
    )
)