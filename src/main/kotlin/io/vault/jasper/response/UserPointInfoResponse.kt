package io.vault.jasper.response

import io.swagger.annotations.ApiModel
import io.swagger.annotations.ApiModelProperty
import io.vault.jasper.enums.ChainType
import io.vault.jasper.model.KolStatus

@ApiModel
data class UserPointInfoResponse(

    @ApiModelProperty("钱包地址", required = true)
    val address: String,

    @ApiModelProperty("总积分", required = true)
    val totalPoint: String,

    @ApiModelProperty("昨日积分", required = true)
    val yesterdayPoint: String,

    @ApiModelProperty("7天积分", required = true)
    val pointsIn7Days: String,

    @ApiModelProperty("30天积分", required = true)
    val pointsIn30Days: String
)
