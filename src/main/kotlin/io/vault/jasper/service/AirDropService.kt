package io.vault.jasper.service

import io.vault.jasper.enums.ChainType
import io.vault.jasper.enums.OptionDirection
import io.vault.jasper.enums.Symbol
import io.vault.jasper.model.*
import io.vault.jasper.repository.*
import org.slf4j.LoggerFactory
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.data.mongodb.core.MongoTemplate
import org.springframework.data.mongodb.core.aggregation.Aggregation
import org.springframework.data.mongodb.core.aggregation.ConvertOperators
import org.springframework.data.mongodb.core.query.Criteria
import org.springframework.data.mongodb.core.query.Query
import org.springframework.data.repository.findByIdOrNull
import org.springframework.stereotype.Service
import org.web3j.crypto.Keys
import java.math.BigDecimal

@Service
class AirDropService @Autowired constructor(
    private val airDropTransactionRepository: AirDropTransactionRepository,
    private val userRepository: UserRepository,
    private val optionOrderRepository: OptionOrderRepository,
    private val mongoTemplate: MongoTemplate,
    private val airDropFreeTradeRecordRepository: AirDropFreeTradeRecordRepository,
    private val airdropParameterRepository: AirdropParameterRepository,
    private val airdropNftParameterRepository: AirdropNftParameterRepository,
    private val airdropTradeRebateRecordRepository: AirdropTradeRebateRecordRepository,
    private val orderRepository: OrderRepository,
    private val discordLevelRepository: DiscordLevelRepository,
    private val userPointRecordRepository: UserPointRecordRepository,
    private val userCampaignInfoRepository: UserCampaignInfoRepository,
    private val airdropSummaryRepository: AirdropSummaryRepository
){

    private val logger = LoggerFactory.getLogger(this::class.java)

    fun checkSocialTask(
        user: User
    ) {

        val parameter = getAirdropParameter()
        val list = airDropTransactionRepository.findByType(AirDropType.SOCIAL_TASK)
        if(list.size >= parameter.socialTaskCount) {
            logger.debug("Social Task has reach the limit ${parameter.socialTaskCount}, skip")
            return
        }

        logger.debug("Check Airdrop Social Task ${user.address}")

        if(isUserSocialTaskFinished(user)){
            logger.debug("Social Task Completed ${user.address}")

            val existingTransactions = airDropTransactionRepository.findByUserIdAndType(
                user.id!!,
                AirDropType.SOCIAL_TASK
            )

            if(existingTransactions.isEmpty()){

                logger.debug("Social Task transaction for ${user.address}")
                airDropTransactionRepository.save(
                    AirdropTransaction(
                        userId = user.id,
                        address = user.address,
                        type = AirDropType.SOCIAL_TASK,
                        amount = BigDecimal("1000")
                    )
                )

                updateUserCampaignInfoTaskPointWithType(
                    user,
                    AirDropType.SOCIAL_TASK
                )

                checkInviteTask(user)

            } else {
                logger.debug("Social Task Has complete for ${user.address}, skip")
            }
        }
    }

    fun checkInviteTask(
        user: User
    ){

        val parameter = getAirdropParameter()
        val list = airDropTransactionRepository.findByType(AirDropType.INVITATION_TASK)
        if(list.size >= parameter.invitationTaskCount) {
            logger.debug("Invitation Task has reach the limit ${parameter.invitationTaskCount}, skip")
            return
        }

        if(user.invitedUserId == null){
            logger.debug("User ${user.address} has not invited user, skip")
            return
        }

        // 被邀用户必须是活动开始后的新用户
        if(user.created.isBefore(parameter.startDate)){
            logger.debug("CheckInviteTask User ${user.address} is not a new user, skip")
            return
        }

        val invitedUser = userRepository.findById(user.invitedUserId!!).orElse(null)
        if(invitedUser == null){
            logger.debug("Invited User ${user.invitedUserId} not found, skip")
            return
        }

        logger.debug("Check Airdrop Invite Task ${user.address} parent ${invitedUser.address}")

        if(user.twitterTaskFinished){

            val existingTransactions = airDropTransactionRepository.findByUserIdAndReferralUserIdAndType(
                invitedUser.id!!,
                user.id!!,
                AirDropType.INVITATION_TASK
            )

            if(existingTransactions.isEmpty()){

                logger.debug("Invite Task transaction for ${invitedUser.address}")
                airDropTransactionRepository.save(
                    AirdropTransaction(
                        userId = invitedUser.id,
                        address = invitedUser.address,
                        type = AirDropType.INVITATION_TASK,
                        amount = BigDecimal("400"),
                        referralUserId = user.id,
                        referralAddress = user.address
                    )
                )

                updateUserCampaignInfoTaskPointWithType(
                    invitedUser,
                    AirDropType.INVITATION_TASK
                )
                updateUserCampaignInfoTaskCountWithType(
                    invitedUser,
                    AirDropType.INVITATION_TASK
                )

            } else {
                logger.debug("Invite Task Has complete for ${user.address}, skip")
            }
        }
    }

    fun checkTradingTask(
        address: String
    ) {

        val user = userRepository.findByAddressIgnoreCase(address) ?: return
        val parameter = getAirdropParameter()

        val list = airDropTransactionRepository.findByType(AirDropType.TRADING_TASK)
        if(list.size >= parameter.tradingTaskCount) {
            logger.debug("Trading Task has reach the limit ${parameter.tradingTaskCount}, skip")
            return
        }

        // 被邀用户必须是活动开始后的新用户
        if(user.created.isBefore(parameter.startDate)){
            logger.debug("CheckTradingTask User ${user.address} is not a new user, skip")
            return
        }

        if(user.invitedUserId == null){
            logger.debug("User ${user.address} has not invited user, skip")
            return
        }

        val invitedUser = userRepository.findById(user.invitedUserId!!).orElse(null)
        if(invitedUser == null){
            logger.debug("Invited User ${user.invitedUserId} not found, skip")
            return
        }

        logger.debug("Check Airdrop Trading Task ${user.address} parent ${invitedUser.address}")

        val startDate = parameter.startDate

        // 不再限制交易的链
        //val chain = ChainType.ARBITRUM

        val buyerAddress = Keys.toChecksumAddress(user.address)
        val userTransactions = optionOrderRepository.findByBuyerIgnoreCaseAndStatusInAndCreatedGreaterThan(
            buyerAddress,
            OptionStatus.executedStatusList(),
            startDate
        )

        val isTaskCompleted = userTransactions.isNotEmpty()

        if(isTaskCompleted){

            val existingTransactions = airDropTransactionRepository.findByUserIdAndReferralUserIdAndType(
                invitedUser.id!!,
                user.id!!,
                AirDropType.TRADING_TASK
            )

            if(existingTransactions.isEmpty()){

                logger.debug("Trading Task transaction for ${invitedUser.address}")
                airDropTransactionRepository.save(
                    AirdropTransaction(
                        userId = invitedUser.id,
                        address = invitedUser.address,
                        type = AirDropType.TRADING_TASK,
                        amount = BigDecimal("1000"),
                        referralUserId = user.id,
                        referralAddress = user.address
                    )
                )

                user.hasTraded = true
                userRepository.save(user)

                updateUserCampaignInfoTaskPointWithType(
                    invitedUser,
                    AirDropType.TRADING_TASK
                )
                updateUserCampaignInfoTaskCountWithType(
                    invitedUser,
                    AirDropType.TRADING_TASK
                )

            } else {
                logger.debug("Trading Task Has complete for ${user.address}, skip")
            }
        }

        // Check if the user has claimed the free trade
        val freeTradeRecords = airDropTransactionRepository.findByUserIdAndTypeAndHasClaimedFreeTrade(
            invitedUser.id!!,
            AirDropType.TRADING_TASK,
            false
        )

        if(freeTradeRecords.size >= parameter.freeTradeCount){

            var transactionIds = ""
            var count = 0
            for(record in freeTradeRecords){
                transactionIds += record.id + ","
                record.hasClaimedFreeTrade = true
                airDropTransactionRepository.save(record)

                count++

                if(count >= parameter.freeTradeCount){
                    break
                }
            }

            airDropFreeTradeRecordRepository.save(
                AirdropFreeTradeRecord(
                    userId = invitedUser.id,
                    address = invitedUser.address,
                    airdropTransactionIds = transactionIds
                )
            )
        }
    }

    fun getTransactionList(
        type: AirDropType,
        userId: String,
    ): List<AirdropTransaction> {
        return airDropTransactionRepository.findByUserIdAndType(
            userId,
            type
        )
    }

    fun getUserTotalPoint(
        user: User
    ): BigDecimal {

        val criteria = Criteria.where(AirdropTransaction::userId.name).`is`(
            user.id!!
        )

        val agg = Aggregation.newAggregation(
            Aggregation.match(criteria),
            Aggregation.group().sum(
                ConvertOperators.valueOf(
                    AirdropTransaction::amount.name
                ).convertToDecimal()).`as`("total")
        )

        val groupResults = mongoTemplate.aggregate(agg, AirdropTransaction::class.java, Map::class.java)
        val result = groupResults.mappedResults.firstOrNull()
        val total = (result?.get("total")?:"0").toString()

        return BigDecimal(total)
    }

    fun getUnclaimedFreeTradeCount(
        user: User
    ): Int {
        return airDropFreeTradeRecordRepository.findByUserIdAndStatus(
            user.id!!,
            AirdropFreeTradeRecordStatus.CRETATED
        ).size
    }

    fun getUnclaimedFreeTradeRecords(
        user: User
    ): List<AirdropFreeTradeRecord> {
        return airDropFreeTradeRecordRepository.findByUserIdAndStatus(
            user.id!!,
            AirdropFreeTradeRecordStatus.CRETATED
        )
    }

    /**
     * 获取空投参数对象
     */
    fun getAirdropParameter(): AirdropParameter {
        val params = airdropParameterRepository.findAll()

        return if (params.isEmpty()) {
            val p = AirdropParameter(null)
            airdropParameterRepository.save(p)
        } else {
            params.first()
        }
    }

    /**
     * 获取 NFT 空投参数对象
     */
    fun getNftAirdropParameter(): AirdropNftParameter {
        val params = airdropNftParameterRepository.findAll()

        return if (params.isEmpty()) {
            val p = AirdropNftParameter(null)
            airdropNftParameterRepository.save(p)
        } else {
            params.first()
        }
    }

    /**
     * 判断是否活动内首单
     */
    fun isFirstOrderInAirdrop(
        optionOrder: OptionOrder
    ): Boolean{

        if(optionOrder.status != OptionStatus.SETTLED){
            return false
        }

        val parameter = getAirdropParameter()

        val optionOrders = optionOrderRepository.findByBuyerAndStatus(
            optionOrder.buyer!!,
            OptionStatus.SETTLED
        ).sortedBy { it.id }

        if(optionOrders.isEmpty()){
            return false
        }

        if(optionOrders[0].id!! != optionOrder.id!!){
            return false
        }

        if(optionOrder.created.isBefore(parameter.startDate)){
            return false
        }

        if(optionOrder.chain != ChainType.ARBITRUM){
            return false
        }

        val user = userRepository.findByAddressIgnoreCase(optionOrder.buyer!!)
        if(user == null || user.created.isBefore(parameter.startDate)){
            return false
        }

        return true
    }

    fun checkAirdropTradeRebate(
        optionOrder: OptionOrder
    ){

        logger.debug("Airdrop Service Check Airdrop First Trade Rebate ${optionOrder.buyer} ${optionOrder.id}")

        // 符合 2H，0.2 ETH 规格的订单
        if(optionOrder.bidAsset == null || optionOrder.bidAsset != Symbol.ETH){
            logger.debug("Airdrop Service Not ETH ${optionOrder.buyer} ${optionOrder.id}")
            return
        }

        if(optionOrder.expiryInHour == null || optionOrder.expiryInHour != "2"){
            logger.debug("Airdrop Service Not 2 hours ${optionOrder.buyer} ${optionOrder.id}")
            return
        }

        if(optionOrder.bidAmount == null || optionOrder.bidAmount!!.compareTo(BigDecimal("0.2")) != 0){
            logger.debug("Airdrop Service Not 0.2 ETH ${optionOrder.buyer} ${optionOrder.id}")
            return
        }

        // 是否已经创建了订单
        val existingRecord = airdropTradeRebateRecordRepository.findByOptionOrderId(
            optionOrder.id!!
        )

        if(existingRecord != null){
            logger.debug("Airdrop Service Already add rebate record ${optionOrder.buyer} ${optionOrder.id}")
            return
        }

        // 是否活动内首单
        if(!isFirstOrderInAirdrop(optionOrder)){
            logger.debug("Airdrop Service Not first order in airdrop ${optionOrder.buyer} ${optionOrder.id}")
            return
        }

        //是否是新用户
        val parameter = getAirdropParameter()
        val user = userRepository.findByAddressIgnoreCase(optionOrder.buyer!!)
        if(user == null || user.created.isBefore(parameter.startDate)){
            logger.debug("Airdrop Service not new user in airdrop ${optionOrder.buyer} ${optionOrder.id}")
            return
        }

        val order = orderRepository.findByIdOrNull(optionOrder.orderId)
        if(order == null){
            logger.debug("Airdrop Service can not find order record ${optionOrder.buyer} ${optionOrder.id}")
            return
        }

        logger.debug("Airdrop Service Add rebate record ${optionOrder.buyer} ${optionOrder.id}")
        val record = AirdropTradeRebateRecord(
            optionOrderId = optionOrder.id,
            buyerAddress = optionOrder.buyer!!,
            direction = optionOrder.direction!!
        )

        record.strikePrice = order.strikePrice.movePointLeft(18)
        record.settlementPrice = BigDecimal(optionOrder.marketPriceAtSettlement!!).movePointLeft(18)

        record.premiumFee = optionOrder.premiumFeePay!!
        record.premiumAsset = Symbol.valueOf(optionOrder.premiumAsset!!.asset)
        record.premiumFeeInUsdt = optionOrder.premiumFeePayInUsdt!!

        record.profit = optionOrder.buyerProfit!!
        record.profitAsset = Symbol.USDT
        record.profitInUsdt = optionOrder.buyerProfit!!

        if(optionOrder.direction == OptionDirection.CALL){
            record.profitAsset = optionOrder.bidAsset!!
            var profitInUsdt = record.profit.movePointLeft(18)
            logger.debug("Airdrop Service Profit in ${optionOrder.buyer} ${record.profitAsset} ${profitInUsdt}")
            profitInUsdt = profitInUsdt.multiply(record.settlementPrice)
            logger.debug("Airdrop Service Profit in ${optionOrder.buyer} ${record.settlementPrice} ${profitInUsdt}")
            profitInUsdt = profitInUsdt.movePointRight(6).setScale(0, BigDecimal.ROUND_HALF_UP)
            logger.debug("Airdrop Service Profit in ${optionOrder.buyer} ${record.settlementPrice} ${profitInUsdt}")
            record.profitInUsdt = profitInUsdt
        }

        val discordLevel = getDiscordLevel(user)
        record.discordLevel = discordLevel

        record.netProfit = record.profitInUsdt.subtract(record.premiumFeeInUsdt)
        if(record.netProfit.compareTo(BigDecimal.ZERO) == -1){
            record.rebateAmount = record.netProfit.multiply(BigDecimal("-1"))
            record.status = AirdropTradeRebateRecordStatus.CREATED

            //获得双倍 杠杆 jpoint
            val userPointRecord = userPointRecordRepository.findByOptionOrderId(
                optionOrder.id
            )
            if(userPointRecord != null){
                userPointRecord.premiumPoint = userPointRecord.premiumPoint.multiply(BigDecimal("2"))
                userPointRecord.totalPoint = userPointRecord.netProfitPoint.add(userPointRecord.premiumPoint)
                userPointRecordRepository.save(userPointRecord)
            }

        } else {
            record.status = AirdropTradeRebateRecordStatus.SETTLED
        }

        airdropTradeRebateRecordRepository.save(record)
    }

    fun getDiscordLevel(
        user: User
    ): Int {

        val discordId = user.discordInfo?.userId ?: return 0
        val level = discordLevelRepository.findByDiscordId(discordId)
        if(level == null){
            return 0
        }

        return level.level
    }

    fun isUserSocialTaskFinished(user: User): Boolean{
        val discordTaskFinished = user.discordInfo?.inGuild ?: false
        if(user.twitterTaskFinished && discordTaskFinished){
            return true
        }

        return false
    }

    fun getTotalSPoint(
        taskType: AirDropType,
    ): BigDecimal{

        val criteria = Criteria.where(AirdropTransaction::type.name).`is`(
            taskType
        )

        val agg = Aggregation.newAggregation(
            Aggregation.match(criteria),
            Aggregation.group().sum(
                ConvertOperators.valueOf(
                    AirdropTransaction::amount.name
                ).convertToDecimal()).`as`("total")
        )

        val groupResults = mongoTemplate.aggregate(agg, AirdropTransaction::class.java, Map::class.java)
        val result = groupResults.mappedResults.firstOrNull()
        val total = (result?.get("total")?:"0").toString()

        return BigDecimal(total)
    }

    fun getUserTotalSPoint(
        user: User,
        taskType: AirDropType,
    ): BigDecimal{

        val criteria = Criteria.where(AirdropTransaction::type.name).`is`(
            taskType
        ).and(AirdropTransaction::address.name).`is`(
            user.address
        )

        val agg = Aggregation.newAggregation(
            Aggregation.match(criteria),
            Aggregation.group().sum(
                ConvertOperators.valueOf(
                    AirdropTransaction::amount.name
                ).convertToDecimal()).`as`("total")
        )

        val groupResults = mongoTemplate.aggregate(agg, AirdropTransaction::class.java, Map::class.java)
        val result = groupResults.mappedResults.firstOrNull()
        val total = (result?.get("total")?:"0").toString()

        return BigDecimal(total)
    }

    fun getFirstTradeRebateCount(): Int {
        return airdropTradeRebateRecordRepository.findByStatusInAndSettleTxIdIsNotNull(
            listOf(AirdropTradeRebateRecordStatus.SETTLED, AirdropTradeRebateRecordStatus.ARB_SETTLED)
        ).size
    }

    fun getUserCampaignInfo(user: User): UserCampaignInfo {
        var existingInfo = userCampaignInfoRepository.findByAddressIgnoreCase(user.address)
        if(existingInfo == null){
            existingInfo = userCampaignInfoRepository.save(
                UserCampaignInfo(
                    userId = user.id!!,
                    address = Keys.toChecksumAddress(user.address)
                )
            )
        }

        return existingInfo!!
    }

    fun updateUserCampaignInfoTaskPointWithType(
        user: User,
        type: AirDropType
    ) {
        val userCampaignInfo = getUserCampaignInfo(user)
        val point = getUserTotalSPoint(
            user,
            type
        )

        if(type == AirDropType.SOCIAL_TASK){
            userCampaignInfo.socialTaskPoint = point
        } else if(type == AirDropType.INVITATION_TASK){
            userCampaignInfo.inviteTaskPoint = point
        } else if(type == AirDropType.TRADING_TASK){
            userCampaignInfo.tradeTaskPoint = point
        }

        userCampaignInfo.totalPoint = userCampaignInfo.socialTaskPoint.add(userCampaignInfo.inviteTaskPoint).add(userCampaignInfo.tradeTaskPoint)

        userCampaignInfoRepository.save(userCampaignInfo)

        //Update SPoint Info in User
        user.sPoint = userCampaignInfo.totalPoint
        user.referralSPoint = userCampaignInfo.tradeTaskPoint
        userRepository.save(user)
    }

    fun updateUserCampaignInfoTaskCountWithType(
        user: User,
        type: AirDropType
    ) {

        val userCampaignInfo = getUserCampaignInfo(user)
        val count = getTransactionList(type, user.id!!).size

        if(type == AirDropType.INVITATION_TASK){
            userCampaignInfo.airdropInviteVerifiedCount = count
        } else if(type == AirDropType.TRADING_TASK){
            userCampaignInfo.airdropInviteTradeCount = count
        }

        userCampaignInfoRepository.save(userCampaignInfo)
    }

    fun updateUserCampaignReferralCount(
        user: User
    ) {
        val userCampaignInfo = getUserCampaignInfo(user)
        val afterTime = getAirdropParameter().startDate

        val query = Query()
        query.addCriteria(Criteria.where(User::invitedUserId.name).`is`(user.id!!))
        query.addCriteria(Criteria.where(User::created.name).gte(afterTime))

        userCampaignInfo.airdropInviteCount = mongoTemplate.count(query, User::class.java).toInt()

        userCampaignInfoRepository.save(userCampaignInfo)
    }

    fun updateUserCampaignInfo(
        user: User
    ) {
        updateUserCampaignReferralCount(user)
        updateUserCampaignInfoTaskPointWithType(user, AirDropType.SOCIAL_TASK)
        updateUserCampaignInfoTaskPointWithType(user, AirDropType.INVITATION_TASK)
        updateUserCampaignInfoTaskPointWithType(user, AirDropType.TRADING_TASK)
        updateUserCampaignInfoTaskCountWithType(user, AirDropType.INVITATION_TASK)
        updateUserCampaignInfoTaskCountWithType(user, AirDropType.TRADING_TASK)
    }

    fun getAllAirdropTransactions(): List<AirdropTransaction> {
        return airDropTransactionRepository.findAll()
    }

    fun getAirdropSummary(): AirdropSummary{

        val params = airdropSummaryRepository.findAll()

        return if (params.isEmpty()) {
            val p = AirdropSummary(null)
            airdropSummaryRepository.save(p)
        } else {
            params.first()
        }
    }
}