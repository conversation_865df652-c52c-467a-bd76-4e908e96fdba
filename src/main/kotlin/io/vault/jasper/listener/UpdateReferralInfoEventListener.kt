package io.vault.jasper.listener

import io.vault.jasper.event.OptionExecutedEvent
import io.vault.jasper.repository.OptionOrderRepository
import io.vault.jasper.repository.UserRepository
import io.vault.jasper.service.BitlayerCampaignService
import io.vault.jasper.service.ReferralInfoService
import org.slf4j.LoggerFactory
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.context.event.EventListener
import org.springframework.data.repository.findByIdOrNull
import org.springframework.stereotype.Component

@Component
class UpdateReferralInfoEventListener @Autowired constructor(
    private val optionOrderRepository: OptionOrderRepository,
    private val referralInfoService: ReferralInfoService,
    private val userRepository: UserRepository
) {

    private val logger = LoggerFactory.getLogger(this::class.java)

    @EventListener
    fun handleOptionExecutedEvent(event: OptionExecutedEvent) {
        logger.info("UpdateReferralInfoEventListener execute completed for order: ${event.orderId}")

        val optionOrder = optionOrderRepository.findByIdOrNull(event.orderId) ?: return
        val buyer = optionOrder.buyer ?: return

        // 更新推荐人的 referrerVolume
        val user = userRepository.findByAddressIgnoreCase(buyer)
        if (user == null) {
            logger.info("UpdateReferralInfoEventListener user not found for address: $buyer")
            return
        }

        if(user.invitedUserId == null){
            logger.info("UpdateReferralInfoEventListener user not invited user for address: $buyer")
            return
        }

        val invitor = userRepository.findByIdOrNull(user.invitedUserId!!)
        if(invitor == null){
            logger.info("UpdateReferralInfoEventListener invitor not found for address: ${user.invitedUserId}")
            return
        }

        referralInfoService.updateReferralVolumeAtDate(invitor)
    }
}