# fly.toml app configuration file generated for v2-server-uat on 2024-04-28T16:50:16+08:00
#
# See https://fly.io/docs/reference/configuration/ for information about how to use this file.
#

app = 'v2-server-uat'
primary_region = 'sin'

[build]

[http_service]
  internal_port = 8080
  force_https = true
  auto_stop_machines = false
  auto_start_machines = true
  min_machines_running = 0
  processes = ['app']

[[vm]]
  memory = '2gb'
  cpu_kind = 'shared'
  cpus = 2
