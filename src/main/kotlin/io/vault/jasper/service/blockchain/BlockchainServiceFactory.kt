package io.vault.jasper.service.blockchain

import io.vault.jasper.enums.ChainType
import io.vault.jasper.service.IBlockchainService
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.stereotype.Service

@Service
class BlockchainServiceFactory @Autowired constructor(
    private val arbitrumService: ArbitrumService,
    private val seiService: SeiService,
    private val baseService: BaseService,
    private val bitlayerTestService: BitlayerTestService,
    private val bitlayerService: BitlayerService,
    private val bscService: BscService
) {

    fun getBlockchainService(chainType: ChainType): IBlockchainService? {
        return when (chainType) {
            ChainType.ARBITRUM -> arbitrumService
            ChainType.SEI -> seiService
            ChainType.BASE -> baseService
            ChainType.BITLAYER -> bitlayerService
            ChainType.BITLAYER_TEST -> bitlayerTestService
            ChainType.BSC -> bscService
            else -> null
        }
    }

    fun isEvmService(service: IBlockchainService?) = service is EvmService
}