package io.vault.jasper.response

import io.swagger.annotations.ApiModel
import io.swagger.annotations.ApiModelProperty
import io.vault.jasper.enums.Symbol
import org.web3j.abi.datatypes.Bool

@ApiModel
data class BinanceCampaignInfoResponse(

    @ApiModelProperty("钱包地址", required = true)
    val address: String,

    @ApiModelProperty("是否已经绑定Discord帐号", required = false)
    var hasBindDiscord: Boolean = false,

    @ApiModelProperty("是否已经加入了Discord频道", required = true)
    val isJoinDiscord: Boolean = false,

    @ApiModelProperty("Discord用户等级", required = false)
    val discordLevel: Int = 0,

    @ApiModelProperty("Twitter是否已经转发Tweet", required = true)
    val isJasperTwitterRetweet: Boolean = false,

    @ApiModelProperty("首笔交易的PNL", required = true)
    val firstTradePNL: String = "0",

    @ApiModelProperty("首笔交易是否0.5H 0.2 ETH", required = true)
    val firstTradeIs2HETH: Boolean = false,

    @ApiModelProperty("能否参加首单永赚的活动", required = true)
    val canTrade: Boolean = true,

    @ApiModelProperty("已经补偿首单永赚的用户数", required = true)
    val firstTradeRebateCount: Int = 0,

    @ApiModelProperty("首单Gross Profit Rate", required = false)
    val firstTradeGrossProfitRate: String = "0",

    @ApiModelProperty("首单Premium", required = false)
    val firstTradePremium: String = "0",

    @ApiModelProperty("首单Premium 币种", required = false)
    val firstTradePremiumAsset: Symbol = Symbol.BTC,

    @ApiModelProperty("首单订单详情", required = false)
    var tradeInfo: ExecutedOrderResponse? = null,

    @ApiModelProperty("能否Claim月光宝盒", required = false)
    var canClaimMoonlightBox: Boolean = false,

    @ApiModelProperty("能否 Claim Bitlayer 积分", required = false)
    var canClaimBitlayerPoint: Boolean = false,

    @ApiModelProperty("能否已经 Claim Bitlayer 积分", required = false)
    var hasClaimBitlayerPoint: Boolean = false
)
