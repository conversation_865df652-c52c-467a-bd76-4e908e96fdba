# Cache maven dependencies as an intermediate docker image
# (This only happens when pom.xml changes or you clear your docker image cache)
FROM maven:3.8.1 as dependencies
COPY pom.xml /build/
WORKDIR /build/
RUN mvn --batch-mode dependency:go-offline dependency:resolve-plugins

# Build the app using Maven and the cached dependencies
# (This only happens when your source code changes or you clear your docker image cache)
# Should work offline, but https://issues.apache.org/jira/browse/MDEP-82
FROM maven:3.8.1 as build
COPY --from=dependencies /root/.m2 /root/.m2
COPY pom.xml /build/
COPY src /build/src
WORKDIR /build/
RUN mvn -P dockerfile --batch-mode --fail-fast package

# Run the application (using the JRE, not the JDK)
# This assumes that your dependencies are packaged in application.jar
FROM openjdk:17 as runtime
WORKDIR /app
COPY --from=build /build/target/jasper-1.0-SNAPSHOT.jar ./app.jar
# ENTRYPOINT ["java", "-jar", "-XX:StartFlightRecording=name=background,maxsize=100m,filename=recording.jfr", "-Djava.security.egd=file:/dev/./urandom", "./app.jar"]
ENTRYPOINT ["java", "-jar", "-Djava.security.egd=file:/dev/./urandom", "./app.jar"]