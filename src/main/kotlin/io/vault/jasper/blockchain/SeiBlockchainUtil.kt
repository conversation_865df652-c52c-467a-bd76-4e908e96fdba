package io.vault.jasper.blockchain

import io.vault.jasper.enums.ChainType
import io.vault.jasper.repository.BlockchainGasPriceRepository
import io.vault.jasper.repository.BlockchainRepository
import org.slf4j.LoggerFactory
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.core.env.Environment
import org.springframework.stereotype.Component

@Component
class SeiBlockchainUtil @Autowired constructor(
    environment: Environment,
    blockchainGasPriceRepository: BlockchainGasPriceRepository
) : IEvmBlockchainUtil(environment, blockchainGasPriceRepository) {

    private val logger = LoggerFactory.getLogger(this::class.java)

    override val chainType = ChainType.SEI
}