package io.vault.jasper.dto

import EVMAddress
import io.swagger.annotations.ApiModel
import io.swagger.annotations.ApiModelProperty
import io.vault.jasper.enums.ChainType

@ApiModel("邀请人权利金请求")
data class ReferralPremiumDTO(
    @ApiModelProperty("钱包地址", required = true)
    @field:EVMAddress
    val address: String?,
    @ApiModelProperty("链", required = true)
    val chain: ChainType,
    @ApiModelProperty("查询起始时间", required = false)
    val startTime: Long?,
    @ApiModelProperty("查询结束时间", required = false)
    val endTime: Long?,
    @ApiModelProperty("邀请码", required = false)
    val inviteCode: String?,
    @ApiModelProperty("页数", required = true)
    val page: Int = 1,
    @ApiModelProperty("每页显示记录数", required = true)
    val pageSize: Int = 20
)
