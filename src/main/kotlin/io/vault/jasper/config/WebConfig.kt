package io.vault.jasper.config

import io.vault.jasper.interceptor.AuthInterceptor
import io.vault.jasper.interceptor.IpBlackListInterceptor
import org.springframework.context.annotation.Configuration
import org.springframework.web.servlet.config.annotation.InterceptorRegistry
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer

@Configuration
open class WebConfig : WebMvcConfigurer {

    override fun addInterceptors(registry: InterceptorRegistry) {
        //registry.addInterceptor(IpBlackListInterceptor()).addPathPatterns("/**")
        registry.addInterceptor(AuthInterceptor()).addPathPatterns(
            "/orders**",
            "/discord/**",
        )
    }
}