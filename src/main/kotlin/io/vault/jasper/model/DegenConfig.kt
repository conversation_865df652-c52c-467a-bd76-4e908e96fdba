package io.vault.jasper.model

import io.vault.jasper.enums.ChainType
import io.vault.jasper.enums.Symbol
import org.springframework.data.annotation.CreatedDate
import org.springframework.data.annotation.Id
import org.springframework.data.annotation.LastModifiedDate
import org.springframework.data.mongodb.core.mapping.Document
import org.springframework.data.mongodb.core.mapping.Field
import java.time.LocalDateTime

@Document("degen_config")
data class DegenConfig(
    @Id
    val id: String? = null,
    val chain: ChainType = ChainType.ARBITRUM,
    @Field("bid_asset")
    var bidAsset: Symbol,
    val underlyingAssetAmount: List<String>,
    @Field("expiry_in_hour")
    var expiryInHour: List<String>?, // 显示在 Trade 页面
    @CreatedDate
    val created: LocalDateTime = LocalDateTime.now(),
    @LastModifiedDate
    val updated: LocalDateTime = LocalDateTime.now(),
    @Field("lp_vault_expiry_in_hour")
    var lpVaultExpiryInHour: List<String>?, // 显示在 LP Vault 选择页面
    @Field("strike_asset")
    var strikeAsset: Symbol = Symbol.USDT,
    @Field("premium_assets")
    var premiumAssets: List<Symbol> = listOf(Symbol.USDT)
)