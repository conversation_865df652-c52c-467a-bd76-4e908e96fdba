package io.vault.jasper.blockchain.evm.arbitrum

import io.vault.jasper.blockchain.EvmScanApi
import org.springframework.beans.factory.annotation.Value
import org.springframework.stereotype.Service

@Service
class BitlayerScanApi : EvmScanApi() {

    @Value("\${blockchain.bitlayer.scan_api.base_url}")
    override lateinit var baseUrl: String

    @Value("\${blockchain.bitlayer.scan_api.api_tokens}")
    override lateinit var apiTokens: List<String>
}