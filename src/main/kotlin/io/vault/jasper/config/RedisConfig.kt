package io.vault.jasper.config

import org.springframework.context.annotation.Bean
import org.springframework.context.annotation.Configuration
import org.springframework.data.redis.connection.RedisConnectionFactory
import org.springframework.data.redis.core.RedisTemplate
import org.springframework.data.redis.serializer.GenericJackson2JsonRedisSerializer
import org.springframework.data.redis.serializer.StringRedisSerializer


@Configuration
open class RedisConfig {

    @Bean
    open fun redisTemplate(redisConnectionFactory: RedisConnectionFactory): RedisTemplate<Any, Any> {

        val redisTemplate: RedisTemplate<Any, Any> = RedisTemplate()
        redisTemplate.setConnectionFactory(redisConnectionFactory)
        val keyStringRedisSerializer = StringRedisSerializer()
        redisTemplate.keySerializer = keyStringRedisSerializer
        redisTemplate.hashKeySerializer = keyStringRedisSerializer
        //redisTemplate.valueSerializer = GenericJackson2JsonRedisSerializer()

        /**必须执行这个函数,初始化RedisTemplate*/
        redisTemplate.afterPropertiesSet();
        return redisTemplate
    }

    @Bean
    open fun redisObjectTemplate(redisConnectionFactory: RedisConnectionFactory): RedisTemplate<String, Any> {

        val redisTemplate: RedisTemplate<String, Any> = RedisTemplate()
        redisTemplate.setConnectionFactory(redisConnectionFactory)
        val keyStringRedisSerializer = StringRedisSerializer()

//        val objectMapper = ObjectMapper()
//        objectMapper.registerModule(JavaTimeModule())

        redisTemplate.keySerializer = keyStringRedisSerializer
        redisTemplate.valueSerializer = GenericJackson2JsonRedisSerializer()

        /**必须执行这个函数,初始化RedisTemplate*/
        redisTemplate.afterPropertiesSet();
        return redisTemplate
    }

}