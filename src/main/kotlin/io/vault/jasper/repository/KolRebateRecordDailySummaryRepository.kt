package io.vault.jasper.repository

import io.vault.jasper.model.KolRebateRecordDailySummary
import org.springframework.data.domain.Page
import org.springframework.data.domain.Pageable
import org.springframework.data.mongodb.repository.MongoRepository
import org.springframework.stereotype.Repository
import java.math.BigDecimal
import java.time.LocalDate

@Repository
interface KolRebateRecordDailySummaryRepository : MongoRepository<KolRebateRecordDailySummary, String> {
    fun findByKolId(kolId: String, page: Pageable): Page<KolRebateRecordDailySummary>
    fun findByKolIdAndRecordDateIsGreaterThanEqual(kolId: String, date: LocalDate, page: Pageable): Page<KolRebateRecordDailySummary>
    fun findByKolIdAndRecordDateIsGreaterThanEqual(kolId: String, date: LocalDate): List<KolRebateRecordDailySummary>
    fun findFirstByKolIdAndRecordDate(kolId: String, date: LocalDate): KolRebateRecordDailySummary?
    fun findByActiveUsersAndTotalPremiumsAndTotalRebate(
        activeUsers: Int,
        totalPremiums: BigDecimal,
        totalRebate: BigDecimal,
        page: Pageable
    ): Page<KolRebateRecordDailySummary>
}