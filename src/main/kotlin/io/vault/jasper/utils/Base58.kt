package io.vault.jasper.utils

import java.math.BigInteger


object Base58 {
    private val ALPHABET = "123456789ABCDEFGHJKLMNPQRSTUVWXYZabcdefghijkmnopqrstuvwxyz"
    private val BASE_58 = ALPHABET.length

    fun encode(input: ByteArray): String {
        val sb = StringBuilder()
        var num = BigInteger(1, input)
        while (num >= BigInteger.valueOf(BASE_58.toLong())) {
            val divmod = num.divideAndRemainder(BigInteger.valueOf(BASE_58.toLong()))
            sb.append(ALPHABET[divmod[1].toInt()])
            num = divmod[0]
        }
        sb.append(ALPHABET[num.toInt()])
        for (b in input) {
            if (b.toInt() == 0) {
                sb.append(ALPHABET[0])
            } else {
                break
            }
        }
        return sb.reverse().toString()
    }

    fun decode(input: String): ByteArray {
        val num = input.length
        var result = BigInteger.valueOf(0)
        var j = BigInteger.valueOf(1)
        for (i in num - 1 downTo 0) {
            val digit = ALPHABET.indexOf(input[i])
            if (digit == -1) {
                throw IllegalArgumentException("Invalid character for Base58")
            }
            result += j * BigInteger.valueOf(digit.toLong())
            j *= BigInteger.valueOf(BASE_58.toLong())
        }
        var bytes = result.toByteArray()
        for (i in 0 until num) {
            if (input[i] != '1') {
                break
            }
            val temp = ByteArray(bytes.size + 1)
            System.arraycopy(bytes, 0, temp, 1, bytes.size)
            bytes = temp
        }
        return bytes
    }
}