package io.vault.jasper.repository

import io.vault.jasper.enums.ChainType
import io.vault.jasper.model.ReportPremiumParameter
import org.springframework.data.mongodb.repository.MongoRepository
import org.springframework.stereotype.Repository

@Repository
interface ReportPremiumParameterRepository : MongoRepository<ReportPremiumParameter, String> {

    fun findByChain(chain: ChainType): List<ReportPremiumParameter>
}