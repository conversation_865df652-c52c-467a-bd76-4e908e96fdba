package io.vault.jasper.model

import org.springframework.data.annotation.Id
import org.springframework.data.mongodb.core.mapping.Document
import org.springframework.data.mongodb.core.mapping.Field
import java.math.BigDecimal
import java.time.LocalDateTime

@Document("loss_is_win_campaign_config")
data class LossIsWinCampaignConfig(
    @Id
    val id: String? = null,

    @Field("start_time")
    val startTime: LocalDateTime,

    @Field("end_time")
    val endTime: LocalDateTime,

    // Per order reward pool
    @Field("total_btr_per_order_pool")
    val totalBtrPerOrderPool: BigDecimal = BigDecimal("20000"),

    @Field("distributed_btr_per_order")
    var distributedBtrPerOrder: BigDecimal = BigDecimal.ZERO,

    @Field("btr_per_order")
    val btrPerOrder: BigDecimal = BigDecimal("2"),

    // Loss-based reward pool
    @Field("total_btr_loss_pool")
    val totalBtrLossPool: BigDecimal = BigDecimal("130000"),

    @Field("enabled")
    var enabled: Boolean = true
) {
    fun isInCampaignPeriod(timestamp: LocalDateTime): Boolean {
        return timestamp in startTime..endTime
    }

    fun canDistributePerOrderBtr(): Boolean {
        return totalBtrPerOrderPool - distributedBtrPerOrder >= btrPerOrder
    }

    /**
     * 计算基于损失的奖励
     */
    fun calculateLossBasedReward(userLoss: BigDecimal, totalLoss: BigDecimal): BigDecimal {
        return if (totalLoss > BigDecimal.ZERO) {
            totalBtrLossPool.multiply(userLoss.divide(totalLoss, 8, BigDecimal.ROUND_DOWN))
                .setScale(2, BigDecimal.ROUND_DOWN)
        } else {
            BigDecimal.ZERO
        }
    }
}