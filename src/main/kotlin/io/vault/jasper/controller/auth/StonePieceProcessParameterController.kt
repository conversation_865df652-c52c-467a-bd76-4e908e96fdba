package io.vault.jasper.controller.auth

import io.swagger.v3.oas.annotations.Operation
import io.swagger.v3.oas.annotations.tags.Tag
import io.vault.jasper.model.StonePieceProcessParameter
import io.vault.jasper.controller.ApiResponse
import io.vault.jasper.service.StonePieceProcessParameterService
import org.slf4j.LoggerFactory
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.web.bind.annotation.*

/**
 * 石头碎片处理参数配置管理 Controller
 */
@RestController
@RequestMapping("/stone-piece-config")
@Tag(name = "Stone Piece Process Configuration", description = "石头碎片处理配置管理")
class StonePieceProcessParameterController @Autowired constructor(
    private val stonePieceProcessParameterService: StonePieceProcessParameterService
) {

    private val logger = LoggerFactory.getLogger(this::class.java)

    @Operation(summary = "获取默认配置")
    @CrossOrigin
    @GetMapping("/default")
    fun getDefaultConfig(): ApiResponse<StonePieceProcessParameter> {
        return try {
            val config = stonePieceProcessParameterService.getDefaultConfig()
            ApiResponse.success(config)
        } catch (e: Exception) {
            logger.error("Error getting default config", e)
            ApiResponse.error("Failed to get default config: ${e.message}")
        }
    }

    @Operation(summary = "根据ID获取配置")
    @CrossOrigin
    @GetMapping("/{id}")
    fun getConfigById(@PathVariable id: String): ApiResponse<StonePieceProcessParameter?> {
        return try {
            val config = stonePieceProcessParameterService.getConfigById(id)
            if (config != null) {
                ApiResponse.success(config)
            } else {
                ApiResponse.error("Config not found: $id")
            }
        } catch (e: Exception) {
            logger.error("Error getting config: $id", e)
            ApiResponse.error("Failed to get config: ${e.message}")
        }
    }

    @Operation(summary = "获取所有启用的配置")
    @CrossOrigin
    @GetMapping("/enabled")
    fun getEnabledConfigs(): ApiResponse<List<StonePieceProcessParameter>> {
        return try {
            val configs = stonePieceProcessParameterService.getEnabledConfigs()
            ApiResponse.success(configs)
        } catch (e: Exception) {
            logger.error("Error getting enabled configs", e)
            ApiResponse.error("Failed to get enabled configs: ${e.message}")
        }
    }

    @Operation(summary = "获取所有配置")
    @CrossOrigin
    @GetMapping("/all")
    fun getAllConfigs(): ApiResponse<List<StonePieceProcessParameter>> {
        return try {
            val configs = stonePieceProcessParameterService.getAllConfigs()
            ApiResponse.success(configs)
        } catch (e: Exception) {
            logger.error("Error getting all configs", e)
            ApiResponse.error("Failed to get all configs: ${e.message}")
        }
    }

    @Operation(summary = "创建或更新配置")
    @CrossOrigin
    @PostMapping
    fun saveConfig(@RequestBody config: StonePieceProcessParameter): ApiResponse<StonePieceProcessParameter> {
        return try {
            val savedConfig = stonePieceProcessParameterService.saveConfig(config)
            ApiResponse.success(savedConfig)
        } catch (e: Exception) {
            logger.error("Error saving config: ${config.configName}", e)
            ApiResponse.error("Failed to save config: ${e.message}")
        }
    }

    @Operation(summary = "删除配置")
    @CrossOrigin
    @DeleteMapping("/{id}")
    fun deleteConfig(@PathVariable id: String): ApiResponse<String> {
        return try {
            val success = stonePieceProcessParameterService.deleteConfig(id)
            if (success) {
                ApiResponse.success("Config deleted successfully: $id")
            } else {
                ApiResponse.error("Config not found: $id")
            }
        } catch (e: Exception) {
            logger.error("Error deleting config: $id", e)
            ApiResponse.error("Failed to delete config: ${e.message}")
        }
    }

    @Operation(summary = "初始化默认配置")
    @CrossOrigin
    @PostMapping("/initialize")
    fun initializeConfig(): ApiResponse<String> {
        return try {
            stonePieceProcessParameterService.initializeConfig()
            ApiResponse.success("Configuration initialized successfully")
        } catch (e: Exception) {
            logger.error("Error initializing config", e)
            ApiResponse.error("Failed to initialize config: ${e.message}")
        }
    }
}
