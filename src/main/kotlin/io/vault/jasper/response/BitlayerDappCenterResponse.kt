package io.vault.jasper.response

import io.swagger.annotations.ApiModel
import io.swagger.annotations.ApiModelProperty
import io.vault.jasper.enums.OptionDirection
import io.vault.jasper.enums.Symbol
import io.vault.jasper.model.OptionStatus
import org.web3j.abi.datatypes.Bool

@ApiModel
data class BitlayerDappCenterResponse(

    @ApiModelProperty("任务是否完成", required = true)
    val finished: Boolean,

    @ApiModelProperty("任务完成时间", required = true)
    val finished_at: Long,
)
