package io.vault.jasper.service.benefit

import io.vault.jasper.enums.OptionDirection
import io.vault.jasper.enums.Symbol
import io.vault.jasper.model.UserZeroPremiumBenefit
import io.vault.jasper.model.ZeroPremiumBenefit
import io.vault.jasper.repository.UserZeroPremiumBenefitRepository
import io.vault.jasper.repository.ZeroPremiumBenefitRepository
import org.slf4j.LoggerFactory
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.data.repository.findByIdOrNull
import org.springframework.stereotype.Service

@Service
class BenefitService @Autowired constructor(
    private val zeroPremiumBenefitRepository: ZeroPremiumBenefitRepository,
    private val userZeroPremiumBenefitRepository: UserZeroPremiumBenefitRepository
) {

    private val logger = LoggerFactory.getLogger(this::class.java)

    companion object {
        const val ACTIVITY_NAME_NFT = "NFT"
        const val ACTIVITY_NAME_COIN98 = "COIN98"
    }

    fun getActivityNameByNftId(id: String): String = "NFT_$id"

    fun getName(underlyingAsset: Symbol, optionDirection: OptionDirection, amount: String, times: Int, expiryInHour: Int = 8): String {
        return if (expiryInHour == 8) {
            "${underlyingAsset}_${optionDirection}_${amount}_$times"
        } else {
            "${underlyingAsset}_${optionDirection}_${amount}_${times}_$expiryInHour"
        }
    }

    fun getList(underlyingAsset: Symbol, optionDirection: OptionDirection, amount: String, expiryInHour: String = "8"): List<ZeroPremiumBenefit> {
        return zeroPremiumBenefitRepository.findByUnderlyingAssetAndAmountAndOptionDirectionAndExpiryInHour(
            underlyingAsset,
            amount,
            optionDirection,
            expiryInHour
        )
    }

    fun getAvailableUserBenefits(
        userId: String,
        underlyingAsset: Symbol,
        amount: String,
        expiryInHour: String,
        optionDirection: OptionDirection
    ): List<UserZeroPremiumBenefit> {
        var benefits = zeroPremiumBenefitRepository.findByUnderlyingAssetAndAmountAndOptionDirectionAndExpiryInHour(
            underlyingAsset,
            amount,
            optionDirection,
            expiryInHour
        )

        var userBenefits: List<UserZeroPremiumBenefit> = emptyList()
        if (benefits.isNotEmpty()) {
            userBenefits = userZeroPremiumBenefitRepository.findByUserIdAndBenefitIdInAndUsed(
                userId,
                benefits.map { it.id!! },
                false
            )
        }
        if (userBenefits.isEmpty()) {
            // 如果没有找到对应的权益，则查找通用权益（可PUT或CALL）
            benefits = zeroPremiumBenefitRepository.findByUnderlyingAssetAndAmountAndOptionDirectionAndExpiryInHour(
                underlyingAsset,
                amount,
                null,
                expiryInHour
            )
            userBenefits = userZeroPremiumBenefitRepository.findByUserIdAndBenefitIdInAndUsed(
                userId,
                benefits.map { it.id!! },
                false
            )
        }

        return userBenefits
    }

    fun updateUserBenefit(userBenefit: UserZeroPremiumBenefit): UserZeroPremiumBenefit =
        userZeroPremiumBenefitRepository.save(userBenefit)
}