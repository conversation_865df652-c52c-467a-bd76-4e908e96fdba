package io.vault.jasper.model

import io.vault.jasper.enums.ChainType
import io.vault.jasper.enums.OptionDirection
import io.vault.jasper.enums.Symbol
import org.springframework.data.annotation.CreatedDate
import org.springframework.data.annotation.Id
import org.springframework.data.annotation.LastModifiedDate
import org.springframework.data.mongodb.core.index.Indexed
import org.springframework.data.mongodb.core.mapping.Document
import org.springframework.data.mongodb.core.mapping.Field
import java.math.BigDecimal
import java.math.BigInteger
import java.time.LocalDateTime

@Document("moonlight_rebate_record")
data class MoonlightRebateRecord(
    @Id
    val id: String? = null,
    @Field("option_order_id")
    @Indexed(background = true)
    val optionOrderId: String,
    @Field("buyer_address")
    @Indexed(background = true)
    val buyerAddress: String,
    var direction: OptionDirection,
    @Field("bid_amount")
    var bidAmount: BigDecimal = BigDecimal("0.01"),
    @Field("bid_asset")
    var bidAsset: Symbol = Symbol.BTC,
    @Field("strike_price")
    var strikePrice: BigDecimal = BigDecimal.ZERO,
    @Field("settlement_price")
    var settlementPrice: BigDecimal = BigDecimal.ZERO,
    @Field("premium_fee")
    var premiumFee: BigDecimal = BigDecimal.ZERO,
    @Field("premium_asset")
    var premiumAsset: Symbol = Symbol.USDT,
    @Field("premium_fee_in_btc")
    var premiumFeeInBtc: BigDecimal = BigDecimal.ZERO,
    @Field("premium_fee_in_usdt")
    var premiumFeeInUsdt: BigDecimal = BigDecimal.ZERO,
    var profit: BigDecimal = BigDecimal.ZERO,
    @Field("profit_asset")
    var profitAsset: Symbol = Symbol.USDT,
    @Field("profit_in_btc")
    var profitInBtc: BigDecimal = BigDecimal.ZERO,
    @Field("profit_in_usdt")
    var profitInUsdt: BigDecimal = BigDecimal.ZERO,
    @Field("net_profit")
    var netProfit: BigDecimal = BigDecimal.ZERO,
    @Field("rebate_amount")
    var rebateAmount: BigDecimal = BigDecimal.ZERO,
    @Field("jump_gala_three_link_time")
    var jumpGalaThreeLinkTime: Long = 0L,
    var status: MoonlightRebateRecordStatus = MoonlightRebateRecordStatus.EXECUTED,
    @Field("settle_tx_id")
    var settleTxId: String? = null,
    @CreatedDate
    val created: LocalDateTime = LocalDateTime.now(),
    @LastModifiedDate
    val updated: LocalDateTime = LocalDateTime.now(),
    var errorMsg: String? = null,
    @Field("expiry_in_hour")
    var expiryInHour: String = "0.5",
    @Field("sbt_nft_id")
    var sbtNFTId: BigInteger = BigInteger.ONE,
    @Field("stone_piece_count")
    var stonePieceCount: Int = 1, // 宝石碎片个数
    @Field("gem_related_option_order_ids")
    var stoneRelatedOptionOrderIds: MutableList<String>? = null, // 宝石关联的期权订单ID
    var chain : ChainType = ChainType.BITLAYER
) {}

enum class MoonlightRebateRecordStatus {
     EXECUTED, CREATED, CLAIMED, PENDING, SETTLED, SETTLE_FAILED
}