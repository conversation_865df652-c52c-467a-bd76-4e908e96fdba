package io.vault.jasper.controller.pub

import EVMAddress
import io.swagger.annotations.ApiOperation
import io.vault.jasper.ApiResponse
import io.vault.jasper.BitlayerResponse
import io.vault.jasper.annotation.ApiCache
import io.vault.jasper.dto.AuthDTO
import io.vault.jasper.enums.ChainType
import io.vault.jasper.enums.Symbol
import io.vault.jasper.exception.BusinessException
import io.vault.jasper.exception.ResultEnum
import io.vault.jasper.model.*
import io.vault.jasper.repository.*
import io.vault.jasper.response.*
import io.vault.jasper.service.*
import io.vault.jasper.service.blockchain.BitlayerService
import io.vault.jasper.service.blockchain.BlockchainServiceFactory
import io.vault.jasper.utils.AuthUtil
import io.vault.jasper.utils.DateTimeUtil
import org.slf4j.LoggerFactory
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.data.domain.PageImpl
import org.springframework.data.domain.PageRequest
import org.springframework.data.domain.Sort
import org.springframework.data.mongodb.core.MongoTemplate
import org.springframework.data.mongodb.core.query.Criteria
import org.springframework.data.mongodb.core.query.Query
import org.springframework.data.repository.findByIdOrNull
import org.springframework.web.bind.annotation.*
import java.math.BigDecimal
import java.math.BigInteger
import java.time.LocalDateTime
import java.util.*
import javax.servlet.http.HttpServletRequest
import javax.validation.Valid

@RestController
@RequestMapping("/bitlayer")
class BitlayerController @Autowired constructor(
    private val authUtil: AuthUtil,
    private val airDropService: AirDropService,
    private val userService: UserService,
    private val mongoTemplate: MongoTemplate,
    private val orderRepository: OrderRepository,
    private val bitlayerService: BitlayerService,
    private val optionOrderRepository: OptionOrderRepository,
    private val userRepository: UserRepository,
    private val bitlayerTradeRebateRecordRepository: BitlayerTradeRebateRecordRepository,
    private val bitlayerCampaignService: BitlayerCampaignService,
    private val moonlightCampaignService: MoonlightCampaignService,
    private val moonlightRebateRecordRepository: MoonlightRebateRecordRepository,
    private val userCampaignInfoRepository: UserCampaignInfoRepository,
    private val galaRebateRecordRepository: GalaRebateRecordRepository,
    private val gala3AddressRepository: Gala3AddressRepository,
    private val btrCampaignRecordRepository: BtrCampaignRecordRepository,
    private val stoneService: StoneService
) {

    private var logger = LoggerFactory.getLogger(this::class.java)

    val sbtNftIds = mutableListOf<BigInteger>(
        BigInteger("2"),
        BigInteger("3"),
        BigInteger("4"),
        BigInteger("5"),
        BigInteger("6"),
        BigInteger("7"),
        BigInteger("8"),
        BigInteger("9"),
        BigInteger("10"),
        BigInteger("11"),
        BigInteger("12"),
        BigInteger("13"),
        BigInteger("32"),
        BigInteger("33"),
        BigInteger("34"),
        BigInteger("35"),
        BigInteger("36"),
        BigInteger("37"),
        BigInteger("38"),
        BigInteger("39"),
        BigInteger("40"),
        BigInteger("41"),
        BigInteger("42"),
        BigInteger("43"),
    )

    @ApiOperation("Request Bitlayer Campaign Info")
    @CrossOrigin
    @ApiCache(key = "bitlayer:info", dynamicKey = ["address"], expire = 10)
    @GetMapping("/info")
    fun info(
        request: HttpServletRequest,
        @RequestParam("address") @EVMAddress address: String,
    ): ApiResponse<BitlayerCampaignInfoResponse> {

        authUtil.filterIPBlacklist(request)
        val user = authUtil.auth(request, address)
        val parameter = bitlayerCampaignService.getCampaignParameter()

        val totalCount = bitlayerCampaignService.getClaimRebateTotalCount(

        )

        if(user.claimBitlayerTimestamp == 0L){
            user.claimBitlayerTimestamp = bitlayerCampaignService.checkBitlayerCoupon(user.address)
            userRepository.save(user)
        }

        var claimTime: Long? = null
        var firstTradeProfitRate = "0"
        var firstTradePremium = "0"
        var firstTradePremiumAsset = Symbol.BTC
        var firstTradeAmount = "0"

        //查询第一单信息
        val firstTrade = optionOrderRepository.findFirstByChainAndBuyerIgnoreCaseAndStatusIn(
            ChainType.BITLAYER,
            user.address,
            listOf(OptionStatus.EXECUTED, OptionStatus.SETTLED)
        )

        var tradeInfo: ExecutedOrderResponse? = null
        if(firstTrade != null && bitlayerCampaignService.isFirstOrderInCampaign(firstTrade)) {
            
            firstTradePremium = firstTrade.premiumFeePay?.stripTrailingZeros()?.toPlainString() ?: "0"
            firstTradePremiumAsset = if (firstTrade.premiumAsset?.asset != null) Symbol.valueOf(firstTrade.premiumAsset!!.asset) else Symbol.BTC


                var lockDateTimeStamp = DateTimeUtil.convertLocalDateTimeToTimestamp(firstTrade.expiryDate)
                if(firstTrade.lockDate != null){
                    lockDateTimeStamp = DateTimeUtil.convertLocalDateTimeToTimestamp(firstTrade.lockDate)
                }

                val stonePieces = stoneService.getNftIdFromOptionOrder(firstTrade)
                tradeInfo = ExecutedOrderResponse(
                    id = firstTrade.id!!,
                    orderId = firstTrade.orderId,
                    orderType = firstTrade.orderType,
                    chain = firstTrade.chain,
                    onChainOrderId = firstTrade.onChainOrderId,
                    buyerAddress = firstTrade.buyer,
                    buyerVault = firstTrade.buyerVault,
                    buyerVaultSalt = firstTrade.buyerVaultSalt,
                    sellerAddress = firstTrade.seller,
                    businessType = firstTrade.direction!!,

                    bidAsset = firstTrade.bidAsset,
                    bidAmount = firstTrade.bidAmount?.stripTrailingZeros()?.toPlainString(),

                    underlyingAsset = firstTrade.underlyingAsset,
                    underlyingAssetAddress = firstTrade.underlyingAssetAddress,
                    underlyingAssetAmount = firstTrade.amount.stripTrailingZeros().toPlainString(),

                    strikeAsset = firstTrade.strikeAsset,
                    strikeAmount = firstTrade.strikeAmount.stripTrailingZeros().toPlainString(),
                    strikePrice = firstTrade.strikePrice!!.stripTrailingZeros().toPlainString(),
                    actualStrikeAmount = firstTrade.actualStrikeAmount?.stripTrailingZeros()?.toPlainString(),

                    premiumFeesAsset = if (firstTrade.premiumAsset?.asset != null) Symbol.valueOf(firstTrade.premiumAsset!!.asset) else Symbol.USDT,
                    premiumFee = firstTrade.premiumFeePay?.stripTrailingZeros()?.toPlainString(),
                    premiumFeeInUsdt = firstTrade.premiumFeePayInUsdt?.stripTrailingZeros()?.toPlainString(),
                    premiumFeeShouldPay = firstTrade.premiumFeeShouldPay?.stripTrailingZeros()?.toPlainString(),
                    premiumFeeShouldPayInUsdt = firstTrade.premiumFeeShouldPayInUsdt?.stripTrailingZeros()?.toPlainString(),

                    createDate = DateTimeUtil.convertLocalDateTimeToTimestamp(firstTrade.created),
                    expiryDate = DateTimeUtil.convertLocalDateTimeToTimestamp(firstTrade.expiryDate),
                    lockDate = lockDateTimeStamp,
                    status = firstTrade.status,
                    degen = firstTrade.jvault,
                    marketPriceAtSettlement = firstTrade.marketPriceAtSettlement,
                    settlementHash = firstTrade.settlementHash,
                    buyerProfit = firstTrade.buyerProfit?.stripTrailingZeros()?.toPlainString(),
                    expiryInHour = firstTrade.expiryInHour,
                    usedMoonlightBox = firstTrade.usedMoonlightBox,
                    usedRealityStone = firstTrade.usedRealityStone ?: false,
                    usedPowerStone = firstTrade.usedPowerStone ?: false,
                    usedSpaceStone = firstTrade.usedSpaceStone ?: false,
                    usedTimeStone = firstTrade.usedTimeStone ?: false,
                    stoneActivityNftId = firstTrade.stoneActivityNftId,
                    txHash = firstTrade.txHash,
                    premiumFeeLog = firstTrade.premiumFeeLog,
                    awardStonePieceNftIds = stonePieces
                )
        }

        var rebateRecord: BitlayerTradeRebateRecord? = null
        val rebateRecords = bitlayerTradeRebateRecordRepository.findByBuyerAddressIgnoreCaseAndStatus(
            user.address,
            BitlayerTradeRebateRecordStatus.CREATED
        )

        var canClaimFirstTrade = false
        if(rebateRecords.isNotEmpty()){
            rebateRecord = rebateRecords[0]

            if(rebateRecord.status == BitlayerTradeRebateRecordStatus.CREATED &&
                rebateRecord.discordJoinTime != null){
                canClaimFirstTrade = true
            }
            
            if(rebateRecord.discordJoinTime != null) {
                val claimDate = rebateRecord.discordJoinTime!!.plusSeconds(parameter.rebateClaimTime)
                claimTime = DateTimeUtil.convertLocalDateTimeToTimestamp(claimDate) / 1000
            }

            var roi = BigDecimal.ZERO
            if(rebateRecord.premiumFee.compareTo(BigDecimal.ZERO) != 0){
                roi = rebateRecord.profit.divide(rebateRecord.premiumFee, 8, BigDecimal.ROUND_HALF_UP)
            }

            firstTradeProfitRate = roi.stripTrailingZeros().toPlainString()

            firstTradeAmount = rebateRecord.rebateAmount.stripTrailingZeros().toPlainString()

        } else if(tradeInfo != null){

            // 看看是否漏掉 rebate record
            val userRebateRecords = bitlayerTradeRebateRecordRepository.findByBuyerAddressIgnoreCase(
                user.address
            )

            if(userRebateRecords.isEmpty()) {
                logger.info("rebate record not found for user ${user.address}, option order id ${firstTrade!!.id}")
                bitlayerCampaignService.createBitlayerTradeRebateRecord(
                    firstTrade,
                    user.claimBitlayerTimestamp
                )
            }
        }

        val claimRebateRecords = bitlayerTradeRebateRecordRepository.findByBuyerAddressIgnoreCaseAndStatusIn(
            user.address,
            listOf(BitlayerTradeRebateRecordStatus.SETTLED, BitlayerTradeRebateRecordStatus.CLAIMED)
        )

        var claimedRebate = false
        if(claimRebateRecords.isNotEmpty()){
            claimedRebate = true
            val claimedRebateRecord = claimRebateRecords[0]

            if(claimedRebateRecord.discordJoinTime != null) {
                val claimDate = claimedRebateRecord.discordJoinTime!!.plusSeconds(parameter.rebateClaimTime)
                claimTime = DateTimeUtil.convertLocalDateTimeToTimestamp(claimDate) / 1000
            }
        }

        //查询bitlayer 订单信息
        var moonlightTradeInfo: ExecutedOrderResponse? = null
        val moonlightParameter = moonlightCampaignService.getCampaignParameter()
        //查询bitlayer 订单信息
        var moonlightFirstTrade = optionOrderRepository.findFirstByChainAndBuyerIgnoreCaseAndStatusInAndCreatedGreaterThanAndUsedMoonlightBoxIsNullOrderByCreatedDesc(
            ChainType.BITLAYER,
            user.address,
            listOf(OptionStatus.EXECUTED, OptionStatus.SETTLED),
            moonlightParameter.startDate
        )

        //看看该订单是否已经领取过月光宝盒
        if(moonlightFirstTrade != null){

            val moonlightRebateRecord = moonlightRebateRecordRepository.findByOptionOrderId(
                moonlightFirstTrade.id!!
            )

            if(moonlightRebateRecord != null){
                if(moonlightRebateRecord.status == MoonlightRebateRecordStatus.CLAIMED){
                    moonlightFirstTrade = null
                }

                if(moonlightRebateRecord.status == MoonlightRebateRecordStatus.SETTLED && moonlightRebateRecord.settleTxId != null){
                    moonlightFirstTrade = null
                }
            }
        }

        if(moonlightFirstTrade != null) {

            firstTradePremium = moonlightFirstTrade.premiumFeePay?.stripTrailingZeros()?.toPlainString() ?: "0"
            firstTradePremiumAsset = if (moonlightFirstTrade.premiumAsset?.asset != null) Symbol.valueOf(moonlightFirstTrade.premiumAsset!!.asset) else Symbol.BTC

            //val order = orderRepository.findByIdOrNull(moonlightFirstTrade.orderId)
            var lockDateTimeStamp = DateTimeUtil.convertLocalDateTimeToTimestamp(moonlightFirstTrade.expiryDate)
            if(moonlightFirstTrade.lockDate != null){
                lockDateTimeStamp = DateTimeUtil.convertLocalDateTimeToTimestamp(moonlightFirstTrade.lockDate!!)
            }
            val stonePieces = stoneService.getNftIdFromOptionOrder(moonlightFirstTrade)
            //if(order != null) {
                moonlightTradeInfo = ExecutedOrderResponse(
                    id = moonlightFirstTrade.id!!,
                    orderId = moonlightFirstTrade.orderId,
                    orderType = moonlightFirstTrade.orderType,
                    chain = moonlightFirstTrade.chain,
                    onChainOrderId = moonlightFirstTrade.onChainOrderId,
                    buyerAddress = moonlightFirstTrade.buyer,
                    buyerVault = moonlightFirstTrade.buyerVault,
                    buyerVaultSalt = moonlightFirstTrade.buyerVaultSalt,
                    sellerAddress = moonlightFirstTrade.seller,
                    businessType = moonlightFirstTrade.direction!!,

                    bidAsset = moonlightFirstTrade.bidAsset,
                    bidAmount = moonlightFirstTrade.bidAmount?.stripTrailingZeros()?.toPlainString(),

                    underlyingAsset = moonlightFirstTrade.underlyingAsset,
                    underlyingAssetAddress = moonlightFirstTrade.underlyingAssetAddress,
                    underlyingAssetAmount = moonlightFirstTrade.amount.stripTrailingZeros().toPlainString(),

                    strikeAsset = moonlightFirstTrade.strikeAsset,
                    strikeAmount = moonlightFirstTrade.strikeAmount.stripTrailingZeros().toPlainString(),
                    strikePrice = moonlightFirstTrade.strikePrice!!.stripTrailingZeros().toPlainString(),
                    actualStrikeAmount = moonlightFirstTrade.actualStrikeAmount?.stripTrailingZeros()?.toPlainString(),

                    premiumFeesAsset = if (moonlightFirstTrade.premiumAsset?.asset != null) Symbol.valueOf(moonlightFirstTrade.premiumAsset!!.asset) else Symbol.USDT,
                    premiumFee = moonlightFirstTrade.premiumFeePay?.stripTrailingZeros()?.toPlainString(),
                    premiumFeeInUsdt = moonlightFirstTrade.premiumFeePayInUsdt?.stripTrailingZeros()?.toPlainString(),
                    premiumFeeShouldPay = moonlightFirstTrade.premiumFeeShouldPay?.stripTrailingZeros()?.toPlainString(),
                    premiumFeeShouldPayInUsdt = moonlightFirstTrade.premiumFeeShouldPayInUsdt?.stripTrailingZeros()?.toPlainString(),

                    createDate = DateTimeUtil.convertLocalDateTimeToTimestamp(moonlightFirstTrade.created),
                    expiryDate = DateTimeUtil.convertLocalDateTimeToTimestamp(moonlightFirstTrade.expiryDate),
                    lockDate = lockDateTimeStamp,
                    status = moonlightFirstTrade.status,
                    degen = moonlightFirstTrade.jvault,
                    marketPriceAtSettlement = moonlightFirstTrade.marketPriceAtSettlement,
                    settlementHash = moonlightFirstTrade.settlementHash,
                    buyerProfit = moonlightFirstTrade.buyerProfit?.stripTrailingZeros()?.toPlainString(),
                    expiryInHour = moonlightFirstTrade.expiryInHour,
                    usedMoonlightBox = moonlightFirstTrade.usedMoonlightBox,
                    usedRealityStone = moonlightFirstTrade.usedRealityStone ?: false,
                    usedPowerStone = moonlightFirstTrade.usedPowerStone ?: false,
                    usedSpaceStone = moonlightFirstTrade.usedSpaceStone ?: false,
                    usedTimeStone = moonlightFirstTrade.usedTimeStone ?: false,
                    stoneActivityNftId = moonlightFirstTrade.stoneActivityNftId,
                    txHash = moonlightFirstTrade.txHash,
                    premiumFeeLog = moonlightFirstTrade.premiumFeeLog,
                    awardStonePieceNftIds = stonePieces
                )
            //}
        }

        val claimCount = moonlightCampaignService.getUserClaimMoonlightBoxRebateTotalCount(user)
        val userCampaignInfo = airDropService.getUserCampaignInfo(user)
        var canClaimMoonlightBox = false
        val moonlightRebateRecords = moonlightRebateRecordRepository.findByBuyerAddressIgnoreCaseAndStatus(
            user.address,
            MoonlightRebateRecordStatus.CREATED
        )

        if(moonlightRebateRecords.isNotEmpty()){
            canClaimMoonlightBox = true
        }

        //val gala3StartDates: List<Long> = moonlightCampaignService.getCampaignParameter().startDateList.map { DateTimeUtil.convertLocalDateTimeToTimestamp(it) / 1000 }
        //val gala3EndDates: List<Long> = moonlightCampaignService.getCampaignParameter().endDateList.map { DateTimeUtil.convertLocalDateTimeToTimestamp(it) / 1000 }

        val gala3StartDates = moonlightCampaignService.getCampaignParameter().startDateList
        val gala3EndDates = moonlightCampaignService.getCampaignParameter().endDateList

        // 创建月光宝盒领取记录
        var existingGala3RebateRecord: GalaRebateRecord? = null
        val existingGala3RebateRecords = galaRebateRecordRepository.findByBuyerAddressIgnoreCase(user.address)
        if(existingGala3RebateRecords.isEmpty()){
            val gala3RebateRecord = GalaRebateRecord(
                buyerAddress = user.address,
                status = GalaRebateRecordStatus.CREATED,
                startDate = gala3StartDates[0],
                endDate = gala3EndDates[0],
                nftId = "1"
            )
            existingGala3RebateRecord = galaRebateRecordRepository.save(gala3RebateRecord)
        } else {
            existingGala3RebateRecord = existingGala3RebateRecords[0]
        }

        if(existingGala3RebateRecord!!.status == GalaRebateRecordStatus.CREATED){
            canClaimMoonlightBox = true
        }

        val tradeInMoonlightMode = optionOrderRepository.findFirstByChainAndBuyerIgnoreCaseAndStatusInAndCreatedGreaterThanAndUsedMoonlightBoxIsNotNullOrderByCreatedDesc(
            ChainType.BITLAYER,
            user.address,
            listOf(OptionStatus.EXECUTED, OptionStatus.SETTLED),
            moonlightParameter.startDate
        )

        var hasUsedMoonlightBox = false
        if(tradeInMoonlightMode != null){
            // 是否还拥有月光宝盒
            val hasMoonlightBox = bitlayerService.isUserHasMoonlightBox(
                moonlightParameter.tradeRebateContractAddress,
                user.address,
                BigInteger.ONE
            )

            if(!hasMoonlightBox) {
                hasUsedMoonlightBox = true
            }
        }

        return ApiResponse.success(
            BitlayerCampaignInfoResponse(
                address = address,
                claimRebateTotalCount = totalCount,
                hasClaimBitlayer = user.claimBitlayerTimestamp > 0,
                tradeInfo = tradeInfo,
                claimTime = claimTime,
                isJoinDiscord = user.discordInfo?.inGuild ?: false,
                firstTradeAmount = firstTradeAmount,
                firstTradeGrossProfitRate = firstTradeProfitRate,
                firstTradePremium = firstTradePremium,
                firstTradePremiumAsset = firstTradePremiumAsset,
                canClaimFirstTrade = canClaimFirstTrade,
                hasClaimRebate = claimedRebate,
                moonlightTradeInfo = moonlightTradeInfo,
                claimMoonlightTotalCount = claimCount,
                jumpToGalaThreeTime = userCampaignInfo.jumpGalaThreeLinkTime,
                canClaimMoonlightBox = canClaimMoonlightBox,
                hasUsedMoonlightBox = hasUsedMoonlightBox
            )
        )
    }

    @PostMapping("/claim_rebate")
    @ApiOperation("Claim First Trade Rebate")
    @CrossOrigin
    fun claimRebate(
        request: HttpServletRequest,
        @Valid @RequestBody dto: AuthDTO
    ): ApiResponse<Boolean> {

        authUtil.filterIPBlacklist(request)
        val user = authUtil.auth(request, dto.address)

        val now = LocalDateTime.now()
        val parameter = bitlayerCampaignService.getCampaignParameter()
        val unclaimedRecords = bitlayerCampaignService.getUnclaimedRebateRecord(user)
        for(record in unclaimedRecords) {

            record.status = BitlayerTradeRebateRecordStatus.CLAIMED
            bitlayerTradeRebateRecordRepository.save(record)
        }

        return ApiResponse.success(true)
    }

    @PostMapping("/claim_moonlight_rebate")
    @ApiOperation("Claim First Trade Rebate")
    @CrossOrigin
    fun claimMoonlightRebate(
        request: HttpServletRequest,
        @Valid @RequestBody dto: AuthDTO
    ): ApiResponse<Boolean> {

        authUtil.filterIPBlacklist(request)
        val user = authUtil.auth(request, dto.address)
        val now = LocalDateTime.now()

        val userCampaignInfo = airDropService.getUserCampaignInfo(user)
        if(userCampaignInfo.jumpGalaThreeLinkTime == 0L){
            throw BusinessException(ResultEnum.VISIT_GALA_THREE_FIRST)
        }

        val unclaimedGalaRecords = moonlightCampaignService.getUnclaimedGalaRebateRecord(user)
        for(record in unclaimedGalaRecords) {

            if(now.isAfter(record.startDate) && now.isBefore(record.endDate)){

                record.status = GalaRebateRecordStatus.CLAIMED
                galaRebateRecordRepository.save(record)
            }
        }

        val unclaimedRecords = moonlightCampaignService.getUnclaimedMoonlightBoxRebateRecord(user)
        for(record in unclaimedRecords) {
            if(record.jumpGalaThreeLinkTime == 0L){
                record.jumpGalaThreeLinkTime = userCampaignInfo.jumpGalaThreeLinkTime
            }

            if(record.jumpGalaThreeLinkTime > 0L) {
                record.status = MoonlightRebateRecordStatus.CLAIMED
                moonlightRebateRecordRepository.save(record)
            }
        }

        return ApiResponse.success(true)
    }

    @ApiOperation("返回GALA 3 链接")
    @CrossOrigin
    @ApiCache(key = "bitlayer:jump_gala_three_link", dynamicKey = ["address"], expire = 10)
    @GetMapping("/jump_gala_three_link")
    fun jumpGalaThreeLink(
        request: HttpServletRequest,
        @RequestParam("address") @EVMAddress address: String
    ): ApiResponse<String> {

        authUtil.filterIPBlacklist(request)
        val user = authUtil.auth(request, address)

        val userCampaignInfo = airDropService.getUserCampaignInfo(user)
        if(userCampaignInfo.jumpGalaThreeLinkTime == 0L) {
            userCampaignInfo.jumpGalaThreeLinkTime = Date().time + 45 * 1000
            userCampaignInfoRepository.save(userCampaignInfo)

            val firstTradeRebateRecords = moonlightRebateRecordRepository.findByBuyerAddressIgnoreCase(
                user.address
            )

            if(firstTradeRebateRecords.isNotEmpty()){
                val rebateRecord = firstTradeRebateRecords[0]
                rebateRecord.jumpGalaThreeLinkTime = userCampaignInfo.jumpGalaThreeLinkTime
                moonlightRebateRecordRepository.save(rebateRecord)
            }
        }

        val parameter = moonlightCampaignService.getCampaignParameter()
        var retweetLink = parameter.galaThreeLink

        return ApiResponse.success(retweetLink)
    }

    @PostMapping("/copy_bitlayer_discord_address")
    @ApiOperation("Copy Bitlayer Discord Address")
    @CrossOrigin
    fun copyBitlayerDiscordAddress(
        request: HttpServletRequest,
        @Valid @RequestBody dto: AuthDTO
    ): ApiResponse<Long?> {

        authUtil.filterIPBlacklist(request)
        val user = authUtil.auth(request, dto.address)
        val parameter = bitlayerCampaignService.getCampaignParameter()

        var claimTime: Long? = null
        val unclaimedRecords = bitlayerCampaignService.getUnclaimedRebateRecord(user)
        if(unclaimedRecords.isNotEmpty()){
            val record = unclaimedRecords[0]
            record.hasCopyAddressToDiscord = true

            if(user.discordInfo!= null && user.discordInfo!!.inGuild && record.discordJoinTime == null){
                record.discordJoinTime = LocalDateTime.now()
            }

            bitlayerTradeRebateRecordRepository.save(record)

            val claimDate = record.discordJoinTime!!.plusSeconds(parameter.rebateClaimTime)
            claimTime = DateTimeUtil.convertLocalDateTimeToTimestamp(claimDate) / 1000
        }

        return ApiResponse.success(claimTime)
    }

    @ApiOperation("Dapp Center Verification")
    @CrossOrigin
    @ApiCache(key = "bitlayer:dapp_center_verification", dynamicKey = ["address"], expire = 60)
    @GetMapping("/dapp_center_verification")
    fun dappCenterVerification(
        request: HttpServletRequest,
        @RequestParam("address") @EVMAddress address: String,
    ): BitlayerResponse<BitlayerDappCenterResponse> {

        var user = userRepository.findByAddressIgnoreCase(address)
        var finished = false
        var finishedAt = 0L

        if(user != null && user.claimBitlayerTimestamp == 0L){
            user.claimBitlayerTimestamp = bitlayerCampaignService.checkBitlayerCoupon(user.address)
            user = userRepository.save(user)
        }

        val firstTrade = optionOrderRepository.findFirstByChainAndBuyerIgnoreCaseAndStatusIn(
            ChainType.BITLAYER,
            address,
            listOf(OptionStatus.EXECUTED, OptionStatus.SETTLED)
        )

        if(firstTrade != null && user != null && user.claimBitlayerTimestamp > 0L){
            finished = true
            finishedAt = DateTimeUtil.convertLocalDateTimeToTimestamp(firstTrade.created)
        }

        if(address.lowercase() == "0xEB467c87e6d5c1a69D364C86E447Db59305389c7".lowercase()){
            finished = true
            finishedAt = 1726194083
        }

        return BitlayerResponse.success(
            BitlayerDappCenterResponse(
                finished = finished,
                finished_at = finishedAt
            )
        )
    }

    @ApiOperation("Gala Three Verification")
    @CrossOrigin
    @ApiCache(key = "bitlayer:gala_three_verification", dynamicKey = ["address", "page", "limit"], expire = 10)
    @GetMapping("/gala_three_verification")
    fun galaThreeVerification(
        request: HttpServletRequest,
        @RequestParam(required = true) @EVMAddress address: String,
        @RequestParam(required = true) page: Int, // 页码
        @RequestParam(required = true) limit: Int, // 每页多少条记录
    ): BitlayerResponse<BitlayerGalaThreeResponse> {

        val parameter = moonlightCampaignService.getCampaignParameter()
        val startTime = LocalDateTime.of(2024, 11, 1, 4, 0, 0)
        logger.info("gala three verification startTime: $startTime")

        val verifiedAddress = gala3AddressRepository.findByAddressIgnoreCase(address)
        val hasVerified = (verifiedAddress != null)

        val p = page ?: 1
        val ps = limit ?: 50
        val pageable = PageRequest.of(p - 1, ps, Sort.by(Sort.Order.desc(OptionOrder::created.name)))

        val criteria = Criteria()
        criteria.and(OptionOrder::buyer.name).regex("(?i)$address")
        criteria.and(OptionOrder::chain.name).`is`(ChainType.BITLAYER)

        if(hasVerified) {

            val andCriteria = Criteria().andOperator(
                Criteria.where(OptionOrder::created.name).gt(startTime),
                Criteria.where(OptionOrder::liquidityType.name).ne(0)
            )

            val orCriteria = Criteria().orOperator(
                Criteria.where(OptionOrder::created.name).lt(startTime),
                andCriteria
            )

            criteria.andOperator(orCriteria)

        } else {

            val andCriteria = Criteria().andOperator(
                Criteria.where(OptionOrder::created.name).gt(parameter.startDate),
                Criteria.where(OptionOrder::liquidityType.name).ne(0)
            )

            criteria.andOperator(andCriteria)
        }

        val query = Query(criteria)
        val total = mongoTemplate.count(query, OptionOrder::class.java)
        query.with(pageable)
        val resultSet = mongoTemplate.find(query, OptionOrder::class.java)
        val optionsPage = PageImpl(resultSet, pageable, total)

        val optionsList = optionsPage.content.mapNotNull {

            BitlayerGalaThreeOrderResponse(
                uuid = it.id!!,
                tx_hash = it.txHash!!,
                finished_at = DateTimeUtil.convertLocalDateTimeToTimestamp(it.created) / 1000,
            )
        }

        return BitlayerResponse.success(
            BitlayerGalaThreeResponse(
                list = optionsList,
                total = total.toInt()
            )
        )
    }

    @ApiOperation("检测是否有领取Bitlayer资格")
    @CrossOrigin
    @ApiCache(key = "bitlayer:check_bitlayer", dynamicKey = ["address"], expire = 10)
    @GetMapping("/check_bitlayer")
    fun checkBitlayer(
        request: HttpServletRequest,
        @RequestParam("address") @EVMAddress address: String
    ): ApiResponse<String> {

        authUtil.filterIPBlacklist(request)
        val user = userRepository.findByAddressIgnoreCase(address)
        if(user != null && user.claimBitlayerTimestamp == 0L){
            user.claimBitlayerTimestamp = bitlayerCampaignService.checkBitlayerCoupon(user.address)
            userRepository.save(user)
        }

        return ApiResponse.success("Success")
    }

    @ApiOperation("检测是否可以领取月光宝盒")
    @CrossOrigin
    @ApiCache(key = "bitlayer:check_moonlight_box", dynamicKey = ["address"], expire = 10)
    @GetMapping("/check_claim_moonlight_box")
    fun checkClaimMoonlightBox(
        request: HttpServletRequest,
        @RequestParam("address") @EVMAddress address: String
    ): ApiResponse<Boolean> {

        authUtil.filterIPBlacklist(request)
        val user = userRepository.findByAddressIgnoreCase(address)

        if(user == null){
            return ApiResponse.success(false)
        }

        var canClaimMoonlightBox = false
        val moonlightRebateRecords = moonlightRebateRecordRepository.findByBuyerAddressIgnoreCaseAndStatus(
            user!!.address,
            MoonlightRebateRecordStatus.CREATED
        )

        if(moonlightRebateRecords.isNotEmpty()){
            canClaimMoonlightBox = true
        }

        val existingGala3RebateRecords = galaRebateRecordRepository.findByBuyerAddressIgnoreCase(user.address)
        if(existingGala3RebateRecords.isNotEmpty()){

            val existingGala3RebateRecord = existingGala3RebateRecords[0]
            if(existingGala3RebateRecord.status == GalaRebateRecordStatus.CREATED){
                canClaimMoonlightBox = true
            }
        }

        return ApiResponse.success(canClaimMoonlightBox)
    }

    @ApiOperation("用户可领取的宝石碎片情况")
    @CrossOrigin
    @ApiCache(key = "bitlayer:unclaimed_stone_piece_info", dynamicKey = ["address", "chain"], expire = 2)
    @GetMapping("/unclaimed_stone_piece_info")
    fun getUnclaimedStonePieceInfo(
        request: HttpServletRequest,
        @RequestParam("address") @EVMAddress address: String,
        @RequestParam("chain") chain: ChainType?,
    ): ApiResponse<Map<BigInteger, Int>> {

        authUtil.filterIPBlacklist(request)

        var requestChain = ChainType.BITLAYER
        if(chain != null){
            requestChain = chain
        }

        val records = moonlightRebateRecordRepository.findByChainAndBuyerAddressIgnoreCaseAndSbtNFTIdInAndStatusIn(
            requestChain,
            address,
            sbtNftIds,
            listOf(
                MoonlightRebateRecordStatus.CREATED,
                MoonlightRebateRecordStatus.EXECUTED
            )
        )

        val resultMap = mutableMapOf<BigInteger, Int>()

        for(record in records){
            val sbtNftId = record.sbtNFTId
            if(resultMap.containsKey(sbtNftId)){
                resultMap[sbtNftId] = resultMap[sbtNftId]!! + record.stonePieceCount
            } else {
                resultMap[sbtNftId] = record.stonePieceCount
            }
        }

        return ApiResponse.success(resultMap)
    }

    @ApiOperation("获取碎片合成配置")
    @CrossOrigin
    @GetMapping("/stone_piece_config")
    fun stonePieceConfig(
        request: HttpServletRequest,
        @RequestParam("nft_id") nftId: String,
    ): ApiResponse<Int> {

        authUtil.filterIPBlacklist(request)

        val nId = nftId.toInt()
        if(nId > 1 && nId < 14){
            return ApiResponse.success(3)
        }

        return ApiResponse.success(7)
    }

    @ApiOperation("批量领取宝石")
    @CrossOrigin
    @GetMapping("/batch_claim_stone")
    fun batchClaimStone(
        request: HttpServletRequest,
        @RequestParam("address") @EVMAddress address: String,
        @RequestParam("nft_id") nftId: String,
        @RequestParam("chain") chain: ChainType,
    ): ApiResponse<Boolean> {

        authUtil.filterIPBlacklist(request)

        val unclaimedRecords = moonlightRebateRecordRepository.findByChainAndBuyerAddressIgnoreCaseAndSbtNFTIdAndStatusIn(
            chain,
            address,
            BigInteger(nftId),
            listOf(
                MoonlightRebateRecordStatus.CREATED
            )
        )

        for(record in unclaimedRecords){
            record.status = MoonlightRebateRecordStatus.CLAIMED
            moonlightRebateRecordRepository.save(record)
        }
        return ApiResponse.success(true)
    }

    @ApiOperation("获取可领取的宝石碎片记录")
    @CrossOrigin
    @ApiCache(key = "bitlayer:unclaimed_stone_piece_records", dynamicKey = ["address", "nftId", "chain"], expire = 1)
    @GetMapping("/unclaimed_stone_piece_records")
    fun getUnclaimedStonePieceRecords(
        request: HttpServletRequest,
        @RequestParam("address") @EVMAddress address: String,
        @RequestParam("nft_id") nftId: String,
        @RequestParam("chain") chain: ChainType?,
    ): ApiResponse<List<StonePiecesResponse>> {

        authUtil.filterIPBlacklist(request)

        var requestChain = ChainType.BITLAYER
        if(chain != null){
            requestChain = chain
        }

        val records = moonlightRebateRecordRepository.findByChainAndBuyerAddressIgnoreCaseAndSbtNFTIdAndStatusIn(
            requestChain,
            address,
            BigInteger(nftId),
            listOf(
                MoonlightRebateRecordStatus.CREATED,
                MoonlightRebateRecordStatus.EXECUTED
            )
        )

        val stonePieceRecords = mutableListOf<StonePiecesResponse>()
        for(record in records){
            val stonePieceRecord = StonePiecesResponse(
                recordId = record.id!!,
                nftId = record.sbtNFTId.toString(),
                status = record.status,
                optionOrderIds = record.stoneRelatedOptionOrderIds!!,
                created = DateTimeUtil.convertLocalDateTimeToTimestamp(record.created) / 1000
            )

            stonePieceRecords.add(stonePieceRecord)
        }

        return ApiResponse.success(stonePieceRecords)
    }

    @ApiOperation("领取宝石")
    @CrossOrigin
    @ApiCache(key = "bitlayer:claim_stone", dynamicKey = ["address", "recordId"], expire = 10)
    @GetMapping("/claim_stone")
    fun claimStone(
        request: HttpServletRequest,
        @RequestParam("address") @EVMAddress address: String,
        @RequestParam("record_id") recordId: String,
    ): ApiResponse<Boolean> {

        authUtil.filterIPBlacklist(request)

        val record = moonlightRebateRecordRepository.findByIdOrNull(recordId)
        if(record == null){
            throw BusinessException(ResultEnum.STONE_RECORD_NOT_FOUND)
        }

        if(address.lowercase() != record.buyerAddress.lowercase()){
            throw BusinessException(ResultEnum.STONE_RECORD_ADDRESS_NOT_MATCH)
        }

        if(record.status != MoonlightRebateRecordStatus.CREATED){
            throw BusinessException(ResultEnum.STONE_RECORD_STATUS_NOT_CREATED)
        }

        record.status = MoonlightRebateRecordStatus.CLAIMED
        moonlightRebateRecordRepository.save(record)

        return ApiResponse.success(true)
    }

    @ApiOperation("币安活动验证")
    @CrossOrigin
    @ApiCache(key = "bitlayer:binance_verification", dynamicKey = ["address", "taskId"], expire = 10)
    @GetMapping("/binance_verification")
    fun binanceVerification(
        request: HttpServletRequest,
        @RequestParam("address") @EVMAddress address: String,
        @RequestParam("taskId") taskId: String,
    ): BitlayerResponse<Boolean> {

        authUtil.filterIPBlacklist(request)

        logger.info("Request Binance Verification, address: $address, taskId: $taskId")
        val result = bitlayerCampaignService.checkAddressCampaign4Result(address)
        logger.info("Request Binance Verification, address: $address, taskId: $taskId result: $result")
        return BitlayerResponse.success(result)
    }

    @ApiOperation("Get BTR Campaign Verification")
    @CrossOrigin
    @GetMapping("/campaign/btr/verification")
    fun btrCampaignVerification(
        @RequestParam("address") @EVMAddress address: String
    ): BitlayerResponse<BitlayerDappCenterResponse> {
        // 获取用户的第一条记录
        val firstRecord = btrCampaignRecordRepository
            .findByAddressIgnoreCase(address)
            .minByOrNull { it.createdTime }

        return if (firstRecord == null) {
            BitlayerResponse.success(
                BitlayerDappCenterResponse(
                    finished = false,
                    finished_at = 0L
                )
            )
        } else {
            BitlayerResponse.success(
                BitlayerDappCenterResponse(
                    finished = true,
                    finished_at = DateTimeUtil.convertLocalDateTimeToTimestamp(firstRecord.createdTime) / 1000
                )
            )
        }
    }
}
