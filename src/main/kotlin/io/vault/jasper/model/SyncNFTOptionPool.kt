package io.vault.jasper.model

import org.springframework.data.annotation.CreatedDate
import org.springframework.data.annotation.Id
import org.springframework.data.annotation.LastModifiedDate
import org.springframework.data.mongodb.core.index.CompoundIndex
import org.springframework.data.mongodb.core.mapping.Document
import org.springframework.data.mongodb.core.mapping.Field
import java.time.LocalDateTime

@Document("sync_nft_option_pool")
data class SyncNFTOptionPool(
    @Id
    val id: String? = null,
    @Field("user_id")
    val userId: String,
    val address: String,
    @Field("benefit_id")
    val benefitId: String,
    @Field("discount_id")
    val discountId: Int,
    var count: Int = 0,
    @CreatedDate
    val created: LocalDateTime = LocalDateTime.now(),
    @LastModifiedDate
    val updated: LocalDateTime = LocalDateTime.now()
)
