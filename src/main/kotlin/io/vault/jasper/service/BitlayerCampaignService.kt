package io.vault.jasper.service

import io.vault.jasper.enums.ChainType
import io.vault.jasper.enums.OptionDirection
import io.vault.jasper.enums.Symbol
import io.vault.jasper.exception.BusinessException
import io.vault.jasper.exception.ResultEnum
import io.vault.jasper.model.*
import io.vault.jasper.repository.*
import io.vault.jasper.service.blockchain.BlockchainServiceFactory
import io.vault.jasper.service.blockchain.EvmService
import io.vault.jasper.utils.ProfileUtil
import org.slf4j.LoggerFactory
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.data.mongodb.core.MongoTemplate
import org.springframework.data.mongodb.core.aggregation.Aggregation
import org.springframework.data.mongodb.core.aggregation.ConvertOperators
import org.springframework.data.mongodb.core.query.Criteria
import org.springframework.data.repository.findByIdOrNull
import org.springframework.stereotype.Service
import org.web3j.abi.TypeReference
import org.web3j.abi.datatypes.Address
import org.web3j.abi.datatypes.Type
import org.web3j.abi.datatypes.Utf8String
import org.web3j.abi.datatypes.generated.Uint256
import org.web3j.crypto.Keys
import java.math.BigDecimal
import java.math.BigInteger
import java.time.LocalDateTime
import java.util.*
import kotlin.collections.ArrayList

@Service
class BitlayerCampaignService @Autowired constructor(
    private val bitlayerTradeRebateRecordRepository: BitlayerTradeRebateRecordRepository,
    private val bitlayerCampaignParameterRepository: BitlayerCampaignParameterRepository,
    private val optionOrderRepository: OptionOrderRepository,
    private val userRepository: UserRepository,
    private val orderRepository: OrderRepository,
    private val discordLevelRepository: DiscordLevelRepository,
    private val blockchainServiceFactory: BlockchainServiceFactory,
    private val bitlayerCampaign4ResultRepository: BitlayerCampaign4ResultRepository,
    private val optionOrderInfoRepository: OptionOrderInfoRepository
){

    private val logger = LoggerFactory.getLogger(this::class.java)

    /**
     * 获取空投参数对象
     */
    fun getCampaignParameter(): BitlayerCampaignParameter {
        val params = bitlayerCampaignParameterRepository.findAll()

        return if (params.isEmpty()) {
            val p = BitlayerCampaignParameter(null)
            bitlayerCampaignParameterRepository.save(p)
        } else {
            params.first()
        }
    }

    /**
     * 判断是否活动内首单
     */
    fun isFirstOrderInCampaign(
        optionOrder: OptionOrder
    ): Boolean{

        if(optionOrder.status != OptionStatus.SETTLED &&
            optionOrder.status != OptionStatus.EXECUTED &&
            optionOrder.status != OptionStatus.SETTLE_FAILED){
            return false
        }

        val parameter = getCampaignParameter()

        // 符合 2H，0.01 BTC 使用 BTC 支付期权费 规格的订单
        if(optionOrder.bidAsset == null || optionOrder.bidAsset != Symbol.BTC){
            logger.info("BitlayerCampaignService Service Not BTC ${optionOrder.buyer} ${optionOrder.id}")
            return false
        }

        if(optionOrder.expiryInHour == null || optionOrder.expiryInHour != "2"){
            logger.info("BitlayerCampaignService Service Not 2 hours ${optionOrder.buyer} ${optionOrder.id}")
            return false
        }

        if(optionOrder.bidAmount == null || optionOrder.bidAmount!!.compareTo(parameter.firstTradeDegenQuantity) != 0){
            logger.info("BitlayerCampaignService Service Not 0.2 ETH ${optionOrder.buyer} ${optionOrder.id}")
            return false
        }

        if(optionOrder.premiumAsset == null || optionOrder.premiumAsset!!.asset != Symbol.BTC.name){
            logger.info("BitlayerCampaignService Service Not BTC premium asset ${optionOrder.buyer} ${optionOrder.id}")
            return false
        }

        val firstOptionOrder = optionOrderRepository.findFirstByChainAndBuyer(
            ChainType.BITLAYER, optionOrder.buyer!!,
        )

        if(firstOptionOrder == null){
            return false
        }

        if(firstOptionOrder.id!! != optionOrder.id!!){
            return false
        }

        if(optionOrder.created.isBefore(parameter.startDate)){
            return false
        }

        return true
    }

    fun createBitlayerTradeRebateRecord(
        optionOrder: OptionOrder,
        getBitlayerCouponTime: Long
    ){

        val parameter = getCampaignParameter()
        val rebateCount = getFirstTradeRebateCount()
        if( rebateCount >= parameter.firstTradeRebateCount){
            logger.info("BitlayerCampaignService first trade rebate task reach max count")
            return
        }

        val now = LocalDateTime.now()
        if(now.isAfter(parameter.endDate)){
            logger.info("BitlayerCampaignService first trade rebate task $now reach end date ${parameter.endDate}")
            return
        }

        // 是否已经创建了订单
        val existingRecord = bitlayerTradeRebateRecordRepository.findByOptionOrderId(
            optionOrder.id!!
        )

        if(existingRecord != null){
            logger.info("BitlayerCampaignService Service Already add rebate record ${optionOrder.buyer} ${optionOrder.id}")
            return
        }

        // 是否有同样地址的订单
        val existingAddressRecords = bitlayerTradeRebateRecordRepository.findByBuyerAddressIgnoreCase(
            optionOrder.buyer!!
        )

        if(existingAddressRecords.isNotEmpty()){
            logger.info("BitlayerCampaignService Service Already same address rebate record ${optionOrder.buyer} ${optionOrder.id}")
            return
        }

        val record = BitlayerTradeRebateRecord(
            optionOrderId = optionOrder.id,
            buyerAddress = optionOrder.buyer!!,
            direction = optionOrder.direction!!,
            bidAmount = optionOrder.bidAmount!!,
            claimBitlayerCouponTime = getBitlayerCouponTime
        )

        bitlayerTradeRebateRecordRepository.save(record)
    }

    fun checkBitlayerTradeRebate(
        record: BitlayerTradeRebateRecord
    ){

        val optionOrder = optionOrderRepository.findByIdOrNull(record.optionOrderId)
        if(optionOrder == null){
            logger.info("BitlayerCampaignService Service can not find order record ${record.buyerAddress} ${record.optionOrderId}")
            return
        }

        if(optionOrder.status == OptionStatus.SETTLE_FAILED){
            logger.info("BitlayerCampaignService Service order settle failed ${optionOrder.buyer} ${optionOrder.id}")
            record.status = BitlayerTradeRebateRecordStatus.SETTLE_FAILED
            bitlayerTradeRebateRecordRepository.save(record)
            return
        }

        if(optionOrder.status != OptionStatus.SETTLED){
            logger.info("BitlayerCampaignService Service Not Settled order ${optionOrder.buyer} ${optionOrder.id}")
            return
        }

        //是否是新用户
        val user = userRepository.findByAddressIgnoreCase(optionOrder.buyer!!)
        if(user == null){
            logger.debug("BitlayerCampaignService Service not new user ${optionOrder.buyer} ${optionOrder.id}")
            return
        }

        val order = orderRepository.findByIdOrNull(optionOrder.orderId)
        if(order == null){
            logger.debug("BitlayerCampaignService Service can not find order record ${optionOrder.buyer} ${optionOrder.id}")
            return
        }

        record.strikePrice = order.strikePrice.movePointLeft(18)
        record.settlementPrice = BigDecimal(optionOrder.marketPriceAtSettlement!!).movePointLeft(18)

        record.premiumFee = optionOrder.premiumFeePay!!
        record.premiumAsset = Symbol.valueOf(optionOrder.premiumAsset!!.asset)
        record.premiumFeeInBtc = optionOrder.premiumFeePay!!
        record.premiumFeeInUsdt = optionOrder.premiumFeePayInUsdt!!

        record.profit = optionOrder.buyerProfit!!
        record.profitAsset = Symbol.BTC
        record.profitInUsdt = optionOrder.buyerProfit!!

        if(optionOrder.direction == OptionDirection.PUT){

            val profit = record.profitInUsdt.movePointLeft(6)
            val settlementPrice = record.settlementPrice
            logger.info("BitlayerCampaignService Service Profit in PUT order ${optionOrder.buyer} ${profit} USDT ${settlementPrice}")
            var profitInBtc = profit.divide(settlementPrice, 18, BigDecimal.ROUND_HALF_UP)
            profitInBtc = profitInBtc.movePointRight(18).setScale(0, BigDecimal.ROUND_HALF_UP)
            logger.info("BitlayerCampaignService Service Profit in ${optionOrder.buyer} ${record.settlementPrice} ${profitInBtc}")
            record.profit = profitInBtc
        }

        val discordLevel = getDiscordLevel(user)
        record.discordLevel = discordLevel

        record.netProfit = record.profit.subtract(record.premiumFee).setScale(0, BigDecimal.ROUND_HALF_UP)
        if(record.netProfit.compareTo(BigDecimal.ZERO) == -1){
            record.rebateAmount = record.netProfit.multiply(BigDecimal("-1"))
            record.status = BitlayerTradeRebateRecordStatus.CREATED
        } else {
            record.status = BitlayerTradeRebateRecordStatus.SETTLED
        }

        bitlayerTradeRebateRecordRepository.save(record)
    }

    fun getDiscordLevel(
        user: User
    ): Int {

        val discordId = user.discordInfo?.userId ?: return 0
        val level = discordLevelRepository.findByDiscordId(discordId)
        if(level == null){
            return 0
        }

        return level.level
    }

    fun getFirstTradeRebateCount(): Int {
        return bitlayerTradeRebateRecordRepository.findAll().size
    }

    fun getClaimRebateTotalCount(): Int {
        return bitlayerTradeRebateRecordRepository.findAll().size
    }

    fun getFirstBitlayerTradesCount(
        bidAmount: BigDecimal
    ): Int {
        return optionOrderRepository.findByChainAndStatusInAndBidAssetAndBidAmountEqualsAndExpiryInHour(
            ChainType.BITLAYER,
            listOf(OptionStatus.SETTLED, OptionStatus.EXECUTED, OptionStatus.SETTLE_FAILED),
            Symbol.BTC,
            bidAmount,
            "2"
        ).size
    }

    fun getUnclaimedRebateRecord(
        user: User
    ): List<BitlayerTradeRebateRecord> {
        return bitlayerTradeRebateRecordRepository.findByBuyerAddressIgnoreCaseAndStatus(
            user.address,
            BitlayerTradeRebateRecordStatus.CREATED
        )
    }

    fun checkBitlayerCoupon(address: String): Long{

        // 去智能合约查询
        var chainType = ChainType.BITLAYER_TEST
        var contract = "******************************************"
        if(ProfileUtil.activeProfile == "prod"){
            chainType = ChainType.BITLAYER
            contract = "******************************************"
        }

        logger.info("The bitlayer contract address is: $contract")

        val evmService = blockchainServiceFactory.getBlockchainService(chainType) as EvmService
        val evmUtil = evmService.evmUtil

        val inputs: MutableList<Type<*>> = ArrayList()
        inputs.add(Utf8String("28"))
        inputs.add(Address(address))
        val outputs: MutableList<TypeReference<*>> = ArrayList()
        outputs.add(object : TypeReference<Uint256>() {})
        val results = evmUtil.ethCall("getUserClaim", contract, inputs, outputs)
            ?: throw BusinessException(ResultEnum.INTERNAL_SERVER_ERROR)
        val resultAmount = results[0].value as BigInteger
        logger.info("checkBitlayer result: ${resultAmount}")

        return resultAmount.toLong()
    }

    fun checkBitlayer4CampaignResult(
        optionOrder: OptionOrder
    ): Boolean{

        if(optionOrder.chain != ChainType.BITLAYER){
            return false
        }

        val txHash = optionOrder.txHash
        val optionInfo = optionOrderInfoRepository.findByTxHash(txHash!!)

        if(optionInfo != null && optionInfo.channel == UserChannel.JASPER_VAULT){
            return true
        }

        return false
    }

    fun checkAddressCampaign4Result(
        address: String
    ): Boolean {

        val result = bitlayerCampaign4ResultRepository.findByAddressIgnoreCase(address)
        if(result != null && result.result){
            return true
        }

        val params = getCampaignParameter()

//        val firstOptionOrder = optionOrderRepository.findFirstByChainAndBuyerIgnoreCaseAndChannelAndWalletAndCreatedAfter(
//            ChainType.BITLAYER,
//            address,
//            UserChannel.JASPER_VAULT,
//            UserWallet.BINANCE,
//            params.campaign4StartDate
//        )

        val firstOptionOrder = optionOrderRepository.findFirstByChainAndBuyerIgnoreCaseAndChannelAndCreatedAfter(
            ChainType.BITLAYER,
            address,
            UserChannel.JASPER_VAULT,
            params.campaign4StartDate
        )

        if(firstOptionOrder != null){
            val nr = BitlayerCampaign4Result(
                address = address,
                result = true
            )
            bitlayerCampaign4ResultRepository.save(nr)

            return true
        }

        return false
    }
}