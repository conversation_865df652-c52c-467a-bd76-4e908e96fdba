package io.vault.jasper.repository

import io.vault.jasper.model.KYT
import io.vault.jasper.model.UserPointDailySummary
import io.vault.jasper.model.UserPointRecord
import io.vault.jasper.model.UserReferralDailySummary
import org.springframework.data.mongodb.repository.MongoRepository
import org.springframework.stereotype.Repository
import java.math.BigDecimal
import java.time.LocalDateTime

@Repository
interface UserReferralDailySummaryRepository : MongoRepository<UserReferralDailySummary, String> {

    fun findTopByAddressOrderByCreatedDesc(
        address: String
    ): UserReferralDailySummary?

    fun findByAddressAndDateString(
        address: String,
        dateString: String
    ): UserReferralDailySummary?
}