package io.vault.jasper.model

import io.vault.jasper.response.BaseAirdropInfoResponse
import io.vault.jasper.response.DLCBTCAirdropInfoResponse
import org.springframework.data.annotation.CreatedDate
import org.springframework.data.annotation.Id
import org.springframework.data.annotation.LastModifiedDate
import org.springframework.data.mongodb.core.index.Indexed
import org.springframework.data.mongodb.core.mapping.Document
import org.springframework.data.mongodb.core.mapping.Field
import org.springframework.data.mongodb.core.mapping.FieldType
import java.math.BigDecimal
import java.time.LocalDateTime

@Document
data class UserCampaignInfo(
    @Id
    val id: String? = null,
    @Indexed(unique = true)
    val address: String, // 大小写敏感
    @Indexed(unique = true)
    val userId: String,
    @CreatedDate
    val created: LocalDateTime = LocalDateTime.now(),
    @LastModifiedDate
    val updated: LocalDateTime = LocalDateTime.now(),
    @Field("follow_dlcbtc_time")
    var followDlcbtcTime: Long = 0L,
    @Field("retweet_dlcbtc_time")
    var retweetDlcbtcTime: Long = 0L,
    @Field("jump_gala_three_link_time")
    var jumpGalaThreeLinkTime: Long = 0L,
    @Field("airdrop_invite_count")
    var airdropInviteCount: Int = 0,
    @Field("airdrop_invite_verified_count")
    var airdropInviteVerifiedCount: Int = 0,
    @Field("airdrop_invite_trade_count")
    var airdropInviteTradeCount: Int = 0,
    @Field("social_task_point", targetType = FieldType.DECIMAL128)
    var socialTaskPoint: BigDecimal = BigDecimal.ZERO,
    @Field("invite_task_point", targetType = FieldType.DECIMAL128)
    var inviteTaskPoint: BigDecimal = BigDecimal.ZERO,
    @Field("trade_task_point", targetType = FieldType.DECIMAL128)
    var tradeTaskPoint: BigDecimal = BigDecimal.ZERO,
    @Field("total_point", targetType = FieldType.DECIMAL128)
    var totalPoint: BigDecimal? = null,
    @Field("base_response")
    var baseResponse: BaseAirdropInfoResponse? = null,
    @Field("dlcbtc_response")
    var dlcbtcResponse: DLCBTCAirdropInfoResponse? = null,
    @Field("follow_echooo_time")
    var followEchoooTime: Long = 0L,
    @Field("follow_echooo_jasper_vault_time")
    var followEchoooJasperVaultTime: Long = 0L,
    @Field("retweet_echooo_time")
    var retweetEchoooTime: Long = 0L,
    @Field("join_discord_time")
    var joinDiscordTime: Long = 0L,
    @Field("twitter_account_id")
    var twitterAccountId: String? = null,
    @Field("retweet_binance_time")
    var retweetBinanceTime: Long = 0L,
    @Field("claim_bitlayer_binance_point_time")
    var claimBitlayerBinancePointTime: Long = 0L,
    @Field("retweet_tge_time")
    var retweetTgeTime: Long = 0L,
    @Field("tge_trade_count")
    var tgeTradeCount: Int = 0,
    @Field("tge_bitlayer_trade_count")
    var tgeBitlayerTradeCount: Int = 0,
)