package io.vault.jasper.task

import io.vault.jasper.enums.ChainType
import io.vault.jasper.model.KYT
import io.vault.jasper.model.LPVaultStatus
import io.vault.jasper.repository.*
import io.vault.jasper.service.*
import io.vault.jasper.utils.ProfileUtil
import org.slf4j.LoggerFactory
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.beans.factory.annotation.Value
import org.springframework.context.annotation.Profile
import org.springframework.scheduling.annotation.Scheduled
import org.springframework.stereotype.Service
import java.math.BigDecimal
import java.time.LocalDateTime

@Service
class CreateLPVaultTask @Autowired constructor(
    private val lpVaultRepository: LPVaultRepository,
    private val jasperVaultService: JasperVaultService,
    private val systemService: SystemService
) {

    private val logger = LoggerFactory.getLogger(this::class.java)

    @Value("\${schedule_task_on}")
    lateinit var scheduleOn: String

    // 每15秒执行一次
    @Scheduled(fixedDelay = 15 * 1000, initialDelay = 15 * 1000)
    fun run() {

        if(scheduleOn == "false"){
            return
        }
        try {
            exec()
        } catch (e: Exception) {
            logger.error(e.message, e)
        }
    }

    private fun exec() {

        //logger.info("Create LP Vault task begin.")

        checkCreateVaultResult()
        requestVaultWhiteList()

        //logger.info("Create LP Vault task end.")
    }

    private fun requestVaultWhiteList(){

        val pendingRequest = lpVaultRepository.findByStatus(LPVaultStatus.PENDING)
        //logger.info("Pending request ${pendingRequest.size}.")

        val lpVaultParameter = systemService.getLPVaultParameter()

        for(lpVault in pendingRequest) {

            var walletId = lpVaultParameter.walletId
            if(lpVault.chain == ChainType.BITLAYER){
                walletId = "88"
            } else if(lpVault.chain == ChainType.BASE){
                walletId = "99"
            } else if(lpVault.chain == ChainType.BSC){
                walletId = "100"
            }

            try {
                val vaultId = jasperVaultService.createLPVault(
                    walletId,
                    lpVault.lpEoaAddress,
                    lpVault.optionType.name.lowercase(),
                    lpVault.optionSymbol,
                    lpVault.lpVaultAddress
                )

                lpVault.requestId = vaultId
                lpVault.status = LPVaultStatus.REQUESTED
                lpVaultRepository.save(lpVault)

                // 等待 10 秒再做下一个请求
                Thread.sleep(10000)

            }catch (e: Exception) {
                logger.error("Create LP Vault failed. ${e.message}", e)
            }
        }
    }

    private fun checkCreateVaultResult(){

        val requestedRequest = lpVaultRepository.findByStatus(LPVaultStatus.REQUESTED)
        //logger.info("Requested LPVault request ${requestedRequest.size}.")

        for(lpVault in requestedRequest) {
            try {

                val requestId = lpVault.requestId!!
                //logger.info("Requested LPVault request $requestId.")

                val (status, addressOrErrorMessage) = jasperVaultService.getLPVaultAddress(
                    requestId
                )

                if(status == "done"){
                    lpVault.lpVaultAddress = addressOrErrorMessage
                    lpVault.status = LPVaultStatus.DONE
                    lpVaultRepository.save(lpVault)
                } else if(status == "error"){
                    lpVault.status = LPVaultStatus.ERROR
                    lpVault.errorMessage = addressOrErrorMessage
                    lpVaultRepository.save(lpVault)
                }

            }catch (e: Exception) {
                logger.error("Create LP Vault failed. ${e.message}", e)
            }
        }
    }
}