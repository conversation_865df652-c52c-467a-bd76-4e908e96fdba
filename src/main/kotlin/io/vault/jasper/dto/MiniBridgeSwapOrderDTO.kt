package io.vault.jasper.dto

import EVMAddress
import io.swagger.annotations.ApiModel
import io.swagger.annotations.ApiModelProperty
import io.vault.jasper.enums.ChainType
import io.vault.jasper.enums.Symbol

@ApiModel("Mini Bridge下订单请求")
data class MiniBridgeSwapOrderDTO(
    @ApiModelProperty("钱包地址", required = true)
    @field:EVMAddress
    val address: String,
    @ApiModelProperty("接收钱包地址", required = true)
    @field:EVMAddress
    val toAddress: String,
    @ApiModelProperty("发起链", required = true)
    val fromChain: ChainType,
    @ApiModelProperty("接收链", required = true)
    val toChain: ChainType,
    @ApiModelProperty("发起资产", required = true)
    val fromAsset: Symbol,
    @ApiModelProperty("接收资产", required = true)
    val toAsset: Symbol,
    @ApiModelProperty("接收数量", required = true)
    val depositAmount: String,
    @ApiModelProperty("接收数量", required = true)
    val withdrawAmount: String,
    @ApiModelProperty("price oracle id", required = true)
    val priceOracleId: String,
)
