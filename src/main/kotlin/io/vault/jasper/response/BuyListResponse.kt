package io.vault.jasper.response

import io.swagger.annotations.ApiModel
import io.swagger.annotations.ApiModelProperty
import io.vault.jasper.enums.ChainType
import io.vault.jasper.enums.OptionDirection
import io.vault.jasper.enums.Symbol
import io.vault.jasper.model.OrderStatus
import io.vault.jasper.model.OrderType
import io.vault.jasper.model.PremiumAsset
import io.vault.jasper.model.PremiumFeeInfo

@ApiModel
data class BuyListResponse(
    val id: String,
    @ApiModelProperty("链类型", required = true)
    val chain: ChainType,
    @ApiModelProperty("订单类型", required = true)
    val orderType: OrderType,
    @ApiModelProperty("买家地址", required = true)
    val buyerAddress: String,
    @ApiModelProperty("买家的vault地址", required = true)
    val holder: String,
    @ApiModelProperty("业务类型(CALL,PUT)", required = true)
    val businessType: OptionDirection,
    @ApiModelProperty("标的资产(ETH,WBTC)", required = true)
    val underlyingAsset: Symbol,
    @ApiModelProperty("标的资产合约地址(可为空)")
    val underlyingAssetAddress: String?,
    @ApiModelProperty("标的资产总数量", required = true)
    val totalAmount: String,
    @ApiModelProperty("行权价格", required = true)
    val strikePrice: String,
    @ApiModelProperty("行权资产", required = true)
    val strikeAsset: PremiumAsset,
    @ApiModelProperty("行权数量", required = true)
    val strikeAmount: String,
    @ApiModelProperty("权利金", required = true)
    val premiumFees: String,
    @ApiModelProperty("权利金资产", required = true)
    val premiumFeesAsset: PremiumAsset,
    @ApiModelProperty("合约于__小时后到期", required = true)
    val expiryInHour: String?,
    @ApiModelProperty("合约到期时间", required = true)
    val expiryDate: Long,
    @ApiModelProperty("挂单时间", required = true)
    val createDate: Long,
    @ApiModelProperty("订单状态", required = true)
    val status: OrderStatus,
    @ApiModelProperty("买家挂单签名信息", required = true)
    val buyerSignatureData: String,
    @ApiModelProperty("买家挂单签名前消息", required = true)
    val messageBeforeSign: String?,
    @ApiModelProperty("权利金价格信息（含签名）", required = false)
    val premiumFeeInfo: PremiumFeeInfo?,
    @ApiModelProperty("是否免权利金", required = false)
    val premiumFree: Boolean
)
