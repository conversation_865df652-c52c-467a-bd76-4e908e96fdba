package io.vault.jasper.model

import org.springframework.data.annotation.CreatedDate
import org.springframework.data.annotation.Id
import org.springframework.data.annotation.LastModifiedDate
import org.springframework.data.mongodb.core.index.Indexed
import org.springframework.data.mongodb.core.mapping.Document
import org.springframework.data.mongodb.core.mapping.Field
import org.springframework.data.mongodb.core.mapping.FieldType
import java.math.BigDecimal
import java.time.LocalDateTime

@Document("user_network")
data class UserNetwork(
    @Id
    val id: String? = null,
    @Indexed(unique = true)
    val address: String, // 大小写敏感
    @Indexed(unique = true)
    val inviteCode: String,
    @Indexed(unique = true)
    val userId: String,
    @Indexed(background = true)
    val invitedUserId: String,
    @Indexed(background = true)
    var invitedNetworkId: String,
    @Indexed(background = true)
    var level: Int = 0,
    @Indexed(background = true)
    var grade: UserNetworkGrade? = null,
    @Field("protocol_fee_percentage")
    var protocolFeePercentage: BigDecimal = BigDecimal.ZERO,
    var tag: String? = null,
    @CreatedDate
    val created: LocalDateTime = LocalDateTime.now(),
    @LastModifiedDate
    val updated: LocalDateTime = LocalDateTime.now(),
    @Field("check_kol_rebate")
    var checkKolRebate: Boolean? = null,
    var alias: String? = null,
    @Field("direct_member_count")
    var directMemberCount: Int = 0,
    @Field("team_member_count")
    var teamMemberCount: Int = 0,
    @Field("self_premium", targetType = FieldType.DECIMAL128)
    var selfPremium: BigDecimal = BigDecimal.ZERO,
    @Field("direct_premium", targetType = FieldType.DECIMAL128)
    var directPremium: BigDecimal = BigDecimal.ZERO,
    @Field("team_premium", targetType = FieldType.DECIMAL128)
    var teamPremium: BigDecimal = BigDecimal.ZERO,
    @Field("parent_rebate", targetType = FieldType.DECIMAL128)
    var parentRebate: BigDecimal = BigDecimal.ZERO, // 为父节点贡献的佣金
    @Field("team_rebate", targetType = FieldType.DECIMAL128)
    var teamRebate: BigDecimal = BigDecimal.ZERO, // 下级代理(客户)为我贡献的佣金
    @Indexed(background = true)
    @Field("grade2_network_id")
    var grade2NetworkId: String? = null, // 如果是用户，那么这个字段是他的二级代理ID
)

enum class UserNetworkGrade {
    ROOT, // 根节点
    WUJIE, // 无界
    GRADE1, // 一级
    GRADE2, // 二级
    USER, // 普通用户
}