package io.vault.jasper.repository

import io.vault.jasper.enums.ChainType
import io.vault.jasper.enums.Symbol
import io.vault.jasper.model.MiniBridgeSwapOrder
import io.vault.jasper.model.MiniBridgeSwapOrderStatus
import org.springframework.data.mongodb.repository.MongoRepository
import org.springframework.stereotype.Repository
import java.math.BigDecimal

@Repository
interface MiniBridgeSwapOrderRepository : MongoRepository<MiniBridgeSwapOrder, String> {

    fun findByStatusAndDepositTxHashIsNull(
        status: MiniBridgeSwapOrderStatus,
    ): List<MiniBridgeSwapOrder>

    fun findByStatusAndDepositTxHashIsNotNull(
        status: MiniBridgeSwapOrderStatus,
    ): List<MiniBridgeSwapOrder>

    fun findByFromChainAndFromAddressIgnoreCaseAndDepositTxHashIsNull(
        fromChain: ChainType,
        fromAddress: String,
    ): List<MiniBridgeSwapOrder>

    fun findByFromChainAndFromAddressIgnoreCaseAndFromAssetAndDepositAmountAndStatusAndDepositTxHashIsNull(
        fromChain: ChainType,
        fromAddress: String,
        fromAsset: Symbol,
        depositAmount: BigDecimal,
        status: MiniBridgeSwapOrderStatus,
    ): List<MiniBridgeSwapOrder>

    fun findByDepositTxHash(
        depositTxHash: String,
    ): List<MiniBridgeSwapOrder>

    fun findByStatus(
        status: MiniBridgeSwapOrderStatus,
    ): List<MiniBridgeSwapOrder>

    fun findFirstByFromAddressAndToAddressAndFromChainAndToChainAndFromAssetAndToAssetAndDepositAmountAndWithdrawAmountAndStatusAndDepositTxHashIsNull(
        fromAddress: String,
        toAddress: String,
        fromChain: ChainType,
        toChain: ChainType,
        fromAsset: Symbol,
        toAsset: Symbol,
        depositAmount: BigDecimal,
        withdrawAmount: BigDecimal,
        status: MiniBridgeSwapOrderStatus,
    ): MiniBridgeSwapOrder?
}