package io.vault.jasper.dto

import EVMAddress
import io.swagger.annotations.ApiModel
import io.swagger.annotations.ApiModelProperty
import io.vault.jasper.enums.ChainType
import io.vault.jasper.enums.Symbol

@ApiModel("Mini Bridge存款请求")
data class SubmitMiniBridgeSwapOrderDepositHashDTO(
    @ApiModelProperty("Mini Bridge 订单ID", required = true)
    val orderId: String,
    @ApiModelProperty("tx hash", required = true)
    val txHash: String
)
