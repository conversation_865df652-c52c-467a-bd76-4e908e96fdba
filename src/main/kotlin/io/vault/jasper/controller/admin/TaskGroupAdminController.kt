package io.vault.jasper.controller.admin

import io.swagger.annotations.Api
import io.swagger.annotations.ApiOperation
import io.vault.jasper.exception.BusinessException
import io.vault.jasper.exception.ResultEnum
import io.vault.jasper.model.activity.TaskGroup

import io.vault.jasper.ApiResponse
import io.vault.jasper.service.activity.TaskGroupService
import org.springframework.web.bind.annotation.*

@RestController
@RequestMapping("/api/admin/task-groups")
@Api(tags = ["Task Group Admin API"])
class TaskGroupAdminController(
    private val taskGroupService: TaskGroupService
) {

    @PostMapping
    @ApiOperation("Create a new task group")
    fun createTaskGroup(@RequestBody taskGroup: TaskGroup): ApiResponse<TaskGroup> {
        return ApiResponse.success(taskGroupService.createTaskGroup(taskGroup))
    }

    @GetMapping("/{id}")
    @ApiOperation("Get a task group by ID")
    fun getTaskGroup(@PathVariable id: String): ApiResponse<TaskGroup> {
        val taskGroup = taskGroupService.getTaskGroupById(id)
            ?: throw BusinessException(ResultEnum.TASK_GROUP_NOT_FOUND)

        return ApiResponse.success(taskGroup)
    }

    @PostMapping("/{taskGroupId}/tasks/{taskId}")
    @ApiOperation("Add a task to a task group")
    fun addTaskToTaskGroup(
        @PathVariable taskGroupId: String,
        @PathVariable taskId: String
    ): ApiResponse<TaskGroup> {
        return ApiResponse.success(taskGroupService.addTaskToTaskGroup(taskGroupId, taskId))
    }

    @DeleteMapping("/{taskGroupId}/tasks/{taskId}")
    @ApiOperation("Remove a task from a task group")
    fun removeTaskFromTaskGroup(
        @PathVariable taskGroupId: String,
        @PathVariable taskId: String
    ): ApiResponse<TaskGroup> {
        return ApiResponse.success(taskGroupService.removeTaskFromTaskGroup(taskGroupId, taskId))
    }

    @GetMapping("/{taskGroupId}/tasks")
    @ApiOperation("Get tasks for a task group")
    fun getTasksForTaskGroup(@PathVariable taskGroupId: String): ApiResponse<List<io.vault.jasper.model.activity.Task>> {
        return ApiResponse.success(taskGroupService.getTasksForTaskGroup(taskGroupId))
    }
}
