package io.vault.jasper.model.activity

import org.springframework.data.annotation.CreatedDate
import org.springframework.data.annotation.Id
import org.springframework.data.annotation.LastModifiedDate
import org.springframework.data.mongodb.core.index.Indexed
import org.springframework.data.mongodb.core.mapping.Document
import org.springframework.data.mongodb.core.mapping.Field
import java.time.LocalDateTime

/**
 * TaskGroup model representing a group of tasks in a campaign
 */
@Document("task_groups")
data class TaskGroup(
    @Id
    val id: String? = null,
    
    @Indexed(background = true)
    val title: String,
    
    @Field("task_ids")
    val taskIds: List<String> = listOf(),
    
    @CreatedDate
    val created: LocalDateTime = LocalDateTime.now(),
    
    @LastModifiedDate
    val updated: LocalDateTime = LocalDateTime.now(),
    
    @Field("i18n_info")
    var i18nInfo: MutableMap<String, I18nTaskGroupInfo>? = null
)

/**
 * I18n information for TaskGroup
 */
data class I18nTaskGroupInfo(
    val title: String,
    val description: String,
    val icon: String = "",
    val extraNote: String? = null
)
