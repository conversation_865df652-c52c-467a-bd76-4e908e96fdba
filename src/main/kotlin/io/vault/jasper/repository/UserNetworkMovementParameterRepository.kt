package io.vault.jasper.repository

import io.vault.jasper.model.UserNetworkMovementParameter
import org.springframework.data.mongodb.repository.MongoRepository
import org.springframework.stereotype.Repository

@Repository
interface UserNetworkMovementParameterRepository : MongoRepository<UserNetworkMovementParameter, String> {
    
    /**
     * 查找启用的参数配置
     */
    fun findFirstByTaskSwitch(taskSwitch: Boolean): UserNetworkMovementParameter?
}
