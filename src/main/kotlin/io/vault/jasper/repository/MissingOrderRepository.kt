package io.vault.jasper.repository

import io.vault.jasper.enums.ChainType
import io.vault.jasper.model.MissingOrder
import org.springframework.data.domain.Pageable
import org.springframework.data.mongodb.repository.MongoRepository
import org.springframework.stereotype.Repository

@Repository
interface MissingOrderRepository : MongoRepository<MissingOrder, String> {
    fun findByReceiptStatusIsNull(): List<MissingOrder>
    fun findByReceiptStatusIsNotNull(): List<MissingOrder>
    fun findFirstByTxHash(txHash: String): MissingOrder?
    fun findByChain(chain: ChainType, pageable: Pageable): List<MissingOrder>
    fun findByIgnoreIsFalse(): List<MissingOrder>
}