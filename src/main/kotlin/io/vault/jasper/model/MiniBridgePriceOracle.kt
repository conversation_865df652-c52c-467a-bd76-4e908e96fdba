package io.vault.jasper.model

import io.vault.jasper.enums.Symbol
import org.springframework.data.annotation.CreatedDate
import org.springframework.data.annotation.Id
import org.springframework.data.annotation.LastModifiedDate
import org.springframework.data.mongodb.core.mapping.Document
import org.springframework.data.mongodb.core.mapping.Field
import java.math.BigDecimal
import java.math.BigInteger
import java.time.LocalDateTime

@Document("mini_bridge_price_oracle")
data class MiniBridgePriceOracle(
    @Id
    val id: String? = null,

    val timestamp: Long,
    val price: BigDecimal,

    @Field("bid_asset")
    val bidAsset: Symbol,
    @Field("quote_asset")
    val quoteAsset: Symbol,

    @Field("oracle_info")
    val oracleInfo: OracleInfo,

    @CreatedDate
    val created: LocalDateTime = LocalDateTime.now(),

    @LastModifiedDate
    val updated: LocalDateTime = LocalDateTime.now(),
)

data class OracleInfo(
    val bidAsset: Symbol,
    val quoteAsset: Symbol,
    val bidAssetUsdPrice: BigDecimal,
    val quoteAssetUsdPrice: BigDecimal
)
