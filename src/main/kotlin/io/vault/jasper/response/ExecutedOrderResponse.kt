package io.vault.jasper.response

import io.swagger.annotations.ApiModel
import io.swagger.annotations.ApiModelProperty
import io.vault.jasper.enums.ChainType
import io.vault.jasper.enums.OptionDirection
import io.vault.jasper.enums.Symbol
import io.vault.jasper.model.OptionStatus
import io.vault.jasper.model.OrderType
import java.math.BigInteger

@ApiModel
data class ExecutedOrderResponse(
    /**
     * 挂单基本信息
     */
    @ApiModelProperty("挂单ID", required = true)
    val id: String,
    @ApiModelProperty("挂单ID", required = true)
    val orderId: String,
    @ApiModelProperty("挂单类型", required = true)
    val orderType: OrderType,
    @ApiModelProperty("链类型", required = true)
    val chain: ChainType,
    @ApiModelProperty("链上订单ID", required = true)
    val onChainOrderId: String?,
    @ApiModelProperty("买家地址", required = true)
    val buyerAddress: String?,
    @ApiModelProperty("买家Vault地址", required = false)
    val buyerVault: String?,
    @ApiModelProperty("买家Vault地址Salt", required = false)
    val buyerVaultSalt: String?,
    @ApiModelProperty("卖家地址", required = true)
    val sellerAddress: String?,
    @ApiModelProperty("业务类型(CALL,PUT)", required = true)
    val businessType: OptionDirection,

    /**
     * 标的信息
     */
    @ApiModelProperty("标的资产", required = false)
    val bidAsset: Symbol?,
    @ApiModelProperty("标的资产数量", required = false)
    val bidAmount: String?,

    /**
     * 抵押信息
     */
    @ApiModelProperty("抵押资产", required = true)
    val underlyingAsset: Symbol,
    @ApiModelProperty("抵押资产合约地址(可为空)", required = false)
    val underlyingAssetAddress: String?,
    @ApiModelProperty("抵押资产数量", required = true)
    val underlyingAssetAmount: String,

    /**
     * 行权信息
     */
    @ApiModelProperty("行权资产", required = true)
    val strikeAsset: Symbol,
    @ApiModelProperty("行权数量", required = true)
    val strikeAmount: String,
    @ApiModelProperty("行权价格", required = true)
    val strikePrice: String,
    @ApiModelProperty("实际行权数量", required = false)
    val actualStrikeAmount: String?,

    /**
     * 权利金信息
     */
    @ApiModelProperty("权利金资产", required = true)
    val premiumFeesAsset: Symbol,
    @ApiModelProperty("权利金", required = true)
    val premiumFee: String?,
    @ApiModelProperty("应付权利金", required = true)
    val premiumFeeShouldPay: String?,

    /**
     * 挂单信息
     */
    @ApiModelProperty("挂单时间", required = true)
    val createDate: Long,
    @ApiModelProperty("合约到期日", required = true)
    val expiryDate: Long,
    @ApiModelProperty("合约锁定日", required = true)
    val lockDate: Long,
    @ApiModelProperty("订单状态", required = true)
    val status: OptionStatus,
    @ApiModelProperty("是否极速订单", required = true)
    val degen: Boolean,
    @ApiModelProperty("结算时的__标的物__市场价格", required = false)
    val marketPriceAtSettlement: String?,
    @ApiModelProperty("结算时的链上Hash", required = false)
    val settlementHash: String?,
    @ApiModelProperty("结算利润", required = false)
    val buyerProfit: String?,
    @ApiModelProperty("到期时间（小时）", required = false)
    val expiryInHour: String?,
    @ApiModelProperty("是否已使用月光宝盒", required = false)
    val usedMoonlightBox: Boolean?,
    @ApiModelProperty("是否现实宝石订单", required = true)
    val usedRealityStone: Boolean,
    @ApiModelProperty("是否力量宝石订单", required = true)
    val usedPowerStone: Boolean,
    @ApiModelProperty("是否空间宝石订单", required = true)
    val usedSpaceStone: Boolean,
    @ApiModelProperty("是否时间宝石订单", required = true)
    val usedTimeStone: Boolean,
    @ApiModelProperty("宝石活动NFT ID", required = false)
    val stoneActivityNftId: String?,
    @ApiModelProperty("宝石活动碎片 NFT ID", required = false)
    val awardStonePieceNftIds: List<BigInteger>?,
    @ApiModelProperty("权利金 USDT", required = true)
    val premiumFeeInUsdt: String?,
    @ApiModelProperty("应付权利金USDT", required = true)
    val premiumFeeShouldPayInUsdt: String?,
    @ApiModelProperty("链上交易hash", required = true)
    val txHash: String?,
    @ApiModelProperty("权利金变化", required = true)
    val premiumFeeLog: String?,
)
