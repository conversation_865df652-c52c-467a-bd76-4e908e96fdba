package io.vault.jasper.repository

import io.vault.jasper.enums.ChainType
import io.vault.jasper.model.LpVaultRecord
import org.springframework.data.mongodb.repository.MongoRepository
import org.springframework.stereotype.Repository

@Repository
interface LpVaultRecordRepository : MongoRepository<LpVaultRecord, String> {
    fun findFirstByChainAndTxHash(chain: ChainType, txHash: String): LpVaultRecord?
}