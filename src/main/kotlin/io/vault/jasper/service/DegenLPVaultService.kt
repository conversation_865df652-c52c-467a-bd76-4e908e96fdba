package io.vault.jasper.service


/**
 * Degen LP Vault Service
 */

import com.fasterxml.jackson.databind.JsonNode
import com.fasterxml.jackson.databind.ObjectMapper
import io.vault.jasper.blockchain.BscBlockchainUtil
import io.vault.jasper.enums.ChainType
import io.vault.jasper.enums.OptionDirection
import io.vault.jasper.enums.Symbol
import io.vault.jasper.model.*
import io.vault.jasper.repository.DegenLPVaultConfigRepository
import io.vault.jasper.repository.DegenLPVaultRepository
import io.vault.jasper.service.blockchain.BlockchainServiceFactory
import io.vault.jasper.service.blockchain.EvmService
import io.vault.jasper.utils.ProfileUtil
import org.slf4j.LoggerFactory
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.data.mongodb.core.MongoTemplate
import org.springframework.data.mongodb.core.aggregation.Aggregation
import org.springframework.data.mongodb.core.aggregation.ConvertOperators
import org.springframework.data.mongodb.core.query.Criteria
import org.springframework.http.client.SimpleClientHttpRequestFactory
import org.springframework.stereotype.Service
import org.springframework.web.client.HttpClientErrorException
import org.springframework.web.client.RestTemplate
import java.math.BigDecimal
import java.math.RoundingMode
import java.text.SimpleDateFormat
import java.time.LocalDateTime
import java.util.*
import kotlin.collections.HashMap

@Service
class DegenLPVaultService @Autowired constructor(
    private val objectMapper: ObjectMapper,
    private val jasperVaultService: JasperVaultService,
    private val degenLPVaultRepository: DegenLPVaultRepository,
    private val degenLPVaultConfigRepository: DegenLPVaultConfigRepository,
    private val systemService: SystemService,
    private val optionOrderService: OptionOrderService,
    private val currencyService: CurrencyService,
    private val blockchainServiceFactory: BlockchainServiceFactory,
    private val mongoTemplate: MongoTemplate,
){

    private val logger = LoggerFactory.getLogger(this::class.java)

    private val updateSettingInterval = 3600L // In Seconds

    private val restTemplate: RestTemplate by lazy {
        val rt = RestTemplate()

        val requestFactory = SimpleClientHttpRequestFactory()
        // Set the 30s timeout in milliseconds
        requestFactory.setConnectTimeout(30000)
        requestFactory.setReadTimeout(30000)

        rt.requestFactory = requestFactory
        rt
    }

    fun updateFromDegenLPVaultConfig(){

        val allConfigs = degenLPVaultConfigRepository.findAll()

        val liquidityMapping = mutableMapOf<String, BigDecimal>()
        val premiumRateMapping = mutableMapOf<String, Map<String, String>>()
        val premiumFloorRateMapping = mutableMapOf<String, Map<String, String>>()
        val maxMapping = mutableMapOf<String, Map<String, String>>()
        val offerIdMapping = mutableMapOf<String, Map<String, String>>()
        val settingListMapping = mutableMapOf<String, List<List<String>>>()

        val now = LocalDateTime.now()

        for(config in allConfigs){
            val chain = config.chain
            val symbol = config.optionSymbol
            val optionType = config.optionType
            val expiryHourList = config.expireInHour
            val orderType = config.orderType

            val strikePrice = jasperVaultService.getPythPrice(
                currencyService.getPriceId(symbol)
            )

            if(strikePrice == null){
                continue
            }

            val readableStrikePrice = strikePrice.movePointLeft(18)

            for(expiryHour in expiryHourList) {

                //logger.info("Update Degen LP Vault Config: $chain $symbol $optionType $expiryHour")
                try {
                    var existingDegenLPVault =
                        degenLPVaultRepository.findByChainAndOptionTypeAndOptionSymbolAndExpireInHourAndOrderType(
                            chain = chain,
                            optionType = optionType,
                            optionSymbol = symbol,
                            expireInHour = expiryHour,
                            orderType = orderType
                        )

                    if (existingDegenLPVault == null) {
                        existingDegenLPVault = degenLPVaultRepository.save(DegenLPVault(
                            chain = chain,
                            orderType = orderType,
                            optionType = optionType,
                            optionSymbol = symbol,
                            expireInHour = expiryHour,
                            address = config.address,
                            created = LocalDateTime.now(),
                            updated = LocalDateTime.now()
                        ))
                    }

                    if (existingDegenLPVault!!.productType == 0) {
                        existingDegenLPVault.productType = (3600.0 * existingDegenLPVault.expireInHour.toDouble()).toInt()
                    }

                    existingDegenLPVault.address = config.address
                    existingDegenLPVault.token = config.token
                    existingDegenLPVault.tokenAddress = config.tokenAddress

                    val underlyingSymbol = existingDegenLPVault.optionSymbol
                    val underlyingAddress = currencyService.getCurrencyContract(
                        existingDegenLPVault.chain,
                        underlyingSymbol
                    )

                    val key = existingDegenLPVault.chain.name + "-" + existingDegenLPVault.address + "-" + existingDegenLPVault.tokenAddress + "-" + underlyingAddress

                    var tokenBalance = liquidityMapping[key]
                    var premiumRateMap = premiumRateMapping[key]
                    var premiumFloorRateMap = premiumFloorRateMapping[key]
                    var maximumMap = maxMapping[key]
                    var offerIdMap = offerIdMapping[key]
                    var settingList = settingListMapping[key]

                    //logger.info("Update Degen LP Vault Config key: $key")

                    if (tokenBalance == null) {
                        val evmService =
                            blockchainServiceFactory.getBlockchainService(existingDegenLPVault.chain) as EvmService

                        tokenBalance = evmService.evmUtil.getBalance(
                            existingDegenLPVault.address,
                            existingDegenLPVault.tokenAddress
                        )

                        liquidityMapping[key] = tokenBalance
                    }

                    existingDegenLPVault.availableLiquidity = tokenBalance

                    var needToUpdateSetting = false
                    if(existingDegenLPVault.lastUpdateSettingTime == null){
                        needToUpdateSetting = true
                    } else {

                        if (existingDegenLPVault.lastUpdateSettingTime!!.plusSeconds(updateSettingInterval).isBefore(now)) {
                            needToUpdateSetting = true
                        }
                    }

                    if(needToUpdateSetting) {
                        if (premiumRateMap == null) {

                            //logger.info("Update Degen LP Vault Config Request Worker key: $key")

                            val lpVaultSettingMap = jasperVaultService.getLPVaultSettingPremiumRates(
                                existingDegenLPVault.chain,
                                existingDegenLPVault.address,
                                underlyingAddress
                            )

                            premiumRateMap = lpVaultSettingMap["premiumRate"] as Map<String, String>
                            premiumRateMapping[key] = premiumRateMap

                            premiumFloorRateMap = lpVaultSettingMap["premiumFloorRate"] as Map<String, String>
                            premiumFloorRateMapping[key] = premiumFloorRateMap

                            maximumMap = lpVaultSettingMap["maximum"] as Map<String, String>
                            maxMapping[key] = maximumMap

                            offerIdMap = lpVaultSettingMap["offerId"] as Map<String, String>
                            offerIdMapping[key] = offerIdMap

                            settingList = lpVaultSettingMap["settingList"] as List<List<String>>
                            settingListMapping[key] = settingList
                        } else {
                            //logger.info("Update Degen LP Vault Config get from cache key: $key")
                        }

                        val productType = existingDegenLPVault.productType
                        val premiumRate = premiumRateMap[productType.toString()]?.toBigDecimal()
                        val premiumFloorRate = premiumFloorRateMap?.get(productType.toString())?.toBigDecimal()
                        val max = maximumMap?.get(productType.toString())?.toBigDecimal()
                        val offerId = offerIdMap?.get(productType.toString())

                        existingDegenLPVault.premiumRate = premiumRate ?: BigDecimal.ONE
                        existingDegenLPVault.premiumFloorPercentage = premiumFloorRate ?: BigDecimal.ONE
                        existingDegenLPVault.maximum = max ?: BigDecimal.ONE
                        existingDegenLPVault.offerId = offerId ?: "0"

                        var settingIndex = -1
                        var productIndex = -1

                        for (i in 0 until settingList!!.size) {
                            val productList = settingList[i]

                            for (j in 0 until productList.size) {
                                if (productList[j] == productType.toString()) {
                                    settingIndex = i
                                    productIndex = j
                                    break
                                }
                            }
                        }

                        existingDegenLPVault.settingIndex = settingIndex.toString()
                        existingDegenLPVault.productIndex = productIndex.toString()

                        existingDegenLPVault.lastUpdateSettingTime = now
                    }

                    if (existingDegenLPVault.token != null && existingDegenLPVault.token != Symbol.USDT && existingDegenLPVault.token != Symbol.USDC) {
                        existingDegenLPVault.availableLiquidityInUsdt = tokenBalance.multiply(readableStrikePrice)
                    } else {
                        existingDegenLPVault.availableLiquidityInUsdt = tokenBalance
                    }

                    //Update Open Interest
                    val oi = optionOrderService.calculateOpenInterest(
                        OrderType.DEGEN,
                        chain,
                        symbol,
                        existingDegenLPVault.optionType,
                        expiryHour,
                        null,
                        existingDegenLPVault.address
                    )

                    existingDegenLPVault.openInterest = oi
                    existingDegenLPVault.openInterestInUsdt = oi.multiply(readableStrikePrice)
                    existingDegenLPVault.utilization = BigDecimal.ZERO
                    existingDegenLPVault.readableMarketPrice = readableStrikePrice

                    degenLPVaultRepository.save(existingDegenLPVault)

                } catch (e: Exception) {
                    logger.error("updateFromDegenLPVaultConfig Update Degen LP Vault Config Error $chain, $symbol, $optionType, $expiryHour, $orderType" + e.message, e)
                }
            }
        }

        try {
            calculateTvl()
        }catch (e: Exception){
                logger.error("Update Degen LP Vault Config calculateTvl " + e.message, e)
        }
    }

    private fun calculateTvl(){

        val allConfigs = degenLPVaultConfigRepository.findAll()
        val keyValueMapping = mutableMapOf<String, BigDecimal>()

        for(config in allConfigs){
            val chain = config.chain
            val symbol = config.optionSymbol
            val optionType = config.optionType
            val expiryHourList = config.expireInHour
            val orderType = config.orderType

            for(expiryHour in expiryHourList) {

                //logger.info("Update Degen LP Vault Config: $chain $symbol $optionType $expiryHour")
                try {
                    val existingDegenLPVault =
                        degenLPVaultRepository.findByChainAndOptionTypeAndOptionSymbolAndExpireInHourAndOrderType(
                            chain = chain,
                            optionType = optionType,
                            optionSymbol = symbol,
                            expireInHour = expiryHour,
                            orderType = orderType
                        )
                    if(existingDegenLPVault == null){
                        continue
                    }

                    var allVaultTotalOIInUsdt = BigDecimal.ZERO

                    val key = chain.toString() + "-" + existingDegenLPVault.address
                    if(keyValueMapping.containsKey(key)){
                        allVaultTotalOIInUsdt = keyValueMapping[key]!!
                    } else {

                        allVaultTotalOIInUsdt = calculateSellVaultOpenInterest(
                            existingDegenLPVault.address
                        )

                        keyValueMapping[key] = allVaultTotalOIInUsdt
                    }

                    //logger.info("Update LPVault Service key: $key allVaultTotalOIInUsdt: $allVaultTotalOIInUsdt")

                    val totalValueLockedInUsdt = existingDegenLPVault.availableLiquidityInUsdt.add(allVaultTotalOIInUsdt)
                    val totalValueLocked = totalValueLockedInUsdt.divide(existingDegenLPVault.readableMarketPrice, 18, RoundingMode.HALF_UP)
                    existingDegenLPVault.tvl = totalValueLocked

                    degenLPVaultRepository.save(existingDegenLPVault)

                } catch (e: Exception) {
                    logger.error("calculateTvl Update Degen LP Vault Config Error $chain, $symbol, $optionType, $expiryHour, $orderType" + e.message, e)
                }
            }
        }
    }

    fun calculateSellVaultOpenInterest(
        sellerVault: String
    ): BigDecimal {

        val criteria = Criteria.where(DegenLPVault::address.name).regex("(?i)$sellerVault")

        val agg = Aggregation.newAggregation(
            Aggregation.match(criteria),
            Aggregation.group().sum(
                ConvertOperators.valueOf(
                    DegenLPVault::openInterestInUsdt.name
                ).convertToDecimal()).`as`("total")
        )
        val groupResults = mongoTemplate.aggregate(agg, DegenLPVault::class.java, Map::class.java)
        val result = groupResults.mappedResults.firstOrNull()
        val total = (result?.get("total")?:"0").toString().toBigDecimal()

        return total
    }

    fun updateAllDegenLPVaultPremium(
        chain: ChainType,
        symbol: Symbol
    ){
        val allLpVaults = degenLPVaultRepository.findByOptionSymbol(
            optionSymbol = symbol
        )

        val strikePrice = jasperVaultService.getPythPrice(
            currencyService.getPriceId(symbol)
        )

        if(strikePrice == null){
            // logger.info("Failed to get strike price for $symbol")
            return
        }

        val readableStrikePrice = strikePrice.movePointLeft(18)

        val productTypeList = allLpVaults.map { it.productType }.distinct()
        val optionPriceList = jasperVaultService.getMulti0DTEPrice(
            symbol,
            readableStrikePrice,
            productTypeList
        )

        for(lpVault in allLpVaults){

            var matchOptionPrice: JsonNode? = null
            for(optionPrice in optionPriceList){

                val lpvaultOptionType = lpVault.optionType.name.substring(0, 1)
                val apiTextVaule = optionPrice.get("option_type").textValue()
                // logger.info("Degen Get Premium lpVaultOptionType: $lpvaultOptionType")
                // logger.info("Degen Get Premium apiTextVaule: $apiTextVaule")

                if(optionPrice.get("product_type").intValue() == lpVault.productType &&
                    apiTextVaule.compareTo(lpvaultOptionType) == 0){
                    matchOptionPrice = optionPrice
                    break
                }
            }

            val premium = matchOptionPrice?.get("option_price")?.doubleValue()!!
            val platformPremium = matchOptionPrice.get("platform_premium")?.doubleValue()!!

            val finalPremium = BigDecimal.valueOf(premium).add(BigDecimal.valueOf(platformPremium))
            lpVault.premium = finalPremium

            degenLPVaultRepository.save(lpVault)
        }
    }

    fun getDegenLPVaultTVL(
        chain: ChainType,
        symbol: Symbol,
        expiryHour: String
    ): BigDecimal {
        val allLpVaults = degenLPVaultRepository.findByOptionSymbolAndExpireInHour(
            symbol,
            expiryHour
        )

        var totalLiquidity = BigDecimal.ZERO
        for(lpVault in allLpVaults){

            totalLiquidity = totalLiquidity.add(lpVault.availableLiquidityInUsdt)
            val optionInterest = optionOrderService.calculateOpenInterest(
                OrderType.DEGEN,
                lpVault.chain,
                lpVault.optionSymbol,
                lpVault.optionType,
                lpVault.expireInHour,
                null,
                lpVault.address
            )

            totalLiquidity = totalLiquidity.add(optionInterest)
        }

        return totalLiquidity
    }
}