package io.vault.jasper.task

import io.vault.jasper.enums.OptionDirection
import io.vault.jasper.enums.Symbol
import io.vault.jasper.event.CheckTgeCampaignOrderEvent
import io.vault.jasper.model.*
import io.vault.jasper.repository.OptionOrderInfoRepository
import io.vault.jasper.repository.OptionOrderRepository
import io.vault.jasper.repository.OrderRepository
import io.vault.jasper.service.*
import io.vault.jasper.service.blockchain.BlockchainServiceFactory
import io.vault.jasper.service.blockchain.EvmService
import io.vault.jasper.utils.DateTimeUtil
import io.vault.jasper.utils.ProfileUtil
import org.slf4j.LoggerFactory
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.beans.factory.annotation.Value
import org.springframework.context.ApplicationEventPublisher
import org.springframework.data.domain.PageRequest
import org.springframework.data.domain.Sort
import org.springframework.data.mongodb.core.MongoTemplate
import org.springframework.data.mongodb.core.query.Criteria
import org.springframework.data.mongodb.core.query.Query
import org.springframework.data.repository.findByIdOrNull
import org.springframework.scheduling.annotation.Scheduled
import org.springframework.stereotype.Service
import java.math.BigDecimal
import java.math.RoundingMode
import java.time.LocalDateTime
import kotlin.math.absoluteValue


@Service
class UpdateOptionsInfoTask @Autowired constructor(
    private val optionOrderService: OptionOrderService,
    private val mongoTemplate: MongoTemplate,
    private val currencyService: CurrencyService,
    private val optionOrderRepository: OptionOrderRepository,
    private val orderRepository: OrderRepository,
    private val optionOrderInfoRepository: OptionOrderInfoRepository,
    private val blockchainServiceFactory: BlockchainServiceFactory,
    private val jasperVaultService: JasperVaultService,
    private val marketplaceApiService: MarketplaceApiService,
    private val eventPublisher: ApplicationEventPublisher,
) {

    private val logger = LoggerFactory.getLogger(this::class.java)

    private val batchCount = 100

    val startTime = LocalDateTime.of(2024, 9, 1, 0, 0, 0)

    private val delayTime = 60 // 60 Seconds

    private var lastUpdatePremiumOptionOrderId: String? = null

    @Value("\${schedule_task_on}")
    lateinit var scheduleOn: String

    @Scheduled(fixedDelay = 10 * 1000, initialDelay = 5 * 1000)
    fun run() {
        if(scheduleOn == "false"){
            return
        }

        logger.info("update option order info task start")

        try {
            updateCreatedUsdPrice()
        } catch (e: Exception) {
            logger.error(e.message, e)
        }

        try {
            updateSettlementUsdPrice()
        } catch (e: Exception) {
            logger.error(e.message, e)
        }

        try{
            updateChannel()
        } catch (e: Exception) {
            logger.error(e.message, e)
        }

        try{
            updateNullChannel()
        } catch (e: Exception) {
            logger.error(e.message, e)
        }

        try{
            updateNullVolume()
        } catch (e: Exception) {
            logger.error(e.message, e)
        }

        try{
            updateProduct()
        } catch (e: Exception) {
            logger.error(e.message, e)
        }

        try{
            updateRoi()
        } catch (e: Exception) {
            logger.error(e.message, e)
        }

        try{
            updateFailedOrders()
        } catch (e: Exception) {
            logger.error(e.message, e)
        }

        try{
            updatePremiumFeeDistribution()
        } catch (e: Exception) {
            logger.error(e.message, e)
        }

        try{
            updateNullLockDate()
        } catch (e: Exception) {
            logger.error(e.message, e)
        }

        try{
            updatePremiumSignInfo()
        } catch (e: Exception) {
            logger.error(e.message, e)
        }

        try{
            updateDistanceInfo()
        } catch (e: Exception) {
            logger.error(e.message, e)
        }

        try{
            updateOTMInfo()
        } catch (e: Exception){
            logger.error(e.message, e)
        }

        logger.info("update option order info task end")
    }

    private fun updateSettlementUsdPrice() {

        val endTime = LocalDateTime.now().minusSeconds(delayTime.toLong())

        val query = Query()
        query.addCriteria(Criteria.where(OptionOrder::status.name).`is`(OptionStatus.SETTLED))
        query.addCriteria(Criteria.where(OptionOrder::created.name).gte(startTime).lte(endTime))
        //query.addCriteria(Criteria.where(OptionOrder::buyerProfit.name).gt(BigDecimal.ZERO))
        query.addCriteria(Criteria.where(OptionOrder::bidAssetPriceInUsdAtSettlement.name).isNull)

//        val orCriteria = Criteria().orOperator(
//            Criteria.where(OptionOrder::buyerProfitInUsd.name).isNull,
//            Criteria.where(OptionOrder::buyerProfitInUsd.name).`is`(BigDecimal.ZERO)
//        )
//        query.addCriteria(orCriteria)

        // 设置返回的文档数量限制
        query.with(Sort.by(Sort.Order.desc(OptionOrder::created.name)))
        query.limit(batchCount) // 只返回 100 条记录

        // 使用 mongoTemplate 执行查询
        val results: List<OptionOrder> = mongoTemplate.find(query, OptionOrder::class.java)

        //logger.info("Update USD price for ${results.size} settled option orders")

        results.forEach { oo ->
            try {
                optionOrderService.updateOptionOrderPriceInUsdAtSettlement(oo)
            } catch (e: Exception) {
                logger.error("Failed to update option order price in USD at settlement for order: ${oo.id}", e)
            }

            Thread.sleep(200)
        }
    }

    private fun updateCreatedUsdPrice() {

        val endTime = LocalDateTime.now().minusSeconds(delayTime.toLong())

        val query = Query()
        query.addCriteria(Criteria.where(OptionOrder::status.name).`in`(
            listOf(OptionStatus.EXECUTED, OptionStatus.SETTLED)
        ))
        query.addCriteria(Criteria.where(OptionOrder::created.name).gte(startTime).lte(endTime))
        query.addCriteria(Criteria.where(OptionOrder::premiumFeePayInUsdt.name).ne(null))
        query.addCriteria(Criteria.where(OptionOrder::premiumFeePayInUsd.name).isNull)

        // 设置返回的文档数量限制
        query.with(Sort.by(Sort.Order.desc(OptionOrder::created.name)))
        query.limit(batchCount) // 只返回 100 条记录

        // 使用 mongoTemplate 执行查询
        val results: List<OptionOrder> = mongoTemplate.find(query, OptionOrder::class.java)

        //logger.info("Update USD price for ${results.size} created option orders later than $startTime")

        results.forEach { oo ->
            try {
                optionOrderService.updateOptionOrderPriceInUsdAtCreated(oo)
            } catch (e: Exception) {
                logger.error("Failed to update option order price in USD at created for order: ${oo.id}", e)
            }

            Thread.sleep(200)
        }
    }

    private fun updateChannel() {

        val endTime = LocalDateTime.now().minusSeconds(delayTime.toLong())
        val startTime = endTime.minusDays(3)

        val query = Query()
        query.addCriteria(Criteria.where(OptionOrderInfo::synced.name).isNull)
        //query.addCriteria(Criteria.where(OptionOrderInfo::created.name).gte(startTime).lte(endTime))

        // 设置返回的文档数量限制
        query.limit(batchCount) // 只返回 100 条记录

        // 使用 mongoTemplate 执行查询
        val results: List<OptionOrderInfo> = mongoTemplate.find(query, OptionOrderInfo::class.java)

        //logger.info("Update Channel for ${results.size} executed option order infos")

        val now = LocalDateTime.now()
        results.forEach { ooi ->
            try {
                val hash = ooi.txHash
                val optionOrders = optionOrderRepository.findByTxHashAndCreatedLessThan(
                    hash,
                    endTime
                )

                if(optionOrders.isEmpty()){
                    if(ooi.created.isBefore(now.minusDays(1))){
                        //logger.info("No option orders found for tx hash: $hash and expired")
                        optionOrderInfoRepository.delete(ooi)
                    }

                    return@forEach
                }

                for(oo in optionOrders){
                    val updateFields = mutableMapOf<String, Any?>(
                        OptionOrder::channel.name to ooi.channel
                    )

                    if(ooi.product != null){
                        updateFields[OptionOrder::product.name] = ooi.product
                    } else {
                        updateFields[OptionOrder::product.name] = optionOrderService.getOptionOrderProduct(
                            ooi.channel,
                            oo
                        )
                    }

                    if(ooi.wallet != null){
                        updateFields[OptionOrder::wallet.name] = ooi.wallet
                    }

                    if(ooi.campaign != null){
                        updateFields[OptionOrder::campaign.name] = ooi.campaign
                    }

                    if(ooi.from != null){
                        updateFields[OptionOrder::from.name] = ooi.from
                    }

                    if(ooi.utmInfo != null){
                        updateFields[OptionOrder::utmInfo.name] = ooi.utmInfo
                    }

                    if(ooi.buyer != null){
                        updateFields[OptionOrder::buyer.name] = ooi.buyer
                    }

                    if(ooi.systemBuyer != null){
                        updateFields[OptionOrder::systemBuyer.name] = ooi.systemBuyer
                    }

                    if(ooi.buyerVault != null){
                        updateFields[OptionOrder::buyerVault.name] = ooi.buyerVault
                    }

                    if(ooi.channel == UserChannel.MINI_APP_BACKEND){
                        updateFields.putAll(
                            mapOf(
                                OptionOrder::usedSpaceStone.name to true,
                                OptionOrder::stoneActivityNftId.name to "8",
                                OptionOrder::premiumFeePay.name to BigDecimal.ZERO,
                                OptionOrder::premiumFeePayInUsdt.name to BigDecimal.ZERO,
                                OptionOrder::premiumFeePayInUsd.name to BigDecimal.ZERO
                            )
                        )
                        optionOrderService.createSystemOptionOrderRebateRecord(oo)
                    }

                    // 提交空间宝石订单
                    if(ooi.channel == UserChannel.BTC_FI_BACKEND){
                        updateFields.putAll(
                            mapOf(
                                OptionOrder::usedTimeStone.name to true,
                                OptionOrder::stoneActivityNftId.name to "11",
                            )
                        )
                        optionOrderService.createSystemOptionOrderRebateRecord(oo)
                    }

                    optionOrderService.updateFields(updateFields, oo.id!!)

                    // 处理 TGE 活动
                    eventPublisher.publishEvent(CheckTgeCampaignOrderEvent(this, oo.id))
                }

                ooi.synced = true
                optionOrderInfoRepository.save(ooi)

            } catch (e: Exception) {
                logger.error("Failed to update option order channel info: ${ooi.id}", e)
            }
        }
    }

    private fun updateNullChannel() {

        val endTime = LocalDateTime.now().minusMinutes(5)

        val query = Query()
        query.addCriteria(Criteria.where(OptionOrder::channel.name).isNull)
        query.addCriteria(Criteria.where(OptionOrder::created.name).gte(startTime).lte(endTime))

        // 设置返回的文档数量限制
        query.limit(batchCount)

        // 使用 mongoTemplate 执行查询
        val results: List<OptionOrder> = mongoTemplate.find(query, OptionOrder::class.java)

        //logger.info("Update Null Channel for ${results.size} option order")

        results.forEach { oo ->
            try {

                optionOrderService.updateFields(
                    mapOf(
                        OptionOrder::channel.name to UserChannel.JASPER_VAULT
                    ),
                    oo.id!!
                )

            } catch (e: Exception) {
                logger.error("Failed to update option order product null channel: ${oo.id}", e)
            }
        }
    }

    private fun updateNullVolume() {

        val endTime = LocalDateTime.now().minusMinutes(5)

        val query = Query()
        query.addCriteria(Criteria.where(OptionOrder::volume.name).isNull)
        query.addCriteria(Criteria.where(OptionOrder::created.name).gte(startTime).lte(endTime))

        // 设置返回的文档数量限制
        query.with(Sort.by(Sort.Order.desc(OptionOrder::created.name)))
        query.limit(batchCount)

        // 使用 mongoTemplate 执行查询
        val results: List<OptionOrder> = mongoTemplate.find(query, OptionOrder::class.java)

        logger.info("Update Null Volume for ${results.size} option order")

        results.forEach { oo ->
            try {

                val volume = optionOrderService.calculateOptionOrderVolume(oo)

                optionOrderService.updateFields(
                    mapOf(
                        OptionOrder::volume.name to volume
                    ),
                    oo.id!!
                )

            } catch (e: Exception) {
                logger.error("Failed to update option order product null channel: ${oo.id}", e)
            }
        }
    }

    private fun updateProduct() {

        val endTime = LocalDateTime.now().minusSeconds(delayTime.toLong())

        val query = Query()
        query.addCriteria(Criteria.where(OptionOrder::product.name).isNull)
        query.addCriteria(Criteria.where(OptionOrder::channel.name).ne(null))
        query.addCriteria(Criteria.where(OptionOrder::created.name).gte(startTime).lte(endTime))

        // 设置返回的文档数量限制
        query.limit(batchCount)

        // 使用 mongoTemplate 执行查询
        val results: List<OptionOrder> = mongoTemplate.find(query, OptionOrder::class.java)

        //logger.info("Update Product for ${results.size} executed option order")

        results.forEach { oo ->
            try {
                val product = optionOrderService.getOptionOrderProduct(
                    oo.channel!!,
                    oo
                )
                optionOrderService.updateFields(
                    mapOf(
                        OptionOrder::product.name to product
                    ),
                    oo.id!!
                )

                // 处理 TGE 活动
                eventPublisher.publishEvent(CheckTgeCampaignOrderEvent(this, oo.id))

            } catch (e: Exception) {
                logger.error("Failed to update option order product info: ${oo.id}", e)
            }
        }
    }

    private fun updateRoi(){

        val profitStartTime = LocalDateTime.of(2024, 12, 10, 0, 0, 0)

        val baseCriteria = Criteria.where(OptionOrder::status.name).`is`(OptionStatus.SETTLED)
        val query = Query().addCriteria(baseCriteria)
            .addCriteria(Criteria.where(OptionOrder::roi.name).isNull)
            .addCriteria(Criteria.where(OptionOrder::expiryDate.name).gte(profitStartTime))

        query.with(Sort.by(Sort.Order.desc(OptionOrder::created.name)))
        query.limit(batchCount)

        val orders = mongoTemplate.find(query, OptionOrder::class.java)

        //logger.info("Update ROI for ${orders.size} settled option orders")

        orders.forEach { oo ->
            //logger.info("更新订单 ROI on chain order id: ${oo.onChainOrderId}")
            val settlementPrice = (oo.marketPriceAtSettlement ?: return@forEach).toBigDecimal()
            val bidAmount = oo.bidAmount ?: return@forEach
            val o = orderRepository.findByIdOrNull(oo.orderId) ?: return@forEach
            val strikePrice = o.strikePrice
            val premiumAsset = Symbol.valueOf(o.availablePremiumAssets.firstOrNull()?.asset ?: "USDT")
            val premiumAssetDecimals = currencyService.getCurrencyDecimal(o.chain, premiumAsset)
            val premiumFee = if (oo.premiumAsset != null && oo.premiumAsset!!.asset != "USDT") {
                o.premiumFee.movePointLeft(premiumAssetDecimals) * strikePrice.movePointLeft(18)
            } else {
                o.premiumFee.movePointLeft(premiumAssetDecimals)
            }
            val priceDiff = if (oo.direction == OptionDirection.CALL) {
                settlementPrice - strikePrice
            } else {
                strikePrice - settlementPrice
            }
            var grossProfit = (priceDiff.movePointLeft(18) * bidAmount).setScale(18, RoundingMode.FLOOR)
            //logger.info("GrossProfit = ${priceDiff}.movePointLeft(18) * $bidAmount = $grossProfit, premiumFee = $premiumFee")

            if(grossProfit < BigDecimal.ZERO){
                grossProfit = BigDecimal.ZERO
            }

            var roi = BigDecimal.ZERO
            if(premiumFee > BigDecimal.ZERO) {
                roi = grossProfit.divide(premiumFee, 4, RoundingMode.CEILING) // 小数
            }

            //logger.info("订单 ROI: ${oo.onChainOrderId} gross profit $grossProfit roi $roi")
            optionOrderService.updateFields(
                mapOf(
                    OptionOrder::grossProfit.name to grossProfit,
                    OptionOrder::roi.name to roi.max(BigDecimal.ZERO)
                ),
                oo.id!!
            )
        }
    }

    private fun updateFailedOrders() {

        if(ProfileUtil.activeProfile != "prod"){
            return
        }

        val endTime = LocalDateTime.now().minusMinutes(15)

        val query = Query()
        query.addCriteria(Criteria.where(OptionOrder::status.name).`in`(
            listOf(
                OptionStatus.SETTLE_FAILED,
                OptionStatus.SETTLE_TO_BE_CONFIRMED
            )
        ))

        query.addCriteria(Criteria.where(OptionOrder::expiryDate.name).gte(startTime))
        query.addCriteria(Criteria.where(OptionOrder::updated.name).lte(endTime))
        // 设置返回的文档数量限制
        query.limit(batchCount)

        // 使用 mongoTemplate 执行查询
        val results: List<OptionOrder> = mongoTemplate.find(query, OptionOrder::class.java)

        if(results.isNotEmpty()) {
            //logger.info("Update Failed for ${results.size} option order")
        }

        results.forEach { oo ->
            try {

                optionOrderService.updateFields(
                    mapOf(
                        OptionOrder::status.name to OptionStatus.EXECUTED,
                        OptionOrder::settlementHash.name to null,
                        OptionOrder::updated.name to LocalDateTime.now()
                    ),
                    oo.id!!
                )

            } catch (e: Exception) {
                logger.error("Failed to update option order failed status: ${oo.id}", e)
            }
        }
    }

    fun updatePremiumFeeDistribution() {
        val endTime = LocalDateTime.now().minusSeconds(delayTime.toLong())

        try {

            // 构建查询条件
            val criteria = Criteria.where("status").`in`(OptionStatus.executedStatusList())
                .and(OptionOrder::sellerPremiumFee.name).`is`(null)
                .and(OptionOrder::txHash.name).ne(null)
                .and(OptionOrder::buyerVault.name).ne(null)
                .and(OptionOrder::sellerVault.name).ne(null)
                .and(OptionOrder::premiumAsset.name).ne(null)

            val query = Query(criteria)

            query.addCriteria(Criteria.where(OptionOrder::expiryDate.name).gte(startTime).lte(endTime))
                .with(PageRequest.of(0, batchCount))

            val orders = mongoTemplate.find(query, OptionOrder::class.java)
            if (orders.isEmpty()) {
                return
            }

            //logger.info("Found ${orders.size} orders to update premium fee distribution")

            orders.forEach { oo ->
                try {
                    val blockchainService = blockchainServiceFactory.getBlockchainService(oo.chain)
                    if (blockchainService !is EvmService) {
                        logger.info("Not an EVM chain: ${oo.chain}")
                        return@forEach
                    }

                    val distribution = blockchainService.getPremiumFeeDistribution(
                        txHash = oo.txHash!!,
                        buyerVault = oo.buyerVault!!,
                        sellerVault = oo.sellerVault!!,
                        premiumAssetAddress = oo.premiumAsset!!.address!!
                    )

                    if (distribution != null) {
                        val savedOo = optionOrderService.updatePremiumFeeDistribution(oo, distribution)
//                        logger.info(
//                            "Updated premium fee distribution for order ${oo.id}: " +
//                                    "seller=${distribution.sellerPremiumFee}(${savedOo?.sellerPremiumFeeInUsdt} USDT), " +
//                                    "platform=${distribution.platformPremiumFee}(${savedOo?.platformPremiumFeeInUsdt} USDT)"
//                        )

                    } else {
                        //logger.warn("Failed to get premium fee distribution for order ${oo.id}")
                    }
                } catch (e: Exception) {
                    logger.error("Error processing premium fee distribution for order ${oo.id}", e)
                }
            }
        } catch (e: Exception) {
            logger.error("Error in premium fee update task", e)
        }
    }

    private fun updateNullLockDate() {

        val query = Query()
        query.addCriteria(Criteria.where(OptionOrder::lockDate.name).isNull)
        query.addCriteria(Criteria.where(OptionOrder::expiryDate.name).ne(null))

        // 设置返回的文档数量限制
        query.limit(batchCount)

        // 使用 mongoTemplate 执行查询
        val results: List<OptionOrder> = mongoTemplate.find(query, OptionOrder::class.java)

        //logger.info("Update Null LockDate for ${results.size} option order")

        results.forEach { oo ->
            try {
                optionOrderService.updateFields(
                    mapOf(
                        OptionOrder::lockDate.name to oo.expiryDate
                    ),
                    oo.id!!
                )

            } catch (e: Exception) {
                logger.error("Failed to update option order null lock date: ${oo.id}", e)
            }
        }
    }

    /**
     * 更新订单 PremiumSign 信息
     */
    private fun updatePremiumSignInfo() {

        // 合约升级后的时间
        val startDate = LocalDateTime.of(2024, 12, 10, 0, 0, 0)
        val endDate = LocalDateTime.now().minusMinutes(5)

        val query = Query()
        query.addCriteria(Criteria.where(OptionOrder::premiumSignInfo.name).isNull)
        query.addCriteria(Criteria.where(OptionOrder::expiryDate.name).gt(startDate).lt(endDate))

        // 设置返回的文档数量限制
        query.with(Sort.by(Sort.Order.desc(OptionOrder::created.name)))
        query.limit(batchCount)

        // 使用 mongoTemplate 执行查询
        val results: List<OptionOrder> = mongoTemplate.find(query, OptionOrder::class.java)

        //logger.info("Update Premium Sign Info for ${results.size} option order")

        results.forEach { oo ->
            try {

                val evmService = blockchainServiceFactory.getBlockchainService(oo.chain) as EvmService

                val receipt = try {
                    evmService.evmUtil.web3j.ethGetTransactionReceipt(oo.txHash!!).send().transactionReceipt.get()
                } catch (e: Exception) {
                    logger.error("Failed to get transaction receipt: ${e.message}", e)
                    return@forEach
                }

                //Sync premium sign info from chain
                val result = evmService.parseStrikePriceInfoFromLog(
                    receipt.logs,
                    oo.onChainOrderId!!.toBigInteger()
                )

                if(result == null){
                    logger.warn("Failed to decode strike price from log for order ${oo.id}")
                    return@forEach
                }

                val strikePrice = result.first
                val validTimestamp = result.second

                // 价格取timestamp前60秒价格
                val timestamp = validTimestamp - 60

                //Get Price from oracle
                var price = jasperVaultService.getOraclePriceAtDate(
                    oo.chain,
                    oo.bidAsset!!,
                    DateTimeUtil.convertTimestampToLocalDateTime(timestamp)
                )

                if(price == null){
                    price = strikePrice
                }
                
                val premiumSignInfo = PremiumSignInfo(
                    price = price,
                    timestamp = timestamp,
                    validTimestamp = validTimestamp
                )

                optionOrderService.updateFields(
                    mapOf(
                        OptionOrder::premiumSignInfo.name to premiumSignInfo
                    ),
                    oo.id!!
                )

            } catch (e: Exception) {
                logger.error("Failed to update option order premium sign: ${oo.id}", e)
            }
        }
    }

    /**
     * 更新订单 Distance 信息
     */
    private fun updateDistanceInfo() {

        // 合约升级后的时间
        val startDate = LocalDateTime.of(2024, 12, 10, 0, 0, 0)
        val endDate = LocalDateTime.now().minusMinutes(5)
        val apiLaunchDate = LocalDateTime.of(2025, 4, 11, 0, 0, 0)

        val query = Query()
        query.addCriteria(Criteria.where(OptionOrder::distance.name).isNull)
        query.addCriteria(Criteria.where(OptionOrder::expiryDate.name).gt(startDate).lt(endDate))

        // 设置返回的文档数量限制
        query.with(Sort.by(Sort.Order.desc(OptionOrder::created.name)))
        query.limit(batchCount)

        // 使用 mongoTemplate 执行查询
        val results: List<OptionOrder> = mongoTemplate.find(query, OptionOrder::class.java)

        //logger.info("Update Distance Info for ${results.size} option order")

        results.forEach { oo ->
            try {

                val evmService = blockchainServiceFactory.getBlockchainService(oo.chain) as EvmService
                val chainId = evmService.evmUtil.chainId

                val distanceData = marketplaceApiService.checkOrderDistance(
                    chainId,
                    oo.onChainOrderId!!.toLong()
                )

                if(oo.expiryDate.isAfter(apiLaunchDate) && distanceData != null && distanceData.orderId != 0L){

                    optionOrderService.updateFields(
                        mapOf(
                            OptionOrder::distance.name to BigDecimal(distanceData.distance.absoluteValue)
                        ),
                        oo.id!!
                    )
                } else if(oo.premiumSignInfo != null && oo.strikePrice != null) {

                    // Check premium sign info
                    val premiumSignInfo = oo.premiumSignInfo!!
                    val strikePrice = oo.strikePrice!!.movePointLeft(18)

                    val distance = (strikePrice - premiumSignInfo.price).divide(premiumSignInfo.price, 6, RoundingMode.CEILING)

                    optionOrderService.updateFields(
                        mapOf(
                            OptionOrder::distance.name to distance.abs()
                        ),
                        oo.id!!
                    )
                }

            } catch (e: Exception) {
                logger.error("Failed to update option order distance : ${oo.id}", e)
            }
        }
    }

    /**
     * 更新订单 OTM 信息
     */
    private fun updateOTMInfo() {

        val endTime = LocalDateTime.now().minusSeconds(delayTime.toLong())

        val query = Query()
        query.addCriteria(Criteria.where(OptionOrder::otmOrder.name).isNull)
        query.addCriteria(Criteria.where(OptionOrder::distance.name).ne(null))
        query.addCriteria(Criteria.where(OptionOrder::created.name).lte(endTime))

        // 设置返回的文档数量限制
        query.with(Sort.by(Sort.Order.desc(OptionOrder::created.name)))
        query.limit(batchCount)

        // 使用 mongoTemplate 执行查询
        val results: List<OptionOrder> = mongoTemplate.find(query, OptionOrder::class.java)

        //logger.info("Update OTM Order Info for ${results.size} option order")

        results.forEach { oo ->
            try {

                var otmOrder = false
                if(oo.distance!! >= BigDecimal("0.001") &&
                    !oo.limitOrder &&
                    oo.product == OptionOrderProduct.MARKET_PLACE){
                    otmOrder = true
                }

                optionOrderService.updateFields(
                    mapOf(
                        OptionOrder::otmOrder.name to otmOrder
                    ),
                    oo.id!!
                )

            } catch (e: Exception) {
                logger.error("Failed to update option order OTM : ${oo.id}", e)
            }
        }
    }
}