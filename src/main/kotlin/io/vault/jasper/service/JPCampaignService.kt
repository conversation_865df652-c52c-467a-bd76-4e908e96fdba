package io.vault.jasper.service

import io.vault.jasper.enums.ChainType
import io.vault.jasper.enums.OptionDirection
import io.vault.jasper.enums.Symbol
import io.vault.jasper.model.*
import io.vault.jasper.repository.*
import org.slf4j.LoggerFactory
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.data.repository.findByIdOrNull
import org.springframework.stereotype.Service
import java.math.BigDecimal
import java.time.LocalDateTime

@Service
class JPCampaignService @Autowired constructor(
    private val userRepository: UserRepository,
    private val optionOrderRepository: OptionOrderRepository,
    private val orderRepository: OrderRepository,
    private val jpCampaignParameterRepository: JPCampaignParameterRepository,
    private val jpFirstTradeRebateRecordRepository: JPFirstTradeRebateRecordRepository,
    private val userJPCampaignInfoRepository: UserJPCampaignInfoRepository
){

    private val logger = LoggerFactory.getLogger(this::class.java)

    /**
     * 获取参数对象
     */
    fun getParameter(): JPCampaignParameter {
        val params = jpCampaignParameterRepository.findAll()

        return if (params.isEmpty()) {
            val p = JPCampaignParameter(null)
            jpCampaignParameterRepository.save(p)
        } else {
            params.first()
        }
    }

    fun getJPCampaignInfo(
        user: User
    ): UserJPCampaignInfo{

        var info = userJPCampaignInfoRepository.findByAddressIgnoreCase(user.address)
        if(info == null){
            info = UserJPCampaignInfo(
                userId = user.id!!,
                address = user.address
            )
            info = userJPCampaignInfoRepository.save(info)
        }

        return info!!
    }

    /**
     * 判断是否活动内首单
     */
    fun isFirstOrderInJPCampaign(
        optionOrder: OptionOrder
    ): Boolean{

        val parameter = getParameter()

        val optionOrders = optionOrderRepository.findByBuyerIgnoreCaseAndStatusIn(
            optionOrder.buyer!!,
            listOf(OptionStatus.SETTLED, OptionStatus.EXECUTED, OptionStatus.SETTLE_FAILED)
        ).sortedBy { it.id }

        if(optionOrders.isEmpty()){
            return false
        }

        if(optionOrders[0].id!! != optionOrder.id!!){
            return false
        }

        if(optionOrder.created.isBefore(parameter.startDate)){
            return false
        }

        if(optionOrder.chain != ChainType.ARBITRUM){
            return false
        }

        val user = userRepository.findByAddressIgnoreCase(optionOrder.buyer!!)
        if(user == null || user.created.isBefore(parameter.startDate)){
            return false
        }

        return true
    }

    fun createTradeRebateRecord(
        optionOrder: OptionOrder
    ){

        val rebateCount = getFirstTradeRebateCount()
        if( rebateCount >= 10000){
            logger.info("JP Campaign first trade rebate task reach max count")
            return
        }

        // 符合 0.5H，0.01 WBTC 规格的订单
        if(optionOrder.bidAsset == null || optionOrder.bidAsset != Symbol.WBTC){
            logger.debug("JP Campaign Service Not WBTC ${optionOrder.buyer} ${optionOrder.id}")
            return
        }

        if(optionOrder.expiryInHour == null || optionOrder.expiryInHour != "0.5"){
            logger.debug("JP Campaign Service Not 0.5 hours ${optionOrder.buyer} ${optionOrder.id}")
            return
        }

        if(optionOrder.bidAmount == null || optionOrder.bidAmount!!.compareTo(BigDecimal("0.01")) != 0){
            logger.debug("JP Campaign Service Not 0.01 WBTC ${optionOrder.buyer} ${optionOrder.id}")
            return
        }

        // 是否已经创建了订单
        val existingRecord = jpFirstTradeRebateRecordRepository.findByOptionOrderId(
            optionOrder.id!!
        )

        if(existingRecord != null){
            logger.info("JP Campaign Service Already add rebate record ${optionOrder.buyer} ${optionOrder.id}")
            return
        }

        // 是否有同样地址的订单
        val existingAddressRecords = jpFirstTradeRebateRecordRepository.findByBuyerAddressIgnoreCase(
            optionOrder.buyer!!
        )

        if(existingAddressRecords.isNotEmpty()){
            logger.info("JP Campaign Service Already same address rebate record ${optionOrder.buyer} ${optionOrder.id}")
            return
        }

        val record = JPFirstTradeRebateRecord(
            optionOrderId = optionOrder.id,
            buyerAddress = optionOrder.buyer!!,
            direction = optionOrder.direction!!,
            bidAmount = optionOrder.bidAmount!!,
        )

        jpFirstTradeRebateRecordRepository.save(record)
    }

    fun checkJPCampaignTradeRebate(
        record: JPFirstTradeRebateRecord
    ){

        val optionOrder = optionOrderRepository.findByIdOrNull(record.optionOrderId)
        if(optionOrder == null){
            logger.info("JP Campaign Service can not find order record ${record.buyerAddress} ${record.optionOrderId}")
            return
        }

        if(optionOrder.status == OptionStatus.SETTLE_FAILED){
            logger.info("JP Campaign Service order settle failed ${optionOrder.buyer} ${optionOrder.id}")
            record.status = JPFirstTradeRebateRecordStatus.SETTLE_FAILED
            jpFirstTradeRebateRecordRepository.save(record)
            return
        }

        if(optionOrder.status != OptionStatus.SETTLED){
            logger.info("JP Campaign Service Not Settled order ${optionOrder.buyer} ${optionOrder.id}")
            return
        }

        logger.debug("JP Campaign Service Check Airdrop First Trade Rebate ${optionOrder.buyer} ${optionOrder.id}")

        //是否是新用户
        val parameter = getParameter()
        val user = userRepository.findByAddressIgnoreCase(optionOrder.buyer!!)
        if(user == null || user.created.isBefore(parameter.startDate)){
            logger.debug("JP Campaign Service not new user in airdrop ${optionOrder.buyer} ${optionOrder.id}")
            return
        }

        val order = orderRepository.findByIdOrNull(optionOrder.orderId)
        if(order == null){
            logger.debug("JP Campaign Service can not find order record ${optionOrder.buyer} ${optionOrder.id}")
            return
        }

        record.strikePrice = order.strikePrice.movePointLeft(18)
        record.settlementPrice = BigDecimal(optionOrder.marketPriceAtSettlement!!).movePointLeft(18)

        record.premiumFee = optionOrder.premiumFeePay!!
        record.premiumAsset = Symbol.valueOf(optionOrder.premiumAsset!!.asset)
        record.premiumFeeInUsdt = optionOrder.premiumFeePayInUsdt!!

        record.profit = optionOrder.buyerProfit!!
        record.profitAsset = Symbol.USDT
        record.profitInUsdt = optionOrder.buyerProfit!!

        if(optionOrder.direction == OptionDirection.CALL){
            record.profitAsset = optionOrder.bidAsset!!
            var profitInUsdt = record.profit.movePointLeft(18)
            logger.debug("JP Campaign Service Profit in ${optionOrder.buyer} ${record.profitAsset} ${profitInUsdt}")
            profitInUsdt = profitInUsdt.multiply(record.settlementPrice)
            logger.debug("JP Campaign Service Profit in ${optionOrder.buyer} ${record.settlementPrice} ${profitInUsdt}")
            profitInUsdt = profitInUsdt.movePointRight(6).setScale(0, BigDecimal.ROUND_HALF_UP)
            logger.debug("JP Campaign Service Profit in ${optionOrder.buyer} ${record.settlementPrice} ${profitInUsdt}")
            record.profitInUsdt = profitInUsdt
        }

        record.netProfit = record.profitInUsdt.subtract(record.premiumFeeInUsdt)
        if(record.netProfit.compareTo(BigDecimal.ZERO) == -1){
            record.rebateAmount = record.netProfit.multiply(BigDecimal("-1"))
            record.status = JPFirstTradeRebateRecordStatus.CREATED
        } else {
            record.status = JPFirstTradeRebateRecordStatus.SETTLED
            record.settled = true
        }

        jpFirstTradeRebateRecordRepository.save(record)
    }

    fun getFirstTradeRebateCount(): Int {
        return jpFirstTradeRebateRecordRepository.findAll().size + getParameter().additionalRebateCount
    }

    fun handleJPArbitrumOptionOrder(optionOrder: OptionOrder) {
        val rebateCount = getFirstTradeRebateCount()
        if( rebateCount >= 10000){
            logger.info("JP Campaign first trade rebate task reach max count")
            return
        }

        if(!isFirstOrderInJPCampaign(optionOrder)){
            logger.info("JP Campaign Option order is not first order in campaign for buyer: ${optionOrder.buyer}")
            return
        }

        logger.info("JP Campaign trade rebate for option order: ${optionOrder.id}")
        try {
            createTradeRebateRecord(optionOrder)
        } catch (e: Exception) {
            logger.error("JP Campaign first trade failed, optionOrderId: ${optionOrder.id}", e)
        }
    }
}