package io.vault.jasper.task

import com.fasterxml.jackson.databind.ObjectMapper
import io.vault.jasper.enums.ChainType
import io.vault.jasper.enums.OptionDirection
import io.vault.jasper.event.OptionSettlementSuccessEvent
import io.vault.jasper.event.OptionSettlementEvent
import io.vault.jasper.model.OptionOrder
import io.vault.jasper.model.OptionStatus
import io.vault.jasper.model.OrderStatus
import io.vault.jasper.model.OrderType
import io.vault.jasper.repository.BlockchainRepository
import io.vault.jasper.repository.OptionOrderRepository
import io.vault.jasper.repository.OrderRepository
import io.vault.jasper.service.*
import io.vault.jasper.service.blockchain.AsyncEvmService
import io.vault.jasper.service.blockchain.BlockchainServiceFactory
import io.vault.jasper.service.blockchain.EvmService
import io.vault.jasper.utils.DateTimeUtil
import io.vault.jasper.utils.ProfileUtil
import org.slf4j.LoggerFactory
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.beans.factory.annotation.Value
import org.springframework.context.ApplicationEventPublisher
import org.springframework.context.annotation.Profile
import org.springframework.data.domain.PageRequest
import org.springframework.data.domain.Sort
import org.springframework.data.repository.findByIdOrNull
import org.springframework.scheduling.annotation.Scheduled
import org.springframework.stereotype.Service
import org.springframework.web.client.HttpClientErrorException
import org.web3j.crypto.Keys
import org.web3j.utils.Numeric
import java.math.BigDecimal
import java.math.BigInteger
import java.time.Instant
import java.time.LocalDateTime


@Service
@Profile("prod", "test", "dev")
class EvmSettlement @Autowired constructor(
    private val orderRepository: OrderRepository,
    private val optionOrderRepository: OptionOrderRepository,
    private val blockchainService: BlockchainService,
    private val jasperVaultService: JasperVaultService,
    private val monitorService: MonitorService,
    private val blockchainServiceFactory: BlockchainServiceFactory,
    private val currencyService: CurrencyService,
    private val blockchainRepository: BlockchainRepository,
    private val subgraphService: SubgraphService,
    private val historyService: HistoryService,
    private val optionOrderService: OptionOrderService,
    private val asyncEvmService: AsyncEvmService,
    private val eventPublisher: ApplicationEventPublisher,
    private val lossIsWinUserSummaryService: LossIsWinUserSummaryService,
    private val alertService: AlertService
) {

    private val logger = LoggerFactory.getLogger(this::class.java)

    private val objectMapper = ObjectMapper()

    @Value("\${settlement_wallet}")
    private lateinit var settlementWalletKey: String

    @Value("\${schedule_task_on}")
    lateinit var scheduleOn: String

    private val bitlayerSettleCount = 5

    private val startDate = LocalDateTime.of(2024, 12, 10, 0, 0, 0)

    /**
     * 最后发起监控的时间(second)
     */
    private var monitorTimestamp: Long = 0

    /**
     * 结算接口
     */
    private val settlementUrls = listOf(
        "https://worker.jaspervault.io/api/v2/pyth_service",
        "https://jaspervault-worker.fly.dev/api/v2/pyth_service"
    )

    @Scheduled(fixedDelay = 2 * 1000, initialDelay = 5 * 1000)
    fun settleBitlayer() {
        if (scheduleOn == "false") return
        // 生产环境结算 Bitlayer 链， 测试环境结算 Bitlayer 测试链
        if (ProfileUtil.activeProfile != "prod" && ProfileUtil.activeProfile != "dev") return

        val expiryDatetime = LocalDateTime.now().minusSeconds(15) // 超过结算时间15s才开始结算
        val pageable = PageRequest.of(0, 50, Sort.by(Sort.Order.asc(OptionOrder::id.name)))
        val ordersPage = optionOrderRepository.findByChainAndStatusAndExpiryDateLessThan(
            ChainType.BITLAYER,
            OptionStatus.EXECUTED,
            expiryDatetime,
            pageable
        )
        val orders = ordersPage.content
        //logger.info("${orders.size} ${ChainType.BITLAYER} orders are going to be settled")

        var urlIndex = 0
        val futures = orders.map { oo ->
            //logger.info("Processing ${ChainType.BITLAYER} order ${oo.id}, url=${settlementUrls[urlIndex]}")
            val result = asyncEvmService.settleOrder(oo, settlementUrls[urlIndex])
            urlIndex = (urlIndex + 1) % settlementUrls.size
            result
        }
        futures.map {
            try {
                it.get()
            } catch (e: Exception) {
                logger.error("BitLayer 结算失败: ", e)
            }
        }

        taskMonitor()
    }

//    @Scheduled(fixedDelay = 5 * 1000, initialDelay = 10 * 1000)
//    fun settleBitlayer2() {
//
//        if(scheduleOn == "false"){
//            return
//        }
//
//        if (ProfileUtil.activeProfile != "prod") return
//        //optionOrderRepository.findByChainAndStatus(ChainType.BITLAYER, OptionStatus.EXECUTED).forEach { settleOrder(it) }
//        //monitorService.taskMonitor(this::class.simpleName)
//
//        val notSettledOrders = optionOrderRepository.findByChainAndStatusAndExpiryDateLessThan(
//            ChainType.BITLAYER,
//            OptionStatus.EXECUTED,
//            LocalDateTime.now()
//        ).sortedByDescending { it.expiryDate }
//
//        var count = 0
//        for(order in notSettledOrders){
//            count++
//            logger.info("EVM Settlement: Processing Bitlayer Descending $count / ${notSettledOrders.size}")
//            settleOrder(order)
//            if(count >= bitlayerSettleCount){
//                break
//            }
//        }
//    }

    @Scheduled(fixedDelay = 2 * 1000, initialDelay = 6 * 1000)
    fun settleOthers() {
        if(scheduleOn == "false"){
            return
        }

        if (ProfileUtil.activeProfile != "prod" && ProfileUtil.activeProfile != "dev") return

        val expiryDatetime = LocalDateTime.now().minusSeconds(15) // 超过结算时间15s才开始结算
        val pageable = PageRequest.of(0, 50, Sort.by(Sort.Order.asc(OptionOrder::id.name)))
        val ordersPage = when (ProfileUtil.activeProfile) {
            "prod" -> {
                // 生产环境：只结算SEI和BITLAYER以外的链
                optionOrderRepository.findByChainNotInAndStatusAndExpiryDateLessThan(
                    listOf(ChainType.SEI, ChainType.BITLAYER),
                    OptionStatus.EXECUTED,
                    expiryDatetime,
                    pageable
                )
            }
            "dev" -> { // dev环境
                optionOrderRepository.findByChainInAndStatusAndExpiryDateLessThan(
                    listOf(ChainType.BASE, ChainType.ARBITRUM),
                    OptionStatus.EXECUTED,
                    expiryDatetime,
                    pageable
                )
            }
            else -> { // UAT环境
                optionOrderRepository.findByChainInAndStatusAndExpiryDateLessThan(
                    listOf(ChainType.BASE, ChainType.BSC),
                    OptionStatus.EXECUTED,
                    expiryDatetime,
                    pageable
                )
            }
        }

        val orders = ordersPage.content
        //logger.info("${orders.size} orders are going to be settled")

        var urlIndex = 0
        val futures = orders.map { oo ->
            val result = asyncEvmService.settleOrder(oo, settlementUrls[urlIndex])
            urlIndex = (urlIndex + 1) % settlementUrls.size
            result
        }
        futures.map {
            try {
                it.get()
            } catch (e: Exception) {
                logger.error("结算失败: ", e)
            }
        }

        taskMonitor()
    }

    @Synchronized
    private fun taskMonitor() {
        val nowTs = Instant.now().epochSecond
        if (nowTs - monitorTimestamp < 5) return
        try {
            monitorService.taskMonitor(this::class.simpleName)
        } catch (e: HttpClientErrorException) {
            logger.warn("Failed to monitor task(${this::class.simpleName})", e)
        } finally {
            monitorTimestamp = nowTs
        }
    }

    private fun settleOrder(oOrder: OptionOrder) {
        var mutableOptionOrder = oOrder
        val startTime = LocalDateTime.of(2024, 9, 1, 0, 0, 0)
        val upgradeTime = LocalDateTime.of(2024, 12, 9, 3, 0, 0)
        if (mutableOptionOrder.expiryDate.isBefore(startTime)) return

        val now = LocalDateTime.now()
        now.minusSeconds(15) // 延迟15秒执行

        if (mutableOptionOrder.expiryDate <= now) {
            logger.info("EVM Settlement: 已到期的期权合约: ${mutableOptionOrder.id} upgrade time $upgradeTime")

            val alreadyHash = getSettlementHashFromOrderID(mutableOptionOrder)
            if (alreadyHash != null) {
                logger.info("EVM Settlement: 已到期的期权合约: ${mutableOptionOrder.id} ${mutableOptionOrder.onChainOrderId} 到 Subgraph 查询结算 Hash: $alreadyHash")
                optionOrderService.updateFields(
                    mapOf(
                        OptionOrder::settlementHash.name to alreadyHash,
                        OptionOrder::status.name to OptionStatus.SETTLE_TO_BE_CONFIRMED
                    ),
                    mutableOptionOrder.id!!
                )
                return
            }

            logger.info("EVM Settlement: 已到期的期权合约: ${mutableOptionOrder.id} ${mutableOptionOrder.onChainOrderId} 需要结算")
            val evmService = blockchainServiceFactory.getBlockchainService(mutableOptionOrder.chain) ?: run {
                logger.error("${mutableOptionOrder.chain} Service not found, Skipped")
                return
            }
            if (evmService !is EvmService) {
                logger.error("${mutableOptionOrder.chain} Settlement Not supported yet, Skipped")
                return
            }
            // 开始结算
            logger.info("EVM Settlement: Start to settle option order: ${mutableOptionOrder.id}")
            val order = orderRepository.findByIdOrNull(mutableOptionOrder.orderId) ?: run {
                val msg = "Order not found: ${mutableOptionOrder.orderId}"
                logger.warn(msg)
                optionOrderService.updateFields(
                    mapOf(
                        OptionOrder::status.name to OptionStatus.SETTLE_FAILED,
                        OptionOrder::errorMsg.name to msg
                    ),
                    mutableOptionOrder.id!!
                )
                return
            }
            try {
                val onChainOrderId = mutableOptionOrder.onChainOrderId ?: throw Exception("On chain order ID not found")
                var tokens = listOf<String>()
                // 1. 获取实时价格，并将价格更新到链上
                logger.info("EVM Settlement: Get and set price for order: ${mutableOptionOrder.id}")
                val usdPrice = try {
                    //Degen 是利差结算，需要调用getAndSetPrice
                    if(mutableOptionOrder.orderType == OrderType.DEGEN) {
                        val bidAssetPythId = try {

                            if(mutableOptionOrder.chain == ChainType.BITLAYER || mutableOptionOrder.chain == ChainType.BITLAYER_TEST) {
                                currencyService.getAproId(mutableOptionOrder.bidAsset!!)
                            } else {
                                currencyService.getPriceId(mutableOptionOrder.bidAsset!!)
                            }

                        } catch (e: Exception) {
                            null
                        }

                        val quoteAssetPythId = try {

                            var quoteAsset = mutableOptionOrder.quoteAsset

                            if(quoteAsset == null) {
                                quoteAsset = currencyService.getOptionQuoteAsset(
                                    mutableOptionOrder.chain,
                                    mutableOptionOrder.bidAsset!!,
                                )
                            }

                            if(mutableOptionOrder.chain == ChainType.BITLAYER || mutableOptionOrder.chain == ChainType.BITLAYER_TEST) {
                                currencyService.getAproId(quoteAsset)
                            } else {
                                currencyService.getPriceId(quoteAsset)
                            }

                        } catch (e: Exception) {
                            null
                        }
                        tokens = listOfNotNull(bidAssetPythId, quoteAssetPythId)

                        // 如果是 PUT, Token 顺序要反过来
                        if(order.optionsType == OptionDirection.PUT) {
                            tokens = listOfNotNull(quoteAssetPythId, bidAssetPythId)
                        }

                        try {
                            jasperVaultService.getPythPriceAtDate(
                                currencyService.getPriceId(order.bidAsset!!),
                                mutableOptionOrder.expiryDate
                            )
                        } catch (e: Exception) {
                            null
                        }
                    } else {

                        try {
                            jasperVaultService.getPythPriceAtDate(
                                currencyService.getPriceId(order.bidAsset!!),
                                mutableOptionOrder.expiryDate
                            )
                        } catch (e: Exception) {
                            null
                        }
                    }
                } catch (e: Exception) {
                    val msg = "Failed to get and set price for order: ${mutableOptionOrder.id}, try again later"
                    // oOrder.errorMsg = msg
                    optionOrderService.updateFields(mapOf(OptionOrder::errorMsg.name to msg), mutableOptionOrder.id!!)
                    logger.error(msg, e)
                    return
//                } ?: run {
//                    logger.info("${order.bidAsset} get and set price not found")
//                    return
                }

                if(usdPrice != null) {
                    mutableOptionOrder = optionOrderService.updateFields(
                        mapOf(
                            OptionOrder::marketPriceAtSettlement.name to usdPrice.stripTrailingZeros().toPlainString()
                        ),
                        mutableOptionOrder.id!!,
                        returnUpdated = true
                    )!!
                }

                val liquidateMode = run {
                    val message = order.contractContent?.get("message")
                    if (message != null) {
                        val msgObj = objectMapper.readTree(message)
                        msgObj["liquidateMode"].asInt().toBigInteger()
                    } else {
                        mutableOptionOrder.liquidateMode?.toBigInteger()
                    } ?: throw Exception("Liquidate mode not found")
                }
                val liquidityType = when {
                    mutableOptionOrder.liquidityType == 0 -> BigInteger.ZERO // 清算
                    mutableOptionOrder.liquidateMode == "2" -> BigInteger.ONE // 实物交割
                    mutableOptionOrder.orderType == OrderType.DEGEN -> BigInteger("2") // 2024-07-31 修改，Degen 的订单总是返回2，即利差结算
                    else -> BigInteger.ZERO // SWAP时总是返回0，即不行权。行权动作由前端用户发起。
                }

                // 3. 结算
                logger.info("EVM Settlement: Liquidate option order: ${mutableOptionOrder.id}")
                val blockchainCfg = blockchainRepository.findFirstByChain(evmService.chainType)
                    ?: throw Exception("Blockchain config not found: ${evmService.chainType}")
                var network = evmService.chainType.toString().lowercase() // 结算接口需要的网络参数
                if (blockchainCfg.uat) {
                    network += "_uat"
                }
                val orderType = when (order.optionsType) {
                    OptionDirection.CALL -> 0
                    OptionDirection.PUT -> 1
                    else -> throw Exception("option type must not be null")
                }

                val expiryInHour = mutableOptionOrder.expiryInHour ?: "2"
                var totalPriceTime: Int? = null

                if(mutableOptionOrder.created < upgradeTime){
                    totalPriceTime = 1
                }

                val settlementHash = try {
                    blockchainService.liquidateOption2(
                        network = network,
                        tokens = tokens,
                        expirationDate = DateTimeUtil.convertLocalDateTimeToTimestamp(mutableOptionOrder.expiryDate) / 1000,
                        orderType = orderType,
                        orderID = onChainOrderId.toLong(),
                        liquidateType = liquidityType.toInt(),
                        expiryInHour = expiryInHour,
                        totalPriceTime = totalPriceTime,
                    )
                } catch (e: Exception) {
                    logger.error("Failed to liquidate option order: ${mutableOptionOrder.id}, try again later", e)
                    logger.info("Failed to liquidate option order: ${mutableOptionOrder.id}, error msg: ${e.message}")
                    // try again later
                    if(e.message != null &&
                        (e.message!!.contains("execution reverted: OptionService:optionOrder not exist") ||
                                e.message!!.contains("error code: 524"))
                        ) {

                        logger.info("Begin to get hash from subgraph for option order: ${mutableOptionOrder.id}")
                        // 到Subgraph 查询结算 Hash
                        val hash = try {
                            subgraphService.getSettlementFromOrderId(mutableOptionOrder.chain, mutableOptionOrder.onChainOrderId!!)
                        } catch (subgraphE: Exception) {
                            logger.info("${mutableOptionOrder.chain} 链上订单 ${mutableOptionOrder.id} ${mutableOptionOrder.onChainOrderId} 查询Subgraph 失败 ${subgraphE.message}")
                            throw e
                        }

                        if (!hash.isNullOrBlank()){
                            logger.info("${mutableOptionOrder.chain} 链上订单 ${mutableOptionOrder.id} ${mutableOptionOrder.onChainOrderId} 查询subgraph hash 为 $hash")
                            hash
                        } else {
                            logger.info("${mutableOptionOrder.chain} 链上订单 ${mutableOptionOrder.id} ${mutableOptionOrder.onChainOrderId} 查询subgraph hash 为空")
                            return
                        }

                    } else {
                        return
                    }
                }
                logger.info("EVM Settlement: OptionOrder=${mutableOptionOrder.id}\torder id=${mutableOptionOrder.onChainOrderId}\tSettlement hash: $settlementHash")

                // oOrder.settlementHash = settlementHash
                optionOrderService.updateFields(
                    mapOf(
                        OptionOrder::settlementHash.name to settlementHash,
                        OptionOrder::status.name to OptionStatus.SETTLE_TO_BE_CONFIRMED
                    ),
                    mutableOptionOrder.id!!
                )
            } catch (e: Exception) {
                logger.error("Failed to settle option order: ${mutableOptionOrder.id}", e)
                optionOrderService.updateFields(
                    mapOf(
                        OptionOrder::status.name to OptionStatus.SETTLE_FAILED,
                        OptionOrder::errorMsg.name to e.message
                    ),
                    mutableOptionOrder.id!!
                )
                order.status = OrderStatus.FAILED
                order.errorMsg = e.message
                orderRepository.save(order)
            }
        }
    }

    @Scheduled(fixedDelay = 5 * 1000, initialDelay = 5 * 1000)
    fun settleToBeConfirmed() {
        if(scheduleOn == "false"){
            return
        }
        try {
            exec()
        } catch (e: Exception) {
            logger.error(e.message)
        }
    }

    private fun exec() {
        optionOrderRepository.findByStatus(OptionStatus.SETTLE_TO_BE_CONFIRMED).forEach optionOrderLoop@{ oo ->
            try {
                val evmService = blockchainServiceFactory.getBlockchainService(oo.chain) ?: run {
                    logger.error("${oo.chain} Service not found, Skipped")
                    return@optionOrderLoop
                }
                if (evmService !is EvmService) {
                    logger.error("${oo.chain} Settlement Not supported yet, Skipped")
                    return@optionOrderLoop
                }
                if (oo.settlementHash.isNullOrBlank()) {
                    optionOrderService.updateFields(
                        mapOf(
                            OptionOrder::status.name to OptionStatus.SETTLE_FAILED,
                            OptionOrder::errorMsg.name to "Settlement hash is empty(${OptionStatus.SETTLE_TO_BE_CONFIRMED} -> ${OptionStatus.SETTLE_FAILED}"
                        ),
                        oo.id!!
                    )
                    return@optionOrderLoop
                }

                // 4. 检查结算交易是否成功
                var settlementSuccess = false
                val transaction = try {
                    evmService.evmUtil.web3j.ethGetTransactionByHash(oo.settlementHash).send().transaction.get()
                } catch (e: Exception) {
                    logger.warn("Failed to get transaction by hash: ${oo.settlementHash}, try again later", e)
                    // oo.status = OptionStatus.SETTLE_FAILED
                    // oo.errorMsg = "Failed to get transaction by hash: ${oo.settlementHash}"
                    // optionOrderRepository.save(oo)

                    return@optionOrderLoop
                }
                val newestBlockNumber = evmService.evmUtil.web3j.ethBlockNumber().send().blockNumber
                if (newestBlockNumber - transaction.blockNumber >= BigInteger("2")) {
                    val receipt = try {
                        evmService.evmUtil.web3j.ethGetTransactionReceipt(oo.settlementHash).send().transactionReceipt.get()
                    } catch (e: Exception) {
                        logger.info("Failed to get transaction receipt: ${e.message}, try again")
                        return@optionOrderLoop
                    }
                    when (receipt.status) {
                        // Success
                        "0x1" -> {
                            receipt.logs.forEach logsLoop@{
                                val liquidityEvent = it.topics.firstOrNull() ?: return@logsLoop

                                logger.info("The liquidity event hash for chain ${evmService.chainType} is ${evmService.liquidityEventHash}")

                                if (liquidityEvent.compareTo(evmService.liquidityEventHash, true) == 0) {
                                    val dataList = it.data.substring(2).chunked(64)
                                    val onChainOrderId = try {
                                        Numeric.toBigIntNoPrefix(dataList[1]).toString()
                                    } catch (e: Exception) {
                                        logger.error("${oo.chain} - 无法获取结算交易(${oo.settlementHash})的订单ID")
                                        return@logsLoop
                                    }
                                    if (onChainOrderId != oo.onChainOrderId) {
                                        logger.error("${oo.chain} - 结算交易(${oo.settlementHash})的订单ID(${onChainOrderId})与期权订单(${oo.onChainOrderId})不匹配")
                                        return@logsLoop
                                    }
                                    logger.info("结算交易(${oo.settlementHash})上链成功")
                                    settlementSuccess = true
                                    return@logsLoop
                                }
                            }
                        }
                        // Failed
                        else -> {
                            logger.error("结算交易(${oo.settlementHash})上链失败，error msg : ${receipt.revertReason} 将重新结算")
                            optionOrderService.updateFields(
                                mapOf(
                                    OptionOrder::status.name to OptionStatus.SETTLE_FAILED,
                                    OptionOrder::errorMsg.name to "Settlement transaction failed: receipt.status=${receipt.status}\t" +
                                            "Reason: ${receipt.revertReason}"
                                ),
                                oo.id!!
                            )
                            alertService.warning("[出现Error结算交易] Tx=${oo.settlementHash} is error, OrderID=${oo.id}") // 发送告警
                            return@optionOrderLoop
                        }
                    }

                    logger.info("EvmSettlement: ${oo.id}, ${oo.buyer}, 结算是否成功 $settlementSuccess")

                    // 更新利润
                    updateProfit(oo)

                    val updateFields = mutableMapOf<String, Any?>()
                    updateFields[OptionOrder::status.name] =
                        if (settlementSuccess) OptionStatus.SETTLED else OptionStatus.SETTLE_FAILED
                    updateFields[OptionOrder::errorMsg.name] =
                        if (!settlementSuccess) "Receipt is success but liquidity event not exist" else null
                    if (oo.status == OptionStatus.SETTLED) {
                        updateFields[OptionOrder::settlementTime.name] = kotlin.run {
                            val block = try {
                                evmService.evmUtil.web3j.ethGetBlockByHash(transaction.blockHash, false).send().block
                            } catch (e: Exception) {
                                logger.error("Failed to get block by hash: ${transaction.blockHash}", e)
                                return@run null
                            }
                            DateTimeUtil.convertTimestampToLocalDateTime(block.timestamp.toLong())
                        }
                    }
                    optionOrderService.updateFields(updateFields, oo.id!!)

                    // 更新Order状态
                    orderRepository.findByIdOrNull(oo.orderId)?.let { o ->
                        o.status = if (settlementSuccess) OrderStatus.COMPLETED else OrderStatus.FAILED
                        orderRepository.save(o)
                    }

                    historyService.addSettlementProfit(oo, evmService)

                    // 检查结算状态并发布事件
                    //if (oo.status == OptionStatus.SETTLED) {
                    if (settlementSuccess) {
                        logger.info("结算成功事件发布: ${oo.id}, ${oo.settlementHash}, ${oo.buyer}")
                        eventPublisher.publishEvent(OptionSettlementSuccessEvent(this, oo))
                    } else {
                        logger.info("结算失败事件发布: ${oo.id}, ${oo.settlementHash}, ${oo.buyer}")
                    }
                }
            } catch (e: Exception) {
                logger.error("Failed to check settlement transaction for order: ${oo.id}, ${oo.buyer}, ${e.message}", e)
            }
        }
    }

    /**
     * 记录每笔已结算的期权合约中，买家的利润
     */
    private fun updateProfit(
        oo: OptionOrder
    ) {
        if (oo.settlementHash.isNullOrBlank()) {
            optionOrderService.updateFields(
                mapOf(
                    OptionOrder::status.name to OptionStatus.SETTLE_FAILED,
                    OptionOrder::errorMsg.name to "Settlement hash is null or blank"
                ),
                oo.id!!
            )
            return
        }
        val service = blockchainServiceFactory.getBlockchainService(oo.chain) ?: run {
            logger.warn("EvmSettlement: ${oo.chain} Service not found, Skipped")
            return
        }

        if(oo.expiryDate.isBefore(startDate)) {
            //logger.warn("Option(${oo.id}) expired before 2024-12-10, Skipped")
            return
        }

        val settlementInfo = service.getSettlementInfo(oo) ?: run {
            logger.warn("EvmSettlement: Settlement info not found, Skipped")
            return
        }

        val profit = settlementInfo.first

        logger.info("EvmSettlement: ${oo.chain}\t${oo.settlementHash}\tbuyer profit = $profit")
        var newOptionOrder = optionOrderService.updateFields(
            mapOf(
                OptionOrder::buyerProfit.name to profit,
                OptionOrder::buyer.name to Keys.toChecksumAddress(oo.buyer!!)
            ),
            oo.id!!,
            true
        )

        if(newOptionOrder == null) {
            logger.error("EvmSettlement: Failed to update buyer profit for order: ${oo.id}")
            return
        }

        /**
         * 有利润，更新结算价格
         */
        val order = orderRepository.findByIdOrNull(newOptionOrder.orderId) ?: return
        if (order.bidAsset == null || order.bidAmount == null || order.bidAmount == BigInteger.ZERO) {
            logger.warn("EvmSettlement: Order(${order.id}) bid asset or bid amount is null or Zero, skipped")
            return
        }

        order.creator = Keys.toChecksumAddress(order.creator)
        orderRepository.save(order)

        // 利润为0，更新结算价格为行权价
        if(profit.compareTo(BigDecimal.ZERO) == 0) {
            logger.info("EvmSettlement: oo(${newOptionOrder.id}) profit is 0, update settlement price = strike price")
            optionOrderService.updateFields(
                mapOf(
                    OptionOrder::marketPriceAtSettlement.name to order.strikePrice.toString()
                ),
                newOptionOrder.id!!
            )
        } else {

            val strikePrice = order.strikePrice.movePointLeft(18) // 行权价
            val bidAmount = order.bidAmount!! // 期权份数
            logger.info("EvmSettlement: oo(${newOptionOrder.id}) strikePrice=$strikePrice, bid amount=$bidAmount")

            val profitAsset = when (newOptionOrder.direction) {
                    OptionDirection.CALL -> newOptionOrder.underlyingAsset
                else -> currencyService.getOptionQuoteAsset(
                    newOptionOrder.chain,
                    newOptionOrder.bidAsset
                )
            }
            val profitDecimals = currencyService.getCurrencyDecimal(newOptionOrder.chain, profitAsset)
            val buyerProfit = newOptionOrder.buyerProfit!!.movePointLeft(profitDecimals) // 利润

            val settlementPrice = when (newOptionOrder.direction) {
                OptionDirection.CALL -> {
                    strikePrice.multiply(bidAmount).divide((bidAmount - buyerProfit), 18, BigDecimal.ROUND_HALF_UP)
                }

                else -> strikePrice - buyerProfit.divide(bidAmount, 18, BigDecimal.ROUND_HALF_UP)
            }.movePointRight(18).toBigInteger()

            newOptionOrder = optionOrderService.updateFields(
                mapOf(
                    OptionOrder::marketPriceAtSettlement.name to settlementPrice.toString()
                ),
                newOptionOrder.id!!,
                true
            )

            if(newOptionOrder == null){
                logger.error("EvmSettlement: Failed to update settlement price for order: ${oo.id}")
                return
            }

            logger.info("EvmSettlement: oo(${newOptionOrder.id}) direction=${newOptionOrder.direction}, buyerProfit=$buyerProfit, settlement price=${newOptionOrder.marketPriceAtSettlement}")
        }

        newOptionOrder = optionOrderService.updateBuyerProfitInUsd(newOptionOrder) ?: run {
            logger.error("EvmSettlement: Failed to update buyer profit in usd for order: ${oo.id}")
            newOptionOrder!!
        } // 更新买家利润的USD值
        newOptionOrder = optionOrderService.updateLossInUsd(newOptionOrder) ?: run {
            logger.error("EvmSettlement: Failed to update loss in usd for order: ${oo.id}")
            newOptionOrder!!
        } // 更新LossInUsd的值

        // 发生亏损，可能可以参与活动
        if (newOptionOrder.lossInUsd != null && newOptionOrder.lossInUsd!! > BigDecimal.ZERO) {
            lossIsWinUserSummaryService.updateLoss(newOptionOrder.id!!, newOptionOrder.buyer!!, newOptionOrder.lossInUsd!!)
        }

        // 事件通知
        logger.info("EvmSettlement: UpdateOptionsProfit 更新利润后事件: ${newOptionOrder.id}")
        eventPublisher.publishEvent(OptionSettlementEvent(this, newOptionOrder.id!!))
    }

    private fun getSettlementHashFromOrderID(
        oOrder: OptionOrder
    ): String? {
        // 到Subgraph 查询结算 Hash
        val hash = try {
            subgraphService.getSettlementFromOrderId(oOrder.chain, oOrder.onChainOrderId!!)
        } catch (subgraphE: Exception) {
            logger.info("${oOrder.chain} 链上订单 ${oOrder.onChainOrderId} 查询Subgraph 失败 ${subgraphE.message}")
            null
        }

        if (!hash.isNullOrBlank()){
            logger.info("${oOrder.chain} 链上订单 ${oOrder.onChainOrderId} 查询subgraph hash 为 $hash")
            return hash
        } else {
            logger.info("${oOrder.chain} 链上订单 ${oOrder.onChainOrderId} 查询subgraph hash 为空")
            return null
        }
    }
}