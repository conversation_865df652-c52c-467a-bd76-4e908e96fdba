package io.vault.jasper.model

import io.vault.jasper.enums.ChainType
import org.springframework.data.annotation.Id
import org.springframework.data.mongodb.core.mapping.Document
import org.springframework.data.mongodb.core.mapping.Field
import java.math.BigDecimal
import java.time.LocalDateTime

@Document("binance_campaign_parameter")
data class BinanceCampaignParameter(
    @Id
    val id: String? = null,
    @Field("start_date")
    var startDate: LocalDateTime = LocalDateTime.now(),
    @Field("end_date")
    var endDate: LocalDateTime = LocalDateTime.of(2025, 12, 14, 0, 0, 0),
    @Field("trade_rebate_chain")
    var tradeRebateChain: ChainType = ChainType.BITLAYER,
    @Field("nft_contract_address") // 徽章 NFT 合约地址
    var nftContractAddress: String = "0xCBF56d9D0431C927bb6fE5cC022fC8d890AEa6A9",
    @Field("moonlight_box_contract_address")
    var moonlightBoxContractAddress: String = "0x5d23B93D82010535B8d6E9bCdDE45b2E8e6a8E12",
    @Field("first_trade_rebate_task_switch")
    var firstTradeRebateTaskSwitch: Boolean = false, // 推送First Trade Rebate任务的开关
    @Field("first_trade_discord_level")
    var firstTradeDiscordLevel: Int = 1,
    @Field("first_trade_degen_quantity")
    var firstTradeDegenQuantity: BigDecimal = BigDecimal("0.01"),
    @Field("retweet_link")
    var retweetLink: String = "https://twitter.com/binance/status/1469780000000000000",
)