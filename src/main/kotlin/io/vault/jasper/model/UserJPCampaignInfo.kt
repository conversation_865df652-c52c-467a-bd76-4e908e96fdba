package io.vault.jasper.model

import io.vault.jasper.response.BaseAirdropInfoResponse
import io.vault.jasper.response.DLCBTCAirdropInfoResponse
import org.springframework.data.annotation.CreatedDate
import org.springframework.data.annotation.Id
import org.springframework.data.annotation.LastModifiedDate
import org.springframework.data.mongodb.core.index.Indexed
import org.springframework.data.mongodb.core.mapping.Document
import org.springframework.data.mongodb.core.mapping.Field
import org.springframework.data.mongodb.core.mapping.FieldType
import java.math.BigDecimal
import java.time.LocalDateTime

@Document
data class UserJPCampaignInfo(
    @Id
    val id: String? = null,
    @Indexed(unique = true)
    val address: String, // 大小写敏感
    @Indexed(unique = true)
    val userId: String,
    @CreatedDate
    val created: LocalDateTime = LocalDateTime.now(),
    @LastModifiedDate
    val updated: LocalDateTime = LocalDateTime.now(),
    @Field("twitter_account_id")
    var twitterAccountId: String? = null,
    @Field("follow_twitter_time")
    var followTwitterTime: Long = 0L,
    @Field("retweet_time")
    var retweetTime: Long = 0L,
    @Field("join_discord_time")
    var joinDiscordTime: Long = 0L
)