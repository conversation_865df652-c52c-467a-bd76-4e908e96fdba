# Campaign System

The Campaign System allows administrators to create campaigns with multiple tasks and rewards for users.

## Overview

The Campaign System consists of the following components:

1. **Campaigns**: A campaign is an activity with a name, description, start and end time, and a list of tasks.
2. **Tasks**: A task is something a user can complete to progress in a campaign. There are 8 types of tasks:
   - New User Registration
   - Option Order Completion
   - Twitter Follow
   - Twitter Retweet
   - Bind Twitter Account
   - Discord Bind
   - Discord Server Join
   - Discord Level Upgrade
3. **Rewards**: A reward is given to users who complete tasks in a campaign. Currently, the system supports order loss compensation rewards.

## Task Types

### 1. New User Registration

This task is completed when a user registers after a specified time.

Configuration parameters:
- `registrationAfterTime`: The time after which a user must register to complete this task.

### 2. Option Order Completion

This task is completed when a user completes an option order that meets specific criteria.

Configuration parameters:
- `bidAsset`: The bid asset of the order (optional).
- `minBidAmount`: The minimum bid amount of the order (optional).
- `chain`: The chain of the order (optional).
- `minPremiumFeeUsdt`: The minimum premium fee in USDT of the order (optional).
- `productType`: The product type of the order (optional).
- `firstOrderInCampaign`: Whether the order must be the user's first order in the campaign (optional).

### 3. Twitter Follow

This task is completed when a user clicks a Twitter follow link.

Configuration parameters:
- `twitterFollowLink`: The Twitter follow link.

### 4. Twitter Retweet

This task is completed when a user clicks a Twitter retweet link.

Configuration parameters:
- `twitterRetweetLink`: The Twitter retweet link.

### 5. Bind Twitter Account

This task is completed when a user binds their Twitter account to Jasper Vault.

No specific configuration parameters. The task is completed when `User.twitterAccountId` is not null.

### 6. Discord Bind

This task is completed when a user binds their Discord account.

No specific configuration parameters.

### 7. Discord Server Join

This task is completed when a user joins the JasperVault Discord server.

No specific configuration parameters.

### 8. Discord Level Upgrade

This task is completed when a user reaches a specified Discord level.

Configuration parameters:
- `minDiscordLevel`: The minimum Discord level required to complete this task.

## Reward Types

### 1. Order Loss Compensation

This reward compensates users for losses on option orders.

The compensation is calculated as:
```
compensation = premiumFeePayInUsdt - buyerProfitInUsdt
```

Where:
- `premiumFeePayInUsdt` is the premium fee paid in USDT.
- `buyerProfitInUsdt` is the buyer profit in USDT.

## API Endpoints

### User Endpoints

- `GET /campaigns`: Get all active campaigns with user progress.
- `GET /campaigns/{id}`: Get a campaign by ID with user progress.
- `POST /campaigns/{campaignId}/tasks/{taskId}/complete`: Mark a task as completed.

### Admin Endpoints

- `POST /api/admin/activities`: Create a new campaign.
- `GET /api/admin/activities/{id}`: Get a campaign by ID.
- `GET /api/admin/activities`: Get all active campaigns.
- `GET /api/admin/activities/{id}/tasks`: Get tasks for a campaign.
- `GET /api/admin/activities/{id}/participants`: Get participant count for a campaign.
- `POST /api/admin/activities/rewards`: Create a new campaign reward.
- `GET /api/admin/activities/{id}/rewards`: Get rewards for a campaign.
- `GET /api/admin/activities/{id}/rewards/distributed`: Get total rewards distributed for a campaign.

- `POST /api/admin/tasks`: Create a new task.
- `GET /api/admin/tasks/{id}`: Get a task by ID.
- `GET /api/admin/tasks`: Get all tasks.
- `PUT /api/admin/tasks/{id}`: Update a task.
- `DELETE /api/admin/tasks/{id}`: Delete a task.

## Integration with Option Orders

The Campaign System is integrated with the option order processing flow. When an option order is settled, the system automatically:

1. Checks if the order completes any tasks in active campaigns.
2. If the order results in a loss, calculates and creates a compensation reward record.

## Scheduled Tasks

The system includes a scheduled task that runs every minute to process pending reward records. This task:

1. Finds all pending reward records that are scheduled to be processed.
2. Updates their status to "processing".
3. Submits the compensation to the smart contract.
4. Updates their status to "completed" or "failed" based on the result.

## Creating a New Campaign

To create a new campaign, follow these steps:

1. Create the tasks for the campaign using the task admin API (`POST /api/admin/tasks`).
2. Create the campaign using the campaign admin API (`POST /api/admin/activities`), referencing the task IDs.
3. Create the campaign reward using the campaign reward admin API (`POST /api/admin/activities/rewards`).

### Example: Creating a Jasper Vault Campaign

Here's an example of creating a campaign for Jasper Vault:

1. Create the tasks:
   - New User Registration task with registration time after campaign start:
     ```json
     {
       "title": "New User Registration",
       "description": "Register as a new user on Jasper Vault",
       "iconUrl": "https://assets.jaspervault.io/icons/registration.png",
       "taskType": "NEW_USER_REGISTRATION",
       "taskConfig": {
         "registrationAfterTime": "2025-05-22T00:00:00"
       }
     }
     ```
   - Twitter Follow task with link to Jasper Vault Twitter:
     ```json
     {
       "title": "Follow Jasper Vault on Twitter",
       "description": "Follow the Jasper Vault Twitter account",
       "iconUrl": "https://assets.jaspervault.io/icons/twitter.png",
       "taskType": "TWITTER_FOLLOW",
       "taskConfig": {
         "twitterFollowLink": "https://x.com/jaspervault"
       }
     }
     ```
   - Twitter Retweet task with link to a specific tweet:
     ```json
     {
       "title": "Retweet Jasper Vault's Tweet",
       "description": "Retweet the specified tweet from Jasper Vault",
       "iconUrl": "https://assets.jaspervault.io/icons/retweet.png",
       "taskType": "TWITTER_RETWEET",
       "taskConfig": {
         "twitterRetweetLink": "https://x.com/jaspervault/status/1924682446454603875"
       }
     }
     ```
   - Discord Bind task:
     ```json
     {
       "title": "Bind Discord Account",
       "description": "Bind your Discord account to Jasper Vault",
       "iconUrl": "https://assets.jaspervault.io/icons/discord.png",
       "taskType": "DISCORD_BIND",
       "taskConfig": {}
     }
     ```
   - Discord Server Join task:
     ```json
     {
       "title": "Join Jasper Vault Discord Server",
       "description": "Join the Jasper Vault Discord server",
       "iconUrl": "https://assets.jaspervault.io/icons/discord-server.png",
       "taskType": "DISCORD_SERVER_JOIN",
       "taskConfig": {}
     }
     ```
   - Discord Level Upgrade task with minimum level 1:
     ```json
     {
       "title": "Upgrade Discord Level",
       "description": "Reach Discord level 1 or higher",
       "iconUrl": "https://assets.jaspervault.io/icons/level-up.png",
       "taskType": "DISCORD_LEVEL_UPGRADE",
       "taskConfig": {
         "minDiscordLevel": "1"
       }
     }
     ```
   - Option Order Completion task with minimum bid amount 0.2 ETH and first order in campaign:
     ```json
     {
       "title": "Complete an ETH Option Order",
       "description": "Complete an option order with at least 0.2 ETH",
       "iconUrl": "https://assets.jaspervault.io/icons/order.png",
       "taskType": "OPTION_ORDER_COMPLETION",
       "taskConfig": {
         "bidAsset": "ETH",
         "minBidAmount": "0.2",
         "firstOrderInCampaign": true
       }
     }
     ```

2. Create the campaign:
   ```json
   {
     "name": "Jasper Vault Campaign",
     "description": "Complete tasks to earn rewards",
     "startTime": "2025-05-22T00:00:00",
     "endTime": "2025-05-31T23:59:59",
     "taskIds": ["task1Id", "task2Id", "task3Id", "task4Id", "task5Id", "task6Id", "task7Id"],
     "totalRewardAmount": "10000",
     "smartContractAddress": "******************************************",
     "active": true
   }
   ```

3. Create the campaign reward:
   ```json
   {
     "campaignId": "campaignId",
     "rewardType": "ORDER_LOSS_COMPENSATION",
     "rewardConfig": {
       "smartContractAddress": "******************************************"
     },
     "totalRewardAmount": 10000
   }
   ```
