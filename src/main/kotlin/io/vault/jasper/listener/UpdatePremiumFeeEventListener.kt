package io.vault.jasper.listener

import io.vault.jasper.event.UpdatePremiumFeeEvent
import io.vault.jasper.repository.OptionOrderRepository
import io.vault.jasper.repository.UserRepository
import io.vault.jasper.service.AirDropService
import io.vault.jasper.service.UserPremiumSummaryService
import org.slf4j.LoggerFactory
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.context.event.EventListener
import org.springframework.data.repository.findByIdOrNull
import org.springframework.stereotype.Component

@Component
open class UpdatePremiumFeeEventListener @Autowired constructor(
    private val optionOrderRepository: OptionOrderRepository,
    private val airDropService: AirDropService,
    private val userRepository: UserRepository,
    private val userPremiumSummaryService: UserPremiumSummaryService
) {

    private val logger = LoggerFactory.getLogger(this::class.java)

    @EventListener
    open fun handleUpdatePremiumFeeEvent(event: UpdatePremiumFeeEvent) {
        val oo = optionOrderRepository.findByIdOrNull(event.orderId) ?: return

        // 更新用户的权利金总额
        val user = userRepository.findByAddressIgnoreCase(oo.buyer!!)
        if(user != null) {
            try {
                userPremiumSummaryService.updateUserPremiumSummary(user)
            } catch (e: Exception) {
                logger.error("Update user premium summary failed", e)
            }
        }

        // 检查 Airdrop 任务
        airDropService.checkTradingTask(oo.buyer!!)
    }
}