package io.vault.jasper.service.blockchain

import io.vault.jasper.blockchain.BlockchainUtilFactory
import io.vault.jasper.blockchain.BscBlockchainUtil
import io.vault.jasper.blockchain.evm.arbitrum.BscScanApi
import io.vault.jasper.repository.*
import io.vault.jasper.service.*
import io.vault.jasper.task.AsyncEvmScanner
import org.slf4j.LoggerFactory
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.beans.factory.annotation.Value
import org.springframework.context.ApplicationEventPublisher
import org.springframework.stereotype.Service

@Service
class BscService @Autowired constructor(
    blockchainUtil: BscBlockchainUtil,
    scanApi: BscScanApi,
    optionOrderRepository: OptionOrderRepository,
    optionOrderInfoRepository: OptionOrderInfoRepository,
    orderRepository: OrderRepository,
    blockchainRepository: BlockchainRepository,
    asyncEvmScanner: AsyncEvmScanner,
    jasperVaultService: JasperVaultService,
    eventPublisher: ApplicationEventPublisher,
    lpVaultRepository: LPVaultRepository,
    contractEventRepository: ContractEventRepository,
    currencyService: CurrencyService,
    optionOrderService: OptionOrderService,
    blockchainUtilFactory: BlockchainUtilFactory,
    subgraphService: SubgraphService,
    historyService: HistoryService,
    miniBridgeSwapOrderRepository: MiniBridgeSwapOrderRepository,
    alertService: AlertService
) : EvmService(
    blockchainUtil,
    blockchainRepository,
    asyncEvmScanner,
    orderRepository,
    optionOrderRepository,
    optionOrderInfoRepository,
    jasperVaultService,
    eventPublisher,
    lpVaultRepository,
    contractEventRepository,
    currencyService,
    optionOrderService,
    blockchainUtilFactory,
    scanApi,
    subgraphService,
    historyService,
    miniBridgeSwapOrderRepository,
    alertService
) {

    private val logger = LoggerFactory.getLogger(this::class.java)

    @Value("\${blockchain.bsc.contract.vault_factory}")
    override lateinit var vaultFactoryContract: String

    @Value("\${blockchain.bsc.contract.option_service}")
    override lateinit var optionServiceContract: String

    @Value("\${blockchain.bsc.contract.diamond}")
    public override lateinit var addOrderLogAddress: String

    @Value("\${blockchain.bsc.contract.issuance_module}")
    override lateinit var issuanceModuleContract: String
}