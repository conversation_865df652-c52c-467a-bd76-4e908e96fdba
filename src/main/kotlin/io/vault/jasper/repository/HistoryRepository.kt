package io.vault.jasper.repository

import io.vault.jasper.enums.ChainType
import io.vault.jasper.model.History
import io.vault.jasper.model.TransactionMethod
import org.springframework.data.domain.Page
import org.springframework.data.domain.Pageable
import org.springframework.data.mongodb.repository.MongoRepository
import org.springframework.stereotype.Repository

@Repository
interface HistoryRepository : MongoRepository<History, String> {

    fun findByChainAndBuyer(chain: ChainType, buyer: String, pageable: Pageable): Page<History>

    fun existsByChainAndBuyerAndTxHashAndMethodAndAsset(
        chain: ChainType,
        buyer: String,
        txHash: String,
        method: TransactionMethod,
        asset: String
    ): <PERSON><PERSON><PERSON>
}