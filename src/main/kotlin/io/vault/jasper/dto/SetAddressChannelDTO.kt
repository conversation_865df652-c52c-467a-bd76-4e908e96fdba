package io.vault.jasper.dto

import EVMAddress
import io.swagger.annotations.ApiModel
import io.swagger.annotations.ApiModelProperty

@ApiModel("设置钱包地址渠道标识请求")
data class SetAddressChannelDTO(
    @ApiModelProperty("钱包地址", required = true)
    @field:EVMAddress
    val address: String,
    @ApiModelProperty("请求时间", required = false)
    val timestamp: Long? = null,
    @ApiModelProperty("从哪个渠道引流过来的地址", required = false)
    var from: String? = null,
    @ApiModelProperty("utm标签 - 流量来源", required = false, name = "utm_source")
    var utmSource: String? = null,
    @ApiModelProperty("utm标签 - 媒介", required = false, name = "utm_medium")
    var utmMedium: String? = null,
    @ApiModelProperty("utm标签 - 营销活动", required = false, name = "utm_campaign")
    var utmCampaign: String? = null,
    @ApiModelProperty("utm标签 - term", required = false, name = "utm_term")
    var utmTerm: String? = null,
    @ApiModelProperty("utm标签 - content", required = false, name = "utm_content")
    var utmContent: String? = null
)
