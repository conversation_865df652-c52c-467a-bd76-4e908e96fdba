package io.vault.jasper.model

import io.vault.jasper.enums.ChainType
import org.springframework.data.annotation.CreatedDate
import org.springframework.data.annotation.Id
import org.springframework.data.annotation.LastModifiedDate
import org.springframework.data.mongodb.core.index.Indexed
import org.springframework.data.mongodb.core.mapping.Document
import org.springframework.data.mongodb.core.mapping.Field
import java.time.LocalDateTime

@Document("contract_event")
data class ContractEvent(
    @Id
    val id: String? = null,

    val chain: ChainType,

    val contract: String,

    @Indexed(unique = true)
    val event: EventName,

    val desc: String? = null,

    @Field("log_address")
    val logAddress: String,

    @Field("fn_hash")
    val fnHash: String,

    @CreatedDate
    val created: LocalDateTime = LocalDateTime.now(),

    @LastModifiedDate
    val updated: LocalDateTime = LocalDateTime.now()
)

enum class EventName {
    REDEEM_NFT,
    COIN98,
    BITLAYER_BINANCE,
}