package io.vault.jasper.repository


import io.vault.jasper.model.*
import org.springframework.data.mongodb.repository.MongoRepository
import org.springframework.stereotype.Repository

@Repository
interface BitlayerTradeRebateRecordRepository : MongoRepository<BitlayerTradeRebateRecord, String> {
    fun findByOptionOrderId(optionOrderId: String): BitlayerTradeRebateRecord?
    fun findTopByOrderByOptionOrderIdDesc(): BitlayerTradeRebateRecord?
    fun findByStatus(
        status: BitlayerTradeRebateRecordStatus
    ): List<BitlayerTradeRebateRecord>

    fun findByBuyerAddressIgnoreCase(
        buyerAddress: String
    ): List<BitlayerTradeRebateRecord>

    fun findByStatusAndSettleTxIdIsNotNull(
        status: BitlayerTradeRebateRecordStatus
    ): List<BitlayerTradeRebateRecord>

    fun findByBuyerAddressAndStatus(
        buyerAddress: String,
        status: BitlayerTradeRebateRecordStatus
    ): List<BitlayerTradeRebateRecord>

    fun findByBuyerAddressIgnoreCaseAndStatus(
        buyerAddress: String,
        status: BitlayerTradeRebateRecordStatus
    ): List<BitlayerTradeRebateRecord>

    fun findByBuyerAddressAndStatusInAndSettleTxIdIsNotNull(
        buyerAddress: String,
        statuses: List<BitlayerTradeRebateRecordStatus>
    ): List<BitlayerTradeRebateRecord>

    fun findByBuyerAddressIgnoreCaseAndStatusIn(
        buyerAddress: String,
        statuses: List<BitlayerTradeRebateRecordStatus>
    ): List<BitlayerTradeRebateRecord>
}