package io.vault.jasper.blockchain

import io.vault.jasper.blockchain.evm.arbitrum.ArbitrumScanApi
import io.vault.jasper.blockchain.evm.arbitrum.BaseScanApi
import io.vault.jasper.blockchain.evm.arbitrum.BitlayerScanApi
import io.vault.jasper.blockchain.evm.arbitrum.BscScanApi
import io.vault.jasper.enums.ChainType
import org.slf4j.LoggerFactory
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.stereotype.Service

@Service
class BlockchainUtilFactory @Autowired constructor(
    private val ethBlockchainUtil: EthBlockchainUtil,
    private val polygonBlockchainUtil: PolygonBlockchainUtil,
    private val arbitrumBlockchainUtil: ArbitrumBlockchainUtil,
    private val seiBlockchainUtil: SeiBlockchainUtil,
    private val baseBlockchainUtil: BaseBlockchainUtil,
    private val bitlayerBlockchainUtil: BitlayerBlockchainUtil,
    private val arbitrumScanApi: ArbitrumScanApi,
    private val baseScanApi: BaseScanApi,
    private val bitlayerScanApi: BitlayerScanApi,
    private val bscScanApi: BscScanApi,
    private val bscBlockchainUtil: BscBlockchainUtil
) {

    private val logger = LoggerFactory.getLogger(this::class.java)

    fun getBlockchainUtil(chainType: ChainType): IEvmBlockchainUtil {
        return when (chainType) {
            ChainType.ETH -> ethBlockchainUtil
            ChainType.POLYGON -> polygonBlockchainUtil
            ChainType.ARBITRUM -> arbitrumBlockchainUtil
            ChainType.SEI -> seiBlockchainUtil
            ChainType.BASE -> baseBlockchainUtil
            ChainType.BITLAYER -> bitlayerBlockchainUtil
            ChainType.BSC -> bscBlockchainUtil
            else -> throw IllegalArgumentException("Unsupported chain")
        }
    }

    fun getScanApi(chainType: ChainType): EvmScanApi {
        return when (chainType) {
            ChainType.ARBITRUM -> arbitrumScanApi
            ChainType.BASE -> baseScanApi
            ChainType.BITLAYER -> bitlayerScanApi
            ChainType.BSC -> bscScanApi
            else -> throw IllegalArgumentException("Unsupported chain")
        }
    }
}