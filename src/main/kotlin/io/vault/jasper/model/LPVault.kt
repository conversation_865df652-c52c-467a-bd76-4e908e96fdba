package io.vault.jasper.model

import io.vault.jasper.enums.ChainType
import io.vault.jasper.enums.OptionDirection
import io.vault.jasper.enums.Symbol
import org.springframework.data.annotation.CreatedDate
import org.springframework.data.annotation.Id
import org.springframework.data.annotation.LastModifiedDate
import org.springframework.data.mongodb.core.index.Indexed
import org.springframework.data.mongodb.core.mapping.Document
import java.math.BigDecimal
import java.time.LocalDateTime

enum class LPVaultStatus {
    PENDING, REQUESTED, DONE, ERROR, ABONDANED
}

enum class LPVaultPermissionType{
    Permissioned, PermissionLess
}

enum class LPVaultPremiumOracleType{
    PAMMS, AMMS
}

/**
 * LP Vault
 */

@Document
data class LPVault(
    @Id
    val id: String? = null,
    @Indexed(background = true)
    val lpEoaAddress: String, // 大小写敏感
    @Indexed(background = true)
    val chain: ChainType = ChainType.ARBITRUM, // 对应的链
    @Indexed(background = true)
    val optionType: OptionDirection = OptionDirection.CALL, // Vault 期权类型
    val optionSymbol: String = "ETH", // Vault 期权标的
    @Indexed(background = true)
    var lpVaultAddress: String? = null, // LP Vault 地址
    @Indexed(background = true)
    var status: LPVaultStatus = LPVaultStatus.PENDING,
    var requestId: String? = null,
    var errorMessage: String? = null,
    @CreatedDate
    val created: LocalDateTime = LocalDateTime.now(),
    @LastModifiedDate
    val updated: LocalDateTime = LocalDateTime.now(),
    var removeVaultHash: String? = null, // redeem transaction hash
    var expiryInHour: BigDecimal = BigDecimal("8"),
    var availableLiquidity: BigDecimal = BigDecimal.ZERO,
    var availableLiquidityInUsdt: BigDecimal = BigDecimal.ZERO,
    var apy: BigDecimal = BigDecimal.ZERO,
    var iv: BigDecimal = BigDecimal.ZERO,
    @Indexed(background = true)
    var permissionType: LPVaultPermissionType = LPVaultPermissionType.Permissioned,
    var poolAsset: Symbol? = null,
    var name: String? = null,
    var premiumOracleType: LPVaultPremiumOracleType = LPVaultPremiumOracleType.PAMMS,
    var feeTier: BigDecimal = BigDecimal.ZERO,
    var premium: BigDecimal = BigDecimal.ZERO,
    var premiumRates: BigDecimal = BigDecimal.ZERO,
    var premiumFloor: BigDecimal = BigDecimal.ZERO,
    var regularPool: String = "",
    var oi: BigDecimal = BigDecimal.ZERO,
    var premiumEarn: BigDecimal = BigDecimal.ZERO,
    var tradingVolume: BigDecimal = BigDecimal.ZERO,
    var lockSymbolBalance: BigDecimal = BigDecimal.ZERO,
    var usdtBalance: BigDecimal = BigDecimal.ZERO
)