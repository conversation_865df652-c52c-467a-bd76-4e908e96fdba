package io.vault.jasper.response

import io.swagger.annotations.ApiModel
import io.swagger.annotations.ApiModelProperty
import io.vault.jasper.enums.Symbol
import org.web3j.abi.datatypes.Bool

@ApiModel
data class AirdropInfoResponse(

    @ApiModelProperty("钱包地址", required = true)
    val address: String,

    @ApiModelProperty("Twitter是否绑定并验证", required = true)
    val isTwitterFollowed: Boolean = false,

    @ApiModelProperty("Twitter是否已经转发Tweet", required = true)
    val isTwitterRetweet: Boolean = false,

    @ApiModelProperty("是否已经加入了Discord", required = true)
    val isJoinDiscord: Boolean = false,

    @ApiModelProperty("Discord用户等级", required = false)
    val discordLevel: Int = 0,

    @ApiModelProperty("活动期间共邀请用户数量", required = true)
    val totalInviteCount: Int = 0,

    @ApiModelProperty("活动期间邀请用户总交易数量", required = true)
    val totalTradeCount: Int = 0,

    @ApiModelProperty("活动期间共邀请用户并验证Twitter账户的数量", required = true)
    val totalInviteAndVerifiedCount: Int = 0,

    @ApiModelProperty("活动期间邀请并有交易的用户数量", required = true)
    val totalReferralTradingCount: Int = 0,

    @ApiModelProperty("共获得的空投积分数量", required = true)
    val totalPoint: String = "0",

    @ApiModelProperty("未领取的免单数量", required = true)
    val unclaimedCount: Int = 0,

    @ApiModelProperty("首笔交易的PNL", required = true)
    val firstTradePNL: String = "0",

    @ApiModelProperty("首笔交易是否2H 0.2 ETH", required = true)
    val firstTradeIs2HETH: Boolean = false,

    @ApiModelProperty("能否参加首单永赚的活动", required = true)
    val canTrade: Boolean = true,

    @ApiModelProperty("需要转推的帖子ID", required = true)
    val retweetId: String = "1825800581090545926",

    @ApiModelProperty("已经领取的任务1sPoint", required = true)
    val sPoint1: String = "0",

    @ApiModelProperty("已经领取的任务2sPoint", required = true)
    val sPoint2: String = "0",

    @ApiModelProperty("已经领取的任务3sPoint", required = true)
    val sPoint3: String = "0",

    @ApiModelProperty("已经补偿首单永赚的用户数", required = true)
    val firstTradeRebateCount: Int = 0,

    @ApiModelProperty("Base链空投情况", required = false)
    val baseInfo: BaseAirdropInfoResponse? = null,

    @ApiModelProperty("DLCBTC空投情况", required = false)
    val dlcbtcInfo: DLCBTCAirdropInfoResponse? = null,

    @ApiModelProperty("NFT 空投情况", required = false)
    val nftInfo: AirdropNFTInfoResponse? = null,

    @ApiModelProperty("JP 交易活动情况", required = false)
    val jpTradeInfo: ExecutedOrderResponse? = null,

    @ApiModelProperty("首单Gross Profit Rate", required = false)
    val firstTradeGrossProfitRate: String = "0",

    @ApiModelProperty("首单Premium", required = false)
    val firstTradePremium: String = "0",

    @ApiModelProperty("首单Premium 币种", required = false)
    val firstTradePremiumAsset: Symbol = Symbol.BTC,

    @ApiModelProperty("首单Rebate金额", required = false)
    val firstTradeAmount: String = "0",
)

@ApiModel
data class BaseAirdropInfoResponse(

    @ApiModelProperty("Discord用户等级", required = false)
    val discordLevel: Int = 0,

    @ApiModelProperty("首笔交易的PNL", required = true)
    val firstTradePNL: String = "0",

    @ApiModelProperty("首笔交易是否2H 0.2 ETH", required = true)
    val firstTradeIs2HETH: Boolean = false,

    @ApiModelProperty("能否参加首单永赚的活动", required = true)
    val canTrade: Boolean = true,

    @ApiModelProperty("已经补偿首单永赚的用户数", required = true)
    val firstTradeRebateCount: Int = 0,

    @ApiModelProperty("是否已经Retweet了帖子", required = true)
    val isTwitterRetweet: Boolean = false
)

@ApiModel
data class DLCBTCAirdropInfoResponse(

    @ApiModelProperty("Discord用户等级", required = false)
    val discordLevel: Int = 0,

    @ApiModelProperty("首笔交易的PNL", required = true)
    val firstTradePNL: String = "0",

    @ApiModelProperty("首笔交易是否2H 0.2 ETH", required = true)
    val firstTradeIs2HETH: Boolean = false,

    @ApiModelProperty("能否参加首单永赚的活动", required = true)
    val canTrade: Boolean = true,

    @ApiModelProperty("已经补偿首单永赚的用户数", required = true)
    val firstTradeRebateCount: Int = 0,

    @ApiModelProperty("是否已经Retweet了帖子", required = true)
    val isTwitterRetweet: Boolean = false,

    @ApiModelProperty("是否已经关注了DLCBTC", required = true)
    val isFollowDlcbtc: Boolean = false
)

@ApiModel
data class AirdropNFTInfoResponse(

    @ApiModelProperty("Discord用户等级", required = false)
    val discordLevel: Int = 0,

    @ApiModelProperty("社媒任务1是否完成", required = true)
    val task1Finished: Boolean = false,

    @ApiModelProperty("是否转发了对应的tweet", required = true)
    val task2Finished: Boolean = false,

    @ApiModelProperty("转发的tweet id", required = true)
    val tweetId: String = "",

    @ApiModelProperty("是否可以领取NFT", required = true)
    val canClaimNft: Boolean = false
)
