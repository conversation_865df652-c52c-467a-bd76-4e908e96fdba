package io.vault.jasper.service.kol

import io.vault.jasper.response.KolLevelDesc
import java.math.BigDecimal

interface IKolLevel {

    fun getName(): String

    fun getOrder(): Int

    fun getIncentiveRate(): BigDecimal

    fun getDesc(): List<KolLevelDesc>

    fun matchConditions(vararg pairs: Pair<String, Any>): Boolean

    /**
     * 匹配是否满足邀请用户数
     */
    fun matchInvitedUsers(userCount: Int): <PERSON><PERSON>an
}