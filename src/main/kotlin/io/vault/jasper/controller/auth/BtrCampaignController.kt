package io.vault.jasper.controller.auth

import EVMAddress
import io.swagger.annotations.Api
import io.swagger.annotations.ApiOperation
import io.vault.jasper.ApiResponse
import io.vault.jasper.annotation.ApiCache
import io.vault.jasper.exception.BusinessException
import io.vault.jasper.exception.ResultEnum
import io.vault.jasper.model.LossIsWinUserSummary
import io.vault.jasper.repository.BtrCampaignConfigRepository
import io.vault.jasper.repository.BtrCampaignRecordRepository
import io.vault.jasper.repository.LossIsWinCampaignConfigRepository
import io.vault.jasper.repository.LossIsWinUserSummaryRepository
import io.vault.jasper.response.LossIsWinOverview
import io.vault.jasper.response.PageResponse
import io.vault.jasper.utils.AuthUtil
import org.slf4j.LoggerFactory
import org.springframework.data.domain.PageRequest
import org.springframework.data.domain.Sort
import org.springframework.web.bind.annotation.*
import java.math.BigDecimal
import javax.servlet.http.HttpServletRequest
import kotlin.text.compareTo

@Api(tags = ["BTR Campaign"])
@RestController
@RequestMapping("/campaign/btr")
class BtrCampaignController(
    private val btrCampaignConfigRepository: BtrCampaignConfigRepository,
    private val btrCampaignRecordRepository: BtrCampaignRecordRepository,
    private val lossIsWinUserSummaryRepository: LossIsWinUserSummaryRepository,
    private val authUtil: AuthUtil,
    private val lossIsWinCampaignConfigRepository: LossIsWinCampaignConfigRepository
) {

    private val logger = LoggerFactory.getLogger(this::class.java)

    data class BtrCampaignInfo(
        val totalBtrAmount: BigDecimal,
        val distributedBtrAmount: BigDecimal,
        val userRecordCount: Long,
        val userDistributedAmount: BigDecimal
    )

    @ApiOperation("Get BTR Campaign Information")
    @CrossOrigin
    @GetMapping
    fun getBtrCampaignInfo(
        request: HttpServletRequest,
        @RequestParam("address") @EVMAddress address: String
    ): ApiResponse<BtrCampaignInfo> {
        // 获取活动配置
        val config = btrCampaignConfigRepository.findFirstByEnabled(true)
            ?: throw BusinessException(ResultEnum.BTR_CAMPAIGN_NOT_FOUND)

        // 获取用户的记录数量
        val userRecordCount = btrCampaignRecordRepository.countByAddressIgnoreCase(address)

        // 获取用户的总奖励金额
        val userRecords = btrCampaignRecordRepository.findByAddressIgnoreCase(address)
        val userDistributedAmount = userRecords.fold(BigDecimal.ZERO) { acc, record ->
            acc.add(record.btrAmount)
        }

        val info = BtrCampaignInfo(
            totalBtrAmount = config.totalBtrAmount,
            distributedBtrAmount = config.distributedBtrAmount,
            userRecordCount = userRecordCount,
            userDistributedAmount = userDistributedAmount
        )

        return ApiResponse.success(info)
    }

    @ApiOperation("Get Loss-is-Win Overview")
    @CrossOrigin
    @ApiCache(
        key = "loss-is-win:global-stats",
        dynamicKey = ["address"],
        expire = 60
    )
    @GetMapping("/loss-is-win/overview")
    fun getLossIsWinOverview(
        request: HttpServletRequest,
        @RequestParam("address") @EVMAddress address: String
    ): ApiResponse<LossIsWinOverview> {
        val userSummary = lossIsWinUserSummaryRepository.findFirstByAddress(address) ?: run {
            LossIsWinUserSummary(address = address)
        }

        val allSummaries = lossIsWinUserSummaryRepository.findAll()
        val totalTransactionCount = allSummaries.sumOf { it.transactionCount }
        val totalUserCount = allSummaries.count()
        val totalLossAmount = allSummaries.sumOf { it.totalLoss }
        val totalLossUserCount = allSummaries.count { it.totalLoss.compareTo(BigDecimal.ZERO) == 1 }

        // Get active campaign config
        val config = lossIsWinCampaignConfigRepository.findFirstByEnabled(true)
            ?: throw BusinessException(ResultEnum.BTR_CAMPAIGN_NOT_FOUND)

        // Calculate user's loss-based reward
        val userLossAmount = config.calculateLossBasedReward(
            userLoss = userSummary.totalLoss,
            totalLoss = totalLossAmount
        )

        val overview = LossIsWinOverview(
            totalTransactionCount = totalTransactionCount,
            totalUserCount = totalUserCount,
            userTransactionCount = userSummary.transactionCount,
            userBtrReward = userSummary.totalBtrEarned,
            totalLossAmount = totalLossAmount,
            totalLossUserCount = totalLossUserCount,
            userLossAmount = userLossAmount,
            totalBtrPerOrderPool = config.totalBtrPerOrderPool,
            totalBtrLossPool = config.totalBtrLossPool
        )

        return ApiResponse.success(overview)
    }

    @ApiOperation("Get Transaction Reward Leaderboard")
    @CrossOrigin
    @GetMapping("/loss-is-win/leaderboard")
    fun getTransactionRewardLeaderboard(
        @RequestParam("page", defaultValue = "0") page: Int,
        @RequestParam("size", defaultValue = "10") size: Int,
        @RequestParam("address") @EVMAddress address: String
    ): ApiResponse<Map<String, Any?>> {
        val pageable = PageRequest.of(page, size, Sort.by(Sort.Direction.DESC, "totalBtrEarned"))
        val leaderboardPage = lossIsWinUserSummaryRepository.findAll(pageable)

        // Find the user's record
        val userRecord = lossIsWinUserSummaryRepository.findFirstByAddress(address)
        val userRank = if (userRecord != null) {
            lossIsWinUserSummaryRepository.countByTotalBtrEarnedGreaterThan(userRecord.totalBtrEarned) + 1
        } else {
            null
        }
        val userTotalBtrEarned = userRecord?.totalBtrEarned

        val pageResponse = PageResponse(
            data = leaderboardPage.content,
            total = leaderboardPage.totalElements,
            totalPage = leaderboardPage.totalPages,
            page = leaderboardPage.number,
            pageSize = leaderboardPage.size,
            hasNext = leaderboardPage.hasNext()
        )

        return ApiResponse.success(
            mapOf(
                "leaderboard" to pageResponse,
                "userRank" to userRank,
                "userTotalBtrEarned" to userTotalBtrEarned
            )
        )
    }

    @ApiOperation("Get Loss Leaderboard")
    @CrossOrigin
    @GetMapping("/loss-is-win/loss-leaderboard")
    fun getLossLeaderboard(
        @RequestParam("page", defaultValue = "0") page: Int,
        @RequestParam("size", defaultValue = "10") size: Int,
        @RequestParam("address") @EVMAddress address: String
    ): ApiResponse<Map<String, Any?>> {
        val pageable = PageRequest.of(page, size, Sort.by(Sort.Direction.DESC, "totalLoss"))
        val leaderboardPage = lossIsWinUserSummaryRepository.findAll(pageable)

        // Find the user's record
        val userRecord = lossIsWinUserSummaryRepository.findFirstByAddress(address)
        val userRank = if (userRecord != null) {
            lossIsWinUserSummaryRepository.countByTotalLossGreaterThan(userRecord.totalLoss) + 1
        } else {
            null
        }
        val userTotalLoss = userRecord?.totalLoss

        val pageResponse = PageResponse(
            data = leaderboardPage.content,
            total = leaderboardPage.totalElements,
            totalPage = leaderboardPage.totalPages,
            page = leaderboardPage.number,
            pageSize = leaderboardPage.size,
            hasNext = leaderboardPage.hasNext()
        )

        return ApiResponse.success(
            mapOf(
                "leaderboard" to pageResponse,
                "userRank" to userRank,
                "userTotalLoss" to userTotalLoss
            )
        )
    }

    @ApiOperation("Get Order Count Leaderboard")
    @CrossOrigin
    @GetMapping("/loss-is-win/order-count-leaderboard")
    fun getOrderCountLeaderboard(
        @RequestParam("page", defaultValue = "0") page: Int,
        @RequestParam("size", defaultValue = "10") size: Int,
        @RequestParam("address") @EVMAddress address: String
    ): ApiResponse<Map<String, Any?>> {
        val pageable =
            PageRequest.of(page - 1, size, Sort.by(Sort.Direction.DESC, LossIsWinUserSummary::transactionCount.name))
        val leaderboardPage = lossIsWinUserSummaryRepository.findAll(pageable)

        // Find the user's record
        val userRecord = lossIsWinUserSummaryRepository.findFirstByAddress(address)
        val userRank = if (userRecord != null) {
            lossIsWinUserSummaryRepository.countByTransactionCountGreaterThan(userRecord.transactionCount) + 1
        } else {
            null
        }
        val userTransactionCount = userRecord?.transactionCount

        val pageResponse = PageResponse(
            data = leaderboardPage.content,
            total = leaderboardPage.totalElements,
            totalPage = leaderboardPage.totalPages,
            page = leaderboardPage.number,
            pageSize = leaderboardPage.size,
            hasNext = leaderboardPage.hasNext()
        )

        return ApiResponse.success(
            mapOf(
                "leaderboard" to pageResponse,
                "userRank" to userRank,
                "userTransactionCount" to userTransactionCount
            )
        )
    }

}