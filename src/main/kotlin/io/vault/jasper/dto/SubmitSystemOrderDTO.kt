package io.vault.jasper.dto

import io.swagger.annotations.ApiModel
import io.swagger.annotations.ApiModelProperty
import io.vault.jasper.enums.ChainType

@ApiModel("提交系统订单请求")
data class SubmitSystemOrderDTO(
    @ApiModelProperty("链", required = true)
    val chain: ChainType,
    @ApiModelProperty("链上交易ID", required = true)
    val txHash: String,
    @ApiModelProperty("用户的下单钱包地址", required = true)
    val address: String,
    @ApiModelProperty("系统下单的钱包地址", required = true)
    val systemAddress: String,
    @ApiModelProperty("用户的下单钱包 AA Vault 地址", required = false)
    val aaVaultAddress: String? = null
)
