package io.vault.jasper.service.activity.processors

import io.vault.jasper.model.User
import io.vault.jasper.model.activity.Task
import io.vault.jasper.model.activity.TaskConfigParams
import io.vault.jasper.model.activity.TaskType
import io.vault.jasper.service.activity.TaskProcessor
import org.springframework.stereotype.Component

/**
 * Processor for TWITTER_RETWEET task type
 * This task is considered completed when the user clicks the link
 */
@Component
class TwitterRetweetTaskProcessor : TaskProcessor {
    override fun getTaskType(): TaskType = TaskType.TWITTER_RETWEET
    
    override fun isTaskCompleted(user: User, task: Task, context: Map<String, Any>): Pair<Boolean, Map<String, Any>> {
        // For Twitter retweet tasks, we simply check if the task is marked as completed in the context
        // This is because we can't verify if the user actually retweeted
        val isCompleted = context["completed"] as? Boolean ?: false
        
        val completionData = if (isCompleted) {
            val twitterRetweetLink = task.taskConfig[TaskConfigParams.TWITTER_RETWEET_LINK] as? String ?: ""
            mapOf(
                "twitterRetweetLink" to twitterRetweetLink,
                "completedAt" to System.currentTimeMillis().toString()
            )
        } else {
            emptyMap()
        }
        
        return Pair(isCompleted, completionData)
    }
}
