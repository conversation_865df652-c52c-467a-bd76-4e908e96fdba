package io.vault.jasper.utils

import io.vault.jasper.exception.BusinessException
import io.vault.jasper.exception.ResultEnum
import io.vault.jasper.model.User
import io.vault.jasper.model.UserChannel
import io.vault.jasper.repository.ChannelPartnerRepository
import io.vault.jasper.repository.UserRepository
import io.vault.jasper.service.Blacklist
import io.vault.jasper.service.SystemService
import org.slf4j.LoggerFactory
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.data.redis.core.RedisTemplate
import org.springframework.stereotype.Service
import java.util.concurrent.TimeUnit
import javax.servlet.http.HttpServletRequest

@Service
class AuthUtil @Autowired constructor(
    private val redisTemplate: RedisTemplate<String, String>,
    private val userRepository: UserRepository,
    private val channelPartnerRepository: ChannelPartnerRepository,
    private val systemService: SystemService
) {

    private var logger = LoggerFactory.getLogger(this::class.java)

    private val accessTokenKey = "access:token:"

    private val adminAddress = "******************************************"

    fun auth(request: HttpServletRequest, address: String) : User {

        if(ProfileUtil.activeProfile == "dev"){
            return userRepository.findByAddressIgnoreCase(address) ?: throw BusinessException(ResultEnum.USER_NOT_FOUND)
        }

        val key = "$accessTokenKey$address"
        val ops = redisTemplate.opsForValue()
        val token = ops.get(key)
        val requestToken = request.getHeader("Authorization")
        logger.info("requestToken: $requestToken, token in redis: $token")

        val adminKey = "$accessTokenKey$adminAddress"
        val adminToken = ops.get(adminKey)

        if(requestToken == null || requestToken.isBlank()){
            throw BusinessException(ResultEnum.LOGIN_FIRST)
        }

        if(adminToken != null && requestToken.compareTo(adminToken) == 0){
            return userRepository.findByAddressIgnoreCase(address) ?: throw BusinessException(ResultEnum.USER_NOT_FOUND)
        }

        if(token == null || token.isBlank()){
            throw BusinessException(ResultEnum.INVALID_AUTHORIZATION_CODE)
        }

        if(token.compareTo(requestToken) != 0){
            throw BusinessException(ResultEnum.INVALID_AUTHORIZATION_CODE)
        }

        val user = userRepository.findByAddressIgnoreCase(address)
        if (user == null) {
            throw BusinessException(ResultEnum.USER_NOT_FOUND)
        }

        return user
    }

    fun setAccessToken(address: String, token: String) {
        val key = "$accessTokenKey$address"
        val ops = redisTemplate.opsForValue()

        val expire = systemService.getAccessTokenExpire()
        val timeUnit = TimeUnit.HOURS

        ops.set(key, token, expire, timeUnit)
    }

    fun authPartner(request: HttpServletRequest) : UserChannel {

        val requestToken = request.getHeader("Authorization")
        if(requestToken == null || requestToken.isBlank()){
            throw BusinessException(ResultEnum.INVALID_AUTHORIZATION_CODE)
        }

        val partner = channelPartnerRepository.findByApiKey(requestToken)
        if(partner == null){
            throw BusinessException(ResultEnum.INVALID_AUTHORIZATION_CODE)
        }

        return partner.channel
    }

    fun getAccessToken(address: String): String? {
        val key = "$accessTokenKey$address"
        val ops = redisTemplate.opsForValue()
        return ops.get(key)
    }

    fun filterIPBlacklist(request: HttpServletRequest): String {
        val ipAddress = getIpAddr(request)

        //if (ipAddress == null || Blacklist.contains(ipAddress)) {
        if (ipAddress == null) {
            throw BusinessException(ResultEnum.SERVICE_NOT_AVAILABLE)
        }
        // logger.info("IP Interceptor request path ${request.requestURI} IP: $ipAddress is not in blacklist")

        return ipAddress
    }

    /**
     * 获取IP地址
     */
    fun getIpAddr(request: HttpServletRequest): String? {
        // nginx代理获取的真实用户ip
        var ip = request.getHeader("X-Real-IP")
        if (ip.isNullOrBlank() || "unknown".equals(ip, ignoreCase = true)) {
            ip = request.getHeader("X-Forwarded-For")
        }
        if (ip.isNullOrBlank() || "unknown".equals(ip, ignoreCase = true)) {
            ip = request.getHeader("Proxy-Client-IP")
        }
        if (ip.isNullOrBlank() || "unknown".equals(ip, ignoreCase = true)) {
            ip = request.getHeader("WL-Proxy-Client-IP")
        }
        if (ip.isNullOrBlank() || "unknown".equals(ip, ignoreCase = true)) {
            ip = request.remoteAddr
        }
        /*
          对于通过多个代理的情况， 第一个IP为客户端真实IP,多个IP按照','分割 "***.***.***.***".length() =
          15
         */if (ip != null && ip.length > 15) {
            if (ip.indexOf(",") > 0) {
                ip = ip.substring(0, ip.indexOf(","))
            }
        }
        return ip
    }

    fun isEVMAddressValid(address: String): Boolean {
        return address.matches("^0x[0-9a-fA-F]{40}$".toRegex())
    }
}