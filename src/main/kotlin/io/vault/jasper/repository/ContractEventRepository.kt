package io.vault.jasper.repository

import io.vault.jasper.enums.ChainType
import io.vault.jasper.model.ContractEvent
import io.vault.jasper.model.EventName
import org.springframework.data.mongodb.repository.MongoRepository
import org.springframework.stereotype.Repository

@Repository
interface ContractEventRepository : MongoRepository<ContractEvent, String> {
    fun findFirstByEvent(event: EventName): ContractEvent?
    fun findFirstByEventAndChain(event: EventName, chain: ChainType): ContractEvent?
}