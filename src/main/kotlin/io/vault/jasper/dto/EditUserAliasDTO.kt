package io.vault.jasper.dto

import EVMAddress
import io.swagger.annotations.ApiModel
import io.swagger.annotations.ApiModelProperty

@ApiModel("编辑用户别名DTO")
data class EditUserAliasDTO(
    @ApiModelProperty("钱包地址", required = true)
    @field:EVMAddress
    val address: String,
    @ApiModelProperty("代理钱包地址", required = true)
    val agentAddress: String,
    @ApiModelProperty("别名", required = true)
    val alias: String
)
