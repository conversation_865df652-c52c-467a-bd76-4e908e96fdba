package io.vault.jasper.service.activity

import io.vault.jasper.exception.BusinessException
import io.vault.jasper.exception.ResultEnum
import io.vault.jasper.model.activity.Task
import io.vault.jasper.model.activity.TaskGroup
import io.vault.jasper.repository.activity.TaskGroupRepository
import io.vault.jasper.repository.activity.TaskRepository
import org.slf4j.LoggerFactory
import org.springframework.data.repository.findByIdOrNull
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional

@Service
class TaskGroupService(
    private val taskGroupRepository: TaskGroupRepository,
    private val taskRepository: TaskRepository
) {
    private val logger = LoggerFactory.getLogger(this::class.java)

    /**
     * Create a new task group
     */
    fun createTaskGroup(taskGroup: TaskGroup): TaskGroup {
        return taskGroupRepository.save(taskGroup)
    }

    /**
     * Get a task group by ID
     */
    fun getTaskGroupById(id: String): TaskGroup? {
        return taskGroupRepository.findByIdOrNull(id)
    }

    /**
     * Get task groups by IDs
     */
    fun getTaskGroupsByIds(ids: List<String>): List<TaskGroup> {
        return taskGroupRepository.findByIdIn(ids)
    }

    /**
     * Add a task to a task group
     */
    @Transactional
    fun addTaskToTaskGroup(taskGroupId: String, taskId: String): TaskGroup {
        val taskGroup = getTaskGroupById(taskGroupId)
            ?: throw BusinessException(ResultEnum.TASK_GROUP_NOT_FOUND)

        val task = taskRepository.findByIdOrNull(taskId)
            ?: throw BusinessException(ResultEnum.TASK_NOT_FOUND)

        // Check if the task is already in the task group
        if (taskGroup.taskIds.contains(taskId)) {
            throw BusinessException(ResultEnum.TASK_ALREADY_IN_TASK_GROUP)
        }

        // Create a new task group with the task added
        val updatedTaskIds = taskGroup.taskIds.toMutableList().apply { add(taskId) }
        val updatedTaskGroup = taskGroup.copy(taskIds = updatedTaskIds)

        return taskGroupRepository.save(updatedTaskGroup)
    }

    /**
     * Remove a task from a task group
     */
    @Transactional
    fun removeTaskFromTaskGroup(taskGroupId: String, taskId: String): TaskGroup {
        val taskGroup = getTaskGroupById(taskGroupId)
            ?: throw BusinessException(ResultEnum.TASK_GROUP_NOT_FOUND)

        val task = taskRepository.findByIdOrNull(taskId)
            ?: throw BusinessException(ResultEnum.TASK_NOT_FOUND)

        // Check if the task is in the task group
        if (!taskGroup.taskIds.contains(taskId)) {
            throw BusinessException(ResultEnum.TASK_NOT_IN_TASK_GROUP)
        }

        // Create a new task group with the task removed
        val updatedTaskIds = taskGroup.taskIds.toMutableList().apply { remove(taskId) }
        val updatedTaskGroup = taskGroup.copy(taskIds = updatedTaskIds)

        return taskGroupRepository.save(updatedTaskGroup)
    }

    /**
     * Get tasks for a task group
     */
    fun getTasksForTaskGroup(taskGroupId: String): List<Task> {
        val taskGroup = getTaskGroupById(taskGroupId)
            ?: throw BusinessException(ResultEnum.TASK_GROUP_NOT_FOUND)

        // Get all tasks for this task group
        return taskRepository.findByIdIn(taskGroup.taskIds)
    }

    /**
     * Get task groups for a task
     */
    fun getTaskGroupsForTask(taskId: String): List<TaskGroup> {
        val task = taskRepository.findByIdOrNull(taskId)
            ?: throw BusinessException(ResultEnum.TASK_NOT_FOUND)

        // Find all task groups that contain this task
        return taskGroupRepository.findByTaskIdsContaining(taskId)
    }
}
