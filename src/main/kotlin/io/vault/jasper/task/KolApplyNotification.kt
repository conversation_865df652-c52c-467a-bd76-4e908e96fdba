package io.vault.jasper.task

import io.vault.jasper.model.KolApplyStatus
import io.vault.jasper.repository.*
import io.vault.jasper.service.*
import org.slf4j.LoggerFactory
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.beans.factory.annotation.Value
import org.springframework.scheduling.annotation.Scheduled
import org.springframework.stereotype.Service

@Service
class KolApplyNotification @Autowired constructor(
    private val kolApplyFormRepository: KolApplyFormRepository,
    private val mailManager: MailManager,
    private val systemService: SystemService
) {

    private val logger = LoggerFactory.getLogger(this::class.java)

    @Value("\${schedule_task_on}")
    lateinit var scheduleOn: String

    @Scheduled(fixedDelay = 5 * 60 * 1000, initialDelay = 1000)
    //@Scheduled(cron = "0 0 * * * *")
    fun run() {
        if(scheduleOn == "false"){
            return
        }
        try {
            exec()
        } catch (e: Exception) {
            logger.error(e.message, e)
        }
    }

    private fun exec() {
        val systemParameter = systemService.getParameter()
        val mailTo = systemParameter.kolApplyReceivingMails
        if (mailTo.isEmpty()) return

        val forms = kolApplyFormRepository.findByStatus(KolApplyStatus.SUBMITTED)
        if (forms.isEmpty()) return

        logger.debug("KOL Apply forms found: ${forms.size}")
        forms.forEach { f ->
            // send mail to mailTo
            val title = "New KOL Apply Form: ${f.wallet}"
            val content = "Wallet: ${f.wallet}\n" +
                    "X Handle: ${f.twitterHandle}\n" +
                    "Discord ID: ${f.discordId}\n" +
                    "What do you know about Jasper Vault: ${f.whatDoYouKnowAboutJasperVault}\n" +
                    "Status: ${f.status}\n" +
                    "Created: ${f.created}"
            mailManager.sendSesMail(
                recipients = mailTo.joinToString(","),
                subject = title,
                content = content
            )
            f.status = KolApplyStatus.IN_REVIEW
            kolApplyFormRepository.save(f)
        }
    }
}