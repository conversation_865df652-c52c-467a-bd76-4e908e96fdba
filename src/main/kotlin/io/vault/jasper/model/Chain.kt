package io.vault.jasper.model

import io.vault.jasper.enums.ChainType
import org.springframework.data.annotation.Id
import org.springframework.data.annotation.LastModifiedDate
import org.springframework.data.mongodb.core.mapping.Document
import java.time.LocalDateTime

@Document
data class Chain(
    @Id
    val id: String? = null,
    val chain: ChainType,
    val logo: String,
    var subgraphUrl: String? = null,
    var nftFreeOptionContractAddress: String? = null,
    var tradingCreditContractAddress: String? = null,
    var stoneContractAddress: String? = null,
    var userNetworkRebateAddress: String? = null,
    @LastModifiedDate
    val updated: LocalDateTime = LocalDateTime.now()
)