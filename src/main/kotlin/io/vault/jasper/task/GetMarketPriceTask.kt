package io.vault.jasper.task

import io.vault.jasper.enums.Symbol
import io.vault.jasper.model.*
import io.vault.jasper.repository.*
import io.vault.jasper.service.*
import io.vault.jasper.service.blockchain.BlockchainServiceFactory
import org.slf4j.LoggerFactory
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.beans.factory.annotation.Value
import org.springframework.data.mongodb.core.MongoTemplate
import org.springframework.data.mongodb.core.query.Criteria
import org.springframework.data.mongodb.core.query.Query
import org.springframework.scheduling.annotation.Scheduled
import org.springframework.stereotype.Service
import java.math.BigDecimal
import java.util.*


@Service
class GetMarketPriceTask @Autowired constructor(
    private val miniBridgeSwapOrderService: MiniBridgeSwapOrderService,
    private val miniBridgePriceOracleRepository: MiniBridgePriceOracleRepository,
    private val mongoTemplate: MongoTemplate,
) {

    private val logger = LoggerFactory.getLogger(this::class.java)

    private val houseKeepingDays = 1

    @Value("\${schedule_task_on}")
    lateinit var scheduleOn: String

    @Scheduled(fixedDelay = 10 * 1000, initialDelay = 5 * 1000)
    fun run() {
        if(scheduleOn == "false"){
            return
        }

        try{
            exec()
        } catch (e: Exception) {
            logger.error(e.message, e)
        }

        try{
            houseKeeping()
        } catch (e: Exception) {
            logger.error(e.message, e)
        }
    }

    private fun exec(){

        val priceOracleInfo = miniBridgeSwapOrderService.getMarketPrice(
            Symbol.BTC,
            Symbol.USDT
        )

        val price = priceOracleInfo.bidAssetUsdPrice.divide(priceOracleInfo.quoteAssetUsdPrice, 18, BigDecimal.ROUND_HALF_UP)
        val timestamp = Date().time / 1000

        val oracle = MiniBridgePriceOracle(
            price = price,
            timestamp = timestamp,
            bidAsset = priceOracleInfo.bidAsset,
            quoteAsset = priceOracleInfo.quoteAsset,
            oracleInfo = priceOracleInfo
        )

        miniBridgePriceOracleRepository.save(oracle)
    }

    private fun houseKeeping(){

        val startTime = Date().time / 1000 - houseKeepingDays * 24 * 60 * 60

        val query = Query()
        query.addCriteria(Criteria.where(MiniBridgePriceOracle::timestamp.name).lt(startTime))

        // 使用 mongoTemplate 执行查询
        val results: List<MiniBridgePriceOracle> = mongoTemplate.find(query, MiniBridgePriceOracle::class.java)

        results.forEach {
            miniBridgePriceOracleRepository.delete(it)
        }
    }
}