package io.vault.jasper.repository


import io.vault.jasper.model.*
import org.springframework.data.mongodb.repository.MongoRepository
import org.springframework.stereotype.Repository

@Repository
interface JPFirstTradeRebateRecordRepository : MongoRepository<JPFirstTradeRebateRecord, String> {
    fun findByOptionOrderId(optionOrderId: String): JPFirstTradeRebateRecord?
    fun findTopByOrderByOptionOrderIdDesc(): JPFirstTradeRebateRecord?
    fun findByStatus(
        status: JPFirstTradeRebateRecordStatus
    ): List<JPFirstTradeRebateRecord>

    fun findByBuyerAddress(
        buyerAddress: String
    ): List<JPFirstTradeRebateRecord>

    fun findByBuyerAddressIgnoreCase(
        buyerAddress: String
    ): List<JPFirstTradeRebateRecord>

    fun findByStatusAndSettleTxIdIsNotNull(
        status: JPFirstTradeRebateRecordStatus
    ): List<JPFirstTradeRebateRecord>

    fun findByStatusInAndSettleTxIdIsNotNull(
        statuses: List<JPFirstTradeRebateRecordStatus>,
    ): List<JPFirstTradeRebateRecord>

    fun findByStatusAndSettleTxIdIsNotNullAndSettled(
        status: JPFirstTradeRebateRecordStatus,
        settled: Boolean
    ): List<JPFirstTradeRebateRecord>
}