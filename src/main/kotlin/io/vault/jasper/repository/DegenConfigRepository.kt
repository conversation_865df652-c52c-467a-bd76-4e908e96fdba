package io.vault.jasper.repository

import io.vault.jasper.enums.ChainType
import io.vault.jasper.enums.Symbol
import io.vault.jasper.model.DegenConfig
import org.springframework.data.mongodb.repository.MongoRepository
import org.springframework.stereotype.Repository

@Repository
interface DegenConfigRepository : MongoRepository<DegenConfig, String> {
    fun findByBidAsset(bidAsset: Symbol): List<DegenConfig>
    fun findByBidAssetIsNull(): List<DegenConfig>
    fun findFirstByBidAsset(bidAsset: Symbol): DegenConfig?
    fun findFirstByBidAssetAndChain(
        bidAsset: Symbol,
        chain: ChainType
    ): DegenConfig?
    fun findByChain(chain: ChainType): List<DegenConfig>
}