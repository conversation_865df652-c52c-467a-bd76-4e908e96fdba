package io.vault.jasper.task

import io.vault.jasper.model.KYT
import io.vault.jasper.repository.KYTRepository
import io.vault.jasper.repository.OptionOrderRepository
import io.vault.jasper.service.MistTrackService
import io.vault.jasper.service.SystemService
import org.slf4j.LoggerFactory
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.beans.factory.annotation.Value
import org.springframework.context.annotation.Profile
import org.springframework.scheduling.annotation.Scheduled
import org.springframework.stereotype.Service
import org.web3j.crypto.Keys
import java.time.LocalDateTime

@Service
@Profile("prod")
class UpdateKYTScoreTask @Autowired constructor(
    private val kytRepository: KYTRepository,
    private val mistTrackService: MistTrackService,
    private val systemService: SystemService,
    private val optionOrderRepository: OptionOrderRepository
) {

    private val logger = LoggerFactory.getLogger(this::class.java)

    private val batchUpdateCount = 100

    @Value("\${schedule_task_on}")
    lateinit var scheduleOn: String

    // 每15秒执行一次
    // @Scheduled(fixedDelay = 15 * 1000, initialDelay = 15 * 1000)
    fun run() {
        if(scheduleOn == "false"){
            return
        }

        val parameter = systemService.getParameter()
        if(parameter.kytTaskSwitch == false){
            logger.info("KYT task switch is off.")
            return
        }

        try {
            exec()
        } catch (e: Exception) {
            logger.error(e.message, e)
        }
    }

    /**
     * 更新系统内钱包地址 KYT Score
     */
    private fun exec() {

        // logger.info("Update KYT Score task begin.")

        // 更新所有 Score 为 0 的 KYT 的 Score
//        kytRepository.findByScore(0).forEach { kyt ->
//            updateKYTScore(kyt)
//        }

        // 更新所有最后更新时间为24小时前的 KYT 的 Score
        val allOutdatedKyts = kytRepository.findByUpdatedLessThanAndChannelIsNotNull(LocalDateTime.now().minusMonths(1))
        logger.info("Update outdated kyt score ${allOutdatedKyts.size}.")

        var count = 0
        for(kyt in allOutdatedKyts) {

            updateKYTScore(kyt)
            count++

            if(count >= batchUpdateCount){
                break
            }
        }

        // logger.info("Update outdated kyt score task end.")
    }

    private fun updateKYTScore(kyt: KYT) {
        val address = kyt.address
        try {
            //val score = mistTrackService.getScoreFromMistTrack(address, coin)
            val score = mistTrackService.getScoreFromBeosin(address)
            kyt.score = score
            kytRepository.save(kyt)

            Thread.sleep(500) // Sleep for 0.5s
        } catch (e: Exception) {
            logger.error("Update KYT $address KYT Score error: ${e.message}", e)
        }
    }

    /**
     * 4W次请求，分31日，即每67秒一个请求
     * 每67秒执行一次，取N分钟内发生过交易，且过去N分钟内在KYT没有更新过的地址，更新KYT Score
     */
    @Scheduled(fixedDelay = 67 * 1000, initialDelay = 15 * 1000)
    fun runV2() {
        if (scheduleOn == "false") {
            return
        }

        val parameter = systemService.getParameter()
        if (parameter.kytTaskSwitch == false) {
            logger.info("KYT task switch is off.")
            return
        }

        try {
            execV2()
        } catch (e: Exception) {
            logger.error(e.message, e)
        }
    }

    private fun execV2() {
        val kytCheckIntervalInMin = systemService.getParameter().kytCheckIntervalInMinutes.toLong()
        val timeAgo = LocalDateTime.now().minusMinutes(kytCheckIntervalInMin)
        // 获取KYT.updated在过去N分钟内的地址列表
        val updatedAddresses = kytRepository.findByUpdatedGreaterThan(timeAgo).map { it.address }
        logger.info("Update kyt score task, updatedAddresses: ${updatedAddresses.size}")
        logger.info("Update kyt score task, updatedAddresses List: $updatedAddresses")
        // 找出过去N分钟内插入OptionOrder表的记录里的buyer地址
        optionOrderRepository.findFirstByCreatedGreaterThanAndBuyerNotIn(
            timeAgo,
            updatedAddresses
        )?.buyer?.let { address ->
            // get KYT from address
            val kyt = kytRepository.findByAddressIgnoreCase(address) ?: run {
                val checkSumAddress = Keys.toChecksumAddress(address)
                KYT(address = checkSumAddress, score = 0)
            }
            try {
                kyt.score = mistTrackService.getScoreFromBeosin(kyt.address)
                val saved = kytRepository.save(kyt)
                logger.info("Update kyt score task, updated address: ${saved.address}, score: ${saved.score}, updated time: ${saved.updated}")
            } catch (e: Exception) {
                logger.error("Failed to get kyt score for ${kyt.address}", e)
            }
        } ?: run {
            logger.info("没有找到需要KYT的交易地址.")
        }
    }
}
