package io.vault.jasper.listener

import io.vault.jasper.event.AfterCreateOptionOrderEvent
import io.vault.jasper.model.OptionOrder
import io.vault.jasper.repository.OptionOrderRepository
import io.vault.jasper.repository.OrderRepository
import io.vault.jasper.service.AlertService
import io.vault.jasper.service.BtrCampaignService
import io.vault.jasper.service.LarkNotifierService
import io.vault.jasper.service.OptionOrderService
import io.vault.jasper.service.SystemService
import io.vault.jasper.service.blockchain.BlockchainServiceFactory
import io.vault.jasper.service.blockchain.EvmService
import org.slf4j.LoggerFactory
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.context.event.EventListener
import org.springframework.core.annotation.Order
import org.springframework.data.repository.findByIdOrNull
import org.springframework.stereotype.Component
import java.math.BigDecimal

@Component
class CreatedOptionOrderEventListener @Autowired constructor(
    private val optionOrderRepository: OptionOrderRepository,
    private val blockchainServiceFactory: BlockchainServiceFactory,
    private val orderRepository: OrderRepository,
    private val systemService: SystemService,
    private val larkNotifierService: LarkNotifierService,
    private val optionOrderService: OptionOrderService,
    private val btrCampaignService: BtrCampaignService,
    private val alertService: AlertService
) {

    private val logger = LoggerFactory.getLogger(this::class.java)

    private fun getOptionOrderAndEvmService(event: AfterCreateOptionOrderEvent): Pair<OptionOrder, EvmService>? {
        var oo = optionOrderRepository.findByIdOrNull(event.optionOrderId) ?: run {
            logger.error("Option order not found in ${AfterCreateOptionOrderEvent::class.simpleName}: ${event.optionOrderId}")
            return null
        }
        // 活动仅支持Bitlayer
        // if (oo.chain != ChainType.BITLAYER) return null

        val blockchainService = blockchainServiceFactory.getBlockchainService(oo.chain) ?: return null
        if (!blockchainServiceFactory.isEvmService(blockchainService)) return null
        val evmService = blockchainService as EvmService

        if (oo.blockHeight == null) {
            val tx = try {
                evmService.evmUtil.web3j.ethGetTransactionByHash(oo.txHash).send().transaction.get()
            } catch (e: Exception) {
                logger.error("Failed to get transaction by hash: ${oo.txHash}, ${e.message}")
                return null
            }
            optionOrderService.updateFields(
                mapOf(
                    OptionOrder::blockHeight.name to tx.blockNumber.toLong()
                ),
                oo.id!!
            )
        }

        return Pair(oo, evmService)
    }

    /**
     * 处理月光宝盒活动
     */
    @Order(1)
    @EventListener
    fun handleMoonlightBoxActivity(event: AfterCreateOptionOrderEvent) {
        val (oo, evmService) = getOptionOrderAndEvmService(event) ?: return

        // 是否属于月光宝盒活动
        // 用户在同一交易里有另一笔方向相反的订单
        optionOrderRepository.findByChainAndTxHashAndBuyerAndDirectionNotAndOnChainOrderIdNot(
            oo.chain,
            oo.txHash!!,
            oo.buyer!!,
            oo.direction!!,
            oo.onChainOrderId!!
        ).firstOrNull()?.let {
            evmService.getStoneBurnInfoFromLogForAddress(
                it.txHash!!,
                oo.buyer!!
            )?.let { nftId ->
                if (nftId == 1) { // 月光宝盒的NFT ID=1
                    // if (it.onChainOrderId!!.toLong() < oo.onChainOrderId!!.toLong()) { // 结算模式改为【清算】
                    //     oo.liquidityType = 0
                    // } else {
                    //     it.liquidityType = 0
                    // }
                    
                    // 更新上一笔月光宝盒订单
                    optionOrderService.updateFields(
                        mapOf(
                            OptionOrder::usedMoonlightBox.name to false,
                            OptionOrder::stoneActivityNftId.name to nftId.toString(),
                            OptionOrder::liquidityType.name to 2 // 结算
                        ),
                        it.id!!
                    )

                    // 更新这笔月光宝盒订单
                    optionOrderService.updateFields(
                        mapOf(
                            OptionOrder::usedMoonlightBox.name to false,
                            OptionOrder::stoneActivityNftId.name to nftId.toString(),
                            OptionOrder::liquidityType.name to 2 // 结算
                        ),
                        oo.id!!
                    )
                }
            }
        }
    }

    /**
     * 处理现实宝石活动
     */
    @Order(2)
    @EventListener
    fun handleRealityStoneActivity(event: AfterCreateOptionOrderEvent) {
        val (oo, evmService) = getOptionOrderAndEvmService(event) ?: return

        // 是否属于现实宝石活动
        // 用户在同一交易里有另一笔方向相反的订单
        optionOrderRepository.findByChainAndTxHashAndBuyerAndDirectionNotAndOnChainOrderIdNot(
            oo.chain,
            oo.txHash!!,
            oo.buyer!!,
            oo.direction!!,
            oo.onChainOrderId!!
        ).firstOrNull()?.let {
            evmService.getStoneBurnInfoFromLogForAddress(
                it.txHash!!,
                oo.buyer!!
            )?.let { nftId ->
                if (nftId in 2..3 || nftId in 32..33 || nftId == 6 || nftId == 36) {
                    optionOrderService.updateFields(
                        mapOf(
                            OptionOrder::usedRealityStone.name to true,
                            OptionOrder::stoneActivityNftId.name to nftId.toString()
                        ),
                        it.id!!
                    )

                    optionOrderService.updateFields(
                        mapOf(
                            OptionOrder::usedRealityStone.name to true,
                            OptionOrder::stoneActivityNftId.name to nftId.toString()
                        ),
                        oo.id!!
                    )
                }
            }
        }
    }

    /**
     * 处理力量宝石活动
     */
    @Order(3)
    @EventListener
    fun handlePowerStoneActivity(event: AfterCreateOptionOrderEvent) {
        val (oo, evmService) = getOptionOrderAndEvmService(event) ?: return

        // 是否属于力量宝石活动
        // 用户在同一交易里有另一笔方向相同的订单
        optionOrderRepository.findByChainAndTxHashAndBuyerAndDirectionAndOnChainOrderIdNot(
            oo.chain,
            oo.txHash!!,
            oo.buyer!!,
            oo.direction!!,
            oo.onChainOrderId!!
        ).firstOrNull()?.let {
            evmService.getStoneBurnInfoFromLogForAddress(
                it.txHash!!,
                oo.buyer!!
            )?.let { nftId ->
                if (nftId in 4..5 || nftId in 34..35 || nftId == 7 || nftId == 37) {
                    optionOrderService.updateFields(
                        mapOf(
                            OptionOrder::usedPowerStone.name to true,
                            OptionOrder::stoneActivityNftId.name to nftId.toString()
                        ),
                        it.id!!
                    )

                    optionOrderService.updateFields(
                        mapOf(
                            OptionOrder::usedPowerStone.name to true,
                            OptionOrder::stoneActivityNftId.name to nftId.toString()
                        ),
                        oo.id!!
                    )
                }
            }
        }
    }

    /**
     * 处理空间宝石活动
     */
    @Order(4)
    @EventListener
    fun handleSpaceStoneActivity(event: AfterCreateOptionOrderEvent) {
        val (oo, evmService) = getOptionOrderAndEvmService(event) ?: return

        // 是否属于空间宝石活动
        // 只有一笔订单，且log里有nftid=8,9,10, 38,39,40
        evmService.getStoneBurnInfoFromLogForAddress(
            oo.txHash!!,
            oo.buyer!!
        )?.let { nftId ->
            if (nftId in 8..10 || nftId in 38..40) {
                optionOrderService.updateFields(
                    mapOf(
                        OptionOrder::usedSpaceStone.name to true,
                        OptionOrder::stoneActivityNftId.name to nftId.toString()
                    ),
                    oo.id!!
                )
            }
        }
    }

    /**
     * 处理时间宝石活动
     */
    @Order(5)
    @EventListener
    fun handleTimeStoneActivity(event: AfterCreateOptionOrderEvent) {
        val (oo, evmService) = getOptionOrderAndEvmService(event) ?: return

        if(oo.lockDate != null && oo.lockDate.isBefore(oo.expiryDate)){

//            var nftId = "11"
//            if(oo.expiryInHour == "0.5") {
//                if (oo.bidAmount!!.compareTo(BigDecimal("0.05")) == 0) {
//                    nftId = "12"
//                } else if (oo.bidAmount!!.compareTo(BigDecimal("0.2")) == 0) {
//                    nftId = "13"
//                }
//            }
//
//            if(oo.expiryInHour == "2") {
//                if (oo.bidAmount!!.compareTo(BigDecimal("0.01")) == 0) {
//                    nftId = "41"
//                } else if (oo.bidAmount!!.compareTo(BigDecimal("0.05")) == 0) {
//                    nftId = "42"
//                } else if (oo.bidAmount!!.compareTo(BigDecimal("0.2")) == 0) {
//                    nftId = "43"
//                }
//            }

            optionOrderService.updateFields(
                mapOf(
                    OptionOrder::usedTimeStone.name to true,
                ),
                oo.id!!
            )
        }
    }

    // @Order(5)
    // @EventListener
    // fun handleBtrCampaign(event: AfterCreateOptionOrderEvent) {
    //     val oo = optionOrderRepository.findByIdOrNull(event.optionOrderId) ?: return
    //     try {
    //         btrCampaignService.handleOptionOrder(oo)
    //     } catch (e: Exception) {
    //         logger.error("Failed to handle BTR campaign for option order ${oo.id}", e)
    //     }
    // }

    /**
     * 订单价值超过阈值，发送预警信息
     */
    @Order(6)
    @EventListener
    fun handleNotionalAmountNotify(event: AfterCreateOptionOrderEvent) {
        val oo = optionOrderRepository.findByIdOrNull(event.optionOrderId) ?: return
        val o = orderRepository.findByIdOrNull(oo.orderId) ?: return
        val threshold = systemService.getParameter().orderVolumeWarningThreshold
        if (o.volume.compareTo(threshold) == 1) {
            larkNotifierService.sendLark(
                "订单价值超额预警",
                "${oo.chain}订单=${oo.onChainOrderId}价值超过${threshold.stripTrailingZeros().toPlainString()}, " +
                        "交易哈希=${oo.txHash}"
            )
        }
    }

    /**
     * 获取权利金分发情况
     */
    @Order(7)
    @EventListener
    fun handlePremiumFeeDistribution(event: AfterCreateOptionOrderEvent) {
        val oo = optionOrderRepository.findByIdOrNull(event.optionOrderId) ?: run {
            logger.error("Option order not found: ${event.optionOrderId}")
            return
        }

        // 需要有txHash和买卖家vault地址
        if (oo.txHash.isNullOrBlank() || oo.buyerVault.isNullOrBlank() || oo.sellerVault.isNullOrBlank()) {
            logger.info("Missing required fields for premium fee distribution: txHash=${oo.txHash}, buyerVault=${oo.buyerVault}, sellerVault=${oo.sellerVault}")
            return
        }

        // 需要有权利金资产信息
        val premiumAssetAddress = oo.premiumAsset?.address ?: run {
            logger.info("Missing premium asset address for option order: ${oo.id}")
            return
        }

        try {
            // 获取对应链的区块链服务
            val blockchainService = blockchainServiceFactory.getBlockchainService(oo.chain)
            if (blockchainService !is EvmService) {
                logger.info("Not an EVM chain: ${oo.chain}")
                return
            }

            // 获取权利金分发情况
            val distribution = blockchainService.getPremiumFeeDistribution(
                txHash = oo.txHash!!,
                buyerVault = oo.buyerVault!!,
                sellerVault = oo.sellerVault!!,
                premiumAssetAddress = premiumAssetAddress
            )

            if (distribution != null) {
                // 更新订单记录
                val savedOo = optionOrderService.updatePremiumFeeDistribution(oo, distribution)
                logger.info(
                    "Updated premium fee distribution for order ${oo.id}: " +
                    "seller=${distribution.sellerPremiumFee}(${savedOo?.sellerPremiumFeeInUsdt} USDT), " +
                    "platform=${distribution.platformPremiumFee}(${savedOo?.platformPremiumFeeInUsdt} USDT)"
                )

                // 如果distribution.sellerPremiumFee==0，发送警报
                if (distribution.sellerPremiumFee == BigDecimal.ZERO) {
                    // todo 通过better stack发送预警
                    alertService.emergency("[LP Vault 未收到权利金] Seller premium fee is zero for order ${oo.id}")
                }
            } else {
                logger.warn("Failed to get premium fee distribution for order ${oo.id}")
            }
        } catch (e: Exception) {
            logger.error("Error processing premium fee distribution for order ${oo.id}", e)
        }
    }
}