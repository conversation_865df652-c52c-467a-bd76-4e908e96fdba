package io.vault.jasper.model


import org.springframework.data.annotation.CreatedDate
import org.springframework.data.annotation.Id
import org.springframework.data.mongodb.core.index.Indexed
import org.springframework.data.mongodb.core.mapping.Document
import org.springframework.data.mongodb.core.mapping.Field
import org.springframework.data.mongodb.core.mapping.FieldType
import java.math.BigDecimal
import java.time.LocalDateTime

@Document("user_referral_daily_summary")
data class UserReferralDailySummary(
    @Id
    val id: String? = null,
    @Indexed(background = true)
    @Field("date_string")
    val dateString: String, // 日期字符串
    @Indexed(background = true)
    @Field("user_id")
    val userId: String, // 用户ID
    @Indexed(background = true)
    val address: String, // 钱包地址
    var referees: Int = 0, // 推荐人数
    @Field(name = "referee_volume", targetType = FieldType.DECIMAL128)
    var refereeVolume: BigDecimal = BigDecimal.ZERO, // 推荐人交易量，USDT 为单位
    @CreatedDate
    val created: LocalDateTime = LocalDateTime.now() // 创建时间
)