package io.vault.jasper.task

import io.vault.jasper.blockchain.IEvmBlockchainUtil
import io.vault.jasper.enums.ChainType
import io.vault.jasper.enums.Symbol
import io.vault.jasper.event.BlockScanCompletedEvent
import io.vault.jasper.exception.BusinessException
import io.vault.jasper.exception.ResultEnum
import io.vault.jasper.service.blockchain.EvmService
import org.slf4j.LoggerFactory
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.beans.factory.annotation.Value
import org.springframework.context.ApplicationEventPublisher
import org.springframework.scheduling.annotation.Async
import org.springframework.scheduling.annotation.AsyncResult
import org.springframework.stereotype.Service
import org.web3j.protocol.core.DefaultBlockParameterNumber
import org.web3j.protocol.core.methods.response.EthBlock
import java.math.BigInteger
import java.util.concurrent.Future

@Service
open class AsyncEvmScanner @Autowired constructor(
    private val eventPublisher: ApplicationEventPublisher,
) {

    private val logger = LoggerFactory.getLogger(this::class.java)

    @Value("\${schedule_task_on}")
    lateinit var scheduleOn: String

    private var lastLogBlockNumber: Long = 0

    private val systemDepositAddress = "******************************************"

    private val bitlayerUSDTAddress = "******************************************"

    private val arbitrumUSDTAddress = "******************************************"

    @Async
    open fun scanBlock(
        bn: Long,
        latestBlockHeight: BigInteger,
        blockchainUtil: IEvmBlockchainUtil,
        evmService: EvmService,
        optionServiceContract: String,
        callFnHash: String,
        putFnHash: String,
        issuanceModuleContract: String,
        //coin98MintContract: String?
        bitlayerBinanceEventContract: String?
    ): Future<Unit> {

        val block = try {
            blockchainUtil.web3j.ethGetBlockByNumber(
                DefaultBlockParameterNumber(bn.toBigInteger()),
                true
            ).send()
        } catch (e: Exception) {
            logger.error("Error getting block: $bn", e)
            return AsyncResult(Unit)
        }

        val optionService = optionServiceContract.lowercase() // 结算事件
        val issuanceModule = issuanceModuleContract.lowercase() // LPValue Withdraw 事件

        val matchReceivers = mutableListOf(
            optionService,
            issuanceModule
        )
        if (bitlayerBinanceEventContract != null) {
            matchReceivers.add(bitlayerBinanceEventContract.lowercase())
        }

        if (bn - lastLogBlockNumber > 1000) {
            logger.info("开始扫描区块: $bn chainId=${blockchainUtil.chainId}")
        }

        //logger.info("开始扫描区块: $bn chainId=${blockchainUtil.chainId}")

        block?.block?.transactions?.forEach {
            val tx = it.get() as EthBlock.TransactionObject
            if (tx.to == null) return@forEach
            try {
                when {
                    tx.to.compareTo(optionService, true) == 0 -> {
                        //logger.info("Found Option Service Contract Transaction: ${tx.hash} in Block: $bn, maybe settlement transaction")
                        evmService.saveOptionSettleFromTxHash(tx.hash)
                    }
                    tx.to.compareTo(issuanceModule, true) == 0 -> {
                        //logger.info("Found Issuance Module Contract Transaction: ${tx.hash} in Block: $bn")
                        evmService.saveLpValueWithdrawFromTxHash(tx)
                    }
                    // Mini Bridge Swap Order
                    tx.to.compareTo(systemDepositAddress, true) == 0 -> {

                        //logger.info("Hash=${tx.hash}\t 检测到 Mini Bridge 存款交易")
                        val chain = blockchainUtil.chainType
                        evmService.saveMiniBridgeSwapOrderDepositHash(
                            chain,
                            tx.from,
                            Symbol.BTC,
                            tx.value,
                            tx.hash
                        )
                    }
                    (tx.to.compareTo(bitlayerUSDTAddress, true) == 0 &&
                            blockchainUtil.chainType == ChainType.BITLAYER &&
                            tx.input.startsWith("0xa9059cbb")
                            ) -> {

                        val (toAddress, amount) = evmService.parseERC20TransferToAddressFromInputData(
                            tx.input
                        )

                        if(toAddress != null && amount != null && toAddress.compareTo(systemDepositAddress, true) == 0) {
                            //logger.info("Hash=${tx.hash}\t 检测到 Bitlayer USDT 存款交易")
                            val chain = blockchainUtil.chainType
                            evmService.saveMiniBridgeSwapOrderDepositHash(
                                chain,
                                tx.from,
                                Symbol.USDT,
                                amount,
                                tx.hash
                            )
                        }
                    }
                    (tx.to.compareTo(arbitrumUSDTAddress, true) == 0 &&
                            blockchainUtil.chainType == ChainType.ARBITRUM &&
                            tx.input.startsWith("0xa9059cbb")
                            ) -> {

                        val (toAddress, amount) = evmService.parseERC20TransferToAddressFromInputData(
                            tx.input
                        )

                        if(toAddress != null && amount != null && toAddress.compareTo(systemDepositAddress, true) == 0) {
                            //logger.info("Hash=${tx.hash}\t 检测到 ARBITRUM USDT 存款交易")
                            val chain = blockchainUtil.chainType
                            evmService.saveMiniBridgeSwapOrderDepositHash(
                                chain,
                                tx.from,
                                Symbol.USDT,
                                amount,
                                tx.hash
                            )
                        }
                    }
                    tx.input.startsWith("0x47e1da2a") -> {
                        //logger.info("Hash=${tx.hash}\t 检测到executeBatch方法")

                        evmService.saveOptionOrderFromTxHash(
                            tx.hash,
                            optionOrder = null,
                            lastBlockHeight = latestBlockHeight,
                            block = block.block
                        )
                    }
                    tx.input.startsWith("0x1fad948c") -> {

                        //logger.info("Hash=${tx.hash}\t 检测到bundler方法")
                        val (strikePrice, timestamp) = evmService.decodeExecutedBatchMethodFromBundlerInputData(
                            tx.input,
                            tx.to,
                        )

                        if(strikePrice != null){
                            //logger.info("Hash=${tx.hash}\t 检测到Bundler ExecuteBatch方法, strikePrice=$strikePrice, timestamp=$timestamp")
                            evmService.saveOptionOrderFromTxHash(
                                tx.hash,
                                optionOrder = null,
                                lastBlockHeight = latestBlockHeight,
                                block = block.block
                            )
                        } else {

                            // 监测是否结算交易
                            val isSettlementTx = evmService.decodeSettlementMethodFromBundlerInputData(
                                tx.input,
                                tx.to,
                                optionService
                            )

                            if(isSettlementTx){
                                //logger.info("Found Option Service Contract Transaction: ${tx.hash} in Block: $bn, maybe settlement transaction in bundler")
                                evmService.saveOptionSettleFromTxHash(tx.hash)
                            }
                        }
                    }
                    // Redeem 事件
                    tx.input.startsWith(evmService.redeemMethodId) -> {
                        evmService.checkRedeem(tx)
                    }
                    else -> {
                        // logger.info("Found Unknown Contract Transaction: ${tx.hash} in Block: $bn")
                    }
                }
            } catch (e: BusinessException) {
                if (e.code == ResultEnum.TRANSACTION_FAILED.code) {
                    logger.info("Transaction(${tx.hash}) failed or not AddOrder transaction, skipping")
                } else {
                    logger.error("Error processing transaction: ${tx.hash}", e)
                }
            } catch (e: Exception) {
                logger.error("Error processing transaction: ${tx.hash}", e)
            }
        } ?: run {
            logger.warn("Block $bn is empty")
            return AsyncResult(Unit)
        }

        if(bn - lastLogBlockNumber > 1000) {
            logger.info("结束扫描区块: $bn chainId=${blockchainUtil.chainId}")
            lastLogBlockNumber = bn
        }

        // 发布区块扫描完成事件
        // val startTime = System.currentTimeMillis()
        eventPublisher.publishEvent(BlockScanCompletedEvent(this, evmService.chainType, block.block))
        // val endTime = System.currentTimeMillis()
        // logger.info("Block scan completion event published in ${endTime - startTime}ms")

        return AsyncResult(Unit)
    }
}