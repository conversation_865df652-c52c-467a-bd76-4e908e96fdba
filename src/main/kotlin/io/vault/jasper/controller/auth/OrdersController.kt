package io.vault.jasper.controller.auth

import EVMAddress
import com.fasterxml.jackson.databind.ObjectMapper
import io.swagger.annotations.ApiOperation
import io.vault.jasper.ApiResponse
import io.vault.jasper.annotation.ApiCache
import io.vault.jasper.blockchain.EvmSignatureUtil
import io.vault.jasper.dto.*
import io.vault.jasper.enums.ChainType
import io.vault.jasper.enums.OptionDirection
import io.vault.jasper.enums.Symbol
import io.vault.jasper.exception.BusinessException
import io.vault.jasper.exception.ResultEnum
import io.vault.jasper.model.*
import io.vault.jasper.repository.*
import io.vault.jasper.response.*
import io.vault.jasper.service.*
import io.vault.jasper.utils.AuthUtil
import io.vault.jasper.utils.DateTimeUtil
import org.slf4j.LoggerFactory
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.data.domain.PageImpl
import org.springframework.data.domain.PageRequest
import org.springframework.data.domain.Sort
import org.springframework.data.mongodb.core.MongoTemplate
import org.springframework.data.mongodb.core.query.Criteria
import org.springframework.data.mongodb.core.query.Query
import org.springframework.data.repository.findByIdOrNull
import org.springframework.validation.annotation.Validated
import org.springframework.web.bind.annotation.*
import org.web3j.crypto.Hash
import org.web3j.crypto.Keys
import org.web3j.crypto.Sign
import org.web3j.utils.Numeric
import java.time.LocalDateTime
import java.util.*
import javax.servlet.http.HttpServletRequest
import javax.validation.Valid
import javax.validation.constraints.Min


@RestController
@RequestMapping("/orders")
@Validated
class OrdersController @Autowired constructor(
    private val orderRepository: OrderRepository,
    private val mongoTemplate: MongoTemplate,
    private val evmSignatureUtil: EvmSignatureUtil,
    private val optionOrderRepository: OptionOrderRepository,
    private val mistTrackService: MistTrackService,
    private val degenConfigRepository: DegenConfigRepository,
    private val systemService: SystemService,
    private val authUtil: AuthUtil,
    private val userRepository: UserRepository,
    private val optionOrderInfoRepository: OptionOrderInfoRepository,
    private val degenLPVaultRepository: DegenLPVaultRepository,
    private val echoooCampaignService: EchoooCampaignService,
    private val optionOrderService: OptionOrderService,
    private val stoneService: StoneService,
    private val userNetworkRepository: UserNetworkRepository
) {

    private var logger = LoggerFactory.getLogger(this::class.java)

    private val objectMapper = ObjectMapper()

    @ApiOperation("Degen配置列表")
    @CrossOrigin
    @ApiCache(key = "orders:option:degen:config:list", dynamicKey = ["chain"], expire = 60)
    @GetMapping("/option/degen/config/list")
    fun getDegenConfigList(
        request: HttpServletRequest,
        @RequestParam chain: ChainType
    ): ApiResponse<List<DegenConfig>> {

        authUtil.filterIPBlacklist(request)
        val configList = degenConfigRepository.findByChain(chain)
        return ApiResponse.success(configList)
    }

    @ApiOperation("买家订单记录")
    @CrossOrigin
    @ApiCache(
        key = "orders:optionOrders",
        dynamicKey = ["buyer", "chain", "bidAsset", "page", "pageSize", "orderType", "status"],
        expire = 5
    )
    @GetMapping("/optionOrders")
    fun getOptionOrders(
        @RequestParam @EVMAddress buyer: String,
        @RequestParam chain: ChainType,
        @RequestParam bidAsset: List<Symbol>,
        @RequestParam @Min(1) page: Int,
        @RequestParam @Min(1) pageSize: Int,
        @RequestParam orderType: OrderType,
        @RequestParam status: List<OptionStatus>,
        request: HttpServletRequest
    ): ApiResponse<PageResponse<ExecutedOrderResponse>> {
        authUtil.filterIPBlacklist(request)
        val pageable = PageRequest.of(page - 1, pageSize, Sort.by(Sort.Order.desc(OptionOrder::id.name)))
        val query = Query()
        // query.addCriteria(
        //     Criteria.where(OptionOrder::chain.name).`is`(chain).and(OptionOrder::orderType.name).`is`(orderType)
        //         .and(OptionOrder::buyer.name).regex("(?i)$buyer").and(OptionOrder::status.name).`in`(status)
        //         .and(OptionOrder::bidAsset.name).`in`(bidAsset)
        // )
        val criteria = Criteria.where(OptionOrder::chain.name).`is`(chain).and(OptionOrder::orderType.name).`is`(orderType)
            .and(OptionOrder::buyer.name).regex("(?i)$buyer").and(OptionOrder::status.name).`in`(status)
            .and(OptionOrder::bidAsset.name).`in`(bidAsset)
            .and(OptionOrder::limitOrder.name).ne(true)

        // 月光宝盒活动中，清算订单不显示
        val orCriteria = Criteria().orOperator(
            Criteria.where(OptionOrder::usedMoonlightBox.name).isNull,
            Criteria.where(OptionOrder::liquidityType.name).ne(0)
        )
        criteria.andOperator(orCriteria)

        query.addCriteria(criteria)

        val totalCount = mongoTemplate.count(query, OptionOrder::class.java)
        query.with(pageable)
        val optionsResult = mongoTemplate.find(query, OptionOrder::class.java)
        val pageObj = PageImpl(optionsResult, pageable, totalCount)
        val optionsList = optionsResult.mapNotNull {
            //val order = orderRepository.findByIdOrNull(it.orderId) ?: return@mapNotNull null
            var lockDateTimeStamp = DateTimeUtil.convertLocalDateTimeToTimestamp(it.expiryDate)
            if(it.lockDate != null){
                lockDateTimeStamp = DateTimeUtil.convertLocalDateTimeToTimestamp(it.lockDate)
            }
            val stonePieces = stoneService.getNftIdFromOptionOrder(it)
            ExecutedOrderResponse(
                id = it.id!!,
                orderId = it.orderId,
                orderType = it.orderType,
                chain = it.chain,
                onChainOrderId = it.onChainOrderId,
                buyerAddress = it.buyer,
                buyerVault = it.buyerVault,
                buyerVaultSalt = it.buyerVaultSalt,
                sellerAddress = it.seller,
                businessType = it.direction!!,

                bidAsset = it.bidAsset,
                bidAmount = it.bidAmount?.stripTrailingZeros()?.toPlainString(),

                underlyingAsset = it.underlyingAsset,
                underlyingAssetAddress = it.underlyingAssetAddress,
                underlyingAssetAmount = it.amount.stripTrailingZeros().toPlainString(),

                strikeAsset = it.strikeAsset,
                strikeAmount = it.strikeAmount.stripTrailingZeros().toPlainString(),
                strikePrice = it.strikePrice!!.stripTrailingZeros().toPlainString(),
                actualStrikeAmount = it.actualStrikeAmount?.stripTrailingZeros()?.toPlainString(),

                premiumFeesAsset = if (it.premiumAsset?.asset != null) Symbol.valueOf(it.premiumAsset!!.asset) else Symbol.USDT,
                premiumFee = it.premiumFeePay?.stripTrailingZeros()?.toPlainString(),
                premiumFeeInUsdt = it.premiumFeePayInUsdt?.stripTrailingZeros()?.toPlainString(),
                premiumFeeShouldPay = it.premiumFeeShouldPay?.stripTrailingZeros()?.toPlainString(),
                premiumFeeShouldPayInUsdt = it.premiumFeeShouldPayInUsdt?.stripTrailingZeros()?.toPlainString(),

                createDate = DateTimeUtil.convertLocalDateTimeToTimestamp(it.created),
                expiryDate = DateTimeUtil.convertLocalDateTimeToTimestamp(it.expiryDate),
                lockDate = lockDateTimeStamp,
                status = it.status,
                degen = it.jvault,
                marketPriceAtSettlement = it.marketPriceAtSettlement,
                settlementHash = it.settlementHash,
                buyerProfit = it.buyerProfit?.stripTrailingZeros()?.toPlainString(),
                expiryInHour = it.expiryInHour,
                usedMoonlightBox = it.usedMoonlightBox,
                usedRealityStone = it.usedRealityStone ?: false,
                usedPowerStone = it.usedPowerStone ?: false,
                usedSpaceStone = it.usedSpaceStone ?: false,
                usedTimeStone = it.usedTimeStone ?: false,
                stoneActivityNftId = it.stoneActivityNftId,
                txHash = it.txHash,
                premiumFeeLog = it.premiumFeeLog,
                awardStonePieceNftIds = stonePieces
            )
        }

        return ApiResponse.success(PageResponse(
            page = page,
            pageSize = pageSize,
            totalPage = pageObj.totalPages,
            total = totalCount,
            hasNext = pageObj.hasNext(),
            data = optionsList
        ))
    }

    @ApiOperation("Profolio -> My Positions")
    @CrossOrigin
    @ApiCache(
        key = "orders:my_positions",
        dynamicKey = ["address", "chain", "creatorRole", "direction", "orderType", "bidAsset", "isLeverage", "page", "pageSize"],
        expire = 5
    )
    @GetMapping("/my_positions")
    fun myPositions(
        @RequestParam @EVMAddress address: String,
        @RequestParam chain: ChainType,
        @RequestParam creatorRole: CreatorRole?, // BUYER or SELLER
        @RequestParam direction: OptionDirection?, // CALL or PUT
        @RequestParam orderType: OrderType?, //DEGEN or SWAP
        @RequestParam bidAsset: Symbol?,
        @RequestParam isLeverage: Boolean?,
        @RequestParam page: Int?,
        @RequestParam pageSize: Int?,
        request: HttpServletRequest
    ): ApiResponse<PageResponse<ExecutedOrderResponse>> {
        authUtil.filterIPBlacklist(request)
        val p = page ?: 1
        val ps = pageSize ?: 50
        val pageable = PageRequest.of(p - 1, ps, Sort.by(Sort.Order.desc(OptionOrder::id.name)))

        val criteria = Criteria()
        if(creatorRole != null){
            if(creatorRole == CreatorRole.BUYER) {
                criteria.and(OptionOrder::buyer.name).regex("(?i)$address")
            } else if(creatorRole == CreatorRole.SELLER){
                criteria.and(OptionOrder::seller.name).regex("(?i)$address")
            }else if(creatorRole == CreatorRole.BUYER_VAULT){
                criteria.and(OptionOrder::buyerVault.name).regex("(?i)$address")
            }else if(creatorRole == CreatorRole.SELLER_VAULT){
                criteria.and(OptionOrder::sellerVault.name).regex("(?i)$address")
            }
        } else{
            criteria.orOperator(
                Criteria.where(OptionOrder::buyer.name).regex("(?i)$address"),
                Criteria.where(OptionOrder::seller.name).regex("(?i)$address")
            )
        }
        // 月光宝盒活动中，清算订单不显示
        val orCriteria = Criteria().orOperator(
            Criteria.where(OptionOrder::usedMoonlightBox.name).isNull,
            Criteria.where(OptionOrder::liquidityType.name).ne(0)
        )
        criteria.andOperator(orCriteria)

        if(isLeverage != null){
            criteria.and(OptionOrder::product.name).`is`(OptionOrderProduct.MARKET_PLACE)
            criteria.and(OptionOrder::limitOrder.name).`is`(!isLeverage)
        }

        val query = Query(criteria)
        query.addCriteria(Criteria.where(OptionOrder::chain.name).`is`(chain))
        query.addCriteria(Criteria.where(OptionOrder::status.name).`is`(OptionStatus.EXECUTED))

        if (direction != null) {
            query.addCriteria(Criteria.where(OptionOrder::direction.name).`is`(direction))
        }

        if (orderType != null) {
            query.addCriteria(Criteria.where(OptionOrder::orderType.name).`is`(orderType))
        }

        if (bidAsset != null) {
            query.addCriteria(Criteria.where(OptionOrder::bidAsset.name).`is`(bidAsset))
        }

        val total = mongoTemplate.count(query, OptionOrder::class.java)
        query.with(pageable)
        val resultSet = mongoTemplate.find(query, OptionOrder::class.java)
        val optionsPage = PageImpl(resultSet, pageable, total)

//        val optionsPage = optionOrderRepository.findByChainAndStatusAndBuyerOrSeller(
//            chain, OptionStatus.EXECUTED, address, address, pageable
//        )
//        val usdtCurrency = currencyRepository.findFirstBySymbol(Symbol.USDT.name)
//            ?: throw Exception("USDT currency not found")
        val optionsList = optionsPage.content.mapNotNull {
            //val order = orderRepository.findByIdOrNull(it.orderId) ?: return@mapNotNull null
            val stonePieces = stoneService.getNftIdFromOptionOrder(it)
            var lockDateTimeStamp = DateTimeUtil.convertLocalDateTimeToTimestamp(it.expiryDate)
            if(it.lockDate != null){
                lockDateTimeStamp = DateTimeUtil.convertLocalDateTimeToTimestamp(it.lockDate)
            }
            ExecutedOrderResponse(
                id = it.id!!,
                orderId = it.orderId,
                orderType = it.orderType,
                chain = it.chain,
                onChainOrderId = it.onChainOrderId,
                buyerAddress = it.buyer,
                buyerVault = it.buyerVault,
                buyerVaultSalt = it.buyerVaultSalt,
                sellerAddress = it.seller,
                businessType = it.direction!!,

                bidAsset = it.bidAsset,
                bidAmount = it.bidAmount?.stripTrailingZeros()?.toPlainString(),

                underlyingAsset = it.underlyingAsset,
                underlyingAssetAddress = it.underlyingAssetAddress,
                underlyingAssetAmount = it.amount.stripTrailingZeros().toPlainString(),

                strikeAsset = it.strikeAsset,
                strikeAmount = it.strikeAmount.stripTrailingZeros().toPlainString(),
                strikePrice = it.strikePrice!!.stripTrailingZeros().toPlainString(),
                actualStrikeAmount = it.actualStrikeAmount?.stripTrailingZeros()?.toPlainString(),

                premiumFeesAsset = if (it.premiumAsset?.asset != null) Symbol.valueOf(it.premiumAsset!!.asset) else Symbol.USDT,
                premiumFee = it.premiumFeePay?.stripTrailingZeros()?.toPlainString(),
                premiumFeeInUsdt = it.premiumFeePayInUsdt?.stripTrailingZeros()?.toPlainString(),
                premiumFeeShouldPay = it.premiumFeeShouldPay?.stripTrailingZeros()?.toPlainString(),
                premiumFeeShouldPayInUsdt = it.premiumFeeShouldPayInUsdt?.stripTrailingZeros()?.toPlainString(),

                createDate = DateTimeUtil.convertLocalDateTimeToTimestamp(it.created),
                expiryDate = DateTimeUtil.convertLocalDateTimeToTimestamp(it.expiryDate),
                lockDate = lockDateTimeStamp,
                status = it.status,
                degen = it.jvault,
                marketPriceAtSettlement = it.marketPriceAtSettlement,
                settlementHash = it.settlementHash,
                buyerProfit = it.buyerProfit?.stripTrailingZeros()?.toPlainString(),
                expiryInHour = it.expiryInHour,
                usedMoonlightBox = it.usedMoonlightBox,
                usedRealityStone = it.usedRealityStone ?: false,
                usedPowerStone = it.usedPowerStone ?: false,
                usedSpaceStone = it.usedSpaceStone ?: false,
                usedTimeStone = it.usedTimeStone ?: false,
                stoneActivityNftId = it.stoneActivityNftId,
                txHash = it.txHash,
                premiumFeeLog = it.premiumFeeLog,
                awardStonePieceNftIds = stonePieces
            )
        }

        return ApiResponse.success(
            PageResponse(
                total = optionsPage.totalElements,
                page = p,
                pageSize = ps,
                totalPage = optionsPage.totalPages,
                hasNext = optionsPage.hasNext(),
                data = optionsList
            )
        )
    }

    @ApiOperation("用户订单成交记录")
    @CrossOrigin
    @ApiCache(
        key = "orders:executedList",
        dynamicKey = ["address", "chain", "creatorRole", "direction", "orderType", "bidAsset", "status", "isLeverage","page", "pageSize"],
        expire = 5
    )
    @GetMapping("/executedList")
    fun executedList(
        @RequestParam @EVMAddress address: String,
        @RequestParam chain: ChainType,
        @RequestParam creatorRole: CreatorRole?, // BUYER or SELLER, BUYER_VAULT or SELLER_VAULT
        @RequestParam direction: OptionDirection?, // CALL or PUT
        @RequestParam orderType: OrderType?, // DEGEN or SWAP
        @RequestParam bidAsset: Symbol?,
        @RequestParam status: OptionStatus?,
        @RequestParam isLeverage: Boolean?,
        @RequestParam page: Int?,
        @RequestParam pageSize: Int?,
        request: HttpServletRequest
    ): ApiResponse<PageResponse<ExecutedOrderResponse>> {
        authUtil.filterIPBlacklist(request)
        val p = page ?: 1
        val ps = pageSize ?: 50
        val pageable = PageRequest.of(p - 1, ps, Sort.by(Sort.Order.desc(OptionOrder::created.name)))

        val criteria = Criteria()
        if(creatorRole != null){
            if(creatorRole == CreatorRole.BUYER) {
                criteria.and(OptionOrder::buyer.name).regex("(?i)$address")
            } else if(creatorRole == CreatorRole.SELLER){
                criteria.and(OptionOrder::seller.name).regex("(?i)$address")
            }else if(creatorRole == CreatorRole.BUYER_VAULT){
                criteria.and(OptionOrder::buyerVault.name).regex("(?i)$address")
            }else if(creatorRole == CreatorRole.SELLER_VAULT){
                criteria.and(OptionOrder::sellerVault.name).regex("(?i)$address")
            }
        } else{
            criteria.orOperator(
                Criteria.where(OptionOrder::buyer.name).regex("(?i)$address"),
                Criteria.where(OptionOrder::seller.name).regex("(?i)$address")
            )
        }

        if(status != null){
            criteria.and(OptionOrder::status.name).`is`(status)
        }

        // 月光宝盒活动中，清算订单不显示
        val orCriteria = Criteria().orOperator(
            Criteria.where(OptionOrder::usedMoonlightBox.name).isNull,
            Criteria.where(OptionOrder::liquidityType.name).ne(0)
        )
        criteria.andOperator(orCriteria)

        val query = Query(criteria)
        query.addCriteria(Criteria.where(OptionOrder::chain.name).`is`(chain))
        //query.addCriteria(Criteria.where(OptionOrder::status.name).`is`(OptionStatus.EXECUTED))

        if (direction != null) {
            query.addCriteria(Criteria.where(OptionOrder::direction.name).`is`(direction))
        }

        if (orderType != null) {
            query.addCriteria(Criteria.where(OptionOrder::orderType.name).`is`(orderType))
        }
        if (bidAsset != null) {
            query.addCriteria(Criteria.where(OptionOrder::bidAsset.name).`is`(bidAsset))
        }

        if(isLeverage != null){
            query.addCriteria(Criteria.where(OptionOrder::product.name).`is`(OptionOrderProduct.MARKET_PLACE))
            query.addCriteria(Criteria.where(OptionOrder::limitOrder.name).`is`(!isLeverage))
        }

        val total = mongoTemplate.count(query, OptionOrder::class.java)
        query.with(pageable)
        val resultSet = mongoTemplate.find(query, OptionOrder::class.java)
        val optionsPage = PageImpl(resultSet, pageable, total)

        //val optionsPage = optionOrderRepository.findByChainAndBuyerOrSeller(chain, address, address, pageable)

        val optionsList = optionsPage.content.mapNotNull {
            //val order = orderRepository.findByIdOrNull(it.orderId) ?: return@mapNotNull null
            var lockDateTimeStamp = DateTimeUtil.convertLocalDateTimeToTimestamp(it.expiryDate)
            if(it.lockDate != null){
                lockDateTimeStamp = DateTimeUtil.convertLocalDateTimeToTimestamp(it.lockDate)
            }
            val stonePieces = stoneService.getNftIdFromOptionOrder(it)

            var underlyingAsset = if(it.direction == OptionDirection.CALL){
                it.bidAsset
            } else {
                it.quoteAsset
            }

            if(underlyingAsset == null){
                underlyingAsset = it.underlyingAsset
            }

            ExecutedOrderResponse(
                id = it.id!!,
                orderId = it.orderId,
                orderType = it.orderType,
                chain = it.chain,
                onChainOrderId = it.onChainOrderId,
                buyerAddress = it.buyer,
                buyerVault = it.buyerVault,
                buyerVaultSalt = it.buyerVaultSalt,
                sellerAddress = it.seller,
                businessType = it.direction!!,

                bidAsset = it.bidAsset,
                bidAmount = it.bidAmount?.stripTrailingZeros()?.toPlainString(),

                underlyingAsset = underlyingAsset,
                underlyingAssetAddress = it.underlyingAssetAddress,
                underlyingAssetAmount = it.amount.stripTrailingZeros().toPlainString(),

                strikeAsset = it.strikeAsset,
                strikeAmount = it.strikeAmount.stripTrailingZeros().toPlainString(),
                strikePrice = it.strikePrice!!.stripTrailingZeros().toPlainString(),
                actualStrikeAmount = it.actualStrikeAmount?.stripTrailingZeros()?.toPlainString(),

                premiumFeesAsset = if (it.premiumAsset?.asset != null) Symbol.valueOf(it.premiumAsset!!.asset) else Symbol.USDT,
                premiumFee = it.premiumFeePay?.stripTrailingZeros()?.toPlainString(),
                premiumFeeInUsdt = it.premiumFeePayInUsdt?.stripTrailingZeros()?.toPlainString(),
                premiumFeeShouldPay = it.premiumFeeShouldPay?.stripTrailingZeros()?.toPlainString(),
                premiumFeeShouldPayInUsdt = it.premiumFeeShouldPayInUsdt?.stripTrailingZeros()?.toPlainString(),

                createDate = DateTimeUtil.convertLocalDateTimeToTimestamp(it.created),
                expiryDate = DateTimeUtil.convertLocalDateTimeToTimestamp(it.expiryDate),
                lockDate = lockDateTimeStamp,
                status = it.status,
                degen = it.jvault,
                marketPriceAtSettlement = it.marketPriceAtSettlement,
                settlementHash = it.settlementHash,
                buyerProfit = it.buyerProfit?.stripTrailingZeros()?.toPlainString(),
                expiryInHour = it.expiryInHour,
                usedMoonlightBox = it.usedMoonlightBox,
                usedRealityStone = it.usedRealityStone ?: false,
                usedPowerStone = it.usedPowerStone ?: false,
                usedSpaceStone = it.usedSpaceStone ?: false,
                usedTimeStone = it.usedTimeStone ?: false,
                stoneActivityNftId = it.stoneActivityNftId,
                txHash = it.txHash,
                premiumFeeLog = it.premiumFeeLog,
                awardStonePieceNftIds = stonePieces
            )
        }

        return ApiResponse.success(
            PageResponse(
                total = optionsPage.totalElements,
                page = p,
                pageSize = ps,
                totalPage = optionsPage.totalPages,
                hasNext = optionsPage.hasNext(),
                data = optionsList
            )
        )
    }

    @ApiOperation("用户订单成交记录(按照 holder 查询)")
    @CrossOrigin
    @ApiCache(
        key = "orders:executedListByHolders",
        dynamicKey = ["holders", "chain", "page", "pageSize"],
        expire = 5
    )
    @GetMapping("/executedListByHolders")
    fun executedListByHolders(
        @RequestParam holders: String, // Holder地址，多个地址用逗号分隔
        @RequestParam chain: ChainType,
        @RequestParam page: Int?,
        @RequestParam pageSize: Int?,
        request: HttpServletRequest
    ): ApiResponse<PageResponse<ExecutedOrderResponse>> {
        authUtil.filterIPBlacklist(request)
        val p = page ?: 1
        val ps = pageSize ?: 50
        val pageable = PageRequest.of(p - 1, ps, Sort.by(Sort.Order.desc(OptionOrder::created.name)))

        val holderList = holders.split(",").map {
            it.trim()
        }

        for(holder in holderList){
            if(!authUtil.isEVMAddressValid(holder)){
                throw BusinessException(ResultEnum.INVALID_ADDRESS)
            }
        }

        val criteria = Criteria()

        val query = Query(criteria)
        query.addCriteria(Criteria.where(OptionOrder::chain.name).`is`(chain))
        query.addCriteria(Criteria.where(OptionOrder::buyerVault.name).`in`(holderList))

        val total = mongoTemplate.count(query, OptionOrder::class.java)
        query.with(pageable)
        val resultSet = mongoTemplate.find(query, OptionOrder::class.java)
        val optionsPage = PageImpl(resultSet, pageable, total)

        val optionsList = optionsPage.content.mapNotNull {
            //val order = orderRepository.findByIdOrNull(it.orderId) ?: return@mapNotNull null
            var lockDateTimeStamp = DateTimeUtil.convertLocalDateTimeToTimestamp(it.expiryDate)
            if(it.lockDate != null){
                lockDateTimeStamp = DateTimeUtil.convertLocalDateTimeToTimestamp(it.lockDate)
            }
            val stonePieces = stoneService.getNftIdFromOptionOrder(it)
            ExecutedOrderResponse(
                id = it.id!!,
                orderId = it.orderId,
                orderType = it.orderType,
                chain = it.chain,
                onChainOrderId = it.onChainOrderId,
                buyerAddress = it.buyer,
                buyerVault = it.buyerVault,
                buyerVaultSalt = it.buyerVaultSalt,
                sellerAddress = it.seller,
                businessType = it.direction!!,

                bidAsset = it.bidAsset,
                bidAmount = it.bidAmount?.stripTrailingZeros()?.toPlainString(),

                underlyingAsset = it.underlyingAsset,
                underlyingAssetAddress = it.underlyingAssetAddress,
                underlyingAssetAmount = it.amount.stripTrailingZeros().toPlainString(),

                strikeAsset = it.strikeAsset,
                strikeAmount = it.strikeAmount.stripTrailingZeros().toPlainString(),
                strikePrice = it.strikePrice!!.stripTrailingZeros().toPlainString(),
                actualStrikeAmount = it.actualStrikeAmount?.stripTrailingZeros()?.toPlainString(),

                premiumFeesAsset = if (it.premiumAsset?.asset != null) Symbol.valueOf(it.premiumAsset!!.asset) else Symbol.USDT,
                premiumFee = it.premiumFeePay?.stripTrailingZeros()?.toPlainString(),
                premiumFeeInUsdt = it.premiumFeePayInUsdt?.stripTrailingZeros()?.toPlainString(),
                premiumFeeShouldPay = it.premiumFeeShouldPay?.stripTrailingZeros()?.toPlainString(),
                premiumFeeShouldPayInUsdt = it.premiumFeeShouldPayInUsdt?.stripTrailingZeros()?.toPlainString(),

                createDate = DateTimeUtil.convertLocalDateTimeToTimestamp(it.created),
                expiryDate = DateTimeUtil.convertLocalDateTimeToTimestamp(it.expiryDate),
                lockDate = lockDateTimeStamp,
                status = it.status,
                degen = it.jvault,
                marketPriceAtSettlement = it.marketPriceAtSettlement,
                settlementHash = it.settlementHash,
                buyerProfit = it.buyerProfit?.stripTrailingZeros()?.toPlainString(),
                expiryInHour = it.expiryInHour,
                usedMoonlightBox = it.usedMoonlightBox,
                usedRealityStone = it.usedRealityStone ?: false,
                usedPowerStone = it.usedPowerStone ?: false,
                usedSpaceStone = it.usedSpaceStone ?: false,
                usedTimeStone = it.usedTimeStone ?: false,
                stoneActivityNftId = it.stoneActivityNftId,
                txHash = it.txHash,
                premiumFeeLog = it.premiumFeeLog,
                awardStonePieceNftIds = stonePieces
            )
        }

        return ApiResponse.success(
            PageResponse(
                total = optionsPage.totalElements,
                page = p,
                pageSize = ps,
                totalPage = optionsPage.totalPages,
                hasNext = optionsPage.hasNext(),
                data = optionsList
            )
        )
    }

    @ApiOperation("获取当前 0DTE Vault 列表")
    @CrossOrigin
    @ApiCache(
        key = "orders:dte_vault_list",
        dynamicKey = ["chain", "symbol", "expiryInHourList", "type"],
        expire = 30
    )
    @GetMapping("/dte_vault_list")
    fun dteVaultList(
        @RequestParam chain: ChainType,
        @RequestParam symbol: Symbol,
        @RequestParam(required = false) expiryInHourList: List<String>? = null,
        @RequestParam(required = false) type: OrderType? = null,
        request: HttpServletRequest
    ): ApiResponse<List<DegenLPVaultData>> {

        authUtil.filterIPBlacklist(request)

        val infoList = mutableListOf<DegenLPVaultData>()

        val orderType = type ?: OrderType.DEGEN

        var expiryHourList = expiryInHourList
        if(expiryHourList == null) {
            expiryHourList =
                degenConfigRepository.findFirstByBidAsset(symbol)?.lpVaultExpiryInHour ?: listOf("2", "8", "24")
        }

        for(expiryHour in expiryHourList) {
            val callLpVault = degenLPVaultRepository.findByChainAndOptionTypeAndOptionSymbolAndExpireInHourAndOrderType(
                chain,
                OptionDirection.CALL,
                symbol,
                expiryHour,
                orderType
            )

            val putLpVault = degenLPVaultRepository.findByChainAndOptionTypeAndOptionSymbolAndExpireInHourAndOrderType(
                chain,
                OptionDirection.PUT,
                symbol,
                expiryHour,
                orderType
            )

            if(callLpVault == null || putLpVault == null){
                continue
            }

            val callOI = callLpVault.openInterest
            val putOI = putLpVault.openInterest

            val callUtilization = callLpVault.utilization
            val putUtilization = putLpVault.utilization

            val callTvl = callLpVault.tvl
            val putTvl = putLpVault.tvl

            val lpVaultData = DegenLPVaultData(
                expiryInHour = expiryHour,
                optionSymbol = symbol,
                callAddress = callLpVault.address,
                putAddress = putLpVault.address,
                callAvailableLiquidity = callLpVault.availableLiquidityInUsdt.stripTrailingZeros().toPlainString(),
                putAvailableLiquidity = putLpVault.availableLiquidityInUsdt.stripTrailingZeros().toPlainString(),
                callIV = callLpVault.iv.stripTrailingZeros().toPlainString(),
                putIV = putLpVault.iv.stripTrailingZeros().toPlainString(),
                callOpenInterest = callOI.stripTrailingZeros().toPlainString(),
                putOpenInterest = putOI.stripTrailingZeros().toPlainString(),
                callUtilization = callUtilization.stripTrailingZeros().toPlainString(),
                putUtilization = putUtilization.stripTrailingZeros().toPlainString(),
                callTvl = callTvl.stripTrailingZeros().toPlainString(),
                putTvl = putTvl.stripTrailingZeros().toPlainString(),
                callPremiumRate = callLpVault.premiumRate.stripTrailingZeros().toPlainString(),
                putPremiumRate = putLpVault.premiumRate.stripTrailingZeros().toPlainString(),
                callPremiumFloorRate = callLpVault.premiumFloorPercentage.stripTrailingZeros().toPlainString(),
                putPremiumFloorRate = putLpVault.premiumFloorPercentage.stripTrailingZeros().toPlainString(),
                callMaximum = callLpVault.maximum.stripTrailingZeros().toPlainString(),
                putMaximum = putLpVault.maximum.stripTrailingZeros().toPlainString(),
                callOfferID = callLpVault.offerId,
                putOfferID = putLpVault.offerId,
                callSettingIndex = callLpVault.settingIndex,
                putSettingIndex = putLpVault.settingIndex,
                callProductIndex = callLpVault.productIndex,
                putProductIndex = putLpVault.productIndex,
            )

            infoList.add(lpVaultData)
        }

        return ApiResponse.success(infoList)
    }

    @ApiOperation("获取用户邀请钱包地址")
    @CrossOrigin
    @GetMapping("/invitor_address")
    fun invitorAddress(
        @RequestParam @EVMAddress address: String,
        request: HttpServletRequest
    ): ApiResponse<String?> {

        authUtil.filterIPBlacklist(request)

        val userNetwork = userNetworkRepository.findByAddressIgnoreCase(address) ?: return ApiResponse.success(null)

        if(userNetwork.invitedNetworkId == "root"){
            return ApiResponse.success("root")
        }

        val invitedUser = userNetworkRepository.findByIdOrNull(userNetwork.invitedNetworkId)
        return ApiResponse.success(invitedUser?.address)
    }

     @ApiOperation("使用月光宝盒功能")
     @CrossOrigin
     @PostMapping("/use_moonlight_box")
     fun useMoonlightBox(
         @Valid @RequestBody dto: UseMoonlightBoxDTO,
         request: HttpServletRequest
     ): ApiResponse<Boolean> {
         authUtil.filterIPBlacklist(request)

         userRepository.findByAddressIgnoreCase(dto.address)
             ?: throw BusinessException(ResultEnum.USER_NOT_FOUND)

         val oo = optionOrderRepository.findFirstByChainAndOnChainOrderId(dto.chain, dto.onChainOrderId)
             ?: throw BusinessException(ResultEnum.ORDER_NOT_FOUND)
         //if (oo.usedMoonlightBox == true) throw BusinessException(ResultEnum.MOONLIGHT_BOX_ALREADY_USED)
         val now = LocalDateTime.now()
         val allowMinutes = systemService.getParameter().moonlightBoxActivityAllowMinutes
         if (now.plusMinutes(allowMinutes.toLong()).isAfter(oo.expiryDate)) {
             throw BusinessException(ResultEnum.MOONLIGHT_BOX_EXPIRED)
         }

         val newUse = oo.usedMoonlightBox ?: false

         // 用户在同一区块中方向相反权利金相同的另一笔订单
         optionOrderRepository.findFirstByChainAndTxHashAndOnChainOrderIdNotAndUsedMoonlightBoxNotNull(
             oo.chain,
             oo.txHash!!,
             oo.onChainOrderId!!
         )?.let { nextOo ->
             optionOrderService.updateFields(
                 mapOf(
                     OptionOrder::usedMoonlightBox.name to !newUse,
                     OptionOrder::liquidityType.name to 2
                 ),
                 nextOo.id!!
             )
             optionOrderService.updateFields(
                 mapOf(
                     OptionOrder::usedMoonlightBox.name to !newUse,
                     OptionOrder::liquidityType.name to 2
                 ),
                 oo.id!!
             )
         }

         return ApiResponse.success(true)
     }

    @ApiOperation("获取订单详情")
    @CrossOrigin
    @ApiCache(key = "orders:option_order_detail", dynamicKey = ["optionOrderId"], expire = 30)
    @GetMapping("/option_order_detail")
    fun optionOrderDetail(
        @RequestParam optionOrderId: String,
        request: HttpServletRequest
    ): ApiResponse<ExecutedOrderResponse> {
        authUtil.filterIPBlacklist(request)

        val oo = optionOrderRepository.findByIdOrNull(optionOrderId)
            ?: throw BusinessException(ResultEnum.ORDER_NOT_FOUND)

        //val order = orderRepository.findByIdOrNull(oo.orderId) ?: throw BusinessException(ResultEnum.ORDER_NOT_FOUND)

        var lockDateTimeStamp = DateTimeUtil.convertLocalDateTimeToTimestamp(oo.expiryDate)
        if(oo.lockDate != null){
            lockDateTimeStamp = DateTimeUtil.convertLocalDateTimeToTimestamp(oo.lockDate)
        }

        val stonePieces = stoneService.getNftIdFromOptionOrder(oo)
        return ApiResponse.success(
            ExecutedOrderResponse(
                id = oo.id!!,
                orderId = oo.orderId,
                orderType = oo.orderType,
                chain = oo.chain,
                onChainOrderId = oo.onChainOrderId,
                buyerAddress = oo.buyer,
                buyerVault = oo.buyerVault,
                buyerVaultSalt = oo.buyerVaultSalt,
                sellerAddress = oo.seller,
                businessType = oo.direction!!,

                bidAsset = oo.bidAsset,
                bidAmount = oo.bidAmount?.stripTrailingZeros()?.toPlainString(),

                underlyingAsset = oo.underlyingAsset,
                underlyingAssetAddress = oo.underlyingAssetAddress,
                underlyingAssetAmount = oo.amount.stripTrailingZeros().toPlainString(),

                strikeAsset = oo.strikeAsset,
                strikeAmount = oo.strikeAmount.stripTrailingZeros().toPlainString(),
                strikePrice = oo.strikePrice!!.stripTrailingZeros().toPlainString(),
                actualStrikeAmount = oo.actualStrikeAmount?.stripTrailingZeros()?.toPlainString(),

                premiumFeesAsset = if (oo.premiumAsset?.asset != null) Symbol.valueOf(oo.premiumAsset!!.asset) else Symbol.USDT,
                premiumFee = oo.premiumFeePay?.stripTrailingZeros()?.toPlainString(),
                premiumFeeInUsdt = oo.premiumFeePayInUsdt?.stripTrailingZeros()?.toPlainString(),
                premiumFeeShouldPay = oo.premiumFeeShouldPay?.stripTrailingZeros()?.toPlainString(),
                premiumFeeShouldPayInUsdt = oo.premiumFeeShouldPayInUsdt?.stripTrailingZeros()?.toPlainString(),

                createDate = DateTimeUtil.convertLocalDateTimeToTimestamp(oo.created),
                expiryDate = DateTimeUtil.convertLocalDateTimeToTimestamp(oo.expiryDate),
                lockDate = lockDateTimeStamp,
                status = oo.status,
                degen = oo.jvault,
                marketPriceAtSettlement = oo.marketPriceAtSettlement,
                settlementHash = oo.settlementHash,
                buyerProfit = oo.buyerProfit?.stripTrailingZeros()?.toPlainString(),
                expiryInHour = oo.expiryInHour,
                usedMoonlightBox = oo.usedMoonlightBox,
                usedRealityStone = oo.usedRealityStone ?: false,
                usedPowerStone = oo.usedPowerStone ?: false,
                usedSpaceStone = oo.usedSpaceStone ?: false,
                usedTimeStone = oo.usedTimeStone ?: false,
                stoneActivityNftId = oo.stoneActivityNftId,
                txHash = oo.txHash,
                premiumFeeLog = oo.premiumFeeLog,
                awardStonePieceNftIds = stonePieces
            )
        )
    }

    @ApiOperation("设置订单来源")
    @CrossOrigin
    @PostMapping("/set_option_order_channel")
    fun setOptionOrderChannel(
        @Valid @RequestBody dto: SetOptionOrderChannelDTO,
        request: HttpServletRequest
    ): ApiResponse<Boolean> {
        val ipAddress = authUtil.filterIPBlacklist(request)
        val channel = authUtil.authPartner(request)

        if(dto.from != null && (dto.from!!.length > 20)){
            throw BusinessException(ResultEnum.INVALID_FROM_CHANNEL)
        }

        var utm: UTMInfo? = null
        if(dto.utmMedium != null || dto.utmTerm != null ||
            dto.utmContent != null || dto.utmCampaign != null ||
            dto.utmSource != null){
            utm = UTMInfo(
                utmMedium = dto.utmMedium,
                utmTerm = dto.utmTerm,
                utmContent = dto.utmContent,
                utmCampaign = dto.utmCampaign,
                utmSource = dto.utmSource
            )
        }

        val optionOrderInfo = optionOrderInfoRepository.findByTxHash(dto.txHash)

        if(optionOrderInfo == null){
            OptionOrderInfo(
                txHash = dto.txHash,
                channel = channel,
                ipAddress = ipAddress,
                product = dto.product,
                wallet = dto.wallet,
                from = dto.from,
                utmInfo = utm
            ).let {
                optionOrderInfoRepository.save(it)
            }
        }

        //处理 ECHOOO 活动
        if(channel == UserChannel.ECHOOO) {

            val optionOrders = optionOrderRepository.findByTxHash(dto.txHash)
            for (optionOrder in optionOrders) {
                try {
                    echoooCampaignService.createTradeRebateRecord(optionOrder)
                } catch (e: Exception) {
                    logger.error("OrderController echooo first trade failed, optionOrderId: ${optionOrder.id}", e)
                }
            }
        }

        return ApiResponse.success(true)
    }

    @ApiOperation("设置地址来源")
    @CrossOrigin
    @PostMapping("/set_address_channel")
    fun setAddressChannel(
        @Valid @RequestBody dto: SetAddressChannelDTO,
        request: HttpServletRequest
    ): ApiResponse<Boolean> {
        val ipAddress = authUtil.filterIPBlacklist(request)
        val channel = authUtil.authPartner(request)

        if(dto.from != null && (dto.from!!.length > 20)){
            throw BusinessException(ResultEnum.INVALID_FROM_CHANNEL)
        }

        var utm: UTMInfo? = null
        if(dto.utmMedium != null || dto.utmTerm != null ||
            dto.utmContent != null || dto.utmCampaign != null ||
            dto.utmSource != null){
            utm = UTMInfo(
                utmMedium = dto.utmMedium,
                utmTerm = dto.utmTerm,
                utmContent = dto.utmContent,
                utmCampaign = dto.utmCampaign,
                utmSource = dto.utmSource
            )
        }

        mistTrackService.isAddressHighRisk(
            dto.address,
            channel,
            ipAddress,
            utm
        )

        return ApiResponse.success(true)
    }

    @ApiOperation("获取买家Vault收取的权利金总额，usd为单位")
    @CrossOrigin
    @ApiCache(key = "orders:seller_vault_total_premium", dynamicKey = ["chain", "sellerVault"], expire = 30)
    @GetMapping("/seller_vault_total_premium")
    fun sellerVaultTotalPremium(
        @RequestParam @EVMAddress sellerVault: String,
        @RequestParam chain: ChainType,
        request: HttpServletRequest
    ): ApiResponse<String> {

        authUtil.filterIPBlacklist(request)

        val total = optionOrderService.calculateSellerVaultPremium(
            chain,
            sellerVault
        )

        return ApiResponse.success(total.stripTrailingZeros().toPlainString())
    }

    @ApiOperation("结算时间宝石订单")
    @CrossOrigin
    @PostMapping("/settle_timestone_order")
    fun settleTimeStoneOrder(
        @Valid @RequestBody dto: SettleTimeStoneDTO,
        request: HttpServletRequest
    ): ApiResponse<Boolean> {
        val ipAddress = authUtil.filterIPBlacklist(request)

        val message = "address=${dto.address}&chain=${dto.chain}&orderId=${dto.orderId}&timestamp=${dto.timestamp}"
        val messagePrefix = "\u0019Ethereum Signed Message:\n"
        val messageSize = message.toByteArray().size
        val prefix = (messagePrefix + messageSize).toByteArray()
        val messageWithPrefix = ByteArray(prefix.size + messageSize) {
            if (it < prefix.size) prefix[it] else message[it - prefix.size].toByte()
        }
        val messageHash = Hash.sha3(messageWithPrefix)

        logger.info("Signature: ${dto.signature.substring(2)}")
        val r = dto.signature.substring(2, 66)
        val s = dto.signature.substring(66, 130)
        val v = dto.signature.substring(130, 132)
        //logger.info("r: 0x$r\ts: 0x$s\tv: 0x$v")
        val rB = Numeric.hexStringToByteArray(r)
        val sB = Numeric.hexStringToByteArray(s)
        val vB = Numeric.hexStringToByteArray(v)
        val signature = Sign.SignatureData(vB, rB, sB)

        val publicKey = Sign.signedMessageHashToKey(messageHash, signature)
        val inferAddress = "0x" + Keys.getAddress(publicKey)
        logger.info("清算时间宝石: 签名字符串=${message}")
        logger.info("清算时间宝石: 请求地址=${dto.address}\t推断地址=$inferAddress")

        if (inferAddress.lowercase() != dto.address.lowercase()) {
            throw BusinessException(ResultEnum.INVALID_SIGNATURE)
        }

        val now = Date().time / 1000
        if(dto.timestamp < now - 30){ // 结算价格不能超过30秒
            throw BusinessException(ResultEnum.SETTLEMENT_TIME_EXPIRED)
        }

        val optionOrder = optionOrderRepository.findByChainAndOnChainOrderId(
            dto.chain,
            dto.orderId
        ) ?: throw BusinessException(ResultEnum.ORDER_NOT_FOUND)

        if(optionOrder.usedTimeStone == null || optionOrder.usedTimeStone == false){
            throw BusinessException(ResultEnum.NOT_TIME_STONE_ORDER)
        }

        val newExpirationDate = DateTimeUtil.convertTimestampToLocalDateTime(
            dto.timestamp
        )

        // 更新订单结算时间
        val updateFields = mutableMapOf<String, Any?>(
            OptionOrder::expiryDate.name to newExpirationDate,
        )

        try {
            optionOrderService.updateFields(updateFields, optionOrder.id!!)
        } catch (e: Exception){
            logger.error("Error in settle time stone ${dto.orderId}: ${e.message}")
        }

        return ApiResponse.success(true)
    }
}