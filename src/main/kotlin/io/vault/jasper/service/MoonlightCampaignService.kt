package io.vault.jasper.service

import io.vault.jasper.enums.ChainType
import io.vault.jasper.enums.OptionDirection
import io.vault.jasper.enums.Symbol
import io.vault.jasper.model.*
import io.vault.jasper.repository.*
import io.vault.jasper.service.blockchain.BitlayerService
import org.slf4j.LoggerFactory
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.data.repository.findByIdOrNull
import org.springframework.stereotype.Service
import java.math.BigDecimal
import java.math.BigInteger
import java.time.LocalDateTime

@Service
class MoonlightCampaignService @Autowired constructor(
    private val moonlightRebateRecordRepository: MoonlightRebateRecordRepository,
    private val moonlightCampaignParameterRepository: MoonlightCampaignParameterRepository,
    private val optionOrderRepository: OptionOrderRepository,
    private val userRepository: UserRepository,
    private val orderRepository: OrderRepository,
    private val galaRebateRecordRepository: GalaRebateRecordRepository,
    private val bitlayerService: BitlayerService,
    private val currencyService: CurrencyService
){

    private val logger = LoggerFactory.getLogger(this::class.java)

    /**
     * 获取空投参数对象
     */
    fun getCampaignParameter(): MoonlightCampaignParameter {
        val params = moonlightCampaignParameterRepository.findAll()

        return if (params.isEmpty()) {
            val p = MoonlightCampaignParameter(null)
            moonlightCampaignParameterRepository.save(p)
        } else {
            params.first()
        }
    }

    /**
     * 判断是否活动内首单
     */
    fun isFirstOrderInCampaign(
        optionOrder: OptionOrder
    ): Boolean{

        if(optionOrder.status != OptionStatus.SETTLED &&
            optionOrder.status != OptionStatus.EXECUTED &&
            optionOrder.status != OptionStatus.SETTLE_FAILED){
            return false
        }

        val parameter = getCampaignParameter()

        // 符合 0.5H，0.01 BTC 使用 BTC 支付期权费 规格的订单
        if(optionOrder.bidAsset == null || optionOrder.bidAsset != Symbol.BTC){
            logger.info("MoonlightCampaignService Service Not BTC ${optionOrder.buyer} ${optionOrder.id}")
            return false
        }

        if(optionOrder.expiryInHour == null || optionOrder.expiryInHour == "0.5"){
            logger.info("MoonlightCampaignService expiry hour is 0.5 ${optionOrder.buyer} ${optionOrder.id}")
            return false
        }

        // 活动开始第一单
        val firstOptionOrder = optionOrderRepository.findFirstByChainAndBidAssetAndBuyerIgnoreCaseAndCreatedGreaterThan(
            ChainType.BITLAYER,
            Symbol.BTC,
            optionOrder.buyer!!,
            parameter.startDate
        )

        if(firstOptionOrder == null){
            return false
        }

        if(firstOptionOrder.id!! != optionOrder.id!!){
            return false
        }

        return true
    }

    fun createMoonlightTradeRebateRecord(
        optionOrder: OptionOrder
    ){

        val parameter = getCampaignParameter()

        val now = LocalDateTime.now()
        if(now.isAfter(parameter.endDate)){
            logger.info("MoonlightCampaignService first trade rebate task $now reach end date ${parameter.endDate}")
            return
        }

        // 排除 mini app 订单
        // 符合 2H，0.01 BTC 使用 BTC 支付期权费 规格的订单
        if(optionOrder.bidAsset == null || optionOrder.bidAsset != Symbol.BTC){
            logger.info("MoonlightCampaignService Service Not BTC ${optionOrder.buyer} ${optionOrder.id}")
            return
        }

        if(optionOrder.expiryInHour == null || optionOrder.expiryInHour == "0.5"){
            logger.info("MoonlightCampaignService expiry hour is 0.5 ${optionOrder.buyer} ${optionOrder.id}")
            return
        }

        //是否月光宝盒订单
        if(optionOrder.usedMoonlightBox != null){
            logger.info("MoonlightCampaignService first trade create rebate record option order is moonlight box order")
            return
        }

        // 是否还拥有月光宝盒
        val hasMoonlightBox = bitlayerService.isUserHasMoonlightBox(
            parameter.tradeRebateContractAddress,
            optionOrder.buyer!!,
            BigInteger.ONE
        )

        if(hasMoonlightBox){
            logger.info("MoonlightCampaignService Service Already has moonlight box ${optionOrder.buyer} ${optionOrder.id}")
            return
        }

        // 是否已经创建了订单
        val existingRecord = moonlightRebateRecordRepository.findByOptionOrderId(
            optionOrder.id!!
        )

        if(existingRecord != null){
            logger.info("MoonlightCampaignService Service Already add rebate record ${optionOrder.buyer} ${optionOrder.id}")
            return
        }

        // 是否有同样地址，未领取的订单
        val existingFirstMoonlightRecords = galaRebateRecordRepository.findByBuyerAddressIgnoreCaseAndStatusIn(
            optionOrder.buyer!!,
            listOf(
                GalaRebateRecordStatus.CREATED,
                GalaRebateRecordStatus.CLAIMED,
                GalaRebateRecordStatus.PENDING,
            )
        )

        if(existingFirstMoonlightRecords.isNotEmpty()){
            logger.info("MoonlightCampaignService Service Already has gala 3 rebate record ${optionOrder.buyer} ${optionOrder.id}")
            return
        }

        // 是否有同样地址，未领取的订单
        val existingAddressRecords = moonlightRebateRecordRepository.findByBuyerAddressIgnoreCaseAndStatusIn(
            optionOrder.buyer!!,
            listOf(
                MoonlightRebateRecordStatus.EXECUTED,
                MoonlightRebateRecordStatus.CREATED,
                MoonlightRebateRecordStatus.CLAIMED,
                MoonlightRebateRecordStatus.PENDING,
            )
        )

        if(existingAddressRecords.isNotEmpty()){
            logger.info("MoonlightCampaignService Service Already same address rebate record ${optionOrder.buyer} ${optionOrder.id}")
            return
        }

        val record = MoonlightRebateRecord(
            optionOrderId = optionOrder.id,
            buyerAddress = optionOrder.buyer!!,
            direction = optionOrder.direction!!,
            bidAmount = optionOrder.bidAmount!!,
            chain = optionOrder.chain
        )

        moonlightRebateRecordRepository.save(record)
    }

    fun checkMoonlightTradeRebate(
        record: MoonlightRebateRecord
    ){

        if(record.sbtNFTId != BigInteger.ONE){
            //logger.warn("MoonlightCampaignService Service not moonlight box order ${record.buyerAddress} ${record.optionOrderId}")
            return
        }

        val optionOrder = optionOrderRepository.findByIdOrNull(record.optionOrderId)
        if(optionOrder == null){
            logger.info("MoonlightCampaignService Service can not find order record ${record.buyerAddress} ${record.optionOrderId}")
            return
        }

        if(optionOrder.status == OptionStatus.SETTLE_FAILED){
            logger.info("MoonlightCampaignService Service order settle failed ${optionOrder.buyer} ${optionOrder.id}")
            record.status = MoonlightRebateRecordStatus.SETTLE_FAILED
            moonlightRebateRecordRepository.save(record)
            return
        }

        if(optionOrder.status != OptionStatus.SETTLED){
            logger.info("MoonlightCampaignService Service Not Settled order ${optionOrder.buyer} ${optionOrder.id}")
            return
        }
        
        val user = userRepository.findByAddressIgnoreCase(optionOrder.buyer!!)
        if(user == null){
            logger.debug("BitlayerCampaignService Service not new user ${optionOrder.buyer} ${optionOrder.id}")
            return
        }

        val order = orderRepository.findByIdOrNull(optionOrder.orderId)
        if(order == null){
            logger.debug("MoonlightCampaignService Service can not find order record ${optionOrder.buyer} ${optionOrder.id}")
            return
        }

        record.strikePrice = order.strikePrice.movePointLeft(18)
        record.settlementPrice = BigDecimal(optionOrder.marketPriceAtSettlement!!).movePointLeft(18)

        val usdtDecimal = currencyService.getCurrencyDecimal(
            ChainType.BITLAYER, Symbol.USDT
        )
        val btcDecimal = currencyService.getCurrencyDecimal(
            ChainType.BITLAYER, Symbol.BTC
        )

        record.premiumFee = optionOrder.premiumFeePay!!
        record.premiumAsset = Symbol.valueOf(optionOrder.premiumAsset!!.asset)

        if(record.premiumAsset == Symbol.BTC){
            record.premiumFeeInBtc = optionOrder.premiumFeePay!!
        } else {
            val readablePremiumFee = optionOrder.premiumFeePay!!.movePointLeft(usdtDecimal)
            val readableFeeInBtc = readablePremiumFee.divide(record.strikePrice, btcDecimal, BigDecimal.ROUND_HALF_UP)
            record.premiumFeeInBtc = readableFeeInBtc.movePointRight(btcDecimal).setScale(0, BigDecimal.ROUND_HALF_UP)
        }

        record.premiumFeeInUsdt = optionOrder.premiumFeePayInUsdt!!

        record.profit = optionOrder.buyerProfit!!

        if(record.direction == OptionDirection.CALL){
            record.profitAsset = Symbol.BTC
            record.profitInBtc = optionOrder.buyerProfit!!
            val readableBtcProfit = record.profitInBtc.movePointLeft(btcDecimal)
            val readableUsdcProfit = readableBtcProfit.multiply(record.settlementPrice).setScale(usdtDecimal, BigDecimal.ROUND_HALF_UP)
            record.profitInUsdt = readableUsdcProfit.movePointRight(usdtDecimal).setScale(0, BigDecimal.ROUND_HALF_UP)
        } else {
            record.profitAsset = Symbol.USDT
            record.profitInUsdt = optionOrder.buyerProfit!!
            val readableUsdcProfit = record.profitInUsdt.movePointLeft(usdtDecimal)
            val readableBtcProfit = readableUsdcProfit.divide(record.settlementPrice, btcDecimal, BigDecimal.ROUND_HALF_UP)
            record.profitInBtc = readableBtcProfit.movePointRight(btcDecimal).setScale(0, BigDecimal.ROUND_HALF_UP)
        }

        record.netProfit = record.profit.subtract(record.premiumFee).setScale(0, BigDecimal.ROUND_HALF_UP)

        // 方向猜错才有月光宝盒
        if(record.profit.compareTo(BigDecimal.ZERO) <= 0){
            record.rebateAmount = record.netProfit.multiply(BigDecimal("-1"))
            record.status = MoonlightRebateRecordStatus.CREATED
        } else {
            record.status = MoonlightRebateRecordStatus.SETTLED
        }

        moonlightRebateRecordRepository.save(record)
    }

    fun getClaimRebateTotalCount(): Int {
        var count = moonlightRebateRecordRepository.findByStatusAndSettleTxIdIsNotNull(
            MoonlightRebateRecordStatus.SETTLED
        ).size


        count += galaRebateRecordRepository.findByStatusAndSettleTxIdIsNotNull(
            GalaRebateRecordStatus.SETTLED
        ).size

        return count
    }

    fun getUserClaimMoonlightBoxRebateTotalCount(
        user: User
    ): Int {
        var count = moonlightRebateRecordRepository.findByBuyerAddressIgnoreCaseAndSbtNFTIdAndStatusAndSettleTxIdIsNotNull(
            user.address,
            BigInteger.ONE,
            MoonlightRebateRecordStatus.SETTLED
        ).size

        count += moonlightRebateRecordRepository.findByBuyerAddressIgnoreCaseAndSbtNFTIdAndStatusIn(
            user.address,
            BigInteger.ONE,
            listOf(MoonlightRebateRecordStatus.CLAIMED)
        ).size

        count += galaRebateRecordRepository.findByBuyerAddressIgnoreCaseAndStatusAndSettleTxIdIsNotNull(
            user.address,
            GalaRebateRecordStatus.SETTLED
        ).size

        count += galaRebateRecordRepository.findByBuyerAddressIgnoreCaseAndStatus(
            user.address,
            GalaRebateRecordStatus.CLAIMED
        ).size

        return count
    }

    fun getUnclaimedMoonlightBoxRebateRecord(
        user: User
    ): List<MoonlightRebateRecord> {
        return moonlightRebateRecordRepository.findByBuyerAddressIgnoreCaseAndSbtNFTIdAndStatusIn(
            user.address,
            BigInteger.ONE,
            listOf(MoonlightRebateRecordStatus.CREATED)
        )
    }

    fun getUnclaimedGalaRebateRecord(
        user: User
    ): List<GalaRebateRecord> {
        return galaRebateRecordRepository.findByBuyerAddressIgnoreCaseAndStatus(
            user.address,
            GalaRebateRecordStatus.CREATED
        )
    }
}