package io.vault.jasper.service

import io.vault.jasper.model.StonePieceProcessParameter
import io.vault.jasper.model.MoonlightRebateRecord
import io.vault.jasper.model.MoonlightRebateRecordStatus
import io.vault.jasper.repository.MoonlightRebateRecordRepository
import org.slf4j.LoggerFactory
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.stereotype.Service
import java.math.BigDecimal
import java.math.BigInteger

/**
 * 石头碎片处理定时任务服务
 */
@Service
class StonePieceProcessService @Autowired constructor(
    private val stonePieceProcessParameterService: StonePieceProcessParameterService,
    private val moonlightRebateRecordRepository: MoonlightRebateRecordRepository
) {

    private val logger = LoggerFactory.getLogger(this::class.java)

    /**
     * 处理指定NFT ID的石头碎片失败记录
     */
    fun processStonePieceFailedRecordsForNftId(nftId: String, config: StonePieceProcessParameter? = null): ProcessResult {
        val activeConfig = config ?: stonePieceProcessParameterService.getDefaultConfig()
        val nftIdBigInteger = BigInteger(nftId)
        
        logger.info("Processing stone piece failed records for NFT ID: $nftId")

        // 1. 根据 nftId 找出所有状态为 SETTLE_FAILED 的记录
        val failedRecords = moonlightRebateRecordRepository.findBySbtNFTIdAndStatus(
            nftIdBigInteger,
            MoonlightRebateRecordStatus.SETTLE_FAILED
        )

        if (failedRecords.isEmpty()) {
            logger.info("No failed records found for NFT ID: $nftId")
            return ProcessResult(0, 0, 0, 0)
        }

        logger.info("Found ${failedRecords.size} failed records for NFT ID: $nftId")

        // 2. 按 buyerAddress 分组
        val recordsByAddress = failedRecords.groupBy { it.buyerAddress }
        logger.info("Found ${recordsByAddress.size} unique buyer addresses for NFT ID: $nftId")

        val allNewRecords = mutableListOf<MoonlightRebateRecord>()
        val allDeletedRecordIds = mutableListOf<String>()
        var totalProcessedOrderIds = 0

        // 3. 对每个 buyerAddress 分组进行处理
        recordsByAddress.forEach { (buyerAddress, addressRecords) ->
            if (activeConfig.enableDetailedLogging) {
                logger.info("Processing ${addressRecords.size} records for address: $buyerAddress")
            }

            // 提取该地址下所有 stoneRelatedOptionOrderIds 并去重
            val allOrderIds = mutableSetOf<String>()
            addressRecords.forEach { record ->
                record.stoneRelatedOptionOrderIds?.let { orderIds ->
                    allOrderIds.addAll(orderIds)
                }
            }

            if (allOrderIds.isEmpty()) {
                if (activeConfig.enableDetailedLogging) {
                    logger.info("No stone related option order IDs found for address: $buyerAddress")
                }
                return@forEach
            }

            if (activeConfig.enableDetailedLogging) {
                logger.info("Extracted ${allOrderIds.size} unique order IDs for address: $buyerAddress")
            }
            totalProcessedOrderIds += allOrderIds.size

            // 重新生成记录，每7个订单ID为一组生成一条记录
            val orderIdsList = allOrderIds.toList()
            val templateRecord = addressRecords.first()

            var maxPieceCount = 7
            if (templateRecord.expiryInHour == "0.5" && templateRecord.bidAmount.compareTo(BigDecimal("0.01")) == 0) {
                maxPieceCount = 3
            }

            // 将订单ID按每maxPieceCount个分组
            val orderIdGroups = orderIdsList.chunked(maxPieceCount)

            orderIdGroups.forEachIndexed { index, orderGroup ->
                val actualPieceCount = orderGroup.size

                // 当 stonePieceCount = maxPieceCount 时，新生成的记录 status 为 CLAIMED，否则为 EXECUTED
                val newStatus = if (actualPieceCount == maxPieceCount) {
                    MoonlightRebateRecordStatus.CLAIMED
                } else {
                    MoonlightRebateRecordStatus.EXECUTED
                }

                // 使用该地址的第一个失败记录作为模板创建新记录
                val newRecord = createNewRecord(templateRecord, orderGroup, actualPieceCount, newStatus)
                allNewRecords.add(newRecord)

                if (activeConfig.enableDetailedLogging) {
                    logger.info("Prepared record ${index + 1} for address $buyerAddress with ${actualPieceCount} pieces (status: $newStatus)")
                }
            }

            // 收集该地址下要删除的旧记录ID
            val addressDeletedRecordIds = addressRecords.map { it.id!! }
            allDeletedRecordIds.addAll(addressDeletedRecordIds)
        }

        // 4. 批量插入所有新记录
        val savedRecords = moonlightRebateRecordRepository.saveAll(allNewRecords)
        logger.info("Created ${savedRecords.size} new records for NFT ID: $nftId")

        // 5. 批量删除所有旧记录
        moonlightRebateRecordRepository.deleteAllById(allDeletedRecordIds)
        logger.info("Deleted ${allDeletedRecordIds.size} old failed records for NFT ID: $nftId")

        return ProcessResult(
            processedAddresses = recordsByAddress.size,
            createdRecords = savedRecords.size,
            deletedRecords = allDeletedRecordIds.size,
            processedOrderIds = totalProcessedOrderIds
        )
    }

    /**
     * 创建新的MoonlightRebateRecord记录
     */
    private fun createNewRecord(
        templateRecord: MoonlightRebateRecord,
        orderGroup: List<String>,
        actualPieceCount: Int,
        newStatus: MoonlightRebateRecordStatus
    ): MoonlightRebateRecord {
        return MoonlightRebateRecord(
            optionOrderId = templateRecord.optionOrderId,
            buyerAddress = templateRecord.buyerAddress,
            direction = templateRecord.direction,
            bidAmount = templateRecord.bidAmount,
            bidAsset = templateRecord.bidAsset,
            strikePrice = templateRecord.strikePrice,
            settlementPrice = templateRecord.settlementPrice,
            premiumFee = templateRecord.premiumFee,
            premiumAsset = templateRecord.premiumAsset,
            premiumFeeInBtc = templateRecord.premiumFeeInBtc,
            premiumFeeInUsdt = templateRecord.premiumFeeInUsdt,
            profit = templateRecord.profit,
            profitAsset = templateRecord.profitAsset,
            profitInBtc = templateRecord.profitInBtc,
            profitInUsdt = templateRecord.profitInUsdt,
            netProfit = templateRecord.netProfit,
            rebateAmount = templateRecord.rebateAmount,
            jumpGalaThreeLinkTime = templateRecord.jumpGalaThreeLinkTime,
            status = newStatus,
            settleTxId = null, // 重置结算交易ID
            expiryInHour = templateRecord.expiryInHour,
            sbtNFTId = templateRecord.sbtNFTId,
            stonePieceCount = actualPieceCount,
            stoneRelatedOptionOrderIds = orderGroup.toMutableList(),
            chain = templateRecord.chain,
            errorMsg = null // 清除错误信息
        )
    }

    /**
     * 处理结果数据类
     */
    data class ProcessResult(
        val processedAddresses: Int,
        val createdRecords: Int,
        val deletedRecords: Int,
        val processedOrderIds: Int
    )
}
