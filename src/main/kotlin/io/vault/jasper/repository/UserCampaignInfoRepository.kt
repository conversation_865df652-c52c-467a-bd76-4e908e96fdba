package io.vault.jasper.repository

import io.vault.jasper.model.User
import io.vault.jasper.model.UserCampaignInfo
import org.springframework.data.domain.Sort
import org.springframework.data.mongodb.repository.MongoRepository
import org.springframework.stereotype.Repository
import java.time.LocalDateTime

@Repository
interface UserCampaignInfoRepository : MongoRepository<UserCampaignInfo, String> {

    fun findByUserId(userId: String): UserCampaignInfo?
    fun findByAddress(address: String): UserCampaignInfo?
    fun findByAddressIgnoreCase(address: String): UserCampaignInfo?
    fun findByTotalPointIsNull(): List<UserCampaignInfo>
}