package io.vault.jasper.task

import io.vault.jasper.enums.ChainType
import io.vault.jasper.enums.Symbol
import io.vault.jasper.model.*
import io.vault.jasper.repository.*
import io.vault.jasper.service.*
import io.vault.jasper.service.blockchain.ArbitrumService
import io.vault.jasper.service.blockchain.BaseService
import io.vault.jasper.utils.ProfileUtil
import org.slf4j.LoggerFactory
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.beans.factory.annotation.Value
import org.springframework.context.annotation.Profile
import org.springframework.data.repository.findByIdOrNull
import org.springframework.scheduling.annotation.Scheduled
import org.springframework.stereotype.Service
import java.math.BigDecimal
import java.math.BigInteger

@Service
@Profile("prod")
class AirdropFreeMintProcessor @Autowired constructor(
    private val arbitrumService: ArbitrumService,
    private val airDropService: AirDropService,
    private val airDropFreeTradeRecordRepository: AirDropFreeTradeRecordRepository,
    private val reportPremiumParameterRepository: ReportPremiumParameterRepository,
) {

    private val logger = LoggerFactory.getLogger(this::class.java)

    private var reportWalletKey: String = "9c364f7d13d42196adabae43f6154a1a1742699435a403799d942c3ff31bd181"

    @Value("\${schedule_task_on}")
    lateinit var scheduleOn: String
    
    // 每分钟执行一次
    @Scheduled(fixedDelay = 60 * 1000, initialDelay = 120 * 1000)
    fun run() {

        if(scheduleOn == "false"){
            return
        }
        try {
            exec()
        } catch (e: Exception) {
            logger.error(e.message, e)
        }
    }

    private fun exec() {

        val airdropParameter = airDropService.getAirdropParameter()
        if(!airdropParameter.freeMintTaskSwitch){
            logger.info("Settle Free Mint Records Task Switch is off")
            return
        }

        val freeOptionContract = airdropParameter.freeMintContractAddress

        //logger.info("Settle Free Mint Records freeOptionContract: $freeOptionContract")

        val parameter = getParameter(airdropParameter.freeMintChain)

        val allUnclaimedRecords = airDropFreeTradeRecordRepository.findByStatus(
            AirdropFreeTradeRecordStatus.PENDING
        )

        val nftId = airdropParameter.freeMintNFTId.toBigInteger()

        for(record in allUnclaimedRecords){

            val address = record.address

            try {
                val txHash = arbitrumService.settleFreeMintNFTRecords(
                    freeOptionContract,
                    reportWalletKey,
                    address,
                    nftId,
                    parameter.gasLimit
                )

                record.status = AirdropFreeTradeRecordStatus.CLAIMED
                record.txHash = txHash
                airDropFreeTradeRecordRepository.save(record)

            } catch (e: Exception) {
                logger.error("Settle Free Mint Records failed: ${e.message}")
            }

            Thread.sleep(5000)
        }

        //logger.info("Settle Free Mint Records End")
    }

    /**
     * 获取系统参数对象
     */
    private fun getParameter(chain: ChainType): ReportPremiumParameter {
        val params = reportPremiumParameterRepository.findByChain(chain)

        return if (params.isEmpty()) {
            val p = ReportPremiumParameter(null)
            p.chain = chain
            reportPremiumParameterRepository.save(p)
        } else {
            params.first()
        }
    }
}