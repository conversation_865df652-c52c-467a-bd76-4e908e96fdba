package io.vault.jasper.repository

import io.vault.jasper.model.Kol
import io.vault.jasper.model.KolStatus
import org.springframework.data.mongodb.repository.MongoRepository
import org.springframework.stereotype.Repository

@Repository
interface KolRepository : MongoRepository<Kol, String> {
    fun findFirstByWallet(walletAddress: String): Kol?
    fun findFirstByWalletAndLevelNot(
        walletAddress: String,
        level: String
    ): Kol?
    fun findByStatus(status: KolStatus): List<Kol>
    fun findByLevelNotAndStatus(level: String, status: KolStatus): List<Kol>
    fun findByLevelAndStatus(
        level: String,
        status: KolStatus
    ): List<Kol>
}