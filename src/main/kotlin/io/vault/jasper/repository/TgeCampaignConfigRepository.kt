package io.vault.jasper.repository

import io.vault.jasper.model.BtrCampaignConfig
import io.vault.jasper.model.TgeCampaignConfig
import org.springframework.data.mongodb.repository.MongoRepository
import org.springframework.stereotype.Repository

@Repository
interface TgeCampaignConfigRepository : MongoRepository<TgeCampaignConfig, String> {
    fun findFirstByEnabled(enabled: Boolean): TgeCampaignConfig?
} 