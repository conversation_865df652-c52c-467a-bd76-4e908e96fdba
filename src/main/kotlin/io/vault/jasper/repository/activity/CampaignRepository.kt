package io.vault.jasper.repository.activity

import io.vault.jasper.model.activity.Campaign
import org.springframework.data.mongodb.repository.MongoRepository
import org.springframework.stereotype.Repository
import java.time.LocalDateTime
import java.util.Optional

@Repository
interface CampaignRepository : MongoRepository<Campaign, String> {
    fun findByName(name: String): Optional<Campaign>
    
    fun findByActiveTrue(): List<Campaign>
    
    fun findByStartTimeLessThanEqualAndEndTimeGreaterThanEqual(
        currentTime: LocalDateTime,
        currentTime2: LocalDateTime
    ): List<Campaign>
}
