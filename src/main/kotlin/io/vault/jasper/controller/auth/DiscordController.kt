package io.vault.jasper.controller.auth

import EVMAddress
import io.swagger.annotations.ApiOperation
import io.vault.jasper.ApiResponse
import io.vault.jasper.dto.KolApplyDTO
import io.vault.jasper.exception.BusinessException
import io.vault.jasper.exception.ResultEnum
import io.vault.jasper.model.*
import io.vault.jasper.repository.*
import io.vault.jasper.response.*
import io.vault.jasper.service.AirDropService
import io.vault.jasper.service.DiscordService
import io.vault.jasper.service.MistTrackService
import io.vault.jasper.service.UserService
import io.vault.jasper.service.kol.KolLevelService
import io.vault.jasper.utils.AuthUtil
import org.slf4j.LoggerFactory
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.data.domain.PageRequest
import org.springframework.data.domain.Sort
import org.springframework.data.redis.core.RedisTemplate
import org.springframework.validation.annotation.Validated
import org.springframework.web.bind.annotation.*
import java.time.LocalDate
import java.time.LocalDateTime
import java.time.ZoneId
import java.util.*
import javax.servlet.http.HttpServletRequest


@RestController
@RequestMapping("/discord")
class DiscordController @Autowired constructor(
    private val authUtil: AuthUtil,
    private val discordService: DiscordService,
    private val userRepository: UserRepository,
    private val airDropService: AirDropService
) {

    private var logger = LoggerFactory.getLogger(this::class.java)

    private fun genRandomString(): String {
        val length = 32
        val charset = ('a'..'z') + ('A'..'Z') + ('0'..'9')
        return (1..length)
            .map { charset.random() }
            .joinToString("")
    }

    @ApiOperation("Oauth2 获取授权链接")
    @CrossOrigin
    @GetMapping("/oauth2/authorization")
    fun getDiscordOauth2Authorization(
        request: HttpServletRequest,
        @RequestParam("address") @EVMAddress address: String
    ): ApiResponse<String> {
        authUtil.filterIPBlacklist(request)
        val user = authUtil.auth(request, address)
        // val user = userRepository.findByAddressIgnoreCase(address) ?: throw BusinessException(ResultEnum.USER_NOT_FOUND)
        user.discordSession = genRandomString()
        userRepository.save(user)

        val url = discordService.getOauth2AuthorizationUrl(user.discordSession!!)

        return ApiResponse.success(url)
    }

    @ApiOperation("用户是否已加入Jasper Server")
    @CrossOrigin
    @GetMapping("/inGuild")
    fun userInGuild(
        request: HttpServletRequest,
        @RequestParam("address") @EVMAddress address: String
    ): ApiResponse<Boolean> {
        authUtil.filterIPBlacklist(request)
        var user = authUtil.auth(request, address)

        logger.info("userInGuild: $address")

        val discordInfo = user.discordInfo ?: return ApiResponse.success(false)
        if (discordInfo.inGuild) {
            logger.info("userInGuild: already true $address")
            return ApiResponse.success(true)
        }
        if (discordService.isExpired(discordInfo.grantTime, discordInfo.expiresIn)) {
            // refresh token
            val accessTokenResponse = try {
                discordService.refreshToken(discordInfo.refreshToken)
            } catch (e: Exception) {
                logger.error("refresh token error", e)
                return ApiResponse.success(false)
            }
            user.discordInfo!!.tokenType = accessTokenResponse.tokenType
            user.discordInfo!!.accessToken = accessTokenResponse.accessToken
            user.discordInfo!!.grantTime = LocalDateTime.now()
            user.discordInfo!!.expiresIn = accessTokenResponse.expiresIn
            user = userRepository.save(user)
        }
        val inGuild = discordService.inGuild(user.discordInfo!!.tokenType, user.discordInfo!!.accessToken)
        user.discordInfo!!.inGuild = inGuild
        user = userRepository.save(user)

        logger.info("userInGuild: after discord service $inGuild")

        // 派发 sPoint
        //airDropService.checkSocialTask(user)

        return ApiResponse.success(user.discordInfo?.inGuild ?: false)
    }
}