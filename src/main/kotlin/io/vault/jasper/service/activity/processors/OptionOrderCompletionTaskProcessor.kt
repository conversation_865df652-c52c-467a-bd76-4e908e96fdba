package io.vault.jasper.service.activity.processors

import io.vault.jasper.model.OptionOrder
import io.vault.jasper.model.User
import io.vault.jasper.model.activity.Task
import io.vault.jasper.model.activity.TaskConfigParams
import io.vault.jasper.model.activity.TaskType
import io.vault.jasper.repository.OptionOrderRepository
import io.vault.jasper.repository.activity.CampaignRepository
import io.vault.jasper.service.activity.TaskProcessor
import org.springframework.data.repository.findByIdOrNull
import org.springframework.stereotype.Component
import java.math.BigDecimal
import org.slf4j.LoggerFactory

/**
 * Processor for OPTION_ORDER_COMPLETION task type
 */
@Component
class OptionOrderCompletionTaskProcessor(
    private val optionOrderRepository: OptionOrderRepository,
    private val campaignRespository: CampaignRepository
) : TaskProcessor {

    private val logger = LoggerFactory.getLogger(this::class.java)

    override fun getTaskType(): TaskType = TaskType.OPTION_ORDER_COMPLETION
    
    override fun isTaskCompleted(user: User, task: Task, context: Map<String, Any>): Pair<Boolean, Map<String, Any>> {
        // Get the option order from context if provided
        val optionOrder = context["optionOrder"] as? OptionOrder
            ?: context["optionOrderId"]?.let { optionOrderRepository.findById(it.toString()).orElse(null) }
            
        // If no option order is provided, and we're just checking if the user has completed this task type,
        // we need to check all their orders
        if (optionOrder == null) {
            val userFirstOrder = optionOrderRepository.findFirstByBuyer(user.address)
            if(userFirstOrder == null){
                return Pair(false, emptyMap())
            }

            val result = checkOrderMatchesTaskCriteria(userFirstOrder, task, user, context)
            if (result.first) {
                return result
            }

            return Pair(false, emptyMap())
        }
        
        // If the order doesn't belong to this user, it can't complete the task
        if (optionOrder.buyer != user.address) {
            return Pair(false, emptyMap())
        }
        
        return checkOrderMatchesTaskCriteria(optionOrder, task, user, context)
    }
    
    private fun checkOrderMatchesTaskCriteria(
        optionOrder: OptionOrder,
        task: Task,
        user: User,
        context: Map<String, Any>
    ): Pair<Boolean, Map<String, Any>> {
        val taskConfig = task.taskConfig
        
        // Check bid asset if specified
        val bidAssetStr = taskConfig[TaskConfigParams.BID_ASSET] as? String
        if (bidAssetStr != null && optionOrder.bidAsset?.name != bidAssetStr) {
            logger.info("OptionOrderCompletionTaskProcessor bid asset not match ${optionOrder.bidAsset?.name} ${bidAssetStr}")
            return Pair(false, emptyMap())
        }
        
        // Check minimum bid amount if specified
        val minBidAmountStr = taskConfig[TaskConfigParams.MIN_BID_AMOUNT] as? String
        if (minBidAmountStr != null) {
            val minBidAmount = BigDecimal(minBidAmountStr)
            if (optionOrder.bidAmount == null || optionOrder.bidAmount!!.compareTo(minBidAmount) < 0) {
                logger.info("OptionOrderCompletionTaskProcessor bid amount less than min bid amount ${optionOrder.bidAmount} ${minBidAmount}")
                return Pair(false, emptyMap())
            }
        }

        val bidAmountStr = taskConfig[TaskConfigParams.BID_AMOUNT] as? String
        if (bidAmountStr != null) {
            val bidAmount = BigDecimal(bidAmountStr)
            if (optionOrder.bidAmount == null || optionOrder.bidAmount!!.compareTo(bidAmount) != 0) {
                logger.info("OptionOrderCompletionTaskProcessor bid amount not match ${optionOrder.bidAmount} ${bidAmount}")
                return Pair(false, emptyMap())
            }
        }
        
        // Check chain if specified
        val chainStr = taskConfig[TaskConfigParams.CHAIN] as? String
        if (chainStr != null && optionOrder.chain.name != chainStr) {
            logger.info("OptionOrderCompletionTaskProcessor chain not match ${optionOrder.chain.name} ${chainStr}")
            return Pair(false, emptyMap())
        }
        
        // Check minimum premium fee in USDT if specified
        val minPremiumFeeUsdtStr = taskConfig[TaskConfigParams.MIN_PREMIUM_FEE_USDT] as? String
        if (minPremiumFeeUsdtStr != null) {
            val minPremiumFeeUsdt = BigDecimal(minPremiumFeeUsdtStr)
            if (optionOrder.premiumFeePayInUsdt == null || optionOrder.premiumFeePayInUsdt!! < minPremiumFeeUsdt) {
                logger.info("OptionOrderCompletionTaskProcessor premium fee less than min premium fee ${optionOrder.premiumFeePayInUsdt} ${minPremiumFeeUsdt}")
                return Pair(false, emptyMap())
            }
        }
        
        // Check product type if specified
        val productTypeStr = taskConfig[TaskConfigParams.PRODUCT_TYPE] as? String
        if (productTypeStr != null && optionOrder.product?.name != productTypeStr) {
            logger.info("OptionOrderCompletionTaskProcessor product type not match ${optionOrder.product?.name} ${productTypeStr}")
            return Pair(false, emptyMap())
        }
        
        // Check if it's the first order in the campaign if specified
        val firstOrderInCampaign = taskConfig[TaskConfigParams.FIRST_ORDER_IN_CAMPAIGN] as? Boolean
        if (firstOrderInCampaign == true) {
            val campaignId = context["campaignId"] as String?
            if (campaignId != null) {
                val campaign = campaignRespository.findByIdOrNull(campaignId)
                if (campaign != null) {
                    val userOrders = optionOrderRepository.findByBuyerAndCreatedBetween(
                        user.address,
                        campaign.startTime,
                        campaign.endTime
                    )
                    
                    // If this is not the first order in the activity time range
                    if (userOrders.isEmpty() || userOrders.first().id != optionOrder.id) {
                        logger.info("OptionOrderCompletionTaskProcessor not first order in campaign ${optionOrder.id}")
                        return Pair(false, emptyMap())
                    }
                }
            }
        }
        
        // All criteria matched
        val completionData = mutableMapOf<String, Any>(
            "optionOrderId" to (optionOrder.id ?: ""),
            "bidAsset" to (optionOrder.bidAsset?.name ?: ""),
            "bidAmount" to (optionOrder.bidAmount?.toString() ?: "0"),
            "chain" to optionOrder.chain.name,
            "premiumFeePayInUsdt" to (optionOrder.premiumFeePayInUsdt?.toString() ?: "0"),
            "product" to (optionOrder.product?.name ?: "")
        )
        
        return Pair(true, completionData)
    }
}
