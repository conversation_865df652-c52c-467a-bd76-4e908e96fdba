package io.vault.jasper.dto

import EVMAddress
import io.swagger.annotations.ApiModel
import io.swagger.annotations.ApiModelProperty

@ApiModel("更新用户网络迁移参数请求")
data class UpdateUsernetworkMovementParameterDTO(
    @ApiModelProperty("钱包地址", required = true)
    @field:EVMAddress
    val address: String,
    
    @ApiModelProperty("任务开关", required = false)
    val taskSwitch: Boolean? = null,
    
    @ApiModelProperty("根地址", required = false)
    val rootAddress: String? = null,
    
    @ApiModelProperty("每次处理的用户数量", required = false)
    val batchSize: Int? = null,
    
    @ApiModelProperty("是否重置已处理的计数", required = false)
    val resetProcessedCount: Boolean? = null
)
