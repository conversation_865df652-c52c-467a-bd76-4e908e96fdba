package io.vault.jasper.model

import io.vault.jasper.enums.ChainType
import org.springframework.data.annotation.Id
import org.springframework.data.annotation.LastModifiedDate
import org.springframework.data.mongodb.core.index.Indexed
import org.springframework.data.mongodb.core.mapping.Document
import org.springframework.data.mongodb.core.mapping.Field
import java.math.BigInteger
import java.time.LocalDateTime

@Document
data class Blockchain(
    @Id
    val id: String? = null,
    @Indexed(unique = true)
    val chain: ChainType,
    //@Field("gas_price")
    //var gasPrice: BigInteger = BigInteger.ZERO, // Unit: GWEI 废弃
    //@Field("gas_limit")
    //var gasLimit: Int = 0, // 废弃
    //@Field("contract_gas_limit")
    //var contractGasLimit: Int = 0, //废弃
    @Field("block_number")
    var blockNumber: Long = 0,
    @Field("confirm_count")
    var confirmCount: Int = 0,
    @LastModifiedDate
    val updated: LocalDateTime = LocalDateTime.now(),
    var warningDiffCount: Int = 100, // Block number difference between the latest block and the current block
    var eventListenerBlockNumber: Long = 0,

    @Field("check_missing_orders_block_height")
    var checkMissingOrdersBlockHeight: Long = 0,

    @Field("last_checked_time_in_subgraph")
    var lastCheckedTimeInSubgraph: LocalDateTime? = null,

    var uat: Boolean = false
)
