package io.vault.jasper.repository.activity

import io.vault.jasper.model.activity.CampaignReward
import io.vault.jasper.model.activity.RewardType
import org.springframework.data.mongodb.repository.MongoRepository
import org.springframework.stereotype.Repository
import java.util.Optional

@Repository
interface CampaignRewardRepository : MongoRepository<CampaignReward, String> {
    fun findByCampaignId(
        campaignId: String
    ): List<CampaignReward>
    
    fun findByCampaignIdAndRewardType(
        campaignId: String,
        rewardType: RewardType
    ): Optional<CampaignReward>
}
