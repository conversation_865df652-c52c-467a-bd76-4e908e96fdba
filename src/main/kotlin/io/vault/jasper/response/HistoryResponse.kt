package io.vault.jasper.response

import io.swagger.annotations.ApiModel
import io.swagger.annotations.ApiModelProperty
import io.vault.jasper.enums.ChainType
import io.vault.jasper.enums.OptionDirection
import io.vault.jasper.enums.Symbol
import io.vault.jasper.model.OptionStatus
import io.vault.jasper.model.TransactionMethod

@ApiModel
data class GetPriceResponse(

    @ApiModelProperty("价格ID", required = true)
    val priceId: String,

    @ApiModelProperty("价格", required = true)
    val price: String,

    @ApiModelProperty("价格时间", required = true)
    val timestamp: Long,

)
