package io.vault.jasper.model

import io.vault.jasper.enums.ChainType
import org.springframework.data.annotation.Id
import org.springframework.data.mongodb.core.mapping.Document
import org.springframework.data.mongodb.core.mapping.Field
import java.math.BigInteger
import java.time.LocalDateTime

@Document("lp_vault_record")
data class LpVaultRecord(
    @Id
    val id: String? = null,

    val chain: ChainType,

    @Field("tx_hash")
    val txHash: String,

    @Field("tx_block_height")
    val txBlockHeight: Long,

    @Field("block_time")
    val blockTime: LocalDateTime,

    val method: String,

    val from: String?,

    val to: String?,

    @Field("asset_amount")
    val assetAmount: BigInteger,

    @Field("lp_amount")
    val lpAmount: BigInteger,

    val vault: String?,

    val created: LocalDateTime = LocalDateTime.now()
)