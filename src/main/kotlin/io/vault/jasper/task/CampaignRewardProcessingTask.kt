package io.vault.jasper.task

import io.vault.jasper.enums.ChainType
import io.vault.jasper.enums.Symbol
import io.vault.jasper.model.activity.RewardRecordStatus
import io.vault.jasper.repository.activity.CampaignRepository
import io.vault.jasper.repository.activity.CampaignRewardRecordRepository
import io.vault.jasper.repository.activity.UserCampaignProgressRepository
import io.vault.jasper.service.CurrencyService
import io.vault.jasper.service.activity.CampaignService
import io.vault.jasper.service.blockchain.ArbitrumService
import org.slf4j.LoggerFactory
import org.springframework.beans.factory.annotation.Value
import org.springframework.data.repository.findByIdOrNull
import org.springframework.scheduling.annotation.Scheduled
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional
import java.math.BigDecimal
import java.time.LocalDateTime

/**
 * Scheduled task to process pending campaign rewards
 */
@Service
class CampaignRewardProcessingTask(
    private val campaignRewardRecordRepository: CampaignRewardRecordRepository,
    private val arbitrumService: ArbitrumService,
    private val campaignRepository: CampaignRepository,
    private val currencyService: CurrencyService,
    private val userCampaignProgressRepository: UserCampaignProgressRepository,
    private val campaignService: CampaignService
) {
    private val logger = LoggerFactory.getLogger(this::class.java)
    
    @Value("\${schedule_task_on:true}")
    private lateinit var scheduleOn: String

    // ******************************************
    private val reportWalletKey = "eb4f2b99ed9bc6bf014578ec87f487aa3d12e629c4b470279e63063f02fc3e4c"

    /**
     * Process pending rewards every minute
     */
    @Scheduled(fixedRate = 60000)
    fun run() {
        if (scheduleOn == "false") {
            return
        }

        try {
            exec()
        } catch (e: Exception) {
            logger.error("Error processing user trade rebates: ${e.message}", e)
        }
    }

    private fun exec() {
        logger.info("Starting campaign reward processor")

        try {
            changeStatusToProcessing()
        } catch (e: Exception) {
            logger.error("Error changing status to processing", e)
        }

        pushToContract()

        logger.info("End campaign reward processor")
    }

    fun changeStatusToProcessing() {

        logger.info("Processing pending campaign rewards")

        try {
            val now = LocalDateTime.now()
            val pendingRecords = campaignRewardRecordRepository.findByStatusAndScheduledTimeBefore(
                RewardRecordStatus.PENDING,
                now
            )

            logger.info("Found ${pendingRecords.size} pending reward records to process")

            for (record in pendingRecords) {
                try {

                    val campaign = campaignRepository.findByIdOrNull(record.campaignId)
                    if(campaign == null){
                        continue
                    }

                    // check whether all task in campaign is finished
                    val userProgress = campaignService.getUserCampaignProgress(
                        record.userId,
                        record.address,
                        record.campaignId
                    )

                    if(userProgress.completedTaskIds.size == campaign.taskIds.size){
                        record.status = RewardRecordStatus.PROCESSING
                        campaignRewardRecordRepository.save(record)
                    }

                } catch (e: Exception) {
                    logger.error("Error processing reward record ${record.id}", e)

                    // Mark as failed
                    record.status = RewardRecordStatus.FAILED
                    record.errorMessage = e.message
                    campaignRewardRecordRepository.save(record)
                }
            }
        } catch (e: Exception) {
            logger.error("Error processing pending rewards", e)
        }
    }

    fun pushToContract() {
        
        logger.info("Processing push to contract campaign rewards")
        
        try {
            val now = LocalDateTime.now()
            val pendingRecords = campaignRewardRecordRepository.findByStatusAndScheduledTimeBefore(
                RewardRecordStatus.PROCESSING,
                now
            )
            
            logger.info("Found ${pendingRecords.size} processing reward records to push")

            val arbitrumUsdtDecimal = currencyService.getCurrencyDecimal(ChainType.ARBITRUM, Symbol.USDT)

            for (record in pendingRecords) {
                try {

                    val campaign = campaignRepository.findByIdOrNull(record.campaignId) ?: continue

                    // 发起链上交易
                    if(record.amount.compareTo(BigDecimal.ZERO) > 0) {

                        val settleTxHash = arbitrumService.settleAirdropebate(
                            contractAddress = campaign.smartContractAddress!!,
                            privateKey = reportWalletKey,
                            addressList = listOf(record.address),
                            amountList = listOf(record.amount.movePointRight(arbitrumUsdtDecimal).toBigInteger()),
                            gasLimit = 200000
                        )

                        record.transactionHash = settleTxHash
                    }

                    // For now, just mark it as completed
                    record.status = RewardRecordStatus.COMPLETED
                    campaignRewardRecordRepository.save(record)

                    val campaignCompleteCount = campaignRewardRecordRepository.countByCampaignIdAndStatus(
                        record.campaignId,
                        RewardRecordStatus.COMPLETED
                    )

                    campaign.completedCount = campaignCompleteCount.toInt()
                    campaignRepository.save(campaign)
                    
                    logger.info("Processed reward record ${record.id}")

                    Thread.sleep(5000)

                } catch (e: Exception) {
                    logger.error("Error processing reward record ${record.id}", e)
                    
                    // Mark as failed
                    record.status = RewardRecordStatus.FAILED
                    record.errorMessage = e.message
                    campaignRewardRecordRepository.save(record)
                }
            }
        } catch (e: Exception) {
            logger.error("Error processing pending rewards", e)
        }
    }
}
