package io.vault.jasper.task

import io.vault.jasper.model.User
import io.vault.jasper.model.UserNetwork
import io.vault.jasper.model.UserNetworkMovementParameter
import io.vault.jasper.repository.UserNetworkRepository
import io.vault.jasper.repository.UserRepository
import io.vault.jasper.repository.UserNetworkMovementParameterRepository
import org.slf4j.LoggerFactory
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.beans.factory.annotation.Value
import org.springframework.context.annotation.Profile
import org.springframework.data.mongodb.core.MongoTemplate
import org.springframework.scheduling.annotation.Scheduled
import org.springframework.stereotype.Service
import java.time.LocalDateTime

/**
 * 用户网络迁移定时任务
 * 将用户的推荐关系迁移到UserNetwork结构中
 */
@Service
@Profile("prod", "test")
class UserNetworkMovementTask @Autowired constructor(
    private val userNetworkRepository: UserNetworkRepository,
    private val userRepository: UserRepository,
    private val usernetworkMovementParameterRepository: UserNetworkMovementParameterRepository,
    private val mongoTemplate: MongoTemplate
) {
    private val logger = LoggerFactory.getLogger(this::class.java)

    @Value("\${schedule_task_on}")
    lateinit var scheduleOn: String

    /**
     * 每小时执行一次
     */
    @Scheduled(fixedDelay = 60 * 1000, initialDelay = 60 * 1000)
    fun run() {
        if (scheduleOn == "false") {
            return
        }

        try {
            // 检查任务是否启用
            val parameter = getOrCreateParameter()
            if (!parameter.taskSwitch) {
                logger.info("Usernetwork movement task is disabled")
                return
            }

            logger.info("Starting usernetwork movement task")
            exec(parameter)
            logger.info("Completed usernetwork movement task")
        } catch (e: Exception) {
            logger.error("Error in usernetwork movement task: ${e.message}", e)
        }
    }

    /**
     * 获取或创建参数配置
     */
    private fun getOrCreateParameter(): UserNetworkMovementParameter {
        val parameter = usernetworkMovementParameterRepository.findFirstByTaskSwitch(true)
        if (parameter != null) {
            return parameter
        }

        // 如果没有启用的配置，查找任意一个配置
        val anyParameter = usernetworkMovementParameterRepository.findAll().firstOrNull()
        if (anyParameter != null) {
            return anyParameter
        }

        // 如果没有任何配置，创建一个新的配置
        return usernetworkMovementParameterRepository.save(
            UserNetworkMovementParameter(
                taskSwitch = false,
                rootAddress = "0x55E13E114d4fa80c1dE6F84Ae1ADE25D578B402b"
            )
        )
    }

    /**
     * 执行迁移任务
     */
    private fun exec(parameter: UserNetworkMovementParameter) {
        // 1. 从 MongoDB Usernetwork Document 中查找根节点 usernetwork
        val rootUserNetwork = userNetworkRepository.findByAddressIgnoreCase(parameter.rootAddress)
        if (rootUserNetwork == null) {
            logger.error("Root UserNetwork not found for address: ${parameter.rootAddress}")
            return
        }

        logger.info("Found root UserNetwork: ${rootUserNetwork.address}, id: ${rootUserNetwork.id}")

        // 2. 查询需要处理的用户
        val user = userRepository.findByAddressIgnoreCase(parameter.processAddress)
        if (user == null) {
            logger.info("No more users to process")
            return
        }

        // 3. 处理用户
        val processedCount = moveReferralToUsernetworkRecursive(parameter.processAddress, rootUserNetwork)
        val lastProcessedUserId = user.id
        
        // 4. 更新参数
        parameter.lastProcessedUserId = lastProcessedUserId
        parameter.processedCount += processedCount
        parameter.lastExecutionTime = LocalDateTime.now()
        parameter.taskSwitch = false
        usernetworkMovementParameterRepository.save(parameter)
        
        logger.info("Processed $processedCount users in this batch, total: ${parameter.processedCount}")
    }

    /**
     * 递归创建 UserNetwork
     *
     * @param address 当前用户地址
     * @param parentUserNetwork 父用户的 UserNetwork
     * @return 创建的 UserNetwork 数量
     */
    private fun moveReferralToUsernetworkRecursive(
        address: String,
        parentUserNetwork: UserNetwork
    ): Int {
        var count = 0

        // 3. 从 MongoDB User Document 中查找 address=$address 的用户
        val user = userRepository.findByAddressIgnoreCase(address) ?: run {
            logger.warn("User not found for address: $address")
            return 0
        }

        // 检查用户是否已经有 UserNetwork
        val existingUserNetwork = userNetworkRepository.findByAddressIgnoreCase(address)
        if (existingUserNetwork != null) {
            //logger.info("UserNetwork already exists for user: $address, id: ${existingUserNetwork.id}")

            if(existingUserNetwork.invitedNetworkId != parentUserNetwork.id){
                logger.error("User $address already has UserNetwork, but invitedNetworkId is not correct, should be ${parentUserNetwork.id}, but is ${existingUserNetwork.invitedNetworkId}")
            }

            // 即使已经存在，也需要继续处理子用户
            val childUsers = userRepository.findByInvitedUserId(user.id!!)
            //logger.info("Found ${childUsers.size} child users for user: $address")

            childUsers.forEach { childUser ->
                count += moveReferralToUsernetworkRecursive(childUser.address, existingUserNetwork)
            }

            return count
        }

        // 4. 创建新的 usernetwork
        try {
            val newUserNetwork = UserNetwork(
                address = user.address,
                inviteCode = user.inviteCode,
                userId = user.id!!,
                invitedUserId = parentUserNetwork.userId,
                invitedNetworkId = parentUserNetwork.id!!,
                level = parentUserNetwork.level + 1
            )

            val savedUserNetwork = userNetworkRepository.save(newUserNetwork)
            logger.info("Created new UserNetwork for user: $address, id: ${savedUserNetwork.id}, level: ${savedUserNetwork.level}")
            count++

            user.useKolRebate = false
            userRepository.save(user)

            // 5. 查找 invitedUserId = user.id 的所有用户
            val childUsers = userRepository.findByInvitedUserId(user.id)
            //logger.info("Found ${childUsers.size} child users for user: $address")

            // 6. 对于每个 childUser, 递归处理
            childUsers.forEach { childUser ->
                count += moveReferralToUsernetworkRecursive(childUser.address, savedUserNetwork)
            }
        } catch (e: Exception) {
            logger.error("Error creating UserNetwork for user: $address", e)
        }

        return count
    }
}
