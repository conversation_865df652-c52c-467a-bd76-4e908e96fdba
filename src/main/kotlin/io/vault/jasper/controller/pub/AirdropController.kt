package io.vault.jasper.controller.pub

import EVMAddress
import io.swagger.annotations.ApiOperation
import io.vault.jasper.ApiResponse
import io.vault.jasper.annotation.ApiCache
import io.vault.jasper.dto.AuthDTO
import io.vault.jasper.enums.ChainType
import io.vault.jasper.enums.Symbol
import io.vault.jasper.model.*
import io.vault.jasper.repository.*
import io.vault.jasper.response.*
import io.vault.jasper.service.*
import io.vault.jasper.utils.AuthUtil
import io.vault.jasper.utils.DateTimeUtil
import org.slf4j.LoggerFactory
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.data.domain.PageImpl
import org.springframework.data.domain.PageRequest
import org.springframework.data.domain.Sort
import org.springframework.data.mongodb.core.MongoTemplate
import org.springframework.data.mongodb.core.query.Criteria
import org.springframework.data.mongodb.core.query.Query
import org.springframework.data.repository.findByIdOrNull
import org.springframework.web.bind.annotation.*
import org.web3j.crypto.Keys
import java.math.BigDecimal
import java.util.*
import javax.servlet.http.HttpServletRequest
import javax.validation.Valid

@RestController
@RequestMapping("/airdrop")
class AirdropController @Autowired constructor(
    private val authUtil: AuthUtil,
    private val userService: UserService,
    private val airDropService: AirDropService,
    private val airDropFreeTradeRecordRepository: AirDropFreeTradeRecordRepository,
    private val mongoTemplate: MongoTemplate,
    private val airdropFirstTradeRebateRecordRepository: AirdropTradeRebateRecordRepository,
    private val baseTradeRebateRecordRepository: BaseTradeRebateRecordRepository,
    private val twitterOAuthService: TwitterOAuthService,
    private val retweetUserIdRepository: RetweetUserIdRepository,
    private val nftRetweetUserIdRepository: NftAirdropRetweetUserIdRepository,
    private val optionOrderRepository: OptionOrderRepository,
    private val userRepository: UserRepository,
    private val baseCampaignService: BaseCampaignService,
    private val dlcbtcCampaignService: DlcbtcCampaignService,
    private val dlcbtcTradeRebateRecordRepository: DlcbtcTradeRebateRecordRepository,
    private val userCampaignInfoRepository: UserCampaignInfoRepository,
    private val airdropNFTRebateRecordRepository: AirdropNFTRebateRecordRepository
) {

    private var logger = LoggerFactory.getLogger(this::class.java)

    @ApiOperation("Request Airdrop Info of User")
    @CrossOrigin
    @ApiCache(key = "airdrop:info", dynamicKey = ["address"], expire = 10)
    @GetMapping("/info")
    fun info(
        request: HttpServletRequest,
        @RequestParam("address") @EVMAddress address: String,
    ): ApiResponse<AirdropInfoResponse> {

        authUtil.filterIPBlacklist(request)
        val user = authUtil.auth(request, address)

        // 检查 Twitter 任务是否完成
        if(!user.twitterTaskFinished && user.twitterAccountId != null) {
            retweetUserIdRepository.findByRetweetUserId(user.twitterAccountId!!)?.let {
                user.twitterTaskFinished = true
                userRepository.save(user)

                airDropService.checkSocialTask(user)
            }
        }

        val airdropParameter = airDropService.getAirdropParameter()

//        val referralAddresses = userService.getRefereesAddressAfterTime(
//            user,
//            airdropParameter.startDate
//        )

        val userCampaignInfo = airDropService.getUserCampaignInfo(user)
        val airdropSummary = airDropService.getAirdropSummary()

        var canTrade = user.created.isAfter(airdropParameter.startDate)

//        val sPoint1 = airDropService.getTotalSPoint(AirDropType.SOCIAL_TASK)
//        val sPoint2 = airDropService.getTotalSPoint(AirDropType.INVITATION_TASK)
//        val sPoint3 = airDropService.getTotalSPoint(AirDropType.TRADING_TASK)
        val sPoint1 = airdropSummary.socialTaskTotalPoint
        val sPoint2 = airdropSummary.inviteTaskTotalPoint
        val sPoint3 = airdropSummary.tradeTaskTotalPoint

        var rebateCount = airDropService.getFirstTradeRebateCount()
        if(rebateCount >= 10000){
            rebateCount = 10000
        }

//        val referralVerifiedCount = airDropService.getTransactionList(
//            AirDropType.INVITATION_TASK,
//            user.id!!
//        ).size
//        val referralTradeCount = airDropService.getTransactionList(
//            AirDropType.TRADING_TASK,
//            user.id
//        ).size

        val referralCount = userCampaignInfo.airdropInviteCount
        val referralVerifiedCount = userCampaignInfo.airdropInviteVerifiedCount
        val referralTradeCount = userCampaignInfo.airdropInviteTradeCount

        //val totalPoint = airDropService.getUserTotalPoint(user)
        val totalPoint = userCampaignInfo.totalPoint?: BigDecimal.ZERO
        val unclaimedCount = airDropService.getUnclaimedFreeTradeCount(user)

        val discordLevel = airDropService.getDiscordLevel(user)
        val firstTradeRebateRecords = airdropFirstTradeRebateRecordRepository.findByBuyerAddressIgnoreCase(
            user.address
        )

        var firstTradePNL = "0"
        var firstTradeIs2H = false

        if(firstTradeRebateRecords.isNotEmpty()) {
            canTrade = false
            firstTradePNL = firstTradeRebateRecords[0].netProfit.movePointLeft(6).stripTrailingZeros().toPlainString()
            firstTradeIs2H = true
        } else {
            val firstTrade = optionOrderRepository.findFirstByChainAndBuyerIgnoreCase(
                ChainType.ARBITRUM,
                user.address
            )

            //logger.info("AirdropController First trade: $firstTrade")
            if(firstTrade != null && firstTrade.expiryInHour != null && firstTrade.bidAsset != null && firstTrade.bidAmount != null) {

                if(firstTrade.expiryInHour == "2" && firstTrade.bidAsset == Symbol.ETH && firstTrade.bidAmount!!.compareTo(BigDecimal("0.2")) == 0) {
                    firstTradeIs2H = true
                }
            }
        }

        val apiParameter = twitterOAuthService.getParameter()

        var baseResponse = userCampaignInfo.baseResponse

        if(baseResponse == null) {
            baseResponse = getBaseTradeResponse(
                user,
                discordLevel
            )
            userCampaignInfo.baseResponse = baseResponse
            userCampaignInfoRepository.save(userCampaignInfo)
        }

        var dlcbtcAirdropInfoResponse = userCampaignInfo.dlcbtcResponse

        if(dlcbtcAirdropInfoResponse == null) {
            dlcbtcAirdropInfoResponse = getDLCBTCTradeResponse(
                user,
                discordLevel
            )
            userCampaignInfo.dlcbtcResponse = dlcbtcAirdropInfoResponse
            userCampaignInfoRepository.save(userCampaignInfo)
        }

        val airdropNFTInfoResponse = getAirdropNFTResponse(
            user,
            discordLevel
        )

        return ApiResponse.success(
            AirdropInfoResponse(
                address = user.address,
                isTwitterFollowed = user.twitterAccountId != null && user.twitterAccountId!!.isNotEmpty(),
                isTwitterRetweet = user.twitterTaskFinished,
                isJoinDiscord = user.discordInfo?.inGuild ?: false,
                totalInviteCount = referralCount,
                totalTradeCount = referralTradeCount,
                totalInviteAndVerifiedCount = referralVerifiedCount,
                totalReferralTradingCount = referralTradeCount,
                totalPoint = totalPoint.stripTrailingZeros().toPlainString(),
                unclaimedCount = unclaimedCount,
                discordLevel = discordLevel,
                firstTradePNL = firstTradePNL,
                firstTradeIs2HETH = firstTradeIs2H,
                retweetId = apiParameter.retweetId,
                canTrade = canTrade,
                sPoint1 = sPoint1.stripTrailingZeros().toPlainString(),
                sPoint2 = sPoint2.stripTrailingZeros().toPlainString(),
                sPoint3 = sPoint3.stripTrailingZeros().toPlainString(),
                firstTradeRebateCount = rebateCount,
                baseInfo = baseResponse,
                dlcbtcInfo = dlcbtcAirdropInfoResponse,
                nftInfo = airdropNFTInfoResponse
            )
        )
    }

    private fun getAirdropNFTResponse(
        user: User,
        discordLevel: Int
    ): AirdropNFTInfoResponse{

        val parameter = airDropService.getNftAirdropParameter()
        var existingNftRebateRecord = airdropNFTRebateRecordRepository.findByAddressIgnoreCase(
            user.address
        )

        if(existingNftRebateRecord == null){
            existingNftRebateRecord = AirdropNFTRebateRecord(
                userId = user.id!!,
                address = Keys.toChecksumAddress(user.address)
            )
        }

        existingNftRebateRecord.twitterAccountId = user.twitterAccountId
        existingNftRebateRecord.twitterTaskFinished = user.twitterTaskFinished
        existingNftRebateRecord.discordLevel = discordLevel
        existingNftRebateRecord.hasJoinDiscord = user.discordInfo?.inGuild ?: false

        if(existingNftRebateRecord.twitterAccountId != null){
            val retweetUserId = nftRetweetUserIdRepository.findByRetweetUserId(existingNftRebateRecord.twitterAccountId!!)
            if(retweetUserId != null){
                existingNftRebateRecord.hasRetweetNftTweet = true
            }
        }

        airdropNFTRebateRecordRepository.save(existingNftRebateRecord)

        var task1Finished = false
        if(!existingNftRebateRecord.twitterAccountId.isNullOrEmpty() &&
            existingNftRebateRecord.twitterTaskFinished &&
            existingNftRebateRecord.hasJoinDiscord){
            task1Finished = true
        }

        var task2Finished = false
        if(existingNftRebateRecord.hasRetweetNftTweet){
            task2Finished = true
        }

        if(task1Finished && task2Finished && existingNftRebateRecord.status == AirdropNFTRebateRecordStatus.CREATED){
            existingNftRebateRecord.status = AirdropNFTRebateRecordStatus.CLAIMED
            airdropNFTRebateRecordRepository.save(existingNftRebateRecord)
        }

        return AirdropNFTInfoResponse(
            discordLevel = existingNftRebateRecord.discordLevel,
            task1Finished = task1Finished,
            task2Finished = task2Finished,
            tweetId = parameter.tweetId,
            canClaimNft = task1Finished && task2Finished
        )
    }

    private fun getBaseTradeResponse(
        user: User,
        discordLevel: Int
    ): BaseAirdropInfoResponse {

        val baseParameter = baseCampaignService.getCampaignParameter()
        var rebateCount = baseCampaignService.getFirstTradeRebateCount()
        if(rebateCount >= 10000){
            rebateCount = 10000
        }

        val firstTradeRebateRecords = baseTradeRebateRecordRepository.findByBuyerAddressIgnoreCase(
            user.address
        )

        var firstTradePNL = "0"
        var firstTradeIs2H = false
        var canTrade = true

        if(firstTradeRebateRecords.isNotEmpty()) {
            canTrade = false
            firstTradePNL = firstTradeRebateRecords[0].netProfit.movePointLeft(6).stripTrailingZeros().toPlainString()
            firstTradeIs2H = true
        } else {
            val firstTrade = optionOrderRepository.findFirstByChainAndBuyerIgnoreCase(
                ChainType.BASE,
                user.address
            )

            //logger.info("AirdropController Base First trade: $firstTrade")
            if(firstTrade != null && firstTrade.expiryInHour != null && firstTrade.bidAsset != null && firstTrade.bidAmount != null) {

                if(baseCampaignService.isFirstOrderInCampaign(firstTrade)){
                    baseCampaignService.createBaseTradeRebateRecord(firstTrade)
                }

                if(firstTrade.expiryInHour == "2" &&
                    firstTrade.bidAsset == Symbol.CBBTC &&
                    firstTrade.bidAmount!!.compareTo(baseParameter.firstTradeDegenQuantity) == 0) {
                    firstTradeIs2H = true
                    canTrade = false
                }
            }
        }

        var isRetweet = false
        if(user.retweetBaseTime > 0) {
            isRetweet = user.retweetBaseTime < Date().time
        }

        return BaseAirdropInfoResponse(
            firstTradePNL = firstTradePNL,
            firstTradeIs2HETH = firstTradeIs2H,
            canTrade = canTrade,
            firstTradeRebateCount = rebateCount,
            discordLevel = discordLevel,
            isTwitterRetweet = isRetweet
        )
    }

    private fun getDLCBTCTradeResponse(
        user: User,
        discordLevel: Int
    ): DLCBTCAirdropInfoResponse {

        val parameter = dlcbtcCampaignService.getCampaignParameter()
        var rebateCount = dlcbtcCampaignService.getFirstTradeRebateCount()
        if(rebateCount >= 10000){
            rebateCount = 10000
        }

        val firstTradeRebateRecords = dlcbtcTradeRebateRecordRepository.findByBuyerAddressIgnoreCase(
            user.address
        )

        var firstTradePNL = "0"
        var firstTradeIs2H = false
        var canTrade = true

        if(firstTradeRebateRecords.isNotEmpty()) {
            canTrade = false
            firstTradePNL = firstTradeRebateRecords[0].netProfit.movePointLeft(6).stripTrailingZeros().toPlainString()
            firstTradeIs2H = true
        } else {
            val firstTrade = optionOrderRepository.findFirstByChainAndBidAssetAndBuyerIgnoreCaseAndCreatedGreaterThan(
                ChainType.ARBITRUM,
                Symbol.DLCBTC,
                user.address,
                parameter.startDate
            )

            //logger.info("AirdropController DLCBTC First trade: $firstTrade")
            if(firstTrade != null && firstTrade.expiryInHour != null && firstTrade.bidAsset != null && firstTrade.bidAmount != null) {

                if(dlcbtcCampaignService.isFirstOrderInCampaign(firstTrade)){
                    dlcbtcCampaignService.createTradeRebateRecord(firstTrade)
                }

                if(firstTrade.expiryInHour == "2" &&
                    firstTrade.bidAsset == Symbol.DLCBTC &&
                    firstTrade.bidAmount!!.compareTo(parameter.firstTradeDegenQuantity) == 0) {
                    firstTradeIs2H = true
                    canTrade = false
                }
            }
        }

        var isRetweet = false
        val userCampaignInfo = airDropService.getUserCampaignInfo(user)
        if(userCampaignInfo.retweetDlcbtcTime > 0) {
            isRetweet = user.retweetBaseTime < Date().time
        }

        var isFollow = false
        if(userCampaignInfo.followDlcbtcTime > 0) {
            isFollow = user.retweetBaseTime < Date().time
        }

        return DLCBTCAirdropInfoResponse(
            firstTradePNL = firstTradePNL,
            firstTradeIs2HETH = firstTradeIs2H,
            canTrade = canTrade,
            firstTradeRebateCount = rebateCount,
            discordLevel = discordLevel,
            isTwitterRetweet = isRetweet,
            isFollowDlcbtc = isFollow
        )
    }

    @PostMapping("/claim_free_trade")
    @ApiOperation("Claim Free Trade")
    @CrossOrigin
    fun claimFreeTrade(
        request: HttpServletRequest,
        @Valid @RequestBody dto: AuthDTO
    ): ApiResponse<Boolean> {

        authUtil.filterIPBlacklist(request)
        val user = authUtil.auth(request, dto.address)

        val unclaimedRecords = airDropService.getUnclaimedFreeTradeRecords(user)
        for(record in unclaimedRecords) {
            record.status = AirdropFreeTradeRecordStatus.PENDING
            airDropFreeTradeRecordRepository.save(record)
        }

        return ApiResponse.success(true)
    }

    @PostMapping("/claim_airdrop_nft")
    @ApiOperation("Claim Airdrop NFT")
    @CrossOrigin
    fun claimAirdropNft(
        request: HttpServletRequest,
        @Valid @RequestBody dto: AuthDTO
    ): ApiResponse<Boolean> {

        authUtil.filterIPBlacklist(request)
        val user = authUtil.auth(request, dto.address)



        return ApiResponse.success(true)
    }

    @ApiOperation("Request Airdrop Referral List of User")
    @CrossOrigin
    @ApiCache(key = "airdrop:referral_list", dynamicKey = ["address", "page", "pageSize"], expire = 60)
    @GetMapping("/referral_list")
    fun referralList(
        request: HttpServletRequest,
        @RequestParam("address") @EVMAddress address: String,
        @RequestParam(required = false) page: Int?,
        @RequestParam(required = false) pageSize: Int?
    ): ApiResponse<PageResponse<AirdropUserInfoResponse>> {

        authUtil.filterIPBlacklist(request)
        val user = authUtil.auth(request, address)

        val parameter = airDropService.getAirdropParameter()
//        val referralAddressList = userService.getRefereesAddressAfterTime(
//            user,
//            parameter.startDate
//        )


        val p = page ?: 1
        val ps = pageSize ?: 50
        val pageable = PageRequest.of(p - 1, ps, Sort.by(Sort.Order.desc(User::id.name)))

        val query = Query()
        query.addCriteria(Criteria.where(User::invitedUserId.name).`is`(user.id))
        query.addCriteria(Criteria.where(User::created.name).gte(parameter.startDate))

        val total = mongoTemplate.count(query, User::class.java)
        query.with(pageable)
        val resultSet = mongoTemplate.find(query, User::class.java)
        val optionsPage = PageImpl(resultSet, pageable, total)

        val optionsList = optionsPage.content.mapNotNull {

            val isSocialTaskFinished = airDropService.isUserSocialTaskFinished(it)

            AirdropUserInfoResponse(
                address = it.address,
                isTwitterVerified = isSocialTaskFinished,
                hasTraded = it.hasTraded
            )
        }

        return ApiResponse.success(
            PageResponse(
                total = optionsPage.totalElements,
                page = p,
                pageSize = ps,
                totalPage = optionsPage.totalPages,
                hasNext = optionsPage.hasNext(),
                data = optionsList
            )
        )
    }

    @ApiOperation("Request Airdrop Referral List of User")
    @CrossOrigin
    @ApiCache(key = "airdrop:new_users", dynamicKey = ["count"], expire = 60)
    @GetMapping("/new_users")
    fun topUserList(
        request: HttpServletRequest,
        count: Int = 10
    ): ApiResponse<List<UserResponse>> {

        authUtil.filterIPBlacklist(request)

        var pageCount = count;
        if(pageCount > 100) {
            pageCount = 100
        }

        val query = Query()
        query.with(Sort.by(Sort.Order.desc(User::created.name))
            .and(Sort.by(Sort.Order.asc(User::id.name)))
            .and(Sort.by(Sort.Order.asc(User::address.name)))
        )
        query.limit(pageCount)

        val resultSet = mongoTemplate.find(query, User::class.java)
        val userList = resultSet.mapNotNull {

            var parentAddress: String? = null

            if(it.invitedUserId != null) {
                val parentUser = userRepository.findByIdOrNull(it.invitedUserId!!)
                parentAddress = parentUser?.address
            }

            UserResponse(
                address = it.address,
                createDate = DateTimeUtil.convertLocalDateTimeToTimestamp(it.created),
                parentAddress = parentAddress
            )
        }

        return ApiResponse.success(userList)
    }

    @ApiOperation("返回推特帖子链接")
    @CrossOrigin
    @ApiCache(key = "airdrop:retweet_link", dynamicKey = ["address"], expire = 60)
    @GetMapping("/retweet_base")
    fun retweetBase(
        request: HttpServletRequest,
        @RequestParam("address") @EVMAddress address: String,
    ): ApiResponse<String> {

        authUtil.filterIPBlacklist(request)
        val user = authUtil.auth(request, address)

        if(user.retweetBaseTime == 0L) {
            user.retweetBaseTime = Date().time + 45 * 1000
            userRepository.save(user)

            val firstTradeRebateRecords = baseTradeRebateRecordRepository.findByBuyerAddressIgnoreCase(
                user.address
            )

            if(firstTradeRebateRecords.isNotEmpty()){
                val rebateRecord = firstTradeRebateRecords[0]
                rebateRecord.retweetBaseTime = user.retweetBaseTime
                baseTradeRebateRecordRepository.save(rebateRecord)
            }
        }

        val parameter = baseCampaignService.getCampaignParameter()
        return ApiResponse.success(parameter.retweetLink)
    }

    @ApiOperation("返回DLCBTC 推特帖子链接")
    @CrossOrigin
    @ApiCache(key = "airdrop:retweet_dlc_btc", dynamicKey = ["address", "lang"], expire = 60)
    @GetMapping("/retweet_dlc_btc")
    fun retweetDlcbtc(
        request: HttpServletRequest,
        @RequestParam("address") @EVMAddress address: String,
        @RequestParam(required = false) lang: String? = null
    ): ApiResponse<String> {

        authUtil.filterIPBlacklist(request)
        val user = authUtil.auth(request, address)

        val userCampaignInfo = airDropService.getUserCampaignInfo(user)
        if(userCampaignInfo.retweetDlcbtcTime == 0L) {
            userCampaignInfo.retweetDlcbtcTime = Date().time + 45 * 1000
            userCampaignInfoRepository.save(userCampaignInfo)

            val firstTradeRebateRecords = dlcbtcTradeRebateRecordRepository.findByBuyerAddressIgnoreCase(
                user.address
            )

            if(firstTradeRebateRecords.isNotEmpty()){
                val rebateRecord = firstTradeRebateRecords[0]
                rebateRecord.retweetDlcbtcTime = userCampaignInfo.retweetDlcbtcTime
                dlcbtcTradeRebateRecordRepository.save(rebateRecord)
            }
        }

        val parameter = dlcbtcCampaignService.getCampaignParameter()
        var retweetLink = parameter.retweetLink
        if(lang != null && lang == "JP"){
            retweetLink = "https://x.com/JaspervaultJP/status/1848193552234504202"
        }

        return ApiResponse.success(retweetLink)
    }

    @ApiOperation("返回社媒任务1推特帖子链接")
    @CrossOrigin
    @ApiCache(key = "airdrop:retweet_media_task_1", dynamicKey = ["address", "lang"], expire = 60)
    @GetMapping("/retweet_media_task_1")
    fun retweetMediaTask1(
        request: HttpServletRequest,
        @RequestParam("address") @EVMAddress address: String,
        @RequestParam(required = false) lang: String? = null
    ): ApiResponse<String> {

        authUtil.filterIPBlacklist(request)
        val user = authUtil.auth(request, address)

        if(user.twitterTaskFinished == false){
            user.twitterTaskFinished = true
            userRepository.save(user)

            // 派发 sPoint
            airDropService.checkSocialTask(user)
        }

        val parameter = airDropService.getAirdropParameter()
        val retweetId = parameter.retweetId
        val link = "https://twitter.com/Jaspervault/status/$retweetId"

        return ApiResponse.success(link)
    }

    @ApiOperation("返回DLCBTC 推特主页链接")
    @CrossOrigin
    @ApiCache(key = "airdrop:follow_dlc_btc", dynamicKey = ["address"], expire = 60)
    @GetMapping("/follow_dlc_btc")
    fun followDlcbtc(
        request: HttpServletRequest,
        @RequestParam("address") @EVMAddress address: String,
    ): ApiResponse<String> {

        authUtil.filterIPBlacklist(request)
        val user = authUtil.auth(request, address)

        val userCampaignInfo = airDropService.getUserCampaignInfo(user)
        if(userCampaignInfo.followDlcbtcTime == 0L) {
            userCampaignInfo.followDlcbtcTime = Date().time + 45 * 1000
            userCampaignInfoRepository.save(userCampaignInfo)

            val firstTradeRebateRecords = dlcbtcTradeRebateRecordRepository.findByBuyerAddressIgnoreCase(
                user.address
            )

            if(firstTradeRebateRecords.isNotEmpty()){
                val rebateRecord = firstTradeRebateRecords[0]
                rebateRecord.followDlcbtcTime = userCampaignInfo.followDlcbtcTime
                dlcbtcTradeRebateRecordRepository.save(rebateRecord)
            }
        }

        val parameter = dlcbtcCampaignService.getCampaignParameter()
        return ApiResponse.success(parameter.twitterAccountLink)
    }
}
