package io.vault.jasper.initializer

import io.vault.jasper.model.LossIsWinCampaignConfig
import io.vault.jasper.repository.LossIsWinCampaignConfigRepository
import org.springframework.stereotype.Component
import java.math.BigDecimal
import java.time.LocalDateTime
import javax.annotation.PostConstruct

@Component
class LossIsWinCampaignConfigInitializer(
    private val lossIsWinCampaignConfigRepository: LossIsWinCampaignConfigRepository
) {

    @PostConstruct
    fun initializeCampaignConfig() {
        val existingEnabledConfig = lossIsWinCampaignConfigRepository.findFirstByEnabled(true)
        if (existingEnabledConfig == null) {
            val defaultConfig = LossIsWinCampaignConfig(
                startTime = LocalDateTime.of(2023, 5, 6, 0, 0),
                endTime = LocalDateTime.of(2023, 6, 6, 23, 59),
                totalBtrPerOrderPool = BigDecimal("20000"),
                distributedBtrPerOrder = BigDecimal.ZERO,
                btrPerOrder = BigDecimal("2"),
                totalBtrLossPool = BigDecimal("130000"),
                enabled = true
            )
            lossIsWinCampaignConfigRepository.save(defaultConfig)
        }
    }
}