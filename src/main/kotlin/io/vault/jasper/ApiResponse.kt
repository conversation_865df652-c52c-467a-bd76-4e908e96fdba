package io.vault.jasper

data class ApiResponse<T>(
    val status: Int,
    val message: String,
    val data: T?
) {

    companion object {
        fun <T> success(data: T): ApiResponse<T> {
            return ApiResponse(0, "success", data)
        }

        fun <T> error(status: Int, message: String): ApiResponse<T> {
            return ApiResponse(status, message, null)
        }
    }

}

data class BitlayerResponse<T>(
    val code: Int,
    val message: String,
    val data: T?
) {

    companion object {
        fun <T> success(data: T): BitlayerResponse<T> {
            return BitlayerResponse(0, "success", data)
        }

        fun <T> error(status: Int, message: String): BitlayerResponse<T> {
            return BitlayerResponse(status, message, null)
        }
    }

}
