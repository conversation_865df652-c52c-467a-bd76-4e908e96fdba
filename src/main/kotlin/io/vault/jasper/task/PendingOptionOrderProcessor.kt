package io.vault.jasper.task

import io.vault.jasper.exception.BusinessException
import io.vault.jasper.exception.ResultEnum
import io.vault.jasper.repository.PendingOptionOrderRepository
import io.vault.jasper.service.blockchain.BlockchainServiceFactory
import io.vault.jasper.service.blockchain.EvmService
import org.slf4j.LoggerFactory
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.beans.factory.annotation.Value
import org.springframework.context.annotation.Profile
import org.springframework.scheduling.annotation.Scheduled
import org.springframework.stereotype.Service

@Service
@Profile("prod", "test")
class PendingOptionOrderProcessor @Autowired constructor(
    private val pendingOptionOrderRepository: PendingOptionOrderRepository,
    private val blockchainServiceFactory: BlockchainServiceFactory
) {

    private val logger = LoggerFactory.getLogger(this::class.java)

    @Value("\${schedule_task_on}")
    lateinit var scheduleOn: String

    @Scheduled(fixedDelay = 10 * 1000, initialDelay = 10 * 1000)
    fun run() {
        if(scheduleOn == "false"){
            return
        }
        pendingOptionOrderRepository.findBySaved(false).forEach { po ->
            val evmService = blockchainServiceFactory.getBlockchainService(po.chain) as? EvmService ?: run {
                po.saved = true
                po.message = "No service found for chain ${po.chain}"
                pendingOptionOrderRepository.save(po)
                logger.error(po.message)
                return@forEach
            }

            try {
                evmService.saveOptionOrderFromTxHash(po.txHash, createdTime = po.blockTime, optionStruct = po.optionOrderStruct)
            } catch (e: BusinessException) {
                when (e.code) {
                    ResultEnum.TRANSACTION_NOT_CONFIRMED.code -> return@forEach
                    ResultEnum.ORDER_ALREADY_EXISTS.code -> {}
                    else -> {
                        po.message = e.message
                    }
                }
            } catch (e: Exception) {
                po.message = e.message
            }

            po.saved = true
            pendingOptionOrderRepository.save(po)
            if (po.message != null) {
                logger.error(po.message)
            }
        }
    }
}