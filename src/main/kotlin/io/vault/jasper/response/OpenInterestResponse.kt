package io.vault.jasper.response

import io.swagger.annotations.ApiModel
import io.swagger.annotations.ApiModelProperty
import io.vault.jasper.enums.ChainType
import io.vault.jasper.enums.Symbol
import io.vault.jasper.model.OrderType
import java.math.BigDecimal

@ApiModel("Open Interest 信息")
data class OpenInterestResponse(
    @ApiModelProperty("Call Open Interest", required = true)
    val callOpenInterest: String,
    @ApiModelProperty("邀请用户总消费的权利金总数", required = true)
    val putOpenInterest: String
)